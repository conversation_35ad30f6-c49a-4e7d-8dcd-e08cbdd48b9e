(function () {
    'use strict';

    angular
        .module('app')
        .controller('DashboardController', DashboardController);

  /** @ngInject */
  function DashboardController($http, API_CONFIG, $interval, $rootScope, AuthorizationService) {

    var vm = this;

    

    vm.aps = [];
    vm.hostsparados = [];
    vm.graficos = {
        'pocos' : 'http://lab.pocos-net.com.br/chart2.php?graphid=12327&period=7200&width=540&height=150&decache=' + Math.random(),
        'andradas' : 'http://lab.pocos-net.com.br/chart2.php?graphid=12328&period=7200&width=540&height=150&decache=' + Math.random(),
    };

    vm.selecionaHost = selecionaHost;  
    vm.host_selecionado = {};  
      
    activate();

    function activate() {
        getResource();
    
        vm.reload_graficos = $interval(function(){
            vm.graficos = {
                    'pocos' : 'http://lab.pocos-net.com.br/chart2.php?graphid=12327&period=7200&width=540&height=150&decache=' + Math.random(),
                    'andradas' : 'http://lab.pocos-net.com.br/chart2.php?graphid=12328&period=7200&width=540&height=150&decache=' + Math.random(),
                };
        },30000);
        
        vm.reload_hosts = $interval(function(){
                $http.get(API_CONFIG.url + '/dashboard/hosts/parados').then(function (response) {
                    vm.hostsparados = response.data;
                });
        },30000);
        
      
        var dereg = $rootScope.$on('$locationChangeSuccess', function() {
            $interval.cancel(vm.reload_graficos);
            $interval.cancel(vm.reload_hosts);
            dereg();
        });
    }

    function getResource() {
      $http.get(API_CONFIG.url + '/dashboard/aps').then(function (response) {
        vm.aps = response.data;
	    });

      $http.get(API_CONFIG.url + '/dashboard/hosts/parados').then(function (response) {
        vm.hostsparados = response.data;
      });

    }
      
    function getData() {
            cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/eventos?page=' + vm.pagination[0].page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder +
              '&username=' + vm.username;
            if (vm.status !== '') {
                urlApi += '&status=' + vm.status;
            }
            $http.get(urlApi).then(function (response) {
                angular.copy(response.data.rows, vm.eventos[0]);
                angular.copy(response.data.pagination, vm.pagination[0]);

            });
            urlApi = API_CONFIG.url + '/eventos?page=' + vm.pagination[1].page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder + '&username=!' + vm.username;

            if (vm.status !== '') {
                urlApi += '&status=' + vm.status;
            }
            $http.get(urlApi).then(function (response) {
                angular.copy(response.data.rows, vm.eventos[1]);
                angular.copy(response.data.pagination, vm.pagination[1]);
                cfpLoadingBar.complete();
            });

    }  
      
    function selecionaHost(host){
        vm.host_selecionado = host;
    }  
      
  }

})();
