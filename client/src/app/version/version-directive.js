'use strict';

angular.module('app.version.version-directive', [])

    .directive('appVersion', ['version', function (version) {
        return function (scope, elm) {
            elm.text(version);
        };
    }])

    .directive('appCompilationDate', ['date', function (date) {
        return function (scope, elm) {
            elm.text(date);
        };
    }])

    .directive('appLog', ['log', function (log) {
        return function (scope, elm) {
            elm.text(log);
        };
    }]);
