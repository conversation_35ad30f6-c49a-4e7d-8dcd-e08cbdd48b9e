<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-wrench"></i> Ferramentas</a></li>
    <li><i class="glyphicon glyphicon-cloud-upload"></i> Serviço de Provisionamento</li>
</ol>

<div class="barra container-fluid">
    <div class="form-group">
        <form class="form-inline" role="form">
            <div class="form-group">
                Status do Serviço:
                <span class="label label-default" ng-class="[{'label-success': AMC.service_status=='UP'},
                    {'label-danger': AMC.service_status=='DOWN'}]"><b>{{AMC.service_status}}</b></span> <span
                    id="last_uptime">{{AMC.service_lastuptime}}</span> | Provisionadas Hoje: <b>{{AMC.today}}</b>
            </div>
        </form>


    </div>
</div>

<div class="container">
    <div class="row">
        <div class="table-responsive">
            <table id="unauthorized" class="table table-dark table-striped table-hover table-sm caption-top">
                <caption><svg ng-show="AMC.service_status=='UP'" class="blinking" width="8" height="5">
                        <circle fill="#337ab7" r="2" cx="2" cy="2"></circle>
                    </svg> Fila de Provisionamento</caption>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Data Inclusão</th>
                        <th>Data Atualização</th>
                        <th>Serial</th>
                        <th>Modelo</th>
                        <th>Patrimonio</th>
                        <th>Username</th>
                        <th>OLT</th>
                        <th>Placa</th>
                        <th>Porta</th>
                        <th>Script</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in AMC.unauthorized">
                        <td>{{item.status}}</td>
                        <td>{{item.data_alarme | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                        <td>{{item.data_atualizacao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                        <td>{{item.serial}}</td>
                        <td>{{item.modelo}}</td>
                        <td>{{item.patrimonio}}</td>
                        <td>{{item.username}}</td>
                        <td>{{item.olt}}</td>
                        <td>{{item.placa}}</td>
                        <td>{{item.porta}}</td>
                        <td>{{item.script}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div><!-- /.container -->

<div class="container">
    <div class="row">
        <div class="table-responsive">
            <table id="authlogs" class="table table-dark table-striped table-hover table-sm caption-top">
                <caption><svg ng-show="AMC.service_status=='UP'" class="blinking" width="8" height="5">
                        <circle fill="#337ab7" r="2" cx="2" cy="2"></circle>
                    </svg> Tarefas</caption>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Data</th>
                        <th>Tarefa</th>
                        <th>Log</th>
                        <th>Serial</th>
                        <th>Username</th>
                        <th>Verificado</th>
                        <th>Tentativas</th>
                        <th>Última Tentativa</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in AMC.authlogs">
                        <td>{{item.id}}</td>
                        <td>{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                        <td>{{item.task}}</td>
                        <td>{{item.log}}</td>
                        <td>{{item.serial}}</td>
                        <td>{{item.username}}</td>
                        <td><span class="label label-success" ng-if="item.verified==1">Sim</span>
                            <span class="label label-warning" ng-if="item.verified==0">Não</span>
                        </td>
                        <td>{{item.retries}}</td>
                        <td>{{item.last_retry | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div><!-- /.container -->