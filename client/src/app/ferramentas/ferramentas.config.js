'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/ferramentas/systemlog', {
                templateUrl: 'app/ferramentas/systemlog.html',
                controller: 'SystemLogController',
                controllerAs: 'SLC',
                title: 'System Log',
                authorize: ['ferramentas.read', 'ferramentas.write']
            })

            .when('/ferramentas/auth-monitor', {
                templateUrl: 'app/ferramentas/auth-monitor.html',
                controller: 'AuthMonitorController',
                controllerAs: 'AMC',
                title: 'Serviço de Provisionamento',
                authorize: ['ferramentas.read', 'ferramentas.write']
            })

            .when('/ferramentas/provision', {
                templateUrl: 'app/ferramentas/auth-workers.html',
                controller: 'AuthWorkersController',
                controllerAs: 'AWC',
                title: 'Serviço de Provisionamento (Workers)',
                authorize: ['ferramentas.read', 'ferramentas.write']
            })

            .when('/ferramentas/blacklist', {
                templateUrl: 'app/ferramentas/blacklist.html',
                controller: 'BlacklistController',
                controllerAs: 'BLC',
                title: 'OLT Blacklist',
                authorize: ['ferramentas.read', 'ferramentas.write']
            })

            .when('/ferramentas/zteadmin', {
                templateUrl: 'app/ferramentas/zteadmin.html',
                controller: 'ZTEAdminController',
                controllerAs: 'ZAC',
                title: 'ZTE Admin',
                authorize: ['ferramentas.read', 'ferramentas.write']
            });
    });
