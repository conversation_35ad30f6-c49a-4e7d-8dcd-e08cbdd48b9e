(function () {
    'use strict';

    angular
        .module('app')
        .controller('AuthMonitorController', AuthMonitorController);

    /** @ngInject */
    function AuthMonitorController(authMonitorSocket, $interval, $filter) {

        var vm = this;
        vm.authlogs = [];
        vm.unauthorized = [];
        vm.today = 0;
        vm.limit = 20;
        vm.service_status = 'VERIFICANDO...';
        vm.service_lastuptime = undefined
        vm.last_seen = 0

        activate();

        function activate() {



            $interval(function () {
                vm.last_seen = vm.last_seen + 1;
                if (vm.last_seen >= 30) {
                    vm.service_status = 'DOWN';
                }

            }, 1000);
        }

        authMonitorSocket.on('unauthorized', function (data) {
            vm.unauthorized = data;
        });


        authMonitorSocket.on('service_status', function (data) {
            vm.service_status = 'UP';
            vm.last_seen = 0;
            vm.service_lastuptime = new Date(data.now).toLocaleString();
        });

        authMonitorSocket.on('connect', function (data) {
            authMonitorSocket.emit('today');
        });

        authMonitorSocket.on('today', function (data) {
            vm.today = data.count;
        });

        authMonitorSocket.on('logs', function (data) {
            vm.authlogs = data;
        });

        authMonitorSocket.on('authlogs', function (data) {
            var index = vm.authlogs.findIndex(function (x) {
                return x.id === data.id;
            });
            if (index > -1) {
                vm.authlogs[index] = data;
            } else {
                vm.authlogs.push(data);
                vm.authlogs = $filter('orderBy')(vm.authlogs, 'data', true)
            }
        });

    }

})();


