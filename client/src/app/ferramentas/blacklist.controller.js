(function () {
    'use strict';

    angular
        .module('app')
        .controller('BlacklistController', BlacklistController);

    /** @ngInject */
    function BlacklistController($http, API_CONFIG, toaster) {

        var vm = this;
        vm.blacklist = [];
        vm.olts = [];
        vm.olt = '';
        vm.getBlacklist = getBlacklist;
        vm.removeBlacklist = removeBlacklist;

        activate();

        function activate() {
            getOlts();
        }

        function getOlts() {
            $http({
                url: API_CONFIG.nocws + '/oltlist',
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.olts = response.data;
            });
        }

        function getBlacklist() {
            vm.blacklist = [];
            $http({
                url: API_CONFIG.nocws + '/checkblacklist',
                method: "POST",
                data: { "olt": vm.olt }
            }).then(function (response) {
                vm.blacklist = response.data.BLACKLIST;
            });
        }

        function removeBlacklist(item) {
            $http({
                url: API_CONFIG.nocws + '/removeblacklist',
                method: "POST",
                data: {
                    "serial": item.ONU_SN,
                    "olt": item.OLT,
                    "placa": item.SLOT,
                    "porta": item.PON
                }
            }).then(function (response) {
                if (response.data == true) {
                    toaster.pop('success', "Blacklist", "ONU removida da blacklist");
                    getBlacklist();
                } else {
                    toaster.pop('error', "Blacklist", "Não foi possível remover a ONU da blacklist");
                }
            });
        }

    }

})();


