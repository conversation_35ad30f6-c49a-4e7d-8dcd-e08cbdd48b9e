(function () {
    'use strict';

    angular
        .module('app')
        .controller('ZTEAdminController', ZTEAdminController);

    /** @ngInject */
    function ZTEAdminController($rootScope, $scope, $http, ZTE_API, toaster) {
        var vm = this;
        vm.olt = {};
        vm.olts = [];
        vm.onus = [];
        vm.pending = [];
        vm.selectedonu = {};
        vm.filter = {
            condition: '~',
            field: 'Nome',
            value: ''
        };
        vm.filters = [];
        vm.filterfields = [
            'ID', 'Status', 'Serial', 'Nome', 'Tipo'
        ]
        vm.selectedOlt = {};
        vm.selectedCard = {};
        vm.atualizandoCards = false;
        vm.atualizandoOlts = false;
        vm.atualizandoPendings = false;
        vm.atualizandoOnus = false;
        vm.selectedPort = null;

        vm.removeFilter = function (filter) {
            vm.filters.forEach(function (f, index) {
                if (f.condition === filter.condition
                    && f.field === filter.field
                    && f.value === filter.value) {
                    vm.filters.splice(index, 1);
                }
            });

            vm.getonus();
        }

        vm.filteradd = function (filter) {
            if (!filter.field) {
                alert('É necessário preencher o campo do filtro.');
                return false;
            }
            if (!filter.condition) {
                alert('É necessário preencher o operador do filtro.');
                return false;
            }
            if (!filter.value) {
                alert('É necessário preencher o valor do filtro.');
                return false;
            }

            var duplicated = false;
            vm.filters.forEach(function (f) {
                if (f.condition === filter.condition
                    && f.field === filter.field
                    && f.value === filter.value) {
                    duplicated = true;
                }
            });

            if (!duplicated) {
                var newfilter = {};
                angular.copy(filter, newfilter);
                vm.filters.push(newfilter);
                vm.filter = {
                    condition: '~',
                    field: 'Nome',
                    value: ''
                };
            }
            else {
                alert('Este filtro já foi adicionado!');
                return false
            }

            vm.getonus();
        }

        vm.selectOlt = function (olt) {
            if (olt.status == 'offline')
                return false;

            vm.selectedOlt = olt;
            vm.selectedCard = {};
            vm.selectedPort = null;

            this.getoltcards(olt);
            this.getonus();
        }

        vm.selectCard = function (card) {
            vm.selectedCard = card;
            vm.selectedPort = null;
        }

        vm.selectPort = function (port) {
            vm.selectedPort = port;
        }


        vm.getoltinformation = function (olt) {
            vm.selectedOlt = {};

            $http({
                url: ZTE_API.url + '/showoltinformation',
                headers: {
                    'x-access-token': ZTE_API.token,
                },
                method: "POST",
                data: { "olt": olt.ip },
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.olt.info = response.data;
            });
        }

        vm.getoltcards = function (olt) {
            vm.atualizandoCards = true;
            vm.selectedOlt.cards = [];

            $http({
                url: ZTE_API.url + '/listoltcards',
                headers: {
                    'x-access-token': ZTE_API.token,
                },
                method: "POST",
                data: { "olt": olt.ip },
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.atualizandoCards = false;
                vm.selectedOlt.cards = response.data;
            });
        }

        vm.getoltsinformation = function getoltsinformation() {
            vm.atualizandoOlts = true;

            $http({
                url: ZTE_API.url + '/listoltsinformation',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.atualizandoOlts = false;
                vm.olts = response.data;
                vm.olts.forEach(function (olt, i) {
                    if (olt.processor) {
                        olt.processor.cpu_1m = parseFloat(olt.processor.cpu_1m);
                        olt.processor.cpu_5m = parseFloat(olt.processor.cpu_5m);
                        olt.processor.cpu_5s = parseFloat(olt.processor.cpu_5s);
                        olt.processor.mem = Math.round(parseFloat(olt.processor.mem));
                        olt.processor.phymem = parseFloat(olt.processor.phymem);
                        olt.processor.freemem = parseFloat(olt.processor.freemem);
                        olt.processor.peak = parseFloat(olt.processor.peak);
                    }
                });
            });
        };

        vm.getonus = function getonus() {
            var olt = vm.selectedOlt;
            vm.atualizandoOnus = true;
            vm.onus = [];

            $http({
                url: ZTE_API.url + '/listonustate',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "POST",
                data: {
                    "olt": olt.ip,
                    "showdevices": 1,
                    filters: vm.filters
                },
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.atualizandoOnus = false;
                vm.onus = response.data;
            });
        };

        vm.getpending = function getpending() {
            vm.pending = [];
            vm.atualizandoPendings = true;
            $http({
                url: ZTE_API.url + '/listunauth',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.atualizandoPendings = false;
                vm.pending = response.data;
            });
        };

        vm.deauth = function deauth(onu) {
            $http({
                url: ZTE_API.url + '/deauth',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "POST",
                data: { "serial": onu["serial"] },
                ignoreLoadingBar: true
            }).then(function (response) {
                if (response.data == 'Deauth OK') {
                    toaster.pop('success', "ONU desautorizada", "ONU desautorizada com sucesso!");
                } else {
                    toaster.pop('error', "Erro ao desautorizar", response.data);
                }
            });

        };

        vm.auth = function auth(onu) {
            $http({
                url: ZTE_API.url + '/auth',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "POST",
                data: { "olt": onu["olt_ip"], "serial": onu["serial"], "type": onu["onutype"], "name": onu["name"] },
                ignoreLoadingBar: true
            }).then(function (response) {
                if (response.data.hasOwnProperty('onuid')) {
                    toaster.pop('success', "ONU autorizada", "ONU autorizada com sucesso!");
                } else {
                    toaster.pop('error', "Erro ao autorizar", response.data);
                }
            });

        };

        vm.getsignal = function (item) {
            vm.getonudetail(item);
            item['atualizandosinal'] = 1;
            item['signal'] = '';
            $http({
                url: ZTE_API.url + '/signal',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "POST",
                data: { "serial": item["serial"] },
                ignoreLoadingBar: true
            }).then(function (response) {
                item['atualizandosinal'] = 0;
                item["signal"] = response.data;
            }).catch(function (err) {
                item['atualizandosinal'] = 0;
                item["signal"] = "Erro ao consultar o sinal";
            });

        }

        vm.selectonu = function (onu) {
            vm.selectedonu = onu;
        }

        vm.itemclick = function (item) {
            if (item.hasOwnProperty('expanded')) {
                if (item['expanded'] == 1) {
                    item['expanded'] = 0;
                } else {
                    item['expanded'] = 1;
                }
            } else {

                item['expanded'] = 1;
            }

            if (item['expanded'] == 1) {
                // this.getsignal(item);
            }

        }

        vm.getonudetail = function (item) {
            item['atualizandodetail'] = 1;
            item['detail'] = '';
            $http({
                url: ZTE_API.url + '/showdetailonu',
                headers: {
                    'x-access-token': ZTE_API.token
                },
                method: "POST",
                data: { "serial": item["serial"] },
                ignoreLoadingBar: true
            }).then(function (response) {
                item['atualizandodetail'] = 0;
                item["detail"] = response.data;
            }).catch(function (err) {
                item['atualizandodetail'] = 0;
                item["detail"] = "Erro ao consultar o sinal";
            });
        }

        function containsObject(obj, list) {
            var i;
            for (i = 0; i < list.length; i++) {
                if (list[i] === obj) {
                    return true;
                }
            }

            return false;
        }

        this.getoltsinformation();
        this.getpending();
    };

})();
