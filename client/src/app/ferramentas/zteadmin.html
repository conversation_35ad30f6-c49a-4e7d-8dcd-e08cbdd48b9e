<ol class="breadcrumb">
    <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li class="active"><i class="glyphicon glyphicon-modal-window"></i> ZTE Admin</li>
</ol>

<table class="table table-striped table-hover table-bordered align-center valign-middle" style="margin-bottom: 0px;">
    <thead>
        <tr>
            <th colspan="7" style="text-align: center">
                <h4>ONUs aguardando provisionamento:</h4>
            </th>
        </tr>
        <tr>
            <th>OLT</th>
            <th>Serial</th>
            <th>Tipo</th>
            <th>Chassi</th>
            <th>Placa</th>
            <th>Porta</th>
            <th>Ações</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-if="ZAC.atualizandoPendings" class="bg-warning">
            <td colspan="7"><img src="assets/images/ajax-loader.gif" class="margin-right-5"
                    ng-show="ZAC.atualizandoPendings">Carregando...</td>
        </tr>
        <tr ng-if="!ZAC.atualizandoPendings && ZAC.pending.length == 0" class="text-success">
            <td colspan="7">Não há ONUs aguardando provisionamento</td>
        </tr>
        <tr ng-repeat="item in ZAC.pending">
            <td>{{item.olt_name}}</td>
            <td>{{item.serial}}</td>
            <td>{{item.type}}</td>
            <td>{{item.chassi}}</td>
            <td>{{item.slot}}</td>
            <td>{{item.pon}}</td>
            <td>
                <a authorize="['ferramentas.write', 'develop.read']" href="" data-toggle="modal" data-target="#frmauth" ng-click="ZAC.selectonu(item)"
                    class="btn btn-success btn-sm" title="Autorizar ONU"><i class="glyphicon glyphicon-plus"></i></a>
            </td>
        </tr>
    </tbody>
</table>

<div class="barra ng-scope" style="text-align: center; margin-top: 10px;">
    <div ng-if="ZAC.atualizandoOlts" style="padding: 15px 0px;">
        <img src="assets/images/spinner-blue.gif" style="width: 25px; margin-bottom: 5px;">
        <br />
        Buscando OLTs...
    </div>
    <div ng-if="!ZAC.atualizandooLTS" class="flex row"
        style="margin-left: 0px; margin-right: 0px; margin: 0 auto !important;justify-content: center;">
        <div ng-repeat="olt in ZAC.olts" class="col-sm td valign-middle" style="padding: 8px 0px;">
            <div class="olt-item align-center"
                ng-class="{'selected':olt.ip == ZAC.selectedOlt.ip, 'disabled': olt.status == 'offline'}"
                ng-click="ZAC.selectOlt(olt)" title="Hardware: {{olt.hardware}}&#10;Software: {{olt.software}}">
                <b>{{olt.descricao}}</b>
                <div>
                    {{olt.ip}}
                    <br />
                    <table class="full-width">
                        <tr>
                            <td style="width: 15%;">
                                CPU:
                            </td>
                            <td style="padding: 0px 10px;">
                                <div class="progress progress-bordered progress-sm">
                                    <div class="progress-bar"
                                        ng-class="[{'progress-bar-success': olt.processor.cpu_5s < 20},{'progress-bar-danger': olt.processor.cpu_5s > 80}]"
                                        role="progressbar" ng-style="{'width': olt.processor.cpu_5s + '%'}">
                                    </div>
                                </div>
                            </td>
                            <td style="width: 2%;">
                                {{olt.processor ? olt.processor.cpu_5s + '%' : ''}}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                RAM:
                            </td>
                            <td style="padding: 0px 10px;">
                                <div class="progress progress-bordered progress-sm">
                                    <div class="progress-bar"
                                        ng-class="[{'progress-bar-success': olt.processor.mem < 20},{'progress-bar-danger': olt.processor.mem > 80}]"
                                        role="progressbar" ng-style="{'width': olt.processor.mem + '%'}">
                                    </div>
                            </td>
                            <td>
                                {{olt.processor ? olt.processor.mem + '%' : ''}}
                            </td>
                        </tr>
                        <tr ng-if="olt.status == 'online'">
                            <td colspan="3" style="padding-top: 5px;">
                                <b>UP</b>: {{olt.uptime}}
                            </td>
                        </tr>
                        <tr ng-if="olt.status == 'offline'">
                            <td colspan="3" style="padding-top: 5px;">
                                <span class="text-danger"><b>OFFLINE</b></span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<table class="full-width">
    <tr>
        <td colspan="2">
            <table class="full-width table table-hover table-striped table-bordered align-center valign-middle"
                style="margin-bottom: 5px;">
                <thead>
                    <tr>
                        <th>
                            <h4>ONUs provisionadas:</h4>
                        </th>
                    </tr>
                </thead>
            </table>
        </td>
    </tr>
    <tr>

        <td style="width: 140px; vertical-align: top; padding-right: 5px;">

            <div>
                <table class="table full-width table-bordered align-center valign-middle">
                    <thead>
                        <tr class="barra">
                            <th colspan="2" class="align-center">
                                <h5>Placa</h5>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="ZAC.atualizandoCards" class="bg-warning">
                            <td colspan="2"><img src="assets/images/ajax-loader.gif" class="margin-right-5"
                                    ng-show="ZAC.atualizandoCards">Carregando...</td>
                        </tr>
                        <tr ng-if="(ZAC.selectedOlt | json) == '{}'" class="bg-warning">
                            <td>Selecione um OLT...</td>
                        </tr>
                        <tr ng-if="(ZAC.selectedOlt | json) != '{}' && !ZAC.atualizandoCards"
                            ng-class="{'selected': (ZAC.selectedCard | json) == '{}'}" class="pointer hoverable"
                            ng-click="ZAC.selectCard({})">
                            <td><b>Visão geral</b></td>
                        </tr>
                        <!-- Talvez colocar filtragem de placas do tipo GFGH - placas que não são de serviço -->
                        <!--ng-if="card.type == 'GFGH'"-->
                        <tr class="pointer hoverable" ng-class="{'selected': card.slot == ZAC.selectedCard.slot}"
                            ng-repeat="card in ZAC.selectedOlt.cards" ng-click="ZAC.selectCard(card);">
                            <td>{{card.slot}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div>
                <table class="table full-width table-bordered align-center valign-middle">
                    <thead>
                        <tr class="barra">
                            <th class="align-center">
                                <h5>Porta</h5>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="(ZAC.selectedCard | json) == '{}'" class="bg-warning">
                            <td>Selecione uma placa...</td>
                        </tr>
                        <tr ng-if="(ZAC.selectedCard | json) != '{}' && !ZAC.atualizandoCards"
                            ng-class="{'selected': ZAC.selectedPort == null}" class="pointer hoverable"
                            ng-click="ZAC.selectPort(null);">
                            <td><b>Visão geral</b></td>
                        </tr>
                        <tr ng-if="(ZAC.selectedCard | json) != '{}'" ng-class="{'selected': i == ZAC.selectedPort}"
                            ng-repeat="i in generateRange(1, ZAC.selectedCard.port, 1)" class="pointer hoverable"
                            ng-click="ZAC.selectPort(i);">
                            <td>{{i}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </td>

        <td style="vertical-align: top">

            <div class="barra ng-scope align-center" style="margin-top: 0px;">
                <div class="form-group">
                    <form class="form-inline" role="form">
                        <div class="form-group">
                            Filtro: <select class="form-control" ng-model="ZAC.filter.field">
                                <option hidden value="">Selecione...</option>
                                <option ng-repeat="item in ZAC.filterfields" value="{{item}}">{{item}}</option>
                            </select>
                            <select class="form-control" ng-model="ZAC.filter.condition">
                                <option hidden value="">Selecione...</option>
                                <option value="=">igual a</option>
                                <option value="~">contém</option>
                                <option value="<>">diferente de</option>
                            </select>
                            <input class="form-control" ng-model="ZAC.filter.value">
                            <button ng-disabled="(ZAC.selectedOlt | json) == '{}'" class="btn btn-default btn-sm"
                                ng-click="ZAC.filteradd(ZAC.filter)"><i class="glyphicon glyphicon-plus"></i>
                                Adicionar filtro</button>

                        </div>
                        <!-- <div class="form-group">
                            <button class="btn btn-default btn-sm" ng-click="ZAC.getonus(ZAC.selectedOlt)"><i
                                    class="glyphicon glyphicon-search"></i>
                                Pesquisar</button>
                        </div> -->

                    </form>
                </div>
            </div>


            <div class="col-md-12" ng-if="ZAC.filters.length > 0"
                style="padding-left: 0px; padding-right: 0px; padding-bottom: 5px;">
                <b>Filtros ativos:</b>
                <span class="label label-default" ng-repeat="item in ZAC.filters track by $index"
                    style="margin-right: 5px;">
                    '{{item.field}}' <b>{{item.condition == '=' ? 'igual a' : (item.condition == '~' ? 'contém' :
                        'diferente de')}}</b> '{{item.value}}' <a class="span-close-btn" href="#" title="Remover filtro"
                        ng-click="ZAC.removeFilter(item)">X</a></span>
            </div>




            <table class="table table-hover table-striped table-bordered align-center valign-middle">
                <thead>
                    <tr>
                        <th></th>
                        <th>ID</th>
                        <th>Status</th>
                        <th>Serial</th>
                        <th>Nome</th>
                        <th>Tipo</th>
                        <th>Placa</th>
                        <th>Porta</th>
                        <th>Sinal ONU</th>
                        <th>Sinal OLT</th>

                        <th>Ações</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-if="ZAC.atualizandoOnus" class="bg-warning">
                        <td colspan="11"><img src="assets/images/ajax-loader.gif" class="margin-right-5"
                                ng-show="ZAC.atualizandoOnus">Carregando...</td>
                    </tr>
                    <tr ng-if="(ZAC.selectedOlt | json) == '{}'" class="bg-warning">
                        <td colspan="11">Selecione um OLT...</td>
                    </tr>
                    <tr ng-if="!ZAC.atualizandoOnus && (ZAC.selectedOlt | json) != '{}' && ZAC.onus.length == 0"
                        class="bg-warning">
                        <td colspan="11">Nenhuma ONU encontrada</td>
                    </tr>
                    <tr class="clickable" ng-repeat-start="item in ZAC.onus"
                        ng-if="((ZAC.selectedCard | json) == '{}' || ZAC.selectedCard.slot == item.slot) && (ZAC.selectedPort === null || ZAC.selectedPort == item.pon)">
                        <td style="cursor: pointer;" title="Mais informações" ng-click="ZAC.itemclick(item)"
                            data-toggle="collapse" data-target="#details-{{item.slot}}-{{item.pon}}-{{item.onuid}}">
                            <span class="glyphicon glyphicon-info-sign" style="cursor: pointer;"></span>
                        </td>
                        <td>{{item.onuid}}</td>
                        <td>
                            <span class="label"
                                ng-class="[{'label-success': item.phase == 'working'}, {'label-danger': item.phase !== 'working'}]">
                                {{item.phase}}
                            </span>
                        </td>
                        <td>{{item.serial}}</td>
                        <td>{{item.name}}</td>
                        <td>{{item.type}}</td>
                        <td>{{item.slot}}</td>
                        <td>{{item.pon}}</td>
                        <td>
                            <img src="assets/images/ajax-loader.gif"
                                ng-show="item.atualizandosinal">{{item.signal.down.onu}}
                        </td>
                        <td>
                            <img src="assets/images/ajax-loader.gif"
                                ng-show="item.atualizandosinal">{{item.signal.up.olt}}
                        </td>


                        <td>
                            <a href="" title="Atualizar sinal" class="btn btn-default btn-sm"
                                ng-click="ZAC.getsignal(item)" ng-disabled="item.atualizandosinal"><i
                                    class="glyphicon glyphicon-signal"></i></a>
                            <a authorize="['ferramentas.write', 'develop.read']" href="" ng-really-message="Tem certeza que deseja configurar a ONU {{item.serial}} ?"
                                ng-really-click="ZAC.config(item)" item="host" class="btn btn-default btn-sm"
                                title="Configurar ONU"><i class="glyphicon glyphicon-wrench"></i></a>
                            <a authorize="['ferramentas.write', 'develop.read']" href="" ng-really-message="Tem certeza que deseja desautorizar a ONU {{item.serial}} ?"
                                ng-really-click="ZAC.deauth(item)" item="host" class="btn btn-danger btn-sm"
                                title="Desautorizar ONU"><i class="glyphicon glyphicon-remove"></i></a>
                        </td>
                    </tr>
                    <tr ng-repeat-end
                        ng-if="((ZAC.selectedCard | json) == '{}' || ZAC.selectedCard.slot == item.slot) && (ZAC.selectedPort === null || ZAC.selectedPort == item.pon)">
                        <td colspan="13" style="padding: 0 !important;">
                            <div id="details-{{item.slot}}-{{item.pon}}-{{item.onuid }}" class="collapse">

                                <div class="col-md-2 vert-align">
                                    <table class="table table-bordered">
                                        <caption>Outras informações</caption>
                                        <tr>
                                            <td scope="col" class="vert-align text-center col-md-2">
                                                <strong>OMCC_STATE</strong>
                                            </td>
                                            <td class="vert-align text-center col-md-2">
                                                <span class="label"
                                                    ng-class="[{'label-success': item.omcc == 'enable'}, {'label-danger': item.omcc !== 'enable'}]">
                                                    {{item.omcc}}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td scope="col" class="vert-align text-center col-md-2">
                                                <strong>ADMIN_STATE</strong>
                                            </td>
                                            <td class="vert-align text-center col-md-2">
                                                <span class="label"
                                                    ng-class="[{'label-success': item.admin == 'enable'}, {'label-danger': item.admin !== 'enable'}]">{{item.admin}}</span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-2 vert-align">

                                    <table class="table table-bordered">
                                        <caption>Detalhes</caption>

                                        <tr>
                                            <td scope="col" class="vert-align text-center col-md-2">
                                                <strong>Online Duration</strong>
                                            </td>
                                            <td class="vert-align text-center col-md-2">
                                                {{item.detail['Online Duration']}}<img
                                                    src="assets/images/ajax-loader.gif"
                                                    ng-show="item.atualizandodetail">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td scope="col" class="vert-align text-center col-md-2">
                                                <strong>ONU Distance</strong>
                                            </td>
                                            <td class="vert-align text-center col-md-2">
                                                {{item.detail['ONU Distance']}}
                                            </td>
                                        </tr>

                                    </table>

                                </div>

                                <div class="col-md-4 vert-align">
                                    <table class="table table-bordered table-hover">
                                        <caption>Status</caption>

                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Authpass</th>
                                                <th>Offline</th>
                                                <th>Cause</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="status in item.detail['status']">
                                                <td>{{status.id}}<img src="assets/images/ajax-loader.gif"
                                                        ng-show="item.atualizandodetail"></td>
                                                <td>{{status.authpass_date}} {{status.authpass_time}}</td>
                                                <td>{{status.offline_date}} {{status.offline_time}}</td>
                                                <td>{{status.cause}}</td>
                                            </tr>
                                        </tbody>


                                    </table>

                                </div>
                        </td>
                    </tr>


        </td>
    </tr>
</table>

<div class="modal" id="frmauth" tabindex="-1" role="dialog" aria-labelledby="frmauthlabel" aria-hidden="true"
    modal="showModal" close="cancel()">

    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmauthlabel">Autorizar ONU</h4>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="serial">Serial</label>
                        <input type="text" class="form-control" id="serial" ng-model="ZAC.selectedonu.serial" disabled>
                    </div>
                    <div class="form-group">
                        <label for="type">Type</label>
                        <select class="form-control" id="type" ng-model="ZAC.selectedonu.onutype">
                            <option value="F660">F660</option>
                            <option value="F670L">F670L</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="serial">Name</label>
                        <input type="text" class="form-control" id="name" ng-model="ZAC.selectedonu.name">
                    </div>
                </form>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer">

                <button authorize="['ferramentas.write', 'develop.read']" type="button" class="btn btn-primary" ng-click="ZAC.auth(ZAC.selectedonu)" data-dismiss="modal">
                    OK </button>
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    Cancelar
                </button>
            </div>
        </div>
    </div>
</div>