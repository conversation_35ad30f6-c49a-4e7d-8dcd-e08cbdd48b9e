<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-wrench"></i> Ferramentas</a></li>
    <li><i class="glyphicon glyphicon-eye-close"></i> OLT Blacklist</li>
</ol>

<div class="barra ng-scope">
    <div class="form-group">
        <div class="form-group pull-left">
            <form class="form-inline" role="form">
                <div class="form-group">
                    <select class="form-control ng-pristine ng-valid ng-not-empty ng-touched" ng-model="BLC.olt"
                        ng-change="BLC.getBlacklist()">
                        <option hidden selected value="">Selecione a OLT</option>
                        <optgroup label="OLT:">
                            <option ng-repeat="olt in BLC.olts" value="{{olt.ip}}">
                                {{olt.nome}}
                            </option>
                        </optgroup>

                    </select>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="col-md-6">
    <h6>Resultado da pesquisa:</h6>
    <table class="table table-striped table-hover table-bordered">
        <thead>
            <tr>
                <th class="vert-align text-center">OLT</th>
                <th class="vert-align text-center">Placa</th>
                <th class="vert-align text-center">Porta</th>
                <th class="vert-align text-center">Serial</th>
                <th class="vert-align text-center">ID</th>
                <th class="vert-align text-center">Tempo</th>
                <th class="vert-align text-center">Flag</th>
                <th class="vert-align text-center">Ação</th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="item in BLC.blacklist">
                <td class="vert-align text-center">{{item.OLT}}</td>
                <td class="vert-align text-center">{{item.SLOT}}</td>
                <td class="vert-align text-center">{{item.PON}}</td>
                <td class="vert-align text-center">{{item.ONU_SN}}</td>
                <td class="vert-align text-center">{{item.ONU_ID}}</td>
                <td class="vert-align text-center">{{item.TIME | amDurationFormat : 'seconds'}}</td>
                <td class="vert-align text-center">{{item.FLAG}}</td>
                <td class="vert-align text-center"><a authorize="['ferramentas.write', 'develop.read']" href=""
                        ng-really-message="Tem certeza que deseja remover da blacklist ?"
                        ng-really-click="BLC.removeBlacklist(item)" item="item" class="btn btn-danger btn-sm"
                        title="Remover blacklist"><i class="glyphicon glyphicon-trash"></i></a></td>
            </tr>
        </tbody>
    </table>

</div>