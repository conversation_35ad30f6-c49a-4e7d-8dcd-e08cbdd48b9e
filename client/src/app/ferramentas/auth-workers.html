<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-wrench"></i> Ferramentas</a></li>
    <li><i class="glyphicon glyphicon-cloud-upload"></i> Serviço de Provisionamento</li>
</ol>

<div class="container-fluid">




<div class="row">
    <a href="http://172.16.10.39:5005/rq" target="_blank" authorize="develop.write" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-dashboard"></i> RQ Dashboard</a>
    <a href="*************************************************" target="_blank" authorize="develop.write" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-list-alt"></i> Supervisor</a>
    <br><br>
                                <div><svg class="blinking" width="8" height="6">
                                    <circle fill="#337ab7" r="3" cx="3" cy="3"></circle>
                                </svg> <i>*Atualizada em tempo real</i></div>

</div>
    <div class="row">
        <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('queued')">
                <i class="glyphicon glyphicon-indent-left"></i> <b>Na fila </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('failed')">
                <i class="glyphicon glyphicon-remove-sign"></i> <b>Falha </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('canceled')">
                <i class="glyphicon glyphicon-ban-circle"></i> <b>Cancelados </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('finished')">
                <i class="glyphicon glyphicon-ok-sign"></i> <b>Finalizados </b></a>
            </li>
            <li ng-if="AWC.userRoles.includes('root')">
              <a data-target="#log_detalhado" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('log_detalhado')">
                <i class="glyphicon glyphicon-list"></i> <b>LOG DETALHADO </b></a>
            </li>
          </ul>

          <div class="tab-content">

            <div class="tab-pane active" id="lista">
        <div class="table-responsive">
<table class="table table-hover table-striped table-bordered align-center valign-middle">

    <thead>
        <tr>
            <th></th>
            <th>Status</th>
            <th>Data</th>
            <th>Username</th>
            <th>Serial</th>
            <th>Modelo</th>
            <th>OLT</th>
            <th>OLT IP</th>
            <th>OLT Modelo</th>
            <th>Placa</th>
            <th>Porta</th>
            <th>Info</th>
        </tr>
    </thead>

    <tbody>

        <tr class="clickable" ng-repeat-start="item in AWC.provisions.current">
            <td style="cursor: pointer;" title="Mais informações" ng-click="AWC.itemclick(item)"
                data-toggle="collapse" data-target="#details-{{item.id}}">
                <span class="glyphicon" ng-class="[{'glyphicon-chevron-down': item.expanded != 1}, {'glyphicon-chevron-up': item.expanded == 1} ]" style="cursor: pointer;"></span>
            </td>
            <td><span class="label"
                ng-class="[{'label-success': item.status == 'finished'},
                          {'label-warning': item.status == 'queued'},
                          {'label-default': item.status == 'started'},
                          {'label-danger': item.status == 'failed' || item.status == 'canceled'}]">{{item.status}}</span>
            </td>

            <td>{{item.enqueued_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td><a href="/helpdesk?username={{item.username}}" target="_blank">{{item.username}}</a></td>
            <td>
              <a href="#" ng-if="AWC.userRoles.includes('root')" ng-click="AWC.searchLogsBySerial(item.serial)" class="serial-link" title="Ver logs deste serial">{{item.serial}}</a>
              <span ng-if="!AWC.userRoles.includes('root')">{{item.serial}}</span>
            </td>
            <td>{{item.model}}</td>
            <td>{{item.olt}}</td>
            <td>{{item.olt_ip}}</td>
            <td>{{item.olt_model}}</td>
            <td>{{item.slot}}</td>
            <td>{{item.pon}}</td>
            <td>{{item.exc_info}}</td>
        </tr>
        <tr ng-repeat-end>
            <td colspan="13" style="padding: 0 !important;">
                <div id="details-{{item.id}}" class="collapse">

                    <div class="col-md-5 vert-align">

                        <table class="table table-bordered table-hover">
                            <caption>Jobs <button class="btn btn-default btn-sm"
                                ng-click="AWC.getJobs(item)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button></caption>

                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Descrição</th>
                                    <th>Data Início</th>
                                    <th>Data Fim</th>
                                    <th>Info</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="job in item.jobs">
                                    <td><span class="label"
                                        ng-class="[{'label-success': job.status == 'finished'},
                                                  {'label-warning': job.status == 'queued'},
                                                  {'label-default': job.status == 'deferred'},
                                                  {'label-default': job.status == 'started'},
                                                  {'label-danger': job.status == 'failed' || job.status == 'canceled'}]">{{job.status}}</td>
                                    <td>{{job.description}}</td>
                                    <td>{{job.started_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                    <td>{{job.ended_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                    <td>{{job.exc_info}}</td>
                                    <td><button ng-if="job.status == 'failed'" class="btn btn-sm btn-default" ng-click="AWC.requeueJob(job.id)"><i class="glyphicon glyphicon-repeat"></i> Reiniciar</button></td>

                                </tr>
                            </tbody>


                        </table>

                    </div>
            </td>
        </tr>
    </tbody>
</table>
<p>
    <b>Na Fila</b> Exibe os provisionamentos que atendem os requisitos e entraram na fila <br>
    <b>Falha</b> Exibe os provisionamentos que tiveram falha em algum processo do provisionamento <br>
    <b>Cancelados</b> Exibe os provisionamentos que não entraram na fila por não atenderem um ou mais requisitos <br>
    <b>Finalizados</b> Exibe os provisionamentos concluídos com sucesso
</p>
</div>



</div>

<div id="log_detalhado" class="tab-pane fade in">
  <div class="row">
    <div class="col-md-12">
      <h4><i class="glyphicon glyphicon-list"></i> Logs do AuthService</h4>

      <!-- Filtros -->
      <div class="panel panel-default">
        <div class="panel-heading">
          <h5 class="panel-title">Filtros</h5>
        </div>
        <div class="panel-body">
          <form class="form-inline">
            <div class="form-group">
              <label for="filter_serial">Serial:</label>
              <input type="text" class="form-control" id="filter_serial" ng-model="AWC.logFilters.serial" placeholder="Serial da ONU">
            </div>

            <div class="form-group">
              <label for="filter_olt_ip">OLT IP:</label>
              <input type="text" class="form-control" id="filter_olt_ip" ng-model="AWC.logFilters.olt_ip" placeholder="IP da OLT">
            </div>

            <div class="form-group">
              <label for="filter_data_inicio">Data Início:</label>
              <input type="datetime-local" class="form-control" id="filter_data_inicio" ng-model="AWC.logFilters.data_inicio">
            </div>

            <div class="form-group">
              <label for="filter_data_fim">Data Fim:</label>
              <input type="datetime-local" class="form-control" id="filter_data_fim" ng-model="AWC.logFilters.data_fim">
            </div>

            <button type="button" class="btn btn-primary" ng-click="AWC.searchLogs()">
              <i class="glyphicon glyphicon-search"></i> Buscar
            </button>

            <button type="button" class="btn btn-default" ng-click="AWC.clearLogFilters()">
              <i class="glyphicon glyphicon-remove"></i> Limpar
            </button>
          </form>
        </div>
      </div>

      <!-- Loading -->
      <div ng-if="AWC.loadingLogs" class="text-center">
        <i class="fa fa-spinner fa-spin"></i> Carregando logs...
      </div>

      <!-- Tabela de Logs -->
      <div ng-if="!AWC.loadingLogs && AWC.authLogs.length > 0" class="table-responsive">
        <table class="table table-hover table-striped table-bordered">
          <thead>
            <tr>
              <th>ID</th>
              <th>Data/Hora</th>
              <th>Flag</th>
              <th>Mensagem</th>
              <th>Extra Info</th>
              <th>Serial</th>
              <th>OLT IP</th>
              <th>OLT Nome</th>
              <th>Slot/PON</th>
              <th>Processo</th>
              <th>Erro</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="log in AWC.authLogs" ng-class="{'danger': log.error}">
              <td>{{log.id}}</td>
              <td>{{log.created_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
              <td>
                <span class="label"
                      ng-class="{'label-info': log.flag == 'INFO',
                                'label-warning': log.flag == 'WARNING',
                                'label-danger': log.flag == 'ERROR',
                                'label-success': log.flag == 'SUCCESS'}">
                  {{log.flag}}
                </span>
              </td>
              <td>{{log.message}}</td>
              <td>
                <div ng-if="log.extra_info">
                  <button class="btn btn-xs btn-info" ng-click="AWC.showExtraInfoModal(log)" title="Ver detalhes do JSON">
                    <i class="glyphicon glyphicon-list-alt"></i> JSON
                  </button>
                </div>
                <span ng-if="!log.extra_info" class="text-muted">-</span>
              </td>
              <td>{{log.onu_serial}}</td>
              <td>{{log.olt_ip}}</td>
              <td>{{log.olt_name}}</td>
              <td>{{log.slot}}/{{log.pon}}</td>
              <td>{{log.process}}</td>
              <td>
                <div ng-if="log.error">
                  <span class="label label-danger">ERRO</span>
                  <div ng-if="log.error_message" class="text-danger small">
                    {{log.error_message}}
                  </div>
                  <div ng-if="log.error_stack">
                    <button class="btn btn-xs btn-danger" ng-click="AWC.toggleErrorStack(log)">
                      <i class="glyphicon glyphicon-exclamation-sign"></i> Ver Stack
                    </button>
                    <div ng-if="log.showErrorStack" class="well well-sm" style="margin-top: 5px;">
                      <pre class="error-stack">{{log.error_stack}}</pre>
                    </div>
                  </div>
                </div>
                <span ng-if="!log.error" class="text-success">OK</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mensagem quando não há logs -->
      <div ng-if="!AWC.loadingLogs && AWC.authLogs.length === 0 && AWC.logsSearched" class="alert alert-info">
        <i class="glyphicon glyphicon-info-sign"></i> Nenhum log encontrado com os filtros aplicados.
      </div>

      <!-- Mensagem inicial -->
      <div ng-if="!AWC.loadingLogs && !AWC.logsSearched" class="alert alert-warning">
        <i class="glyphicon glyphicon-search"></i> Use os filtros acima para buscar logs do AuthService.
      </div>
    </div>
  </div>

  <!-- Modal para ExtraInfo -->
  <div class="modal fade" id="extraInfoModal" tabindex="-1" role="dialog" aria-labelledby="extraInfoModalLabel">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="extraInfoModalLabel">
            <i class="glyphicon glyphicon-list-alt"></i> Informações Extras do Log
          </h4>
        </div>
        <div class="modal-body">
          <div ng-if="AWC.selectedLog">
            <div class="row">
              <div class="col-md-6">
                <strong>ID do Log:</strong> {{AWC.selectedLog.id}}
              </div>
              <div class="col-md-6">
                <strong>Data/Hora:</strong> {{AWC.selectedLog.created_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
              </div>
            </div>
            <div class="row" style="margin-top: 10px;">
              <div class="col-md-6">
                <strong>Flag:</strong>
                <span class="label"
                      ng-class="{'label-info': AWC.selectedLog.flag == 'INFO',
                                'label-warning': AWC.selectedLog.flag == 'WARNING',
                                'label-danger': AWC.selectedLog.flag == 'ERROR',
                                'label-success': AWC.selectedLog.flag == 'SUCCESS'}">
                  {{AWC.selectedLog.flag}}
                </span>
              </div>
              <div class="col-md-6">
                <strong>Serial:</strong> {{AWC.selectedLog.onu_serial}}
              </div>
            </div>
            <div class="row" style="margin-top: 10px;">
              <div class="col-md-12">
                <strong>Mensagem:</strong> {{AWC.selectedLog.message}}
              </div>
            </div>
            <hr>
            <div class="row">
              <div class="col-md-12">
                <h5><strong>Informações Extras (JSON):</strong></h5>
                <pre class="json-viewer-modal">{{AWC.formatJSON(AWC.selectedLog.extra_info)}}</pre>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">
            <i class="glyphicon glyphicon-remove"></i> Fechar
          </button>
          <button type="button" class="btn btn-primary" ng-click="AWC.copyJSONToClipboard()">
            <i class="glyphicon glyphicon-copy"></i> Copiar JSON
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Tab content -->
</div>


</div>
</div>

<style>
.json-viewer {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-stack {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  max-height: 150px;
  overflow-y: auto;
  white-space: pre-wrap;
  color: #721c24;
}

.form-inline .form-group {
  margin-right: 15px;
  margin-bottom: 10px;
}

.form-inline label {
  margin-right: 5px;
  font-weight: bold;
}

#log_detalhado .table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

#log_detalhado .table td {
  vertical-align: middle;
}

#log_detalhado .table tr.danger {
  background-color: #f2dede;
}

.panel-title {
  margin: 0;
  font-weight: bold;
}

.serial-link {
  color: #337ab7;
  text-decoration: none;
  cursor: pointer;
  font-weight: bold;
}

.serial-link:hover {
  color: #23527c;
  text-decoration: underline;
}

.json-viewer-modal {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.4;
}

.modal-lg .modal-body {
  padding: 20px;
}

.modal-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.modal-title {
  color: #337ab7;
  font-weight: bold;
}
</style>

