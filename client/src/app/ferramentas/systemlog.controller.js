(function () {
    'use strict';

    angular
        .module('app')
        .controller('SystemLogController', SystemLogController);

    /** @ngInject */
    function SystemLogController($http, API_CONFIG, limitToFilter, $filter, $scope) {

        var vm = this;

        vm.logs = [];
        vm.hosts = [];
        vm.busca = busca;
        vm.limpa = limpa;
        vm.getHosts = getHosts;
        vm.filtro = '';
        vm.host = '';
        vm.open1 = open1;
        vm.opened1 = false;
        vm.open2 = open2;
        vm.opened2 = false;
        vm.valida = valida;

        vm.pagination = {
            page: 1
        };

        var inicio = new Date();
        var fim = new Date();

        vm.dtinicio = inicio;
        vm.dtfim = fim;

        var horainicio = new Date();
        horainicio.setHours(0,0,0);
        vm.horainicio = horainicio;

        var horafim = new Date();
        horafim.setHours(23,59,59,999);
        vm.horafim = horafim;

        $scope.$watch('horainicio', function (value) {
                if (!value) return;
                vm.horainicio = new Date(value);
        }, true);

        $scope.$watch('horafim', function (value) {
                if (!value) return;
                vm.horafim = new Date(value);
        }, true);

        function valida(){
          if(vm.dtinicio > vm.dtfim){
            vm.dtfim = vm.dtinicio;
          }
        }

        function open1() {
          vm.opened1 = true;
        };

        function open2() {
          vm.opened2 = true;
        };


        function getHosts(val) {
          return $http.get(API_CONFIG.url + "/hosts.json?&q="+val).then(function(response){
            return limitToFilter(response.data, 15);
          });
        };

       function busca(filtro){

         $http({
               url: API_CONFIG.url + '/ferramentas/systemlog',
               method: "POST",
               data: {
                 'filtro' : filtro,
                 'host': vm.host,
                 'dtinicio': $filter('date')(vm.dtinicio, "yyyy-MM-dd"),
                 'dtfim': $filter('date')(vm.dtfim, "yyyy-MM-dd"),
                 'horainicio': $filter('date')(vm.horainicio, "HH:mm"),
                 'horafim': $filter('date')(vm.horafim, "HH:mm"),
                 'page': vm.pagination.page
               }
         })
         .then(function(response) {
           angular.copy(response.data.rows, vm.logs);
           angular.copy(response.data.pagination, vm.pagination);

           },
           function(response) {

           });

        }

        function limpa(){
          vm.logs = [];
          vm.filtro = '';
      }
    }

})();
