<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-wrench"></i> Ferramentas</a></li>
    <li><i class="glyphicon glyphicon-cog"></i> System Log</li>
</ol>

  <div class="barra ng-scope">
    <div class="form-group">
     <div class="form-group pull-left">
        <form class="form-inline" role="form">
          <input type="text" ng-model="SLC.host"
          uib-typeahead="suggestion for suggestion in SLC.getHosts($viewValue)"
            placeholder="Digite o host" class="form-control" size="40">
            <div class="form-group">
                <b>Período:</b>
            </div>
            <div class="input-group">
              <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="SLC.dtinicio" is-open="SLC.opened1" clear-text="Limpar" close-text="Fechar" current-text="Hoje" ng-change="SLC.valida()" size="8"/>
              <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="SLC.open1()"><i class="glyphicon glyphicon-calendar"></i></button>
              </span>
              <png-time-input model="SLC.horainicio" time-mode="24"></png-time-input>
           </div>
           <div class="input-group">
            <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="SLC.dtfim" is-open="SLC.opened2" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="dtfim" ng-change="SLC.valida()" size="8"/>
            <span class="input-group-btn">
              <button type="button" class="btn btn-default" ng-click="SLC.open2()"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
            <png-time-input model="SLC.horafim" time-mode="24"></png-time-input>
          </div>
          <div class="form-group">
              <b>Filtro:</b>
          </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="SLC.filtro">
                <button class="btn btn-default" title="Pesquisar" ng-click="SLC.busca(SLC.filtro)" ng-disabled="SLC.host == ''">Pesquisar</button>
                <button class="btn btn-default" ng-click="SLC.limpa()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
        </div>
</div>
</div>

<div class="col-md-6">
  <h6>Resultado da pesquisa:</h6>
  <table class="table table-striped table-hover table-bordered">
  <thead>
  <tr>
  <th class="vert-align text-center">Data/Hora</th>
  <th class="vert-align text-center">Host</th>
  <th class="vert-align text-center">Mensagem</th>

  </tr>
  </thead>
  <tbody>
    <tr ng-repeat="log in SLC.logs">
    <td class="vert-align text-center">{{log.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
    <td class="vert-align text-center">{{log.host}}</td>
    <td class="vert-align text-center">{{log.mensagem}}</td>
  </tr>
  </tbody>
  </table>
  {{SLC.page}}
<div id="total" class="text-center" ng-if="SLC.pagination.size > 0">Exibindo registro(s) <b>{{SLC.pagination.progress}}</b> de um total de <b>{{SLC.pagination.size}}</b> registro(s)</div>
<div class="text-center" ng-if="SLC.pagination.size > 0">
  <uib-pagination total-items="SLC.pagination.size" ng-model="SLC.pagination.page" ng-change="SLC.busca(SLC.filtro)" items-per-page="50" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
</div>
</div>
