(function () {
    'use strict';

    angular
        .module('app')
        .constant('toastr', toastr)
        .constant('API_CONFIG', {
            url: 'https://noc.telemidia.net.br:4000',
            api_juniper_url: 'https://torch.pocos-net.com.br',
            netconfws: 'https://api.telemidia.net.br:5000/test',
            authws: 'https://provision.telemidia.net.br:5003/app',
            nocws: 'https://api.telemidia.net.br:5001/tl1',
            helpdeskws: 'https://api.telemidia.net.br:5001/helpdeskws',
            pingws: 'https://api.telemidia.net.br:5002/ping',
            authmonitor: 'https://provision.telemidia.net.br:5004/monitor',
            api_juniper_token: 'bm9jOkNRbXFQdGtJU1NnSnN2TjdVYjZNVUkyM2o5Rk5WQ2Q0eGR4V2ZCazRCa050VjVVVEVX',
        })

        .constant('ZTE_API', {
            url: 'https://api.telemidia.net.br:8000',
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NDI3ODAzNzIsImV4cCI6MzMxNjgxNTMxNzJ9.OgEbhlHCXVUhzT8DZ5amCpzJSCswODxSgCDYoDRllz0'
        })

        .constant('PROVISION_API', {
            url: 'https://provision.telemidia.net.br:8000',
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2Nzg0NzQ3NTUsImV4cCI6MzMyMDM4NDc1NTV9.6hGxRvNmlYnjxOIDVZnX_uPmD3i9qqzbEqm4yH-dO-o',
            provisionws: 'https://provision.telemidia.net.br:5010/provision',
        })

        .constant('MIKROTIK_API', { url: 'https://api.telemidia.net.br:32000' })

        .constant('PORTAL_ASSINANTE_API', { url: 'https://portal.telemidia.net.br:8000' })

        .constant('GENIE_API', { url: 'https://genie-api.telemidia.net.br:9000' })

        .constant('GENIE_ACS', { url: 'http://acs.telemidia.net.br:3000' })

        .constant('SIGNAL_COLLECT_API', { url: 'https://api.telemidia.net.br:5005/signalcollect' })

        .constant('RAS_SERVER', {
            urlPrefix: 'https://',
            urlPostfix: '.ras.telemidia.net.br:8080'
        })

        .constant('DIR_ANEXOS', {
            url: 'https://noc.telemidia.net.br/anexos'
        })
        .constant('moment', moment)
        .constant('SCRIPT_ERROR_MSG', 'Ocorreu um erro na aplicação e os detalhes foram coletados.')
        .constant('LOGGING_URL', '/errors/javascript');

})();
