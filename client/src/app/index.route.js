(function () {
    'use strict';

    angular
        .module('app')
        .config(routeConfig);

    function routeConfig($routeProvider) {
        $routeProvider

            .when('/login', {
                templateUrl: 'app/login/login.form.html',
                controller: 'LoginController as L<PERSON>',
                title: 'Login',
                authorize: false
            })

            

           .otherwise({
                templateUrl: 'app/telas/404.html',
                title: '404',
                authorize: false
                //redirectTo: '/login'
            });


    }

})();
