'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/relatorios/capacidade', {
                templateUrl: 'app/concentrador/concentrador.pesquisa.html',
                controller: 'ConcentradorPesquisaController',
                controllerAs: 'CPC',
                title: 'Capacidade de APs',
                authorize: ['concentrador.read', 'concentrador.write']
            })

            .when('/relatorios/pppoe', {
                templateUrl: 'app/concentrador/concentrador.html',
                controller: 'ConcentradorController',
                controllerAs: 'CC',
                title: 'Sessões PPPoE',
                authorize: ['concentrador.read', 'concentrador.write']
            })


    });
