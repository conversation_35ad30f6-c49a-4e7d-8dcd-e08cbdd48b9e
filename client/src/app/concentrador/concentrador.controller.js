(function () {
    'use strict';

    angular
        .module('app')
        .controller('ConcentradorController', ConcentradorController);

    /** @ngInject */
    function ConcentradorController($http, API_CONFIG) {

        var vm = this;

        vm.busca = busca;

        vm.filtro = '';
        vm.concentrador = '172.16.5.11';
        vm.resultados = [];
        vm.concentradores = [];

        getConcentradores();

        function getConcentradores(){
          $http({
            method: 'GET',
            url: API_CONFIG.url + '/concentrador/concentradores.json'
          }).then(function successCallback(response) {
             vm.concentradores = response.data;
              //vm.concentradores = response.data;

          }, function errorCallback(response) {
            // called asynchronously if an error occurs
            // or server returns response with an error status.
          });
       }

        function busca(filtro, concentrador){
            vm.resultados = [];
            $http.post(API_CONFIG.url+'/concentrador/mk', {filtro: filtro, concentrador: concentrador})
                .then(function(response) {
                    angular.copy(response.data, vm.resultados);
                });
        }
    }

})();
