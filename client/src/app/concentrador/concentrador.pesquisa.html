<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
  <li class="active"><i class="glyphicon glyphicon-stats"></i> Capacidade de Aps</li>

</ol>

<div class="barra">
  <div class="form-group">
    <div class="form-group pull-left">
      <form class="form-inline" role="form">
        <div class="form-group">
          <select class="form-control" ng-model="CPC.tipo" ng-init="CPC.tipo = 'nome'">
            <option value="nome">Nome / SSID</option>
          </select>
        </div>
        <div class="form-group">
          <input size="30" maxlength="30" class="form-control" type="text" ng-model="CPC.termos">
          <button class="btn btn-default" title="Pesquisar" ng-click="CPC.busca(CPC.termos)">Pesquisar</button>
        </div>
      </form>
    </div>
  </div>
</div>
<table class="table table-striped table-hover table-bordered">
  <thead>
    <tr>
      <th class="vert-align text-center">AP</a></th>
      <th class="vert-align text-center">SSID</th>
      <th class="vert-align text-center">IP</th>
      <th class="vert-align text-center">PPPoE Server</th>
      <th class="vert-align text-center">IP PPPoE Server</th>
      <th class="vert-align text-center">Clientes</th>
      <th class="vert-align text-center">Vagas</th>
    </tr>
  </thead>
  <tbody>
    <tr ng-repeat="resultado in CPC.resultados">
      <td class="vert-align text-center">{{resultado.ap}}</td>
      <td class="vert-align text-center">{{resultado.ssid}}</td>
      <td class="vert-align text-center"><a href="http://{{resultado.ip}}:8922" target="_blank">{{resultado.ip}}</a>
      </td>
      <td class="vert-align text-center">{{resultado.pppoeserver}}</td>
      <td class="vert-align text-center">{{resultado.pppoeserverip}}</td>
      <td class="vert-align text-center"><span class="label label-default">{{resultado.clientes}}</span></td>
      <td class="vert-align text-center"><span class="label" ng-class="[{'label-success': resultado.vagas > 5}, {'label-warning': resultado.vagas <= 5},
                                          {'label-danger': resultado.vagas <= 0}]">{{resultado.vagas}}</span></td>

    </tr>
  </tbody>
</table>