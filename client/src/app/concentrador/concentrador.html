<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-move"></i> Concentrador</li>
    <li class="active"><i class="glyphicon glyphicon-import"></i> PPPoE</li>

</ol>

<div class="barra">
    <div class="form-group">
     <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                  Concentrador:
                  <select class="form-control" ng-model="CC.concentrador" ng-init="CC.concentrador = '172.16.5.11'" ng-options="o for o in CC.concentradores">

                </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="CC.termos"  placeholder="Username">
                <button class="btn btn-default" title="Pesquisar" ng-click="CC.busca(CC.termos, CC.concentrador)">Pesquisar</button>
                </div>
              </form>
        </div>
</div>
</div>

 <div class="table-responsive">
    <table class="table table-striped table-hover table-bordered" ng-repeat="(key, value) in CC.resultados | groupBy: 'ap'">
      <thead>
        <tr>
          <th class="vert-align text-center" colspan="3">{{key}}</th>
        </tr>
        <tr>
          <th class="vert-align text-center">Username</th>
          <th class="vert-align text-center">Uptime</th>
          <th class="vert-align text-center">IP</th>
        </tr>
      </thead>
      <tbody>
      <tr ng-repeat="user in value track by $index">
          <td class="vert-align text-center">{{user.username}}</td>
          <td class="vert-align text-center">{{user.uptime}}</a></td>
          <td class="vert-align text-center">{{user.ip}}</a></td>
      </tr>
      <tr>
        <td colspan="3" class="vert-align text-center"><span class="badge badge-default">{{value.length}}</span> cliente(s)</td>
      </tr>
      </tbody>

    </table>
</div>
