(function () {
    'use strict';

    angular
        .module('app')
        .controller('ConcentradorPesquisaController', ConcentradorPesquisaController);

    /** @ngInject */
    function ConcentradorPesquisaController($http, API_CONFIG) {

        var vm = this;

        vm.busca = busca;

        vm.termos = '';
        vm.resultados = [];
        
        function busca(filtro){

            $http.post(API_CONFIG.url+'/concentrador/pesquisa', {filtro: filtro})
                .then(function(response) {
                    angular.copy(response.data, vm.resultados);
                });  
        }
    }

})();
