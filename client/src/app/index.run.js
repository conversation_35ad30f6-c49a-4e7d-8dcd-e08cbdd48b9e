(function () {
  'use strict';

  angular
    .module('app')
    //.run(runBlock);
    .run(run);



  /** @ngInject */
  /*  
  function runBlock($log, amMoment, $rootScope, $location, $cookies) {

    amMoment.changeLocale('br');
    $rootScope.title = 'Dashboard';

    $rootScope.eventoSelecionado = {};
    $rootScope.operador = {};

    $rootScope.eventoSelecionado.id = $cookies.get('eventoSelecionado.id');
    $rootScope.eventoSelecionado.descricao = $cookies.get('eventoSelecionado.descricao');
    $rootScope.eventoSelecionado.categoria = $cookies.get('eventoSelecionado.categoria');

    if ($rootScope.eventoSelecionado.id === undefined) {
      $rootScope.eventoSelecionado.id = '';
    }

    $rootScope.operador.username = $cookies.get('operador.username');

    if ($rootScope.operador.username === undefined) {
      $rootScope.operador = {username: ''};
    }


    $rootScope.filtro = '';

    $rootScope.$on('$routeChangeSuccess', function (event, current) {
      if(current.$$route !== undefined){
        $rootScope.title = current.$$route.title;  
      }
      
      $rootScope.currentPath = $location.path().split("/")[1]; // Somente primeira parte da rota
      $rootScope.eventoSelecionado.id = $cookies.get('eventoSelecionado.id');
      $rootScope.eventoSelecionado.descricao = $cookies.get('eventoSelecionado.descricao');

      if ($rootScope.eventoSelecionado.id === undefined) {
        $rootScope.eventoSelecionado.id = '';
      }

        
        if($rootScope.currentPath === 'login' && $rootScope.operador.username !== undefined &&      $rootScope.operador.username !== ''){
            $location.path("/dashboard");  
        }     
    });

  
    
      
    $rootScope.$on("$routeChangeStart", function (event, next) {

      $rootScope.operador.username = $cookies.get('operador.username');

      
        
      if ($rootScope.operador.username === undefined || $rootScope.operador.username === '') {
        //if (next.templateUrl === "../app/basicos/login.html") {
        //} else {
          $location.path("/login");
        //}
      }
    });
  }
  */

  function run(amMoment, $rootScope, $http, $location, $localStorage, $cookies, jwtHelper, AuthorizationService) {

    $rootScope.keys = Object.keys;

    $rootScope.eventoSelecionado = $localStorage.evento;
    if ($rootScope.eventoSelecionado == undefined) {
      $rootScope.eventoSelecionado = { id: '' };
    }


    $rootScope.operador = { 'username': '' };
    amMoment.changeLocale('br');
    // keep user logged in after page refresh
    if ($localStorage.currentUser) {      
      var tokenPayload = jwtHelper.decodeToken($localStorage.currentUser.token);
      
      $http.defaults.headers.common.Authorization = 'Bearer ' + $localStorage.currentUser.token;
      $rootScope.operador.username = $localStorage.currentUser.username;
      $rootScope.operador.role = tokenPayload.role;
    }

    /*
    // redirect to login page if not logged in and trying to access a restricted page
    $rootScope.$on('$locationChangeStart', function (event, next, current) {
        console.log(current);
        var publicPages = ['/login'];
        var restrictedPage = publicPages.indexOf($location.path()) === -1;
        if (restrictedPage && !$localStorage.currentUser) {
            $location.path('/login');
        }
    });
    */
    function getPath(route) {
      if (!!route && typeof (route.originalPath) === "string")
        return "'" + route.originalPath + "'";
      return "[unknown route, using otherwise]";
    }

    /**
    * @description determine if an array contains one or more items from another array.
    * @param {array} haystack the array to search.
    * @param {array} arr the array providing items to check for in the haystack.
    * @return {boolean} true|false if haystack contains at least one item from arr.
    */
    var findOne = function (haystack, arr) {
      return arr.some(function (v) {
        return haystack.indexOf(v) >= 0;
      });
    };
    $rootScope.$on("$routeChangeStart", function (evt, to, from) {
      if ($localStorage.currentUser) {
        var tokenPayload = jwtHelper.decodeToken($localStorage.currentUser.token);
      
      $http.defaults.headers.common.Authorization = 'Bearer ' + $localStorage.currentUser.token;
      $rootScope.operador.username = $localStorage.currentUser.username;
      $rootScope.operador.role = tokenPayload.role;
      } else {
        $rootScope.operador = { 'username': '' };
      }

      if (to.authorize !== false) {

        //to.resolve = to.resolve || {};
        //if (!to.resolve.authorizationResolver){
        //  to.resolve.authorizationResolver = function(AuthorizationService) {

        if (!AuthorizationService.Authorize(to.authorize)) {

          //$rootScope.operador = {};
          //$rootScope.eventoSelecionado = {id: ''};
          //$cookies.put('eventoSelecionado.id', '');
          //$cookies.put('eventoSelecionado.descricao', '');
          //$cookies.put('eventoSelecionado.categoria', '');
          $location.path("/sempermissao");
        }

      };
      // }
      // }  
    });

    $rootScope.$on('$routeChangeSuccess', function (event, current) {
      if (current.$$route !== undefined) {
        $rootScope.title = current.$$route.title;
      }

      $rootScope.currentPath = $location.path().split("/")[1]; // Somente primeira parte da rota
      $rootScope.currentUrl = $location.path();
      $rootScope.eventoSelecionado = $localStorage.evento;
      if ($rootScope.eventoSelecionado == undefined) {
        $rootScope.eventoSelecionado = { id: '' };
      }



      /*
        if($rootScope.currentPath === 'login' && $rootScope.operador.username !== undefined && $rootScope.operador.username !== ''){
          $location.path("/dashboard");  
        }  
        */
    });


    /*
    $rootScope.$on("$routeChangeError", function(evt, to, from, error){
      console.log("Route change ERROR from", getPath(from), "to", getPath(to), error);
      
      if (error)
            {
                console.log("Redirecting to login");
                $location.path("/login").search("returnTo", to.originalPath);
            }
      });
      */
    
    /**
    * Generates range for ngRepeat
    * @param start
    * @param stop
    * @param increment
    */
    $rootScope.generateRange = function (start, stop, increment) {
      var a = [];

      for (; start <= stop;) {
        a.push(start);
        start += increment;
      }

      return a;
    };

  }

})();
