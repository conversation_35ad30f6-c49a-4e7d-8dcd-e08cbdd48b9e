<div class="barra">
        <div class="form-group">
            <a href="/enlaces/{{enlace.id}}/{{enlace.enlace}}/calculos/ptp/novo" type="button" class="btn btn-success btn-incluir text-center" rel="tooltip" type="submit" ng-if="eventoSelecionado.id != ''" title="Incluir Host" authorize="['enlaces.write']">
                <span class="glyphicon glyphicon-plus"></span><br>Incluir
            </a>      
        </div>
    </div>  

    <table class="table table-striped table-hover table-bordered" >
        <thead>
            <tr>
              
              <th colspan="1" rowspan="1" class="text-center success">Cálculo</th>
              <th colspan="5" class="alert-info text-center">Host1</th>
              <th colspan="5" class="alert-warning text-center">Host2</th>
              <th colspan="1">Ação</th>
            </tr>
            <tr>
 
              <th class="text-center">Data Cálculo</th>  
              <th class="text-center">Host</th>
              <th class="text-center">Sinal Calc</th>
              <th class="text-center">Sinal Real</th>
              <th class="text-center">Tilt Calc</th>
              <th class="text-center">Tilt Real</th>
              <th class="text-center">Host</th>
              <th class="text-center">Sinal Calc</th>
              <th class="text-center">Sinal Real</th>
              <th class="text-center">Tilt Calc</th>
              <th class="text-center">Tilt Real</th>
              <th colspan="1" rowspan="1"></th>
            </tr>
      </thead>
      <tbody>
      <tr ng-repeat="calculoptp in calculosptp" class="clickable-row" data-href="url://">
        
        <td class="vert-align text-center">{{calculoptp.datacad | amDateFormat:'DD/MM/YYYY hh:mm:ss'}}</td>
        <td class="vert-align text-center">{{calculoptp.host1}}</td>
        <td class="vert-align text-center">{{calculoptp.sinal_calc_host1}}</td>
        <td class="vert-align text-center">{{calculoptp.sinal_real_host1}}</td>
        <td class="vert-align text-center">{{calculoptp.tilt_calc_host1}}</td>
        <td class="vert-align text-center">{{calculoptp.tilt_real_host1}}</td>
        <td class="vert-align text-center">{{calculoptp.host2}}</td>
        <td class="vert-align text-center">{{calculoptp.sinal_calc_host2}}</td>
        <td class="vert-align text-center">{{calculoptp.sinal_real_host2}}</td>
        <td class="vert-align text-center">{{calculoptp.tilt_calc_host2}}</td>
        <td class="vert-align text-center">{{calculoptp.tilt_real_host2}}</td>
          <td><a href="/enlaces/{{enlace.id}}/{{enlace.enlace}}/calculos/ptp/{{calculoptp.id}}" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-search"></i></button></td>
      </tr>
      </tbody>
    </table>