'use strict';

angular.module('app')

    .factory('CalculoPtpService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/enlaces/:enlace/calculos/ptp/:calculo', {},
            {
                getEnlaceCalculosPtp: {method: 'GET', isArray: false},
                insertEnlaceCalculoPtp: {
                    method: 'POST',
                    params: {enlace: '@enlace'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteEnlaceCalculoPtp: {
                    method: 'DELETE',
                    params: {enlace: '@enlace', id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    });
