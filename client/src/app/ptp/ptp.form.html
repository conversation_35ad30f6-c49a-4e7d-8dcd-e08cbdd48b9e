<ol class="breadcrumb">
  <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><a href="/enlaces"><i class="glyphicon glyphicon-link"></i> Enlaces</a></li>
  <li><a href="/enlaces/{{::enlace.id}}/{{::enlace.enlace}}">{{::enlace.enlace}}</a></li>
    <li>Calculos</a></li>
    <li class="active" ng-if="inserindo">Novo</li>
    <li class="active" ng-if="!inserindo">#{{::calculoptp.id}}</li>
</ol>

<div class="barra">
    <div class="form-group">
    <button ng-click="saveCalculoPtp(calculoptp)" class="btn btn-primary btn-info btn-incluir text-center" type="submit"  ng-if="eventoSelecionado.id != ''" ng-show="inserindo">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
      </button>    
    </div>
</div>  


<div class="row">
  <div class="col-md-10">
    <div class="col-md-5 col-md-offset-2">
      <form class="form-horizontal" id="frmCalculo" name="frmCalculo">
<fieldset>

<div class="form-group">
  <label class="col-md-2 control-label" for="host1">Host 1</label>
  <div class="col-md-10">

<select class="form-control" ng-model="calculoptp.host1" ng-options="o.hostservicos for o in hosts track by o.hostservicos" ng-change="updateHost1()" required ng-show="inserindo"></select>
<input class="form-control" ng-model="calculoptp.host1" disabled ng-show="!inserindo">
  </div>
</div>


<div class="form-group">
  <label class="col-md-2 control-label" for="host2">Host 2</label>
  <div class="col-md-10">
  <select class="form-control" ng-model="calculoptp.host2" ng-options="o.hostservicos for o in hosts track by o.hostservicos" ng-change="updateHost2()" required ng-show="inserindo"></select>
  <input class="form-control" ng-model="calculoptp.host2" disabled ng-show="!inserindo">
  </div>
</div>

<div class="form-group">
  <label class="col-md-2 control-label" for="distancia">Distância</label>
  <div class="col-md-3">
  <input id="distancia" name="distancia" placeholder="Distância (m)" class="form-control input-md" required ng-model="calculoptp.distancia" ng-change="calculo_ptp()" ng-disabled="!inserindo">
  </div>
  <label class="col-md-4 control-label" for="frequencia">Frequência</label>
  <div class="col-md-3">
  <input id="frequencia" name="frequencia" placeholder="Frequência (MHz)" class="form-control input-md" required ng-model="calculoptp.frequencia" ng-change="calculo_ptp()" ng-disabled="!inserindo">
  </div>
</div>
<div class="form-group">
  <label class="col-md-2 control-label" for="soma_perdas">Perdas</label>
  <div class="col-md-3">
  <input id="soma_perdas" name="soma_perdas" placeholder="Perdas Totais (dB)" class="form-control input-md" ng-model="calculoptp.perdas" ng-change="calculo_ptp()" required ng-disabled="!inserindo">

  </div>
</div>



<!-- Text input-->
<div class="form-group">
<div class="col-md-12">
<table class="table">
      <!-- <caption>Resultado do cálculo</caption> -->
      <thead>
        <tr>
          <th>Parâmetro</th>
          <th class="text-center">AP</th>
          <th class="text-center">Cliente</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Potência</td>
          <td>
              <input id="potencia_ap" name="potencia_ap" placeholder="Potência (AP)" class="form-control input-md" required ng-model="calculoptp.potencia_host1" ng-change="calculo_ptp()" ng-disabled="!inserindo">
          </td> 
          <td>
            <input id="potencia_cliente" name="potencia_cliente" placeholder="Potência (Cliente)" class="form-control input-md" required ng-model="calculoptp.potencia_host2" ng-change="calculo_ptp()" ng-disabled="!inserindo"> 
          </td>  
        </tr>
        <tr>
          <td>Ganho</td>
          <td>
            <input id="ganho_ap" name="ganho_ap" placeholder="Ganho AP (dBi)" class="form-control input-md" ng-model="calculoptp.ganho_host1" ng-change="calculo_ptp()" required ng-disabled="!inserindo">  
          </td>
          <td>
            <input id="ganho_cliente" name="ganho_cliente" placeholder="Ganho Cliente (dBi)" class="form-control input-md" ng-model="calculoptp.ganho_host2" ng-change="calculo_ptp()" required ng-disabled="!inserindo">
          </td>
        </tr>
        <tr>
          <td>Elevação</td>
          <td>
           <input id="elevacao_ap" name="elevacao_ap" placeholder="Elevação (AP)" class="form-control input-md" ng-model="calculoptp.elevacao_host1" ng-change="calculo_ptp()" required ng-disabled="!inserindo">  
          </td>
          <td>
            <input id="elevacao_cliente" name="elevacao_cliente" placeholder="Elevação (Cliente)" class="form-control input-md" ng-model="calculoptp.elevacao_host2" ng-change="calculo_ptp()" required ng-disabled="!inserindo">
          </td>
        </tr>
        <tr>
        <td>Altura Antena</td>
        <td>
          <input id="altura_ap" name="altura_ap" placeholder="Altura das antenas (AP)" class="form-control input-md" ng-model="calculoptp.altura_ant_host1" ng-change="calculo_ptp()" required ng-disabled="!inserindo">  
        </td>
        <td>
        <input id="altura_cliente" name="altura_cliente" placeholder="Altura das antenas (Cliente)" class="form-control input-md" ng-model="calculoptp.altura_ant_host2" ng-change="calculo_ptp()" required ng-disabled="!inserindo">
        </td>

      </tbody>
     </table>   
  </div>   
</div>  


    </div>
    <div class="col-md-5">
      <table class="table">
      <!-- <caption>Resultado do cálculo</caption> -->
      <thead>
        <tr>
          <th>Cálculo</th>
          <th class="text-center">AP</th>
          <th class="text-center">Cliente</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Sinal Recebido (dBm)</td>
          <td><input class="form-control input-md" type="text" ng-model="calculoptp.sinal_calc_host1" disabled></td>
          <td><input ng-model="calculoptp.sinal_calc_host2" class="form-control input-md" type="text" disabled></td>
        </tr>
        <tr>
          <td>Tilt da antena</td>
          <td><input ng-model="calculoptp.tilt_calc_host1" class="form-control input-md" type="text" disabled></td>
          <td><input ng-model="calculoptp.tilt_calc_host2" class="form-control input-md" type="text" disabled></td>
        </tr>
      </tbody>
    </table>
    <table class="table">
      <!-- <caption>Resultado do cálculo</caption> -->
      <thead>
        <tr>
          <th>Valores reais</th>
          <th class="text-center">AP</th>
          <th class="text-center">Cliente</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Sinal Recebido (dBm)</td>
          <td><input class="form-control input-md" ng-model="calculoptp.sinal_real_host1" required ng-disabled="!inserindo"></td>
          <td><input ng-model="calculoptp.sinal_real_host2" class="form-control input-md" required ng-disabled="!inserindo"></td>
        </tr>
        <tr>
          <td>Tilt da antena</td>
          <td><input ng-model="calculoptp.tilt_real_host1" class="form-control input-md" required ng-disabled="!inserindo"></td>
          <td><input ng-model="calculoptp.tilt_real_host2" class="form-control input-md"  required ng-disabled="!inserindo"></td>
        </tr>
      </tbody>
    </table>
</fieldset>
</form>
    </div>
  </div>
</div>    

    

