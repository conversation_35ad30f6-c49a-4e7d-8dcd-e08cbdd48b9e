'use strict';

angular.module('app')

    .controller('CalculosPtpCtrl', ['$scope', '$rootScope', '$route', '$location', 'dadosAPI', 'formType',
        'CalculoPtpService', 'EnlaceHostService', 'EnlaceService', 'toaster',
        function ($scope, $rootScope, $route, $location, dadosAPI, formType,
                  CalculoPtpService, EnlaceHostService, EnlaceService, toaster) {

            $scope.calculosptp = [];

            var Calculos = CalculoPtpService.getEnlaceCalculosPtp({enlace: $route.current.params.enlace});
            Calculos.$promise.then(function (data) {
                $scope.calculosptp = data.dados;
            });

            $scope.enlace = dadosAPI[0].dados[0];
            $scope.hosts = dadosAPI[1].dados;

            $scope.updateHost1 = function () {

                $scope.calculoptp.frequencia = $scope.calculoptp.host1.frequencia_mhz;
                $scope.calculoptp.potencia_host1 = $scope.calculoptp.host1.potencia_dbm;
                $scope.calculoptp.ganho_host1 = $scope.calculoptp.host1.ganho_dbi;
                $scope.calculoptp.elevacao_host1 = $scope.calculoptp.host1.elevacao;
                $scope.calculoptp.altura_ant_host1 = $scope.calculoptp.host1.alturaantena_m;

                $scope.calculo_ptp();

            };

            $scope.updateHost2 = function () {

                $scope.calculoptp.frequencia = $scope.calculoptp.host2.frequencia_mhz;
                $scope.calculoptp.potencia_host2 = $scope.calculoptp.host2.potencia_dbm;
                $scope.calculoptp.ganho_host2 = $scope.calculoptp.host2.ganho_dbi;
                //$scope.calculoptp.perdas = $scope.calculoptp.host1.perdas_db + $scope.calculoptp.host2.perdas_db;
                $scope.calculoptp.elevacao_host2 = $scope.calculoptp.host2.elevacao;
                $scope.calculoptp.altura_ant_host2 = $scope.calculoptp.host2.alturaantena_m;

                $scope.calculo_ptp();

            };

            $scope.calculo_ptp = function () {
                var frequencia = $scope.calculoptp.frequencia;
                var distancia = $scope.calculoptp.distancia;
                var potencia_ap = $scope.calculoptp.potencia_host1;
                var potencia_cliente = $scope.calculoptp.potencia_host2;
                var ganho_ap = $scope.calculoptp.ganho_host1;
                var ganho_cliente = $scope.calculoptp.ganho_host2;
                var soma_perdas = $scope.calculoptp.perdas;
                var elevacao_solo_ap = $scope.calculoptp.elevacao_host1;
                var elevacao_solo_cliente = $scope.calculoptp.elevacao_host2;
                var altura_ap = $scope.calculoptp.altura_ant_host1;
                var altura_cliente = $scope.calculoptp.altura_ant_host2;

                var vel_luz_vacuo = 299792458; // Velocidade da luz no vacuo (m/s)
                var comprimento_onda = vel_luz_vacuo / (frequencia * 1000000);

                var atenuacao_el = -20 * (Math.log10(((4 * Math.PI) * distancia) / comprimento_onda)); // Atenuação no espaço livre (dB)

                var sinal_ap = (Number(potencia_cliente) + Number(ganho_ap) + Number(ganho_cliente) + Number(atenuacao_el)) - Number(soma_perdas);
                var sinal_cliente = (Number(potencia_ap) + Number(ganho_ap) + Number(ganho_cliente) + Number(atenuacao_el) - Number(soma_perdas));

                var elevacao_antena_ap = (Math.atan(((Number(elevacao_solo_cliente) + Number(altura_cliente)) - (Number(elevacao_solo_ap) + Number(altura_ap))) / Number(distancia))) * 180 / Math.PI;

                var elevacao_antena_cliente = (Math.atan(((Number(elevacao_solo_ap) + Number(altura_ap)) - (Number(elevacao_solo_cliente) + Number(altura_cliente))) / Number(distancia))) * 180 / Math.PI;

                $scope.calculoptp.sinal_calc_host1 = parseFloat(Math.round(sinal_ap * 100) / 100).toFixed(2);
                $scope.calculoptp.sinal_calc_host2 = parseFloat(Math.round(sinal_cliente * 100) / 100).toFixed(2);
                $scope.calculoptp.tilt_calc_host1 = parseFloat(Math.round(elevacao_antena_ap * 100) / 100).toFixed(2);
                $scope.calculoptp.tilt_calc_host2 = parseFloat(Math.round(elevacao_antena_cliente * 100) / 100).toFixed(2);

            };


            if (formType === 'create') {

                $scope.inserindo = true;

                $scope.calculoptp = {
                    enlace: $scope.enlace.enlace,
                    idevento: $rootScope.eventoSelecionado.id,
                    username: $rootScope.operador.username,
                    distancia: $scope.enlace.distancia_m,
                    frequencia: 0,
                    potencia_host1: 0,
                    elevacao_host1: 0,
                    altura_ant_host1: 0,
                    ganho_host1: 0,
                    potencia_host2: 0,
                    elevacao_host2: 0,
                    altura_ant_host2: 0,
                    ganho_host2: 0,
                    perdas: 0,
                };


                $scope.saveCalculoPtp = function () {

                    $scope.calculoptp.host1 = $scope.calculoptp.host1.hostservicos;
                    $scope.calculoptp.host2 = $scope.calculoptp.host2.hostservicos;

                    CalculoPtpService.insertEnlaceCalculoPtp($scope.calculoptp, function (response) {
                        if (response.status === 'OK') {
                            toaster.pop('success', "Calculo adicionado", "Calculo adicionado com sucesso!");
                            $location.path('/enlace/' + $scope.enlace.enlace);
                        } else {
                            toaster.pop('error', response.mensagem, response.dados);
                        }

                    });


                };


            } else {
                $scope.calculoptp = dadosAPI[0].dados[0];

                $scope.enlace = dadosAPI[1].dados[0];
                $scope.inserindo = false;

            }


        }]);


