<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-random"></i> Serviços</li>
</ol>

<div class="barra">
    <div class="form-group">

        
    <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                  <select class="form-control" ng-model="SLC.tipo" ng-init="SLC.tipo = 'nome'">
                <option value="nome">Nome Serviço</option>
            </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="SLC.busca(termos)">Pesquisar</button>
                </div>
              </form>
        </div>
</div>
</div>   



  <div class="table-responsive">
    <span class="counter pull-right"></span>
    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Serviço</th>
          <th class="vert-align text-center">Tipo Serviço</th>
          <th class="vert-align text-center">Host</th>
          <th class="vert-align text-center">Data Cadastro</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">Evento</a></th>
        </tr>
          </thead>
              <tbody>
                <tr ng-repeat="servico in SLC.servicos" ng-class="{'success': data.idevento == eventoSelecionado.id}">
                    
                    <td class="vert-align text-center"><a href="/hosts/{{::servico.idhost}}/{{::servico.host}}/servicos/{{servico.id}}/{{::servico.nomeservico}}">{{::servico.nomeservico}}</a></td>
                    <td class="vert-align text-center">{{::servico.tipohost}}</td>
                    <td class="vert-align text-center"><a href="/hosts/{{::servico.idhost}}/{{::servico.host}}">{{::servico.host}}</a></td>
                    <td class="vert-align text-center">{{::servico.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{::servico.username}}</td>
                    <td class="vert-align text-center"><a href="/eventos/{{::servico.idevento}}">#{{::servico.idevento}}</a></td>
                </tr>
             </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="SLC.totalItems" ng-model="SLC.currentPage" ng-change="SLC.pageChanged()" items-per-page="20" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Exibindo registros <span class="badge">{{SLC.start}}</span> até  <span class="badge">{{SLC.end}}</span> de um total de <span class="badge">{{SLC.totalItems}}</span>
            </div>
          </div>
