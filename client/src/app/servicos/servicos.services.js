'use strict';

angular.module('app')

    .factory('Servicos', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:id', {},
            {
                disableServico: {
                    method: 'DELETE',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                enableServico: {
                    method: 'PATCH',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                 }

            });
    })

    .factory('ServicosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:host/servicos/:servico', {},
            {
                getServicos: {
                    method: 'GET', isArray: false
                },
                insertServico: {
                    method: 'POST',
                    params: {
                        host: '@host'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateServico: {
                    method: 'PUT',
                    params: {
                        host: '@host'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteServico: {
                    method: 'DELETE',
                    params: {
                        host: '@host'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                enableServico: {
                    method: 'PATCH',
                    params: {
                        host: '@host'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                 }

            });
    })

    

    .factory('ServicosJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:host/servicos.json', {},
            {
                getServicos: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('PrefixosJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:servico/pppoe/prefixos.json', {},
            {
                getPrefixos: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('PppoeServersJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pppoeservers.json', {},
            {
                getServers: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('PlacaPortaJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/placaporta.json', {},
            {
                getPlacaPorta: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('OLTsJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/olts.json', {},
            {
                getOlts: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('PppoeServersService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:servico/pppoe', {},
            {
                getServers: {
                    method: 'GET', isArray: false
                },
                insertServer: {
                    method: 'POST',
                    params: {
                        servico: '@servico'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            });
    });
