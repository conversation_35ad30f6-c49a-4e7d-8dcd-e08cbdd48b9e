<div class="barra">
        <div class="form-group">
            <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmservicos" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="novoServico()"><i class="glyphicon glyphicon-plus"></i><br>Incluir</button>
    </div>
</div>    

<span class="counter pull-right"></span>
    <!-- table -->
    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Status</th>
          <th class="vert-align text-center">Serviço</th>
          <th class="vert-align text-center">Tipo do Serviço</th>
          <th class="vert-align text-center">Evento</th>
            <th class="vert-align text-center">Ação</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="servico in servicos | filter:filtro" ng-class="{'danger': servico.ativo == 0}">
          <td class="vert-align text-center"><span class="label label-success" style="cursor: default;" ng-if="servico.ativo==1" uib-tooltip="{{ servico.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}" tooltip-placement="top">Ativo</span><span class="label label-danger" style="cursor: default;" ng-if="servico.ativo==0" uib-tooltip="{{ servico.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}" tooltip-placement="top">Inativo</span></td>
          <td class="vert-align text-center"><a href="/hosts/{{::HFC.host.id}}/{{::HFC.host.host}}/servicos/{{servico.id}}/{{::servico.nomeservico}}"><span ng-class="{'strike': servico.ativo == 0}">{{::servico.nomeservico}}</span></a></td>
            <td class="vert-align text-center"><span ng-class="{'strike': servico.ativo == 0}">{{::servico.tipohost}}</span></td>
            <td class="vert-align text-center"><a href="/eventos/{{::servico.idevento}}"><span ng-class="{'strike': servico.ativo == 0}">#{{::servico.idevento}}</span></a></td>
            <td class="vert-align text-center">
          <a href="/hosts/{{::HFC.host.id}}/{{::HFC.host.host}}/servicos/{{servico.id}}/{{::servico.nomeservico}}" class="btn btn-default btn-sm" title="Visualizar Serviço"><i class="glyphicon glyphicon-search"></i></a>
           <button class="btn btn-warning btn-sm" title="Editar Serviço" ng-click="editServico(servico)" data-toggle="modal" data-target="#frmservicos" ng-show="eventoSelecionado.id !== ''"><i class="glyphicon glyphicon-edit"></i></button>
           <a href="" ng-really-message="Tem certeza que deseja desativar este serviço ?" ng-really-click="desativaServico(servico)" item="servico" class="btn btn-danger btn-sm" title="Desabilitar Serviço" ng-if="servico.ativo==1" ng-show="eventoSelecionado.id !== ''"><i class="glyphicon glyphicon-remove"></i></a> <a href="" ng-really-message="Tem certeza que deseja ativar este serviço ?" ng-really-click="ativaServico(servico)" item="servico" class="btn btn-success btn-sm" title="Habilitar Serviço" ng-if="servico.ativo==0" ng-show="eventoSelecionado.id !== ''"><i class="glyphicon glyphicon-check"></i></a>
          </td>
        </tr>

      </tbody>
      </table>
