<ol class="breadcrumb">
    <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><a href="/hosts"><i class="glyphicon glyphicon-tasks"></i> Hosts</a></li>
    <li><a href="/hosts/{{::host.id}}/{{::host.host}}">{{::host.host}}</a></li>
    <li><a href="/hosts/{{::host.id}}/{{::host.host}}"><i class="glyphicon glyphicon-random"></i> Serviços</a></li>
    <li class="active"><b>{{::servico.nomeservico}}</b></li>
</ol>

<ul class="nav nav-tabs">
   <li class="active"><a data-toggle="tab" data-target="#dados" style="cursor: pointer;" ng-click="atualizaBarra('dados')"><i class="glyphicon glyphicon-list-alt"></i> Dados</a></li>
   <li><a data-toggle="tab" data-target="#parametrosradio" ng-show="servico.radio == '1'" style="cursor: pointer;" ng-click="atualizaBarra('parametros')"><i class="glyphicon glyphicon-cog"></i> Parâmetros de Rádio</a></li>
    <li><a data-toggle="tab" data-target="#parametrosftth" ng-if="servico.ftth == '1'" style="cursor: pointer;" ng-click="atualizaBarra('parametros')"><i class="glyphicon glyphicon-cog"></i> Parâmetros FTTH</a></li>
   <li ng-show="servico.pppoe_client == '1'"><a data-toggle="tab" data-target="#pppoe" style="cursor: pointer;" ng-click="atualizaBarra('pppoe')"><i class="glyphicon glyphicon-hdd" ></i> PPPoE Server</a></li>
   <li ng-show="servico.radio == '1'"><a data-toggle="tab" data-target="#calculosptmp" style="cursor: pointer;" ng-click="atualizaBarra('calculos')"><i class="glyphicon glyphicon-stats"></i> Cálculos PTMP</a></li>
   <li><a data-toggle="tab" data-target="#coberturas" style="cursor: pointer;" ng-click="atualizaBarra('coberturas')"><i class="glyphicon glyphicon-globe"></i> Coberturas</a></li>
   <li><a data-toggle="tab" data-target="#anexos" style="cursor: pointer;" ng-click="atualizaBarra('anexos')"><i class="glyphicon glyphicon-paperclip"></i> Anexos</a></li>
</ul>
<div class="tab-content">
    <div id="dados" class="tab-pane fade in active">
      <br>
      <div class="form-horizontal">
          <div class="row">
              <div class="form-group">
                  <label class="col-md-1 control-label" for="host">Host</label>
                  <div class="col-md-5">
                      <input type="text" class="form-control" ng-model="servico.host" disabled>
                  </div>
              </div>
              <div class="form-group">
                  <label class="col-md-1 control-label" for="nome">Nome</label>
                  <div class="col-md-5">
                      <input type="text" class="form-control" ng-model="servico.nomeservico" disabled>
                  </div>
              </div>
              <div class="form-group">
                  <label class="col-md-1 control-label" for="tipo">Tipo</label>
                  <div class="col-md-3">
                      <input type="text" class="form-control" ng-model="servico.tipohost" disabled>
                  </div>
              </div>
              <div class="form-group">
                  <label class="col-md-1 control-label" for="data">Data</label>
                  <div class="col-md-3">
                      {{::servico.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
                  </div>
              </div>
              <div class="form-group">
                  <label class="col-md-1 control-label" for="operador">Operador</label>
                  <div class="col-md-2">
                      <input type="text" class="form-control" ng-model="servico.username" disabled>
                  </div>
              </div>

              <div class="form-group">
                    <label class="col-md-1 control-label" for="olt">OLT</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" ng-model="servico.olt" disabled>
                    </div>
               </div>

               <div class="form-group">
                    <label class="col-md-1 control-label" for="placa_porta">Placa / Porta</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" ng-model="servico.placa_porta" disabled>
                    </div>
               </div>
               <div class="form-group">
                    <label class="col-md-1 control-label" for="vlanid">VLANID</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" ng-model="servico.vlanid" disabled>
                    </div>
               </div>

               <div class="form-group">
                <label class="col-md-1 control-label" for="vlanid_multicast">VLANID Unicast IPTV</label>
                <div class="col-md-2">
                    <input type="text" class="form-control" ng-model="servico.vlanid_unicast_iptv" disabled>
                </div>
           </div>

          </div>
      </div>

    </div>
    
    <div id="parametrosftth" class="tab-pane fade" ng-if="servico.ftth == '1'" ng-controller="ParametrosFtthListController">
        <div ng-include="'app/parametros/parametros.servicos.ftth.list.html'"></div>
        <div ng-include="'app/parametros/parametros.servicos.ftth.form.html'"></div>

    </div>

    <div id="parametrosradio" class="tab-pane fade" ng-if="servico.radio == '1'" ng-controller="ParametrosRadioListCtrl">
        <div ng-include="'app/parametros/parametros.radio.list.html'"></div>
        <div ng-include="'app/parametros/parametros.radio.form.html'"></div>
    </div>
    <div id="pppoe" class="tab-pane fade in" ng-controller="pppoeCtrl" ng-show="servico.pppoe_client == '1'">
        <div ng-include="'app/servicos/pppoe.list.html'"></div>
        <div ng-include="'app/servicos/pppoe.form.html'"></div>
    </div>
    <div id="calculosptmp" class="tab-pane fade in" ng-show="servico.radio == '1'">
        <div ng-include="'app/ptmp/ptmp.list.html'"></div>
    </div>
    <div id="coberturas" class="tab-pane fade in">
        <div ng-include="'app/servicos/coberturas.list.html'"></div>
    </div>

    <div id="anexos" class="tab-pane fade in">
        <div ng-include="'app/servicos/anexos/anexosViewList.html'" ng-controller="ServicosAnexoController"></div>
    </div>
</div>