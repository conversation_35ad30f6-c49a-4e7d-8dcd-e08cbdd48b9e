<div class="barra">
    <div class="form-group">
    <!-- Parâmetros -->    
      <button type="button" ngf-select="uploadFile($file)" accept=".kml" class="btn btn-success btn-incluir text-center" data-placement="top" rel="tooltip" authorize="['servicos.write']"><i class="glyphicon glyphicon-import"></i> <br>Incluir</button>
  
    </div>
</div>    

<table class="table table-striped table-hover table-bordered">
            <thead>
              <tr>
                <th class="vert-align text-center">Data Inclusão</th>
                <th class="vert-align text-center">Descrição</th>
                <th class="vert-align text-center">Data Prevista</th>
                <th class="vert-align text-center">Data Ativação</th>
                <th class="vert-align text-center">Operador</th>
                <th class="vert-align text-center">Download</th>
                <th class="vert-align text-center">Ação</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="cobertura in coberturas">
                  <td class="vert-align text-center">{{cobertura.dataatualizacao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{cobertura.descricao}}</td>
                  <td class="vert-align text-center">{{cobertura.dataprevista | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{cobertura.dataativacao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{cobertura.username}}</td>
                  <td class="vert-align text-center"><a href="" ng-click="download(cobertura.id, cobertura.descricao)"><i class="glyphicon glyphicon-download-alt"></i> KML</a>  </td>
                  <td class="vert-align text-center"><a href="" ng-really-message="Tem certeza que deseja excluir esta cobertura?" ng-really-click="deleteCobertura(cobertura.id)" item="cobertura" class="btn btn-danger btn-sm" title="Excluir cobertura"><i class="glyphicon glyphicon-remove"></i></a></td>
              </tr>
            </tbody>
</table>