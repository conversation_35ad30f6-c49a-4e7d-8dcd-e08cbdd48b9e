<div class="modal fade" id="frmpppoe" tabindex="-1" role="dialog" 
     aria-labelledby="frmpppoelabel" aria-hidden="true" modal="showModal" close="cancel()">
     
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <button type="button" class="close" 
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fe<PERSON>r</span>
                </button>
                <h4 class="modal-title" id="frmpppoelabel">
                    Dados do PPPoE Server
                </h4>
            </div>
            
            <!-- Modal Body -->
            <div class="modal-body">
                <form role="form" name="frmpppoe">
                <div class="form-group">
                    <label for="server">PPPoE Server</label>
                    <ol class="nya-bs-select form-control"
                        title="Selecione um PPPoE Server"
                        id="server"
                        ng-model="server.pppoeserver"
                        data-live-search="true"
                        data-size="8"
                        required
                    >
                        <li nya-bs-option="server in pppoeservers">
                            <a>
                                {{ ::server }}
                                <span class="glyphicon glyphicon-ok check-mark"></span>
                            </a>
                        </li>
                    </ol>
                      <!-- <select class="form-control" ng-model="server.pppoeserver"
                              ng-options="o as o for o in pppoeservers" required></select>
                              -->
                  </div>    
                </form>
            </div>
                                                                                                
            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>
                <button type="button" class="btn btn-primary" ng-click="savePppoe(server);" ng-disabled="frmpppoe.$invalid" data-dismiss="modal">
                    Salvar</button>
            </div>
        </div>
    </div>
</div>