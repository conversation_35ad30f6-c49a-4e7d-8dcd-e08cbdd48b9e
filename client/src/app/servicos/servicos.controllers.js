'use strict';

angular.module('app')

    .controller('ServicosCtrl', function ($scope, $route, $rootScope, Servicos,
        ServicosService, PopsHostsJSONService, //HostsByPopServiceJSON,
        HostsServiceJSON, toaster, ServicosTiposService, PlacaPortaJSONService, OLTsJSONService, Host, $http) {

        $scope.servicos = [];

        var serv = ServicosService.get({ host: $route.current.params.host });
        serv.$promise.then(function (data) {
            $scope.servicos = data.dados;
        });

        $scope.tipos = [];

        $scope.getTipos = function () {
            var filtro = '';
            if (Host.tipo == '15') filtro = '?ftth=1';
            ServicosTiposService.getTipos(filtro)
                .then(function (response) {
                    $scope.tipos = response.data;
                },
                    function (data) {
                        console.log('erro ao recuperar lista de tipos.')
                    });
        };

        $scope.getTipos();


        $scope.getPlacaPorta = function () {

            var PlacaPortaJSON = PlacaPortaJSONService.getPlacaPorta({ olt: $scope.servico.olt });
            PlacaPortaJSON.$promise.then(function (data) {
                $scope.placaporta = angular.copy(data);
            });
        };

        $scope.saveServico = function (servico) {

            var novoServico = {};
            novoServico = angular.copy(servico);
            //novoServico.host = host;
            novoServico.username = $rootScope.operador.username;
            novoServico.idevento = $rootScope.eventoSelecionado.id;

            if ($scope.edit) {
                ServicosService.updateServico(novoServico, servicoCallback);
            } else {
                ServicosService.insertServico(novoServico, servicoCallback);
            }

            function servicoCallback(response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Serviço salvo", "Serviço salvo com sucesso!");
                    var Servicos = ServicosService.get({ host: $route.current.params.host });
                    Servicos.$promise.then(function (data) {
                        $scope.servicos = angular.copy(data.dados);
                    });
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            }
        };

        $scope.editServico = function (servico) {

            //var Hosts = HostsByPopServiceJSON.getHostsJSON({idpop: Host.pop});
            var Hosts = PopsHostsJSONService.getHosts({ pop: Host.pop });

            Hosts.$promise.then(function (data) {
                $scope.hosts = angular.copy(data);
                $scope.servico = angular.copy(servico);

                $scope.servico.nomeanterior = servico.nomeservico;
                $scope.edit = true;
            });


            console.log(servico);
            var HostsJSON = HostsServiceJSON.getHostsJSON();
            HostsJSON.$promise.then(function (data) {
                $scope.hostsvpn = angular.copy(data);
            });

            var OltsJSON = OLTsJSONService.getOlts();
            console.log('OltsJSON');
            OltsJSON.$promise.then(function (data) {
                $scope.olts = angular.copy(data);


                var PlacaPortaJSON = PlacaPortaJSONService.getPlacaPorta({ olt: servico.olt });
                PlacaPortaJSON.$promise.then(function (data) {
                    $scope.placaporta = angular.copy(data);
                });
            });



        };

        $scope.novoServico = function () {



            //var Hosts = HostsByPopServiceJSON.getHostsJSON({idpop: Host.pop});
            var Hosts = PopsHostsJSONService.getHosts({ pop: Host.pop });
            Hosts.$promise.then(function (data) {
                $scope.hosts = angular.copy(data);
            });

            var HostsJSON = HostsServiceJSON.getHostsJSON();
            HostsJSON.$promise.then(function (data) {
                $scope.hostsvpn = angular.copy(data);
            });

            var OltsJSON = OLTsJSONService.getOlts();
            console.log('OltsJSON');
            OltsJSON.$promise.then(function (data) {
                $scope.olts = angular.copy(data);
            });

            $scope.servico = {
                host: $route.current.params.host
            };

            $scope.edit = false;
        };

        $scope.ativaServico = function (servico) {
            Servicos.enableServico(servico, function (response) {
                if (response.status === 'OK') {
                    servico.ativo = 1;
                    toaster.pop('success', "Serviço habilitado", "Serviço habilitado com sucesso!");
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        }

        $scope.desativaServico = function (servico) {
            Servicos.disableServico(servico, function (response) {
                if (response.status === 'OK') {
                    servico.ativo = 0;
                    toaster.pop('success', "Serviço desabilitado", "Serviço desabilitado com sucesso!");
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        }


    })

    .controller('ServicosDetailCtrl', function ($scope, $rootScope, $cookies, $location, $route,
        ServicosService, toaster, dadosAPI, Upload, API_CONFIG, $http) {

        $scope.host = { host: $route.current.params.host, id: $route.current.params.id };

        $scope.servico = dadosAPI[0].dados[0];
        $scope.calculosptmp = dadosAPI[1].dados;

        $scope.visualizarCalculo = function (calculo) {
            $scope.calculoptmp = angular.copy(calculo);
            $scope.visualizandoCalculo = true;

        };

        $scope.cobertura = {};

        $scope.getCoberturas = function () {
            $http({
                url: API_CONFIG.url + '/servicos/' + $scope.servico.id + '/coberturas',
                method: "GET"
            })
                .then(function (response) {
                    $scope.coberturas = response.data;
                });
        };

        $scope.deleteCobertura = function (idcobertura) {
            $http({
                url: API_CONFIG.url + '/servicos/' + $scope.servico.id + '/coberturas/' + idcobertura,
                method: "DELETE"
            })
                .then(function (response) {
                    console.log(response);
                });
        };

        $scope.getCoberturas();

        $scope.uploadFile = function (file) {

            $scope.cobertura = {
                kml: file,
                username: $rootScope.operador.username,
                descricao: file.name
            };

            file.upload = Upload.upload({
                url: API_CONFIG.url + '/servicos/' + $scope.servico.id + '/coberturas',
                data: $scope.cobertura,
            });

            file.upload.then(function (response) {
                if (response.data.status === 'OK') {
                    toaster.pop('success', "Cobertura atualizada", "Cobertura atualizada com sucesso!");
                    $scope.getCoberturas();
                } else {
                    toaster.pop('error', "Ocorreu um erro", response.data);
                }

            });

        };


        $scope.download = function (id, descricao) {

            var url = API_CONFIG.url + '/coberturas/' + id;
            $http({
                method: 'GET',
                url: url,
                responseType: 'arraybuffer'
            }).then(function (response) {

                var headers = response.headers();
                var filename = 'cobertura-' + id + '-' + descricao + '.kml';
                var contentType = headers['content-type'];

                var blob = new Blob([response.data], {
                    type: contentType
                });
                if (navigator.msSaveBlob)
                    navigator.msSaveBlob(blob, filename);
                else {
                    // Try using other saveBlob implementations, if available
                    var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
                    if (saveBlob === undefined) {
                        var linkElement = document.createElement('a');
                        try {
                            var blob = new Blob([response.data], { type: contentType });
                            var url = window.URL.createObjectURL(blob);
                            linkElement.setAttribute('href', url);
                            linkElement.setAttribute("download", filename);

                            var clickEvent = new MouseEvent("click", {
                                "view": window,
                                "bubbles": true,
                                "cancelable": false
                            });
                            linkElement.dispatchEvent(clickEvent);
                        } catch (ex) {
                            console.log(ex);
                        }
                    }

                }

            }, function (error) {
                console.log(error);
            });
        };


        $scope.sair = function () {
            $rootScope.eventoSelecionado = { id: '' };
            $cookies.put('eventoSelecionado.id', '');
            $cookies.put('eventoSelecionado.descricao', '');
            $rootScope.$broadcast("eventoSelecionado");
            //window.location.reload();
            //window.location.assign("#/eventos");
            $location.path('/eventos');
        };
    });
