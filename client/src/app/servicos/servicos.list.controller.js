(function () {
    'use strict';

    angular
        .module('app')
        .controller('ServicosListController', ServicosListController);

    /** @ngInject */
    function ServicosListController($http, API_CONFIG, cfpLoadingBar) {

        var vm = this;

        vm.currentPage = 1;
        vm.limit = 20;
        vm.filtro = '';
        vm.servicos = [];

        vm.busca = busca;
        vm.pageChanged = pageChanged;

        activate();

        function activate() {
            getData();
        }

        function getData() {
            cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/servicos?page=' + vm.currentPage + "&limit=" + vm.limit + vm.filtro;
            $http.get(urlApi).then(function (response) {
                vm.totalItems = response.data.pagination.total;
                vm.start = response.data.pagination.start;
                vm.end = response.data.pagination.end;
                angular.copy(response.data.dados, vm.servicos);
                cfpLoadingBar.complete();
            });
        }

        function pageChanged() {
            getData();
        }

        function busca(termos) {

            vm.currentPage = 1;

            if (vm.tipo === 'nome') {
                vm.filtro = '&nome=' + termos;
            }

     
            getData();

        }

    }

})();
