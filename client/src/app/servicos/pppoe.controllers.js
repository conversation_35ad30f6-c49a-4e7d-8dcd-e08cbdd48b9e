'use strict';

angular.module('app')

    .controller('pppoeCtrl', function ($scope, $route, $rootScope, PppoeServersJSONService, PppoeServersService, PrefixosJSONService, toaster) {

        $scope.servers = [];

        var Pppoe = PppoeServersService.getServers({servico: $route.current.params.servico});
        Pppoe.$promise.then(function (data) {
            $scope.servers = data.dados;
            var Prefixos = PrefixosJSONService.getPrefixos({servico: $scope.servers[0].pppoeserver});
            Prefixos.$promise.then(function (data) {
                $scope.prefixos = data;
               
            });
        });


        var PppoeJSON = PppoeServersJSONService.getServers();
        PppoeJSON.$promise.then(function (data) {
            $scope.pppoeservers = data;
        });

        $scope.savePppoe = function (server) {

            var servico = $route.current.params.servico;

            var novoServer = {};
            novoServer = angular.copy(server);
            novoServer.servico = servico;

            novoServer.idevento = $rootScope.eventoSelecionado.id;
            novoServer.username = $rootScope.operador.username;

            PppoeServersService.insertServer(novoServer, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "PPPoE Server salvo", "PPPoE Server adicionado com sucesso!");
                    var Pppoe = PppoeServersService.getServers({servico: $route.current.params.servico});
                    Pppoe.$promise.then(function (data) {
                        $scope.servers = data.dados;
                        var Prefixos = PrefixosJSONService.getPrefixos({servico: $scope.servers[0].pppoeserver});
                        Prefixos.$promise.then(function (data) {
                            $scope.prefixos = data;
                        });
                    });
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

    });
