'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/hosts/:id/:host/servicos/:servico_id/:servico/', {
                templateUrl: 'app/servicos/servicos.details.html',
                controller: 'ServicosDetailCtrl',
                title: 'Serviços',
                resolve: {
                    dadosAPI: function ($q, ServicosService, CalculosPtmpService, $route) {
                        var Servicos = ServicosService.getServicos({
                            host: $route.current.params.host,
                            servico: $route.current.params.servico
                        });

                        var CalculosPtmp = CalculosPtmpService.get({
                            host: $route.current.params.host,
                            servico: $route.current.params.servico
                        });

                        return $q.all([Servicos.$promise, CalculosPtmp.$promise]);

                    }
                },
                authorize: ['servicos.read', 'servicos.write']
            })

            //Formulário de envio de imagem - TESTE
            .when('/hosts/:id/:host/servicos/:id_servico/anexo', {
                templateUrl: 'app/servicos/anexos/formulario.html',
                controller: 'AnexoController',
                title: 'Serviços',
            })

            .when('/servicos', {
                templateUrl: 'app/servicos/servicos.list.html',
                controller: 'ServicosListController',
                controllerAs: 'SLC',
                title: 'Serviços',
                authorize: ['servicos.read', 'servicos.write']

            });
    });

