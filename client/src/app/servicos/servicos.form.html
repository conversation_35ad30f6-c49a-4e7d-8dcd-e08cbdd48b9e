<div class="modal fade" id="frmservicos" tabindex="-1" role="dialog"
     aria-labelledby="frmservicoslabel" aria-hidden="true" modal="showModal" close="cancel()">

    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <button type="button" class="close"
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmservicoslabel">
                    Dados do Serviço
                </h4>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <form role="form" class="form-horizontal" name="frmservicos" novalidate angular-validator>
                  <div class="form-group">
                    <label class="col-md-3 control-label" for="hostservico">Nome</label>
                      <div class="col-md-9">
                        <input  type = "text"
                              placeholder="Nome do serviço"
                              type="text"
                              name = "host"
                              id="hostservico"
                              validate-on="dirty"
                              class = "form-control input-md"
                              ng-model = "servico.nomeservico"
                              ng-pattern="/^[a-zA-Z 0-9_.-]*$/"
                              ng-disabled="eventoSelecionado.id == ''"
                              ng-trim="false"
                              invalid-message="'O nome digitado não é válido.'"
                              required-message="'Este campo é obrigatório'"
                              required
                         >
                         <span class="help-block">
                           <i class="glyphicon glyphicon-info-sign"></i> Caracteres permitidos: Letras (sem acentos), números, espaços, underscore, hífen e pontos
                         </span>
                      </div>
                  </div>

                <div class="form-group">
                    <label class="col-md-3 control-label" for="host">Host</label>
                    <div class="col-md-6">
                      <ol class="nya-bs-select form-control"

                        id="host"
                        ng-model="servico.host"
                        ng-change="getPlacaPorta()"
                        data-live-search="true"
                        data-size="8"

                      >
                        <li nya-bs-option="host in hosts">
                          <a>
                            {{ host }}
                            <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                        </li>
                      </ol>
                    </div>
                 </div>
                 <div class="form-group">

                    <label class="col-md-3 control-label" for="tiposervico">Tipo</label>
                    <div class="col-md-4">
                      <ol class="nya-bs-select form-control"
                         title="Selecione um Tipo"
                         id="tiposervico"
                         ng-model="servico.tipohost"
                         data-live-search="true"
                         data-size="8"
                        >
                        <li nya-bs-option="tipo in tipos">
                          <a>
                              {{ ::tipo }}
                              <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                        </li>
                      </ol>
                    </div>
                     <!-- <select class="form-control" ng-model="servico.tipohost"
                              ng-options="o as o for o in servicostipos"></select>
                              -->
                  </div>
                  <div class="form-group" ng-show="servico.tipohost=='VPN'">
                      <label class="col-md-3 control-label" for="hostdestino">Host Destino</label>
                      <div class="col-md-2">
                        <ol class="nya-bs-select form-control"
                            title="Selecione um Host"
                            id="hostdestino"
                            ng-model="servico.hostdestino"
                            data-live-search="true"
                            data-size="8"
                        >
                        <li nya-bs-option="hostdestino in hostsvpn">
                          <a>
                            {{ ::hostdestino }}
                            <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                        </li>
                      </ol>
                     </div>
                  </div>
                  <div class="form-group" ng-show="servico.tipohost=='VPN'">
                    <label class="col-md-3 control-label" for="protocolo">Protocolo</label>
                      <div class="col-md-3">
                         <ol class="nya-bs-select form-control"
                            title="Selecione um Protocolo"
                            id="protocolo"
                            ng-model="servico.protocolo"
                            data-live-search="true"
                            data-size="8"
                            ng-init="protocolos = ['EoIP', 'VPLS', 'VLAN', 'L2TP']"
                         >
                         <li nya-bs-option="protocolo in protocolos">
                          <a>
                            {{ ::protocolo }}
                            <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                         </li>
                        </ol>
                      </div>
                    </div>
                    <div class="form-group" ng-show="servico.tipohost=='VPN'">
                      <label class="col-md-3 control-label" for="tag">Tag</label>
                      <div class="col-md-2">
                        <input class="form-control" id="tag" type="text" ng-model="servico.tag">
                      </div>
                    </div>
                    <div class="form-group" ng-show="servico.tipohost=='Porta GPON OLT' || servico.tipohost=='FTTA'">
                      <label class="col-md-3 control-label" for="vlanid">VLAN ID</label>
                      <div class="col-md-2">
                        <input class="form-control" id="vlanid" type="text" ng-model="servico.vlanid">
                      </div>
                    </div>

                    <div class="form-group" ng-show="servico.tipohost=='Serviço IPTV'">
                      <label class="col-md-3 control-label" for="vlanid">VLAN Unicast</label>
                      <div class="col-md-2">
                        <input class="form-control" id="vlanid" type="text" ng-model="servico.vlanid">
                      </div>
                    </div>

                    <div class="form-group" ng-show="servico.tipohost=='Serviço IPTV'">
                      <label class="col-md-3 control-label" for="vlanid_multicast">VLAN Unicast IPTV</label>
                      <div class="col-md-2">
                        <input class="form-control" id="vlanid_multicast" type="text" ng-model="servico.vlanid_unicast_iptv">
                      </div>
                    </div>

                    <div class="form-group" ng-show="servico.tipohost=='FTTA'">
                        <label class="col-md-3 control-label" for="olt">OLT</label>
                        <div class="col-md-4">
                        <ol class="nya-bs-select form-control"
                         title="Selecione a OLT"
                         id="olt"
                         ng-model="servico.olt"
                         ng-change="getPlacaPorta()"
                         data-live-search="true"
                         data-size="8"
                        >
                        <li nya-bs-option="item in olts">
                          <a>
                              {{ ::item }}
                              <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                        </li>
                      </ol>
                    </div>
                      </div>

                    <div class="form-group" ng-show="servico.tipohost=='FTTA'">
                        <label class="col-md-3 control-label" for="placaporta">Placa/Porta</label>
                        <div class="col-md-4">
                        <ol class="nya-bs-select form-control"
                         title="Selecione uma placa/porta"
                         id="placaporta"
                         ng-model="servico.placa_porta"
                         data-live-search="true"
                         data-size="8"
                        >
                        <li nya-bs-option="item in placaporta">
                          <a>
                              {{ ::item }}
                              <span class="glyphicon glyphicon-ok check-mark"></span>
                          </a>
                        </li>
                      </ol>
                    </div>
                      </div>

                    
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>
                <button type="button" class="btn btn-primary" ng-click="saveServico(servico);" ng-disabled="frmservicos.$invalid" data-dismiss="modal">
                    Salvar</button>
            </div>
        </div>
    </div>
</div>
