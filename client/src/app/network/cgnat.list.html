<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-random"></i> CGNAT</li>

</ol>
<div class="barra container-fluid">
	<div class="form-group">
		<div class="form-group pull-right">
			<form class="form-inline" role="form">
				<div class="row">
					<div class="form-group">
						<!--span ng-if="$index == 0">&nbsp; </span>
						<span ng-if="$index > 0">E </span>
						<select class="form-control" ng-model="CGN.filtros[$index].tipo">
							<option value="term_sequence">Term sequence</option>
							<option value="neighbor_sequence">Neig. term sequence</option>
							<option value="host">Host</option>
							<option value="policy">Policy</option>
							<option value="term">Term</option>
							<option value="in_out">Import/export</option>
							<option value="group">Group</option>
							<option value="not_prefix">Not Prefix</option>
							<option value="prefix">Prefix</option>
							<option value="prefix_length">Prefix Length</option>
							<option value="action">Action</option>
							<option value="neighbor_description">Neighbor</option>
							<option value="bgp_as_path">BGP As Path</option>
							<option value="bgp_as_path_length">BGP As Path Length</option>
							<option value="set_route_targets">Set Route Targets</option>
							<option value="set_bgp_prepend">Set BGP Prepend</option>
							<option value="set_bgp_local_pref">Set BGP Local Pref</option>
							<option value="set_bgp_communities">Set BGP Communities</option>
							<option value="set_in_nexthop">Set In Next Hop</option>
							<option value="set_out_nexthop">Set Out Next Hop</option>
							<option value="disabled">Disabled</option>
							<option value="timestamp">Timestamp</option>
						</select>
						<select class="form-control" ng-model="CGN.filtros[$index].operador">
							<option value="=">igual</option>
							<option value="like">contém</option>
							<option value="pertence" ng-if="CGN.filtros[$index].tipo == 'prefix'">pertence</option>

						</select>

						<select class="form-control" ng-model="CGN.filtros[$index].termos"
							ng-options="o for o in CGN.timestamps" ng-if="CGN.filtros[$index].tipo == 'timestamp'">

						</select>
						<input size="30" maxlength="30" class="form-control" type="text"
							ng-model="CGN.filtros[$index].termos" ng-if="CGN.filtros[$index].tipo !== 'timestamp'">
						<button class="btn btn-default" title="+ Filtro" ng-click="CGN.add_filtro()">+ Filtro</button>
						<button class="btn btn-default" title="- Filtro" ng-click="CGN.remove_filtro($index)"
							ng-if="$index > 0">-
							Filtro</button-->
						<!--button class="btn btn-default" title="Pesquisar" ng-click="CGN.busca()">Pesquisar</button-->
						<img src="assets/images/ajax-loader.gif" ng-show="CGN.atualizandoLista">
						<button class="btn btn-default" title="Atualizar" ng-click="CGN.getSolicitacoes()"><i
								class="glyphicon glyphicon-refresh"></i> Atualizar</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>

<div class="table-responsive">
	<span class="counter pull-right"></span>

	<table class="table table-striped table-hover table-bordered">
		<thead>
			<tr>
				<th class="vert-align text-center">
					#
				</th>

				<th class="vert-align text-center">
					Solicitado por
				</th>

				<th class="vert-align text-center">
					Usuário
				</th>
				<th class="vert-align text-center">
					IP
				</th>
				<th class="vert-align text-center">
					Cidade
				</th>
				<th class="vert-align text-center">
					Motivo
				</th>
				<th class="vert-align text-center">
					Solicitado em
				</th>
				<th class="vert-align text-center">
					Executado por
				</th>

				<th class="vert-align text-center">
					Executado em
				</th>

				<th class="vert-align text-center">
					Restaurado por
				</th>

				<th class="vert-align text-center">
					Restaurado em
				</th>
				<th class="vert-align text-center">
					Obs.
				</th>
				<th class="vert-align text-center">
					Status
				</th>
				<th class="vert-align text-center" colspan="2">
					Ações
				</th>
			</tr>
		</thead>
		<tbody>

			<tr ng-repeat="solicitacao in CGN.solicitacoes"
				ng-class="{'success': solicitacao.status == 'EXECUTADO', 'danger': solicitacao.status == 'RESTAURADO' || solicitacao.status == 'NEGADO'}">

				<td class="vert-align text-center">
					{{solicitacao.id}}
				</td>

				<td class="vert-align text-center">
					{{solicitacao.operador}}
				</td>

				<td class="vert-align text-center">
					{{solicitacao.usuario}}
				</td>

				<td class="vert-align text-center">
					{{solicitacao.ip_cgnat}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.cidade}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.motivo}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.data_solicitacao}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.executado_por}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.data_execucao}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.restaurado_por}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.data_restauracao}}
				</td>
				<td class="vert-align text-center">
					{{solicitacao.observacao}}
				</td>
				<td class="vert-align text-center">
					<span class="badge">{{solicitacao.status}}</span>
				</td>

				<td class="vert-align text-center">
					<a ng-if="solicitacao.status == 'PENDENTE'" class="btn btn-success btn-sm"
						title="Executar solicitação"
						ng-really-message="Tem certeza de que deseja executar esta remoção de CGNAT?"
						ng-really-click="CGN.executar(solicitacao.id)"><i class="glyphicon glyphicon-ok"></i>
					</a>
				</td>
				<td class="vert-align text-center">
					<a ng-if="solicitacao.status == 'PENDENTE'" class="btn btn-danger btn-sm" title="Negar solicitação"
						ng-click="CGN.negar(solicitacao.id)"><i class="glyphicon glyphicon-ban-circle"></i>
					</a>
					<a ng-if="solicitacao.status == 'EXECUTADO'" class="btn btn-danger btn-sm" title="Restaurar CGNAT"
						ng-click="CGN.restaurar(solicitacao.id)"><i class="glyphicon glyphicon-remove"></i>
					</a>
				</td>
			</tr>

		</tbody>
	</table>
	<div class="text-center">
		<uib-pagination total-items="CGN.pagination.size" ng-model="CGN.pagination.page" ng-change="CGN.pageChanged()"
			items-per-page="CGN.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
			boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
		</uib-pagination>
	</div>
	<div class="text-center">
		Página <span class="badge">{{ CGN.pagination.page}}</span> de <span
			class="badge">{{ CGN.pagination.pages}}</span>
		de <span class="badge">{{ CGN.pagination.size}}</span> registro(s)</span>
	</div>
</div>