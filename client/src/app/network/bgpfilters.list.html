<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-network"></i> Filtros BGP</li>

</ol>
<div class="barra container-fluid">
  <div class="form-group">
    <div class="form-group pull-right">
      <form class="form-inline" role="form">
        <div class="row" ng-repeat="filtros in BFC.filtros">
          <div class="form-group">
            <span ng-if="$index == 0">&nbsp; </span>
            <span ng-if="$index > 0">E </span>
            <select class="form-control" ng-model="BFC.filtros[$index].tipo">
              <option value="term_sequence">Term sequence</option>
              <option value="neighbor_sequence">Neig. term sequence</option>
              <option value="host">Host</option>
              <option value="policy">Policy</option>
              <option value="term">Term</option>
              <option value="in_out">Import/export</option>
              <option value="group">Group</option>
              <option value="not_prefix">Not Prefix</option>
              <option value="prefix">Prefix</option>
              <option value="prefix_length">Prefix Length</option>
              <option value="action">Action</option>
              <option value="neighbor_description">Neighbor</option>
              <option value="bgp_as_path">BGP As Path</option>
              <option value="bgp_as_path_length">BGP As Path Length</option>
              <option value="set_route_targets">Set Route Targets</option>
              <option value="set_bgp_prepend">Set BGP Prepend</option>
              <option value="set_bgp_local_pref">Set BGP Local Pref</option>
              <option value="set_bgp_communities">Set BGP Communities</option>
              <option value="set_in_nexthop">Set In Next Hop</option>
              <option value="set_out_nexthop">Set Out Next Hop</option>
              <option value="disabled">Disabled</option>
              <option value="timestamp">Timestamp</option>
            </select>
            <select class="form-control" ng-model="BFC.filtros[$index].operador">
              <option value="=">igual</option>
              <option value="like">contém</option>
              <option value="pertence" ng-if="BFC.filtros[$index].tipo == 'prefix'">pertence</option>

            </select>

            <select class="form-control" ng-model="BFC.filtros[$index].termos" ng-options="o for o in BFC.timestamps"
              ng-if="BFC.filtros[$index].tipo == 'timestamp'">

            </select>
            <input size="30" maxlength="30" class="form-control" type="text" ng-model="BFC.filtros[$index].termos"
              ng-if="BFC.filtros[$index].tipo !== 'timestamp'">
            <button class="btn btn-default" title="+ Filtro" ng-click="BFC.add_filtro()">+ Filtro</button>
            <button class="btn btn-default" title="- Filtro" ng-click="BFC.remove_filtro($index)" ng-if="$index > 0">-
              Filtro</button>
            <button class="btn btn-default" title="Pesquisar" ng-click="BFC.busca()">Pesquisar</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="table-responsive">
  <span class="counter pull-right"></span>

  <table class="table table-striped table-hover table-bordered">
    <thead>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('host')"> Host
          <span ng-show="BFC.sortBy == 'host' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'host' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('policy')"> Policy
          <span ng-show="BFC.sortBy == 'policy' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'policy' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('term_sequence')"> Term Seq.
          <span ng-show="BFC.sortBy == 'term_sequence' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'term_sequence' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('term')"> Term
          <span ng-show="BFC.sortBy == 'term' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'term' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('in_out')"> In/Out
          <span ng-show="BFC.sortBy == 'in_out' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'in_out' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <!--th class="vert-align text-center"><a href="#" ng-click="BFC.sort('not_prefix')"> Not Prefix
          <span ng-show="BFC.sortBy == 'not_prefix' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'not_prefix' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th-->
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('prefix')"> Prefix
          <span ng-show="BFC.sortBy == 'ip' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'ip' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('prefix_length')"> Prefix Length
          <span ng-show="BFC.sortBy == 'prefix_length' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'prefix_length' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('action')"> Action
          <span ng-show="BFC.sortBy == 'action' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'action' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('group')"> Group
          <span ng-show="BFC.sortBy == 'group' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'group' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('neighbors_sequence')"> Neig. Seq.
          <span ng-show="BFC.sortBy == 'neighbors_sequence' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'neighbors_sequence' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('neighbor_description')"> Neighbor
          <span ng-show="BFC.sortBy == 'neighbor_description' && BFC.sortOrder == 'dsc'"
            class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'neighbor_description' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('bgp_as_path')"> BGP As Path
          <span ng-show="BFC.sortBy == 'bgp_as_path' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'bgp_as_path' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('bgp_as_path_length')"> BGP As Path Length
          <span ng-show="BFC.sortBy == 'bgp_as_path_length' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'bgp_as_path_length' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>

      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_route_targets')"> Set Route Targets
          <span ng-show="BFC.sortBy == 'set_route_targets' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_route_targets' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_bgp_prepend')"> Set BGP Prepend
          <span ng-show="BFC.sortBy == 'set_bgp_prepend' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_bgp_prepend' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_bgp_local_pref')"> Set BGP Local Pref
          <span ng-show="BFC.sortBy == 'set_bgp_local_pref' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_bgp_local_pref' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_bgp_communities')"> Set BGP Communinities
          <span ng-show="BFC.sortBy == 'set_bgp_communities' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_bgp_communities' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_in_nexthop')"> Set In Next Hop
          <span ng-show="BFC.sortBy == 'set_in_nexthop' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_in_nexthop' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('set_out_nexthop')"> Set Out Next Hop
          <span ng-show="BFC.sortBy == 'set_out_nexthop' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'set_out_nexthop' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>

      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('disabled')"> Disabled
          <span ng-show="BFC.sortBy == 'disabled' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'disabled' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>

      <th class="vert-align text-center"><a href="#" ng-click="BFC.sort('timestamp')"> Timestamp
          <span ng-show="BFC.sortBy == 'datacad' && BFC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
          <span ng-show="BFC.sortBy == 'datacad' && BFC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
        </a></th>
      </tr>
    </thead>
    <tbody>

      <tr ng-repeat="filter in BFC.bgpfilters" ng-class="{'danger': filter.disabled == 'true'}">
        <td class="vert-align text-center">{{:: filter.host}}</td>
        <td class="vert-align text-center">{{:: filter.policy}}</td>
        <td class="vert-align text-center">{{:: filter.term_sequence}}
        </td>
        <td class="vert-align text-center">{{:: filter.term}}</td>
        <td class="vert-align text-center">{{:: filter.in_out}}</td>
        <!--td class="vert-align text-center">{{:: filter.not_prefix}}</td-->
        <td class="vert-align text-center">{{:: filter.prefix}}</td>
        <td class="vert-align text-center">{{:: filter.prefix_length}}</td>
        <td class="vert-align text-center"><span class="label label-success"
            ng-if="filter.action=='accept'">{{:: filter.action}}</span><span class="label label-danger"
            ng-if="filter.action=='discard' || filter.action=='reject'">{{:: filter.action}}</span><span
            class="label label-default" ng-if="filter.action=='passthrough'">{{:: filter.action}}</span></td>
        <td class="vert-align text-center">{{:: filter.group}}
        </td>
        <td class="vert-align text-center">{{:: filter.neighbor_sequence}}</td>
        <td class="vert-align text-center">{{:: filter.neighbor_description}}</td>
        <td class="vert-align text-center">{{:: filter.bgp_as_path}}</td>
        <td class="vert-align text-center">{{:: filter.bgp_as_path_length}}</td>
        <td class="vert-align text-center">{{:: filter.set_route_targets}}</td>
        <td class="vert-align text-center">{{:: filter.set_bgp_prepend}}</td>
        <td class="vert-align text-center">{{:: filter.set_bgp_local_pref}}</td>
        <td class="vert-align text-center">{{:: filter.set_bgp_communities}}</td>
        <td class="vert-align text-center">{{:: filter.set_in_nexthop}}</td>
        <td class="vert-align text-center">{{:: filter.set_out_nexthop}}</td>
        <td class="vert-align text-center">{{:: filter.disabled}}</td>
        <td class="vert-align text-center">{{:: filter.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
      </tr>

    </tbody>
  </table>
  <div class="text-center">
    <uib-pagination total-items="BFC.pagination.size" ng-model="BFC.pagination.page" ng-change="BFC.pageChanged()"
      items-per-page="BFC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
      boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
    </uib-pagination>
  </div>
  <div class="text-center">
    Página <span class="badge">{{ BFC.pagination.page}}</span> de <span class="badge">{{ BFC.pagination.pages}}</span>
    de <span class="badge">{{ BFC.pagination.size}}</span> registro(s)</span>
  </div>
</div>