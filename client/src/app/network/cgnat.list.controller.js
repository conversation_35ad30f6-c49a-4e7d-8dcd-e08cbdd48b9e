(function () {
	'use strict';

	angular
		.module('app')
		.controller('CGNATListController', CGNATListController);

	/** @ngInject */
	function CGNATListController($http, API_CONFIG, cfpLoadingBar, $rootScope, toaster, $routeParams, $filter, $location, $base64, $window) {

		var vm = this;

		vm.solicitacoes = []
		vm.pagination = {}

		vm.atualizandoLista = false;
		vm.getSolicitacoes = getSolicitacoes;
		vm.pageChanged = pageChanged;
		vm.executar = executar;
		vm.negar = negar;
		vm.restaurar = restaurar;

		// vm.solicitacoes = [
		// 	{
		// 		id: 1,
		// 		status: 'RESTAURADO',
		// 		operador: 'thales.lima',
		// 		usuario: 'cliente.usuario',
		// 		cidade: 'pocos',
		// 		motivo: 'Motivo da remoção',
		// 		data_solicitacao: '2020-11-17 12:39:16',
		// 		ip_cgnat: '**************',
		// 		executado_por: 'thales.lima',
		// 		data_execucao: '2020-11-17 12:44:33',
		// 		restaurado_por: 'euler.garcia',
		// 		data_restauracao: '2020-11-17 12:54:22'
		// 	}
		// ]
		activate();

		function activate() {
			getSolicitacoes();
		}

		function pageChanged() {
			var url = '/network/cgnat?page=' + vm.pagination.page;
			$location.url(url);
		}

		function executar(id) {
			const data = {
				id: id,
				operador: $rootScope.operador.username
			}

			$http({
				url: API_CONFIG.url + '/network/cgnat/remover',
				method: "POST",
				data: data,
				ignoreLoadingBar: false
			}).then(function (response) {
				if (response.data.status === 'success') {
					$window.alert('O CGNAT foi removido.');
				}
				else if (response.data.status === 'executando') {
					$window.alert('A remoção deste CGNAT já está em execução.');
				}
				else {
					$window.alert('Ocorreu um erro ao remover o CGNAT.');
				}
				getSolicitacoes();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function negar(id) {
			var motivo;
			do {
				motivo = $window.prompt('Especifique o motivo da negação da solicitação:', "");

				if (motivo === null) {
					return;
				}
				else if (motivo.trim() === '') {
					$window.alert('É necessário especificar um motivo.');
				}
			} while (motivo === null || motivo.trim() === '');

			const data = {
				id: id,
				motivo: motivo,
				operador: $rootScope.operador.username
			}

			$http({
				url: API_CONFIG.url + '/network/cgnat/negar',
				method: "POST",
				data: data,
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status === 'success') {
					$window.alert('A remoção foi negada.');
				}
				else {
					$window.alert('Ocorreu um erro ao processar a remoção.');
				}
				getSolicitacoes();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function restaurar(id) {
			var motivo;
			do {
				motivo = $window.prompt('Especifique o motivo da restauração do CGNAT:', "");

				if (motivo === null) {
					return;
				}
				else if (motivo.trim() === '') {
					$window.alert('É necessário especificar um motivo.');
				}
			} while (motivo === null || motivo.trim() === '');

			const data = {
				id: id,
				motivo: motivo,
				operador: $rootScope.operador.username
			}

			$http({
				url: API_CONFIG.url + '/network/cgnat/restaurar',
				method: "POST",
				data: data,
				ignoreLoadingBar: false
			}).then(function (response) {
				if (response.data.status === 'success') {
					$window.alert('O CGNAT foi restaurado.');
				}
				else {
					$window.alert('Ocorreu um erro ao restaurar o CGNAT.');
				}
				getSolicitacoes();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function getSolicitacoes() {
			vm.atualizandoLista = true;

			var data = {
				'cidade': vm.cidade,
				'status': vm.status,
				'atendente': vm.solicitado_por,
				'dtinicio': $filter('date')(vm.dtinicio, "yyyy-MM-dd"),
				'dtfim': $filter('date')(vm.dtfim, "yyyy-MM-dd"),
				'page': $routeParams.page
			}

			$http({
				url: API_CONFIG.url + '/network/cgnat/solicitacoes',
				method: "POST",
				data: data,
				ignoreLoadingBar: true
			}).then(function (response) {
				vm.atualizandoLista = false;
				angular.copy(response.data.rows, vm.solicitacoes);
				angular.copy(response.data.pagination, vm.pagination);


				// Parâmetros para botões de Executar e Negar, enviados por e-mail
				if ($routeParams.executar || $routeParams.negar || $routeParams.restaurar) {
					var solicitacaoParaExecutar = $routeParams.executar ? $routeParams.executar : ($routeParams.negar ? $routeParams.negar : ($routeParams.restaurar ? $routeParams.restaurar : null));

					var solicitacaoEncontrada = null;
					vm.solicitacoes.forEach(function (solicitacao) {
						if (solicitacao.id == solicitacaoParaExecutar)
							solicitacaoEncontrada = solicitacao;
					});

					if ($routeParams.executar) {
						if (solicitacaoEncontrada.status !== 'PENDENTE') {
							alert('Esta solicitação já foi processada. Status: ' + solicitacaoEncontrada.status + '.');
							return;
						}
						executar(solicitacaoParaExecutar);
					}
					else if ($routeParams.negar) {
						if (solicitacaoEncontrada.status !== 'PENDENTE') {
							alert('Esta solicitação já foi processada. Status: ' + solicitacaoEncontrada.status + '.');
							return;
						}
						negar(solicitacaoParaExecutar);
					}
					else if ($routeParams.restaurar) {
						if (solicitacaoEncontrada.status !== 'EXECUTADO') {
							alert('Esta solicitação já foi processada. Status: ' + solicitacaoEncontrada.status + '.');
							return;
						}
						restaurar(solicitacaoParaExecutar);
					}

					$routeParams.executar = $routeParams.negar = $routeParams.restaurar = null;
				}
			})
				.catch(function (err) {
					vm.atualizandoLista = false;
					console.log(err);

				});
		}
	}

})();
