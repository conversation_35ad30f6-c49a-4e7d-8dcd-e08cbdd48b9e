(function () {
    'use strict';

    angular
        .module('app')
        .controller('BgpFiltersListController', BgpFiltersListController);

    /** @ngInject */
    function BgpFiltersListController($http, API_CONFIG, cfpLoadingBar, $rootScope, toaster, $routeParams, $location, $base64) {

        var vm = this;

        vm.limit = 20;
        vm.filtros = [];
        
        vm.filtros = [
            {
                'tipo' : 'host',
                'operador' : 'like',
                'termos' : ''
            }

        ];
        
        vm.bgpfilters = [];
        vm.timestamps = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.sort = sort;
        vm.limpar = limpar;
        vm.add_filtro = add_filtro;
        vm.remove_filtro = remove_filtro;

        activate();

        function add_filtro(){
            vm.filtros.push(
                    {
                        'tipo' : 'host',
                        'operador' : '=',
                        'termos' : ''
                    } 
            );
            };


        function remove_filtro(index){
            vm.filtros.splice(index, 1);
        };

        function activate() {

            if($routeParams.filtros !== undefined){
                vm.filtros = JSON.parse($base64.decode($routeParams.filtros));
            } 


            if($routeParams.sortby !== undefined){
                vm.sortBy = $routeParams.sortby;
            } else {
              vm.sortBy = null;
            }
  
            if($routeParams.sortorder !== undefined){
              vm.sortOrder = $routeParams.sortorder;
            } else {
              vm.sortOrder = 'asc';
            }

            getData();
        }

        function getData() {

            if(vm.sortBy !== null){
                var urlApi = API_CONFIG.url + '/network/bgpfilters?filtros=' + $base64.encode(JSON.stringify(vm.filtros)) + '&page=' + $routeParams.page+'&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
            } else {
                var urlApi = API_CONFIG.url + '/network/bgpfilters?filtros=' + $base64.encode(JSON.stringify(vm.filtros)) + '&page=' + $routeParams.page;
            }

            $http({
                method: 'GET',
                url: urlApi,
              }).then(function (response) {
                angular.copy(response.data.rows, vm.bgpfilters);
                angular.copy(response.data.pagination, vm.pagination);
                angular.copy(response.data.timestamps, vm.timestamps);
                               
           });

        }

        function pageChanged() {
            if(vm.sortBy !== null){
                var urlApi = '/network/bgpfilters?filtros=' + $base64.encode(JSON.stringify(vm.filtros)) + '&page=' + vm.pagination.page+'&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder;
            } else {
                var urlApi = '/network/bgpfilters?filtros=' + $base64.encode(JSON.stringify(vm.filtros)) + '&page=' + vm.pagination.page;
            }    
            $location.url(urlApi);
        }

        function sort(field){
            if(field == vm.sortBy){
                if(vm.sortOrder == 'asc'){
                    vm.sortOrder = 'dsc';
                } else {
                    vm.sortOrder = 'asc';
                }
            }

            vm.sortBy = field;
            pageChanged();
        }

        function busca(termos) {

            vm.pagination.page = 1;
            pageChanged();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          $location.url('/network/bgpfilters');
        }
    }

})();
