<div class="modal" id="sc-agendamento-modal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 500px; max-width: 500px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Agendamento da coleta
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 20px;">

                <div class="input-group">
					<span class="input-group-addon">
						Agendamento da coleta:
					</span>
					<input ng-disabled="!SCC.reagendandoColeta.agendada" type="date" class="form-control" width="100" ng-model="SCC.reagendandoColeta.data"> 
					<span class="input-group-addon">
						às:
					</span>
					<input ng-disabled="!SCC.reagendandoColeta.agendada" type="time" class="form-control" width="100" ng-model="SCC.reagendandoColeta.horario">
				</div>

            </div>

			<div class="modal-footer">
				<button class="btn btn-primary" data-toggle="modal" data-target="#sc-agendamento-modal" ng-really-message="Deseja realmente salvar o novo agendamento?" ng-really-click="SCC.saveAgendamentoModal();">Salvar</button>
			</div>
		</div>
	</div>
</div>