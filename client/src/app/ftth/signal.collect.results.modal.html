<div class="modal" id="sc-results-modal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 800px; max-width: 800px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Resultados da coleta
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<table class="table table-striped">
					<thead ng-if="SCC.signalResults.length > 0">
						<tr>
							<th>
								<a href="#" ng-click="SCC.sortSignals('olt_ip')"> OLT IP
								<span ng-show="SCC.sortSignalsBy == 'olt_ip'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
							  </a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('placa')"> Placa
									<span ng-show="SCC.sortSignalsBy == 'placa'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								  </a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('porta')"> Porta
									<span ng-show="SCC.sortSignalsBy == 'porta'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('nome')"> Nome
									<span ng-show="SCC.sortSignalsBy == 'nome'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('serial')"> Serial
									<span ng-show="SCC.sortSignalsBy == 'serial'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('recv_power')"> Sinal
									<span ng-show="SCC.sortSignalsBy == 'recv_power'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('olt_recv_power')"> Sinal OLT
									<span ng-show="SCC.sortSignalsBy == 'olt_recv_power'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('bias')"> Bias
									<span ng-show="SCC.sortSignalsBy == 'bias'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('rtt')"> RTT
									<span ng-show="SCC.sortSignalsBy == 'rtt'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('send_power')"> Send power
									<span ng-show="SCC.sortSignalsBy == 'send_power'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('temperature')"> Temperatura
									<span ng-show="SCC.sortSignalsBy == 'temperature'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
							<th>
								<a href="#" ng-click="SCC.sortSignals('voltage')"> Voltagem
									<span ng-show="SCC.sortSignalsBy == 'voltage'" ng-class="SCC.sortSignalsOrder == 'asc' ? 'fa fa-caret-down' : 'fa fa-caret-up'"></span>
								</a>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="SCC.signalResults.length == 0">
							<td class="align-center bg-warning" style="font-size: 10pt;">Os resultados desta coleta ainda não estão disponíveis.</td>
						</tr>
						<tr ng-repeat="result in SCC.signalResults">
							<td>{{result.olt_ip}}</td>
							<td>{{result.placa}}</td>
							<td>{{result.porta}}</td>
							<td>{{result.nome}}</td>
							<td>{{result.serial}}</td>
							<td>{{result.recv_power}}</td>
							<td>{{result.olt_recv_power}}</td>
							<td>{{result.bias}}</td>
							<td>{{result.rtt}}</td>
							<td>{{result.send_power}}</td>
							<td>{{result.temperature}}</td>
							<td>{{result.voltage}}</td>
						</tr>
					</tbody>
				</table>

				<div ng-if="SCC.signalResults.length > 0" class="pagination-container">
					<div class="text-center">
						<uib-pagination total-items="SCC.signalPagination.size" ng-model="SCC.signalPagination.page" ng-change="SCC.resultadosPageChanged()"
						items-per-page="SCC.signalPagination.per_page" max-size="9" previous-text="Anterior" next-text="Próximo"
						boundary-links="true" first-text="Primeira" last-text="Última" rotate="false" class="pagination-sm">
						</uib-pagination>
					</div>
					<div class="text-center">
						Página <span class="badge">{{ SCC.signalPagination.page}}</span> de <span class="badge">{{ SCC.signalPagination.pages}}</span>
						de <span class="badge">{{ SCC.signalPagination.size}}</span> registro(s)</span>
					</div>
				</div>
            </div>

			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#sc-results-modal">Fechar</button>
			</div>
		</div>
	</div>
</div>