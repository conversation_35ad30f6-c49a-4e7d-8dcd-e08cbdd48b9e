<div class="modal" id="sc-details-modal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 700px; max-width: 700px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Detalhes da coleta
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                <div class="full-width align-center">
                    Descrição da coleta: {{SCC.detailsColeta.descricao}}
                    <br>
                    Status: <b>{{SCC.detailsColeta.status}}</b>
                    <br>
                    Agendamento: {{SCC.detailsColeta.agendamento ? (SCC.detailsColeta.agendamento | amDateFormat:'DD/MM/YYYY HH:mm:ss') : 'sem agendamento (início imediato)'}}
                </div>

                <div class="full-width align-center" style="border-top: 1px solid #AAA; margin-top: 10px;">
                    <h5>Itens coletados:</h5>
                </div>

                <div ng-repeat="olt in SCC.detailsColeta.requisicaoObj.olts" class="sc-olt-item">
                    <b>{{olt.name}} ({{olt.ip}})</b>:
                    <div ng-repeat="slot in olt.slots">
                        <b>Placa {{slot.number}}</b> - Portas {{slot.pons}}
                    </div>
                </div>

            </div>

			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#sc-details-modal">Fechar</button>
			</div>
		</div>
	</div>
</div>

<style>
    .slot-data-table {
        margin: 0 auto;
    }

    .slot-data-table td {
        padding: 10px;
    }

    .sc-pon-item {
        margin: 2px 0px;
        padding: 3px 0px;
        font-weight: bold;
        border: 1px solid #888;
        border-radius: 3px;
    }

    .sc-pon-item.selected {
        background: #a4ff70;
        border-color: #080;
    }

    .sc-pon-item.selected:hover {
        background: #7aff2d;
    }

    .sc-pon-item:hover {
        background: #EEE;
        border-color: darkcyan;
        cursor: pointer;
    }

    .sc-olt-slot-item {
        background: #EEE;
        text-align: center;
        padding: 3px 5px;
        font-weight: bold;
        border: 1px solid #888;
        border-radius: 3px;
    }
</style>