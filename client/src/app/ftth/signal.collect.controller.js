(function () {
	'use strict';

	angular
		.module('app')
		.controller('SignalCollectController', SignalCollectController);

	/** @ngInject */
	function SignalCollectController($http, API_CONFIG, SIGNAL_COLLECT_API, cfpLoadingBar, $rootScope, toaster, $routeParams, $localStorage, $filter, $location, $base64, $window, AlertService) {

		var vm = this;

		vm.oltsList = {};

		vm.oltsCidades = {};

		vm.selectedItems = {};

		vm.novaColeta = {};

		vm.signalResults = [];

		vm.getColetas = getColetas;
		vm.iniciarColeta = iniciarColeta;

		vm.handleCidadeSelection = handleCidadeSelection;
		vm.handleOltSelection = handleOltSelection;
		vm.handlePonSelection = handlePonSelection;

		vm.verResultados = verResultados;

		vm.selectSlot = selectSlot;
		vm.unselectSlot = unselectSlot;

		vm.openSlotModal = openSlotModal;

		vm.criarColeta = criarColeta;
		vm.repetirColeta = repetirColeta;

		vm.deleteColeta = deleteColeta;

		vm.coletasPageChanged = coletasPageChanged;
		vm.resultadosPageChanged = resultadosPageChanged;

		vm.repeatingColeta = {};
		vm.reagendandoColeta = {};
		vm.detailsColeta = {};
		vm.openRepeatModal = openRepeatModal;
		vm.openAgendamentoModal = openAgendamentoModal;
		vm.openDetailsModal = openDetailsModal;

		vm.saveAgendamentoModal = saveAgendamentoModal;

		vm.sortSignals = sortSignals;
		vm.sortSignalsBy = 'olt_ip';
		vm.sortSignalsOrder = 'asc';

		vm.selectedSlotData = {
			cidade: '',
			olt: {},
			slot: {}
		};

		vm.pagination = {
			page: 1,
			pages: 1,
			size: 0,
			per_page: 50
		};

		vm.signalPagination = {
			page: 1,
			pages: 1,
			size: 0,
			per_page: 20
		};

		vm.historicoColetas = [];
        
		activate();

		function activate() {
			resetNovaColeta();
			getOltsList();
			getColetas();
		}

		function coletasPageChanged() {
			getColetas();
		}

		function resultadosPageChanged() {
			if(vm.lastColetaOpened)
				verResultados(vm.lastColetaOpened);
		}

		function sortSignals(field){
            if(field == vm.sortSignalsBy){
                if(vm.sortSignalsOrder == 'asc'){
                    vm.sortSignalsOrder = 'desc';
                } else {
                    vm.sortSignalsOrder = 'asc';
                }
            }

            vm.sortSignalsBy = field;
            resultadosPageChanged();
        }

		function getOltsList() {
			vm.oltsList = {};

			var token = 'Bearer ' + $localStorage.currentUser.token;

			$http({
				url: SIGNAL_COLLECT_API.url + '/olts?per_page=100',
				method: 'GET'
			}).then(function(response){
				if(response.data.hasOwnProperty('count') && response.data.count > 1) {
					var olts = response.data.items;
					handleOltsList(olts);
					getSlotsList();
				}
				else {
					alert('Ocorreu um erro ao buscar os OLTs.');
				}
			}).then(function(response){

			});
		}

		function getSlotsList() {
			var token = 'Bearer ' + $localStorage.currentUser.token;

			$http({
				url: SIGNAL_COLLECT_API.url + '/slots?per_page=500',
				method: 'GET',
				headers: {
                    'Authorization': token
                }
			}).then(function(response){
				if(response.data.hasOwnProperty('count') && response.data.count > 1) {
					handleSlotsList(response.data.items);
				}
				else {
					alert('Ocorreu um erro ao buscar os slots dos OLTs.');
				}
			}).then(function(response){

			});
		}

		function handleSlotsList(slots) {
			slots.sort(function(a,b){
				return a.placa - b.placa;
			});

			slots.forEach(function(slot, i) {
				var ponsArray = [];
				for(var i=1; i<=slot.portas; i++)
					ponsArray.push(i);

				slot.pons = ponsArray;

				if(!vm.oltsList[slot.olt_ip].hasOwnProperty('slots'))
					vm.oltsList[slot.olt_ip].slots = [];

				vm.oltsList[slot.olt_ip].slots.push(slot);
			});
		}

		function handleOltsList(olts) {
			vm.oltsList = {};
			var oltsCidadesCounter = {};

			olts.forEach(function(olt, i){
				var descricao = olt.descricao.toUpperCase();
				var cidade;

				if(descricao.startsWith('OLT PCS'))
					cidade = 'Poços de Caldas';

				else if(descricao.startsWith('OLT AND') || descricao.startsWith('OLT ADS'))
					cidade = 'Andradas';

				else if(descricao.startsWith('OLT CPS'))
					cidade = 'Campestre';

				else if(descricao.startsWith('OLT ESP'))
					cidade = 'Espírito Santo do Pinhal';

				else if(descricao.startsWith('OLT SAJ'))
					cidade = 'Santo Antônio do Jardim';

				else if(descricao.startsWith('OLT SJV') || descricao.startsWith('OLT SJB'))
					cidade = 'São João da Boa Vista';

				else
					cidade = 'Outras cidades';

				if(!oltsCidadesCounter.hasOwnProperty(cidade)) {
					oltsCidadesCounter[cidade] = 0;
				}

				oltsCidadesCounter[cidade]++;

				olt.cidade = cidade;

				vm.oltsList[olt.ip] = olt;
			});

			// Ordenando cidades de acordo com o número de OLTS (decrescente)
			var orderedCidadesList = Object.keys(oltsCidadesCounter).sort(function(a,b){return oltsCidadesCounter[b]-oltsCidadesCounter[a]});

			orderedCidadesList.forEach(function(cidade, index){
				vm.oltsCidades[cidade] = {selectedState: ''};
			});
		}

		function iniciarColeta(coleta) {
			$http({
				url: SIGNAL_COLLECT_API.url + '/collects/'+coleta.id,
				method: 'PATCH',
				data: {
					status: 'START'
				}
			}).then(function(response) {
				if(response && response.data) {
					AlertService.alert({
						title: 'Sucesso',
						html: 'A coleta será iniciada agora.'
					});

					getColetas();
				}
			}).then(function(response) {

			});
		}

		// ===================================================
		// ===================================================

		function handleCidadeSelection(cidade) {
			if(vm.oltsCidades[cidade].selectedState == 'selected' || vm.oltsCidades[cidade].selectedState == 'partially-selected') {
				unselectCidade(cidade);
			}
			else {
				selectCidade(cidade);
			}
		}

		function selectCidade(cidade) {
			Object.keys(vm.oltsList).forEach(function(oltIp, oltIndex) {
				var olt = vm.oltsList[oltIp];
				if(olt.cidade === cidade) {
					selectOlt(olt);
				}
			});
		}

		function unselectCidade(cidade) {
			Object.keys(vm.oltsList).forEach(function(oltIp, oltIndex) {
				var olt = vm.oltsList[oltIp];
				if(olt.cidade === cidade) {
					unselectOlt(olt);
				}
			});
		}

		// -----

		function handleOltSelection(olt) {
			if(olt.selectedState != 'selected' && olt.selectedState != 'partially-selected' ) {
				selectOlt(olt);
			}
			else {
				unselectOlt(olt);
			}
		}

		function selectOlt(olt) {

			if(!vm.selectedItems.hasOwnProperty(olt.ip))
				vm.selectedItems[olt.ip] = {};

			if(olt.hasOwnProperty('slots') && olt.slots.length > 0) {
				olt.slots.forEach(function(slot, i){
					vm.selectedItems[olt.ip][slot.placa] = slot.pons;
				});
			}

			handleSelectedState();
		}

		function unselectOlt(olt) {
			if(vm.selectedItems.hasOwnProperty(olt.ip)) {
				delete vm.selectedItems[olt.ip];
				handleSelectedState();
			}
		}

		// -------------

		function selectSlot(slot) {
			if(!vm.selectedItems.hasOwnProperty(slot.olt_ip)) {
				vm.selectedItems[slot.olt_ip] = {};
			}

			vm.selectedItems[slot.olt_ip][slot.placa] = slot.pons;
			handleSelectedState();
		}

		function unselectSlot(slot) {
			if(vm.selectedItems.hasOwnProperty(slot.olt_ip) && vm.selectedItems[slot.olt_ip].hasOwnProperty(slot.placa)) {
				vm.selectedItems[slot.olt_ip][slot.placa] = [];
				handleSelectedState();
			}
		}

		// -------------

		function handlePonSelection(olt, slot, pon) {
			if(vm.selectedItems.hasOwnProperty(olt.ip) && vm.selectedItems[olt.ip].hasOwnProperty(slot.placa) && vm.selectedItems[olt.ip][slot.placa].includes(pon)) {
				unselectPon(olt, slot, pon);
			}
			else {
				selectPon(olt, slot, pon);
			}
		}

		function selectPon(olt, slot, pon) {
			if(!vm.selectedItems.hasOwnProperty(olt.ip)) {
				vm.selectedItems[olt.ip] = {};
			}
			if(!vm.selectedItems[olt.ip].hasOwnProperty(slot.placa)) {
				vm.selectedItems[olt.ip][slot.placa] = [];
			}
			if(!vm.selectedItems[olt.ip][slot.placa].includes(pon)) {
				vm.selectedItems[olt.ip][slot.placa].push(pon);
			}

			handleSelectedState();
		}

		function unselectPon(olt, slot, pon) {
			if(vm.selectedItems.hasOwnProperty(olt.ip) && vm.selectedItems[olt.ip].hasOwnProperty(slot.placa) && vm.selectedItems[olt.ip][slot.placa].includes(pon)){ 
				vm.selectedItems[olt.ip][slot.placa] = vm.selectedItems[olt.ip][slot.placa].filter(function(e){
					return e !== pon;
				});
			}

			handleSelectedState();
		}

		// ===================================================
		// ===================================================

		// Atualiza os objetos "vm.oltsCidades" e "vm.oltsList", colocando a propriedade "selectedState" nos itens de cidade, OLTs e slots
		// Os "selectedState" possíveis são: ['', 'partially-selected', 'selected']
		function handleSelectedState() {
			Object.keys(vm.oltsCidades).forEach(function(cidade, cidadeIndex){
				var cidadeState = '';
				var totalOlts = 0;
				var totalSelectedOlts = 0;
				
				Object.keys(vm.oltsList).forEach(function(oltIp, oltIndex) {
					var olt = vm.oltsList[oltIp];
					if(olt.cidade === cidade && olt.hasOwnProperty('slots') && olt.slots.length > 0) {
						totalOlts++;
						var oltState = '';
						var totalSlots = olt.slots.length;
						var totalSelectedSlots = 0;

						olt.slots.forEach(function(slot, slotIndex){
							var slotState = '';

							if(vm.selectedItems.hasOwnProperty(olt.ip) && vm.selectedItems[olt.ip].hasOwnProperty(slot.placa)) {
								var totalPonsInSlot = vm.selectedItems[olt.ip][slot.placa].length;

								if(totalPonsInSlot == slot.portas) {
									slotState = 'selected';
									totalSelectedSlots += 1;
								}
								else if(totalPonsInSlot > 0) {
									slotState = 'partially-selected';
									totalSelectedSlots += 0.5;
								}
							}

							vm.oltsList[oltIp].slots[slotIndex].selectedState = slotState;
						});

						if(totalSelectedSlots == totalSlots) {
							oltState = 'selected';
							totalSelectedOlts += 1;
						}
						else if(totalSelectedSlots > 0) {
							oltState = 'partially-selected';
							totalSelectedOlts += 0.5;
						}

						vm.oltsList[oltIp].selectedState = oltState;
					}
				});

				if(totalSelectedOlts == totalOlts) {
					cidadeState = 'selected';
				}
				else if(totalSelectedOlts > 0) {
					cidadeState = 'partially-selected';
				}

				vm.oltsCidades[cidade].selectedState = cidadeState;
			});
		}

		function openSlotModal(cidade, olt, slot) {
			vm.selectedSlotData = {
				cidade: cidade,
				olt: olt,
				slot: slot
			};

			$('#sc-slot-modal').modal('show');
		}

		function criarColeta() {

			if(vm.novaColeta.descricao == '') {
				AlertService.error('É necessário preencher a descrição da coleta.');
				return false;
			}

			var requisicao = requisicaoBuilder();

			if(!requisicao) {
				AlertService.error('Não foi selecionado nenhum item (OLT, placa ou porta).');
				return false;
			}

			var requestData = {
				descricao: vm.novaColeta.descricao,
				requisicao: requisicao,
				status: 'START'
			};

			if(vm.novaColeta.agendada) {
				if(vm.novaColeta.data == '') {
					AlertService.error('É necessário preencher a data da coleta.');
					return false;
				}
				if(vm.novaColeta.horario == '') {
					AlertService.error('É necessário preencher o horário da coleta.');
					return false;
				}

				var today = new Date(new Date().toDateString());

				if(new Date(vm.novaColeta.data).getTime() < today.getTime()) {
					AlertService.error('A data selecionada precisa ser maior ou igual a data de hoje.');
					return false;
				}
				
				var data = vm.novaColeta.data.toLocaleString('pt-BR').split(', ')[0];
				var horario = vm.novaColeta.horario.toLocaleString('pt-BR').split(', ')[1];

				requestData.agendamento = dmY2Ymd(data) + ' ' + horario
			}

			$http({
				url: SIGNAL_COLLECT_API.url + '/collects',
				method: 'POST',
				data: requestData,
			}).then(function(response){
				if(response && response.data) {
					AlertService.alert({
						title: 'Sucesso!',
						html: 'A coleta foi criada.'
					});
					resetNovaColeta();
					getColetas();
					$('#tab-historico-coletas').click();
				}
			}).then(function(response){
			});
		}

		function resetNovaColeta() {
			vm.novaColeta = {
				agendada: true,
				descricao: '',
				data: '',
				horario: ''
			};

			vm.selectedItems = {};

			handleSelectedState();
		}

		function repetirColeta() {

			if(vm.repeatingColeta.descricao == '') {
				AlertService.error('É necessário preencher a descrição da coleta.');
				return false;
			}

			var requestData = {
				descricao: vm.repeatingColeta.descricao,
				requisicao: vm.repeatingColeta.requisicao
			};

			if(vm.repeatingColeta.agendada) {
				if(vm.repeatingColeta.data == '') {
					AlertService.error('É necessário preencher a data da coleta.');
					return false;
				}
				if(vm.repeatingColeta.horario == '') {
					AlertService.error('É necessário preencher o horário da coleta.');
					return false;
				}

				var today = new Date(new Date().toDateString());
				var data = vm.repeatingColeta.data.toLocaleString('pt-BR').split(', ')[0];
				var horario = vm.repeatingColeta.horario.toLocaleString('pt-BR').split(', ')[1];

				if(new Date(vm.repeatingColeta.data).getTime() < today.getTime()) {
					AlertService.error('A data selecionada precisa ser maior ou igual a data de hoje.');
					return false;
				}

				requestData.agendamento = dmY2Ymd(data) + ' ' + horario
			}
			else {
				requestData.status = 'START';
			}

			$http({
				url: SIGNAL_COLLECT_API.url + '/collects',
				method: 'POST',
				data: requestData,
			}).then(function(response){
				if(response && response.data) {
					$('#sc-repeat-modal').modal('hide');
					getColetas();
				}
			}).then(function(response){
				console.log('response2:', response);
			});
		}

		function openAgendamentoModal(coleta) {
			if(coleta.agendamento) {
				coleta.agendada = true;
				var dataColeta = new Date(coleta.agendamento);
				
				var horario = dataColeta.toLocaleString('pt-BR').split(', ')[1];

				horario = new Date('1970-01-01 '+horario);
				
				coleta.horario = horario;
				coleta.data = dataColeta;
			}
			else {
				coleta.agendada = false;
				coleta.data = '';
				coleta.horario = '';
			}

			vm.reagendandoColeta = coleta;
			$('#sc-agendamento-modal').modal('show');
		}

		function saveAgendamentoModal() {
			var today = new Date(new Date().toDateString());

			if(new Date(vm.reagendandoColeta.data).getTime() < today.getTime()) {
				AlertService.error('A data selecionada precisa ser maior ou igual a data de hoje.');
				return false;
			}
			
			var data = vm.reagendandoColeta.data.toLocaleString('pt-BR').split(', ')[0];
			var horario = vm.reagendandoColeta.horario.toLocaleString('pt-BR').split(', ')[1];

			var agendamento = dmY2Ymd(data) + ' ' + horario;

			$http({
				url: SIGNAL_COLLECT_API.url + '/collects/' + vm.reagendandoColeta.id,
				method: 'PATCH',
				data: {
					agendamento: agendamento
				}
			}).then(function(response) {
				if(response && response.data) {
					getColetas();
					AlertService.alert({
						title: 'Sucesso!',
						html: 'O agendamento da coleta foi alterado.'
					});
				}
			}).then(function(response) {
			});
		}

		/* Monta o objeto da requisição, com base nos itens selecionados (vm.selectedItems), retornando no seguinte formato:
		{
			olts: [
				{
					ip: "X.X.X.X",
					slots: [
						{
							number: X,
							pons: [X, Y]
						}
					]
				}
			]
		} */

		function requisicaoBuilder() {
			var requisicao = {
				olts: []
			};

			var totalPons = 0;
			Object.keys(vm.selectedItems).forEach(function(oltIp, oltIndex){
				var olt = vm.selectedItems[oltIp];
				var slots = [];
				var oltPons = 0;

				Object.keys(olt).forEach(function(slot, index){
					var pons = olt[slot];
					oltPons += pons.length;
					totalPons += oltPons;

					if(pons.length > 0 ) {
						var slotObj = {
							number: slot,
							pons: pons
						}
						slots.push(slotObj);
					}
				});
				
				if(oltPons > 0) {
					var oltObj = {
						ip: oltIp,
						slots: slots
					};
					requisicao.olts.push(oltObj);
				}
			});

			if(totalPons > 0)
				return requisicao;
			else
				return false;
		}

		function getColetas() {
			vm.historicoColetas = [];

			$http({
				url: SIGNAL_COLLECT_API.url + '/collects?per_page='+vm.pagination.per_page+'&page=' + vm.pagination.page,
				method: 'GET'
			}).then(function(response){
				if(response.data.hasOwnProperty('count')) {
					vm.pagination.pages = response.data.pages;
					vm.pagination.size = response.data.count;
					vm.historicoColetas = response.data.items;
				}
				else {
					alert('Ocorreu um erro ao buscar o histórico de coletas');
				}
			}).then(function(response){

			});
		}

		function deleteColeta(id) {
			var token = 'Bearer ' + $localStorage.currentUser.token;

			$http({
				url: SIGNAL_COLLECT_API.url + '/collects/' + id,
				method: 'DELETE',
				headers: {
                    'Authorization': token
                }
			}).then(function(response){
				if(response.data == 'Deleted') {
					AlertService.alert({
						html: 'A coleta foi excluída com sucesso.'
					});
					getColetas();
				}
				else {
					AlertService.error('Ocorreu um erro ao excluir a coleta.');
				}
			}).then(function(response){

			});
		}

		function dmY2Ymd(date) {
			return date.split('/').reverse().join('-');
		}

		function openRepeatModal(coleta) {

			console.log('coleta:', coleta);

			if(coleta.agendamento) {
				coleta.agendada = true;
				var dataColeta = new Date(coleta.agendamento);
				
				var horario = dataColeta.toLocaleString('pt-BR').split(', ')[1];

				horario = new Date('1970-01-01 '+horario);
				
				coleta.horario = horario;
			}
			else {
				coleta.agendada = false;
				coleta.data = '';
				coleta.horario = '';
			}
			
			$('#sc-repeat-modal').modal('show');
			vm.repeatingColeta = coleta;
		}

		function openDetailsModal(coleta) {
			angular.copy(coleta, vm.detailsColeta);

			vm.detailsColeta.requisicao.olts.forEach(function(olt, index) {
				var oltIp = olt.ip;
				vm.detailsColeta.requisicao.olts[index].name = vm.oltsList[oltIp].descricao;

				vm.detailsColeta.requisicao.olts[index] = moveObjectElement('name', '', vm.detailsColeta.requisicao.olts[index]);
			});

			var styledRequisicao = JSON.stringify(vm.detailsColeta.requisicao, undefined, 8);

			vm.detailsColeta.requisicao = styledRequisicao;

			vm.detailsColeta.requisicaoObj = JSON.parse(vm.detailsColeta.requisicao);

			$('#sc-details-modal').modal('show');
		}

		function verResultados(coleta, downloadCsv) {
			downloadCsv = downloadCsv !== undefined;

			if (!downloadCsv) {
				vm.lastColetaOpened = coleta;
				$('#sc-results-modal').modal('show');
			}

			$http({
				method: 'GET',
				url: SIGNAL_COLLECT_API.url + '/signals?coleta_id=' + coleta.id + '&per_page=' + vm.signalPagination.per_page + '&page=' + vm.signalPagination.page + '&sort_by=' + vm.sortSignalsBy + '&sort_order=' + vm.sortSignalsOrder + '&download_csv=' + downloadCsv
			}).then(function (response) {
				if (response) {
					if (downloadCsv) {
						var coletaData = formatDate(new Date(response.data.items[0].data), '-');

						var filename = 'Coleta de sinais - ' + coleta.descricao + ' - ' + coletaData + '.csv';
	
						var builtCsv = csvmaker(response.data.items)

						var blob = new Blob([builtCsv], {
							type: 'text/csv'
						});
						if (navigator.msSaveBlob)
							navigator.msSaveBlob(blob, filename);
						else {
							var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
							if (saveBlob === undefined) {
								var linkElement = document.createElement('a');
								try {
									var blob = new Blob([builtCsv], { type: 'text/csv' });
									var url = window.URL.createObjectURL(blob);
									linkElement.setAttribute('href', url);
									linkElement.setAttribute("download", filename);
	
									var clickEvent = new MouseEvent("click", {
										"view": window,
										"bubbles": true,
										"cancelable": false
									});
									linkElement.dispatchEvent(clickEvent);
								} catch (ex) {
									console.log(ex);
								}
							}
	
						}
	
						return;
					}

					if (response.data && response.data.items) {
						vm.signalResults = response.data.items;
						vm.signalPagination.pages = response.data.pages;
						vm.signalPagination.size = response.data.count;
					}
				}
			}).then(function (response) {

			});
		}

		function moveObjectElement(currentKey, afterKey, obj) {
			var result = {};
			var val = obj[currentKey];
			delete obj[currentKey];
			var next = -1;
			var i = 0;
			if(typeof afterKey == 'undefined' || afterKey == null) afterKey = '';
			$.each(obj, function(k, v) {
				if((afterKey == '' && i == 0) || next == 1) {
					result[currentKey] = val; 
					next = 0;
				}
				if(k == afterKey) { next = 1; }
				result[k] = v;
				++i;
			});
			if(next == 1) {
				result[currentKey] = val; 
			}
			if(next !== -1) return result; else return obj;
		}

		function csvmaker(data) {
			const headers = Object.keys(data[0]);

			data = data.map(function(obj) {
				obj.data = formatDate(new Date(obj.data));
				return obj;
			});
			
			var rows = data.map(function(obj) {
				return headers.map(function(header) {
					return obj[header];
				});
			});
			
			var csvArray = [headers].concat(rows);
			
			var csvString = csvArray.map(function(row) {
				return row.join(',');
			}).join('\n');
			

			return csvString;
		}

		function formatDate(date, defaultSeparator) {
			defaultSeparator = defaultSeparator !== undefined ? defaultSeparator : null;

			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			if (!defaultSeparator) {
				return day + '/' + month + '/' + year + ' ' + hours + ':' + minutes + ':' + seconds;
			} else {
				var sep = defaultSeparator;
				return day + sep + month + sep + year + ' ' + hours + sep + minutes + sep + seconds;
			}
		}
	}

})();
