<div class="modal" id="sc-repeat-modal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 500px; max-width: 500px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Repetir coleta
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<div class="input-group">
					<span class="input-group-addon">
						Descrição:
					</span>
					<input type="text" class="form-control" ng-model="SCC.repeatingColeta.descricao">
				</div>

				<br>
				<div class="full-width align-center">
					<div class="btn-group btn-group-grafico-desempenho" role="group" aria-label="..." style="margin: 0 auto;">

						<button type="button" class="btn" ng-class="SCC.repeatingColeta.agendada ? 'btn-default' : 'btn-primary'" ng-click="SCC.repeatingColeta.agendada = false;">Início imediato</button>
						
						<button type="button" class="btn" ng-class="SCC.repeatingColeta.agendada ? 'btn-primary' : 'btn-default'" ng-click="SCC.repeatingColeta.agendada = true;">Início agendado</button>
			
					</div>
				</div>
				<br>

				<div class="input-group">
					<span class="input-group-addon">
						Agendamento da coleta:
					</span>
					<input ng-disabled="!SCC.repeatingColeta.agendada" type="date" class="form-control" width="100" ng-model="SCC.repeatingColeta.data"> 
					<span class="input-group-addon">
						às:
					</span>
					<input ng-disabled="!SCC.repeatingColeta.agendada" type="time" class="form-control" width="100" ng-model="SCC.repeatingColeta.horario">
				</div>
            </div>

			<div class="modal-footer">
				<button class="btn btn-primary" ng-really-message="Deseja realmente criar uma cópia desta coleta?" ng-really-click="SCC.repetirColeta();"><i class="glyphicon glyphicon-ok margin-right-5"></i> Criar coleta</button>
			</div>
		</div>
	</div>
</div>