<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-signal"></i> Coleta de sinal</li>

</ol>

<ul class="nav nav-tabs">
	<li role="presentation" class="active" data-target="#collects-history-container" data-toggle="tab"><a href="#" id="tab-historico-coletas"><i class="glyphicon glyphicon-th-list margin-right-5"></i>Histórico de coletas</a></li>

	<li role="presentation" data-target="#nova-coleta-container" data-toggle="tab"><a href="#"><i class="glyphicon glyphicon-plus margin-right-5"></i>Nova coleta</a></li>
</ul>

<div class="tab-content">

	<div role="tabpanel" class="tab-pane active" id="collects-history-container">

		<div class="barra container-fluid">
			<div class="form-group">
				<div class="form-group pull-right">
					<form class="form-inline" role="form">
						<div class="row">
							<div class="form-group">
								<img src="assets/images/ajax-loader.gif" ng-show="CGN.atualizandoLista">
								<button class="btn btn-default" title="Atualizar" ng-click="SCC.getColetas();"><i
										class="glyphicon glyphicon-refresh"></i> Atualizar</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<table class="table table-striped table-hoverable align-center valign-middle">
			<thead ng-if="!SCC.buscandoHistorico && SCC.historicoColetas.length > 0">
				<tr>
					<th>ID</th>
					<th>Criada em</th>
					<th>Descrição</th>
					<th>Criada por</th>
					<th>Agendada para</th>
					<th>Status</th>
					<th>Detalhes</th>
					<th>Iniciar</th>
					<th>Resultados</th>
					<th>Recriar</th>
					<th>Excluir</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-if="SCC.buscandoHistorico">
					<td>Carregando...</td>
				</tr>
				<tr ng-if="!SCC.buscandoHistorico && SCC.historicoColetas.length == 0">
					<td class="align-center bg-warning" style="font-size: 10pt;">Não foram realizadas coletas</td>
				</tr>
				<tr ng-repeat="coleta in SCC.historicoColetas">
					<td>{{coleta.id}}</td>
					<td>{{coleta.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
					<td>{{coleta.descricao}}</td>
					<td>{{coleta.user}}</td>
					<td>
						{{coleta.agendamento | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
						<button class="btn btn-vsm btn-primary" ng-if="coleta.agendamento && (coleta.status == 'IDLE' || coleta.status == 'SCHEDULED')" ng-click="SCC.openAgendamentoModal(coleta);"><i class="glyphicon glyphicon-pencil"></i></button>
					</td>
					<td>
						<span ng-class="coleta.status == 'RUNNING' ? 'text-success' : (coleta.status == 'IDLE' ? 'text-warning' : '')">{{coleta.status}}</span>
					</td>
					<td>
						<button class="btn btn-sm btn-info" title="Ver itens coletados" ng-click="SCC.openDetailsModal(coleta);"><i class="glyphicon glyphicon-eye-open"></i></button>
					</td>
					<td>
						<button ng-disabled="coleta.status !== 'IDLE'" class="btn btn-sm btn-success" title="Iniciar coleta agora" ng-really-message="Esta coleta agendada será iniciada agora. Deseja realmente continuar?" ng-really-click="SCC.iniciarColeta(coleta);"><i class="glyphicon glyphicon-play"></i></button>
					</td>
					<td>
						<button ng-disabled="coleta.status === 'IDLE'" class="btn btn-sm btn-primary" title="Ver resultados da coleta" ng-click="SCC.verResultados(coleta);">
							<i class="glyphicon glyphicon-search"></i>
						</button>
						<button ng-disabled="coleta.status === 'IDLE'" class="btn btn-sm btn-primary" title="Exportar resultados da coleta para planilha" ng-click="SCC.verResultados(coleta, true);">
							<i class="glyphicon glyphicon-export"></i>
						</button>
					</td>
					<td>
						<button class="btn btn-sm btn-primary" title="Criar uma cópia desta coleta" ng-click="SCC.openRepeatModal(coleta);">
							<i class="glyphicon glyphicon-retweet"></i>
						</button>
					</td>
					<td>
						<button class="btn btn-sm btn-danger" title="Excluir coleta" ng-really-message="Deseja realmente excluir esta coleta?" ng-really-click="SCC.deleteColeta(coleta.id);"><i class="glyphicon glyphicon-trash"></i></button>
					</td>
				</tr>
			</tbody>
		</table>

		<div class="text-center">
			<uib-pagination total-items="SCC.pagination.size" ng-model="SCC.pagination.page" ng-change="SCC.coletasPageChanged()"
			  items-per-page="SCC.pagination.per_page" max-size="9" previous-text="Anterior" next-text="Próximo"
			  boundary-links="true" first-text="Primeira" last-text="Última" rotate="false" class="pagination-sm">
			</uib-pagination>
		</div>
		<div class="text-center">
			Página <span class="badge">{{ SCC.pagination.page}}</span> de <span class="badge">{{ SCC.pagination.pages}}</span>
			de <span class="badge">{{ SCC.pagination.size}}</span> registro(s)</span>
		  </div>
	</div>



	<div role="tabpanel" class="tab-pane" id="nova-coleta-container">
		
		<div class="barra" style="vertical-align: middle; line-height: 53px;">
			<div class="form-group">
				<div class="table">
					<div class="tr">

						<div class="td">
							<div class="btn-group btn-group-grafico-desempenho" role="group" aria-label="...">

								<button type="button" class="btn" ng-class="SCC.novaColeta.agendada ? 'btn-default' : 'btn-primary'" ng-click="SCC.novaColeta.agendada = false;">Início imediato</button>
					
								<button type="button" class="btn" ng-class="SCC.novaColeta.agendada ? 'btn-primary' : 'btn-default'" ng-click="SCC.novaColeta.agendada = true;">Início agendado</button>
					
							</div>
						</div>

						<div class="td valign-middle align-right padding-right-5">
							<div class="input-group">
								<span class="input-group-addon">
									Agendamento da coleta:
								</span>
								<input ng-disabled="!SCC.novaColeta.agendada" type="date" class="form-control" width="100" ng-model="SCC.novaColeta.data"> 
								<span class="input-group-addon">
									às:
								</span>
								<input ng-disabled="!SCC.novaColeta.agendada" type="time" class="form-control" width="100" ng-model="SCC.novaColeta.horario">
							</div>

							
						</div>

						<div class="td valign-middle" style="width: 30%;">
							<div class="input-group">
								<span class="input-group-addon">
									Descrição:
								</span>
								<input type="text" class="form-control" width="230" ng-model="SCC.novaColeta.descricao">
							</div>
						</div>

						<div class="td valign-middle align-right padding-left-5">
							<button class="btn btn-primary" ng-really-message="Deseja realmente agendar a coleta com os itens selecionados?" ng-really-click="SCC.criarColeta();"><i class="glyphicon glyphicon-ok margin-right-5"></i> Criar coleta</button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="align-center" style="font-size: 11pt;">
			Selecione abaixo o(s) item(ns) a ser(em) coletado(s)...
		</div>

		<div class="full-width sc-cidades-container">
			<div ng-repeat="(cidade, cidadeObj) in SCC.oltsCidades" class="sc-cidade-item {{cidadeObj.selectedState}}">
				<div class="sc-cidade-title">
					{{cidade}}
					<button class="btn btn-sm btn-default pull-right" title="Selecionar cidade (todos OLTs)" ng-click="SCC.handleCidadeSelection(cidade);"><i class="glyphicon glyphicon-ok"></i></button>
				</div>
				<div ng-repeat="(oltIp, olt) in SCC.oltsList" ng-if="olt.cidade === cidade" class="sc-olt-item {{olt.selectedState}}">
					Placas <b>{{olt.descricao}}</b> ({{olt.ip}}):
					<button ng-if="olt.slots.length > 0" class="btn btn-sm btn-default sc-btn-olt pull-right" title="Selecionar OLT (todas placas)" ng-click="SCC.handleOltSelection(olt);"><i class="glyphicon glyphicon-ok"></i></button>
					<br>
					<div ng-if="!olt.hasOwnProperty('slots') || olt.slots.length == 0">
						<span style="color: #C00">Este OLT não possui placas</span>
					</div>
					<div class="sc-slots-container">
						<div class="sc-slot-item {{slot.selectedState}}"

						ng-repeat="slot in olt.slots" title="Placa {{slot.placa}} do OLT {{olt.descricao}}"
						
						ng-click="SCC.openSlotModal(cidade, olt, slot);"
						
						title="">{{slot.placa}}</div>
					</div>
				</div>
			</div>
		</div>
	</div>


</div>

<style>
	.sc-cidades-container {
		display: flex;
		flex-flow: column wrap;
		height: 100vh;
		margin-bottom: 20px;
	}

	.sc-cidade-item {
		margin: 10px;
		border: 2px solid #999;
		border-radius: 3px;
	}

	.sc-cidade-item.selected {
		border-color: #0C0;
	}

	.sc-cidade-item.partially-selected {
		border-color: #00C;
	}

	.sc-slots-container {
		display: flex;
		flex-wrap: wrap;
	}

	.sc-slot-item {
		height: 25px;
		width: 25px;
		border-radius: 3px;
		font-size: 9pt;
		border: 1px solid #999;
		margin: 1px;
		text-align: center;
		vertical-align: middle;
		line-height: 25px; 
	}

	.sc-slot-item.selected {
        background: #a4ff70;
		border-color: #080;
	}

	.sc-slot-item.selected:hover {
		background: #7aff2d;
	}

	.sc-slot-item:hover {
		cursor: pointer;
		background: #EEE;
		border-color:darkcyan;
	}

	.sc-olt-item {
		margin: 10px;
		padding: 3px;
		border-radius: 3px;
		border: 2px solid #888;
		min-height: 35px;
	}

	.sc-olt-item.selected {
		border-color: #0C0;
	}

	.sc-olt-item.partially-selected {
		border-color: #00C;
	}

	.sc-cidade-title {
		text-align: center;
		font-weight: bold;
		font-size: 12pt;
		background: #DDD;
		height: 32px;
		vertical-align: middle;
		line-height: 32px;
		padding: 3px 3px 0px 0px;
	}

	.sc-btn-olt {
		margin-top: 8px;
		margin-right: 3px;
	}

	.sc-cidade-item.selected .sc-cidade-title button, .sc-olt-item.selected > button {
		background: #7aff2d;
		border-color: #0C0;
	}

	.sc-cidade-item.partially-selected .sc-cidade-title button, .sc-olt-item.partially-selected > button, .sc-slot-item.partially-selected {
		background: #2ddfff;
		border-color: #00C;
	}
</style>

<div ng-include="'app/ftth/signal.collect.slot.modal.html'"></div>
<div ng-include="'app/ftth/signal.collect.repeat.modal.html'"></div>
<div ng-include="'app/ftth/signal.collect.results.modal.html'"></div>
<div ng-include="'app/ftth/signal.collect.agendamento.modal.html'"></div>
<div ng-include="'app/ftth/signal.collect.details.modal.html'"></div>