<div class="modal" id="sc-slot-modal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 700px; max-width: 700px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Selecionar portas
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">

                <table class="slot-data-table">
                    <tr>
                        <td style="padding-right: 0px;">
                            <table>
                                <tr>
                                    <td>
                                        <div class="sc-olt-slot-item">
                                            {{SCC.selectedSlotData.olt.descricao}}
                                            <br>
                                            {{SCC.selectedSlotData.olt.ip}}
                                            <br>
                                            {{SCC.selectedSlotData.olt.cidade}}
                                        </div>
                                    </td>
                                    <td style="padding-right: 0px;">
                                        <i class="glyphicon glyphicon-chevron-right" style="font-size: 14pt;"></i>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <div class="sc-olt-slot-item">
                                            Placa {{SCC.selectedSlotData.slot.placa}}
                                        </div>
                                    </td>
                                    <td>
                                        <i class="glyphicon glyphicon-chevron-right" style="font-size: 14pt;"></i>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td style="border: 2px solid #999 !important;">
                            Selecionar portas:
                            <div ng-repeat="pon in SCC.selectedSlotData.slot.pons" class="sc-pon-item align-center"
                            ng-class="SCC.selectedItems[SCC.selectedSlotData.olt.ip][SCC.selectedSlotData.slot.placa].includes(pon) ? 'selected' : ''"
                            ng-click="SCC.handlePonSelection(SCC.selectedSlotData.olt, SCC.selectedSlotData.slot, pon);">
                                {{pon}}
                            </div>
                        </td>
                        <td class="align-center">
                            <button class="btn btn-success" ng-click="SCC.selectSlot(SCC.selectedSlotData.slot);">Selecionar todas</button>
                            <br><br>
                            <button class="btn btn-danger" ng-click="SCC.unselectSlot(SCC.selectedSlotData.slot);">Selecionar nenhuma</button>
                        </td>
                    </tr>
                </table>

            </div>

			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#sc-slot-modal">Fechar</button>
			</div>
		</div>
	</div>
</div>

<style>
    .slot-data-table {
        margin: 0 auto;
    }

    .slot-data-table td {
        padding: 10px;
    }

    .sc-pon-item {
        margin: 2px 0px;
        padding: 3px 0px;
        font-weight: bold;
        border: 1px solid #888;
        border-radius: 3px;
    }

    .sc-pon-item.selected {
        background: #a4ff70;
        border-color: #080;
    }

    .sc-pon-item.selected:hover {
        background: #7aff2d;
    }

    .sc-pon-item:hover {
        background: #EEE;
        border-color: darkcyan;
        cursor: pointer;
    }

    .sc-olt-slot-item {
        background: #EEE;
        text-align: center;
        padding: 3px 5px;
        font-weight: bold;
        border: 1px solid #888;
        border-radius: 3px;
    }
</style>