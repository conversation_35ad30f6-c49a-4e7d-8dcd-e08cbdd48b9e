(function () {
    'use strict';

    angular
        .module('app')
        .controller('PatrimoniosListController', PatrimoniosListController);

    /** @ngInject */
    function PatrimoniosListController($http, API_CONFIG, CentrosDistribService, tipos) {

        var vm = this;

        vm.limit = 58;
        vm.filtro = '';
        vm.materiais = [];
        vm.selecionado = {};
        vm.sortBy = 'datacad,patrimonio';
        vm.sortOrder = 'dsc';
        vm.pagination = {
            page: 1
        };

        vm.pesquisa = {
            tipo: 'patrimonio',
            termos: ''
        }

        vm.breadcrumb = {
            tipo: '',
            termos: ''
        }        

        vm.busca_realizada = false;

        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.limpar = limpar;
        vm.alocar = alocar;

        vm.tipos = tipos;
        vm.tipo_selecionado = '';
        vm.selecionaTipo = selecionaTipo;
        vm.marcas = [];
        vm.modelos = [];
        vm.marca_selecionada = '';
        vm.selecionaMarca = selecionaMarca;
        vm.getMarcas = getMarcas;
        vm.getModelos = getModelos;
        vm.selecionaModelo = selecionaModelo;
        vm.modelo_selecionado='';

        vm.hint = null;
        vm.imprimir = API_CONFIG.url + '/materiais/imprimir?count=' +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;

        vm.navegaMenu = navegaMenu;
        vm.getAlocacaoHint = getAlocacaoHint;

        activate();

        function activate() {
            getData();
        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.pesquisa.tipo = 'patrimonio';
          vm.pesquisa.termos = '';
          getData();

          vm.tipo_selecionado = '';
          vm.marca_selecionada = '';
          vm.modelo_selecionado = '';
          vm.busca_realizada = false;

        }

        function getData() {

            var urlApi = API_CONFIG.url + '/materiais?page=' + vm.pagination.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.materiais);
                angular.copy(response.data.pagination, vm.pagination);
            });

            vm.imprimir = API_CONFIG.url + '/materiais/imprimir?count=' +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
        }

        function pageChanged() {
            getData();
        }

        function busca(termos) {

            vm.breadcrumb.tipo = vm.pesquisa.tipo;
            vm.breadcrumb.termos = vm.pesquisa.termos;

            vm.busca_realizada = true;
            vm.tipo_selecionado = '';
            vm.marca_selecionada = '';
            vm.modelo_selecionado = '';

            vm.currentPage = 1;
            vm.filtro = '&' + vm.pesquisa.tipo + '=|' + termos + '|';

            getData();

            

        }

        function alocar(material){
          vm.selecionado = material;
        }

        function selecionaTipo(tipo){
            
            if(tipo=='-TODOS-'){
                vm.tipo_selecionado = '';    
                vm.marcas=[];
                vm.modelos=[];
            } else {
                vm.tipo_selecionado = tipo;
                vm.getMarcas();
            }

            vm.marca_selecionada = '';    
            vm.modelo_selecionado = '';

            navegaMenu();
            
        }

        function selecionaMarca(marca){
            if(marca=='-TODOS-'){
                vm.marca_selecionada = '';    
                vm.modelos = [];
            } else {
                vm.marca_selecionada = marca;
                vm.getModelos();
            }    

            vm.modelo_selecionado = '';

            navegaMenu();
        }

        function selecionaModelo(modelo){
            if(modelo=='-TODOS-'){
              vm.modelo_selecionado = '';    
            } else {
                vm.modelo_selecionado = modelo;
            }    

            navegaMenu();
        }

        function getMarcas(){
          $http.get(API_CONFIG.url + '/marcas.menu?tipo=' + vm.tipo_selecionado).then(function (data, status) {
             vm.marcas = data.data;
          });
        }

        function getModelos(){
          $http.get(API_CONFIG.url + '/modelos.menu?tipo=' + vm.tipo_selecionado+'&marca='+vm.marca_selecionada).then(function (data, status) {
             vm.modelos = data.data;
          });
        }

        function navegaMenu(){
           vm.currentPage = 1;

           if(vm.tipo_selecionado==''){
            vm.filtro='';
           }

           if(vm.tipo_selecionado!=''){
              vm.filtro = '&tipo=' + vm.tipo_selecionado;  
           }

           if(vm.marca_selecionada!=''){
              vm.filtro += '&marca=' + vm.marca_selecionada;  
           }

           if(vm.modelo_selecionado!=''){
              vm.filtro += '&modelo=' + vm.modelo_selecionado;  
           }


           getData();
        }

        function getAlocacaoHint(patrimonio){
          vm.hint = null;  
          $http.get(API_CONFIG.url + '/materiais/' + patrimonio+'/alocacoes/hint').then(function (data, status) {
             vm.hint = data; 
          });
        }

    }

})();
