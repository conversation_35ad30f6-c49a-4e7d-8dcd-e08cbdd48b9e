'use strict';

angular.module('app')

    .factory('RomaneiosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/romaneios/:id', {},
            {
                getRomaneios: {
                    method: 'GET', isArray: false
                },
                insertRomaneio: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateRomaneio: {
                    method: 'PUT',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteRomaneio: {
                    method: 'DELETE',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }

            });
    });
