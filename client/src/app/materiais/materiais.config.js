'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/materiais/patrimonios', {
                templateUrl: 'app/materiais/patrimonios.list.html',
                controller: 'PatrimoniosListController',
                controllerAs: 'PLC',
                title: 'Patrimônios',
                resolve: {
                  tipos: function(PatrimoniosTiposMenuService){
                    return PatrimoniosTiposMenuService.getTipos().$promise;
                  }
                },
                authorize: ['materiais.read', 'materiais.write']
            })

            .when('/materiais/patrimonios/novo', {
                templateUrl: 'app/materiais/patrimonios.form.html',
                controller: 'PatrimoniosFormController',
                controllerAs: 'PFC',
                title: 'Patrimônios - Novo',
                resolve: {
                  material: function () {
                    return {};
                  },
                  tipos: function(PatrimoniosTiposService){
                    return PatrimoniosTiposService.getTipos().$promise;
                  },
                  fornecedores: function(FornecedoresService){
                    return FornecedoresService.getFornecedores().$promise;
                  },
                  empresas: function(EmpresasService){
                    return EmpresasService.getEmpresas().$promise;
                  },
                  alocacoes: function () {
                    return [];
                  },
                  formType: function () {

                        return 'create';

                    }

                },
                authorize: ['materiais.write']
            })

            .when('/materiais/patrimonios/:id', {
                templateUrl: 'app/materiais/patrimonios.form.html',
                controller: 'PatrimoniosFormController',
                controllerAs: 'PFC',
                title: 'Patrimônios',
                resolve: {
                  material: function($route, PatrimoniosService) {
                    return PatrimoniosService.get({id: $route.current.params.id}).$promise;
                  },
                  tipos: function(PatrimoniosTiposService){
                    return PatrimoniosTiposService.getTipos().$promise;
                  },
                  fornecedores: function(FornecedoresService){
                    return FornecedoresService.getFornecedores().$promise;
                  },
                  empresas: function(EmpresasService){
                    return EmpresasService.getEmpresas().$promise;
                  },
                  alocacoes: function($route, AlocacoesService){
                    return AlocacoesService.getAlocacoes({id: $route.current.params.id}).$promise;
                  },

                  formType: function () {

                        return 'edit';

                  }

                },
                authorize: ['materiais.read', 'materiais.write']
            })


            .when('/materiais/romaneios', {
                templateUrl: 'app/materiais/romaneios.list.html',
                controller: 'RomaneiosListController',
                controllerAs: 'RLC',
                title: 'Romaneios',
                authorize: ['materiais.read', 'materiais.write']
            })

            .when('/materiais/romaneios/novo', {
              templateUrl: 'app/materiais/romaneios.form.html',
              controller: 'RomaneiosFormController',
              controllerAs: 'RFC',
              resolve: {
                  romaneio: function () {
                    return {};
                  },
                  itens: function(){
                    return [];
                  },
                  romaneio_status: function(RomaneiosStatusService){
                    return RomaneiosStatusService.getStatus().$promise;
                  },
                  usuarios: function(UsuariosDominioService){
                    return UsuariosDominioService.getUsuarios().$promise;
                  },
                  formType: function () {

                        return 'create';

                  }
              },
              authorize: ['materiais.write']
          })

          .when('/materiais/romaneios/:id', {
            templateUrl: 'app/materiais/romaneios.form.html',
            controller: 'RomaneiosFormController',
            controllerAs: 'RFC',
            resolve: {

                  romaneio: function($route, RomaneiosService) {
                    return RomaneiosService.get({id: $route.current.params.id}).$promise;
                  },

                  romaneio_status: function(RomaneiosStatusService){
                    return RomaneiosStatusService.getStatus().$promise;
                  },

                  usuarios: function(UsuariosDominioService){
                    return UsuariosDominioService.getUsuarios().$promise;
                  },

                  formType: function () {

                        return 'edit';

                  }

            },
            authorize: ['materiais.read', 'materiais.write']
        })

    });
