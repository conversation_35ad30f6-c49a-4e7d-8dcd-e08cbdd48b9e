<div class="modal" id="frmAlocacao" tabindex="-1" role="dialog"
aria-labelledby="frmalocacaolabel" aria-hidden="true" modal="showModal" close="cancel()"
ng-controller="AlocacaoController as AC" >
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close"
           data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmalocacaolabel">
            Alocação  - Patrimônio <b>{{PLC.selecionado.patrimonio}}</b>
        </h4>
      </div>
      <div class="modal-body">
        <h4>{{PLC.selecionado.tipo}} {{PLC.selecionado.marca}} {{PLC.selecionado.modelo}} ({{PLC.selecionado.mac}})</h4>
        <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#centro"  data-toggle="tab" style="cursor: pointer;">
                Centro de Distribuição</a>
            </li>
            <li>
              <a data-target="#pop"  data-toggle="tab" style="cursor: pointer;">
                POP</a>
            </li>
            <li>
              <a data-target="#usuario"  data-toggle="tab" style="cursor: pointer;">
                Usuário</a>
            </li>
        </ul>

        <div class="tab-content">
          <div id="centro" class="tab-pane fade in active" ng-controller="CentrosDistribController as CDC">
            <h5>Selecione um Centro de Distribuição:</h5>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>Centro</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="centro in CDC.centrosdistrib">
                      <td class="vert-align text-center"><input type="radio" ng-change="CDC.seleciona('centrodistrib', PLC.selecionado, centro)" ng-model="PLC.selecionado.novolocal" value="{{centro}}"></td>
                      <td class="vert-align text-center">{{centro}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div id="pop" class="tab-pane" ng-controller="PopsController as PC">
            <h5>Selecione um POP</h5>
            <form class="form-inline" role="form">
              <div class="form-group">
                  <input class="form-control" ng-model="PC.termos" type="text">
              </div>
              <div class="form-group">
                <button class="btn btn-default filter-col" ng-click="PC.busca(PC.termos)">
                  <span class="glyphicon glyphicon-search"></span> Pesquisar
                </button>
              </div>
            </form>
            <br>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>POP</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="pop in PC.pops">
                      <td class="vert-align text-center"><input type="radio" ng-change="PC.seleciona('pop', PLC.selecionado, pop)" ng-model="PLC.selecionado.novolocal" value="{{pop}}"></td>
                      <td class="vert-align text-center">{{pop}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div id="usuario" class="tab-pane" ng-controller="UsuariosController as UC">
            <h5>Selecione um Usuário</h5>
            <form class="form-inline" role="form">
              <div class="form-group">
                  <input class="form-control" ng-model="UC.termos" type="text">
              </div>
              <div class="form-group">
                <button class="btn btn-default filter-col" ng-click="UC.busca(UC.termos)">
                  <span class="glyphicon glyphicon-search"></span> Pesquisar
                </button>
              </div>
            </form>
            <br>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>Usuário</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="usuario in UC.usuarios">
                      <td class="vert-align text-center"><input type="radio" ng-change="UC.seleciona('usuario', PLC.selecionado, usuario)" ng-model="PLC.selecionado.novolocal" value="{{usuario}}"></td>
                      <td class="vert-align text-center">{{usuario}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
       </div>
    </div>
    <div class="modal-footer">
      <p class="text-center"><b>{{PLC.selecionado.centro_distrib}}</b> <i class="glyphicon glyphicon-arrow-right"></i> <b>{{PLC.selecionado.novolocal}}</b></p>
        <button type="button" class="btn btn-default"
                data-dismiss="modal">
                    Cancelar
        </button>
        <button type="button" class="btn btn-primary" ng-click="AC.save(PLC.selecionado);" ng-disabled="frmAlocacao.$invalid" data-dismiss="modal">
            Salvar</button>
    </div>
</div>
</div>
</div>
