<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-book"></i> Materiais</li>
    <li class="active"><i class="glyphicon glyphicon-barcode"></i> Patrimônios</li>

</ol>

<div class="barra">
    <div class="form-group">

 <a href="/materiais/patrimonios/novo" type="button" class="btn btn-success btn-incluir text-center" authorize="['materiais.write']"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>  <a href="{{PLC.imprimir}}" type="button" class="btn btn-default btn-incluir text-center" target="_blank"><i class="glyphicon glyphicon-print"></i><br> Imprimir</a>

     <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                  <select class="form-control" ng-model="PLC.pesquisa.tipo" ng-init="PLC.pesquisa.tipo = 'patrimonio'">
          <option value="patrimonio">Patrimonio</option>
          <option value="mac">MAC</option>
          <option value="serial">Serial</option>
          <!--<option value="descricao">Descrição</option>-->
          <option value="centro_distrib">Alocado</option>
          <option value="marca">Marca</option>
          <option value="modelo">Modelo</option>
          <option value="fornecedor">Fornecedor</option>
                  </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="PLC.pesquisa.termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="PLC.busca(PLC.pesquisa.termos)">Pesquisar</button>
                <button class="btn btn-default filter-col" ng-click="PLC.limpar()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
        </div>
</div>
</div>  

<ul class="breadcrumb">
  <li ng-if="PLC.busca_realizada"><b>Resultados da pesquisa:</b> {{PLC.breadcrumb.tipo}} contém {{PLC.breadcrumb.termos}}</li>
  <li class="dropdown" ng-if="!PLC.busca_realizada">
      <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Tipos <span class="caret"></span></a>
              <ul class="dropdown-menu scrollable-menu">
                <li><a href="" ng-click="PLC.selecionaTipo('-TODOS-')">Todos os Tipos <span class="badge badge-default">{{PLC.tipos.total}}</span></a></li>
                <li class="divider"></li>
                <li ng-repeat="tipo in PLC.tipos.tipos"><a href="" ng-click="PLC.selecionaTipo(tipo.tipo)">{{tipo.tipo}} <span class="badge badge-default">{{tipo.total}}</span></a></li>
              </ul>
  </li>

  <li class="dropdown" ng-if="PLC.tipo_selecionado != '' && !PLC.busca_realizada">
      <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{PLC.tipo_selecionado}} <span class="caret"></span></a>
              <ul class="dropdown-menu scrollable-menu">
                <li><a href="" ng-click="PLC.selecionaMarca('-TODOS-')">Todas as Marcas <span class="badge badge-default">{{MLC.marcas.total}}</span></a></li>
                <li class="divider"></li>
                <li ng-repeat="marca in PLC.marcas.marcas"><a href="" ng-click="PLC.selecionaMarca(marca.marca)">{{marca.marca}} <span class="badge badge-default">{{marca.total}}</span></a></li>
              </ul>
  </li>

  <li class="dropdown" ng-if="PLC.marca_selecionada != '' && !PLC.busca_realizada">
      <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{PLC.marca_selecionada}} <span class="caret"></span></a>
              <ul class="dropdown-menu scrollable-menu">
                <li><a href="" ng-click="PLC.selecionaModelo('-TODOS-')">Todos os Modelos <span class="badge badge-default">{{PLC.modelos.total}}</span></a></li>
                <li class="divider"></li>
                <li ng-repeat="modelo in PLC.modelos.modelos"><a href="" ng-click="PLC.selecionaModelo(modelo.modelo)">{{modelo.modelo}} <span class="badge badge-default">{{modelo.total}}</span></a></li>
              </ul>
  </li>
  <li ng-if="PLC.modelo_selecionado != '' && !PLC.busca_realizada">{{PLC.modelo_selecionado}}</li>
</ul>



<div class="table-responsive">

           <table class="table table-striped table-hover table-bordered table-condensed" fixed-header>
              <thead>
                <tr>
                  <th class="vert-align text-center">Patrimônio</th>
                  <th class="vert-align text-center">Data Cadastro</th>
                  <!-- <th class="vert-align text-center">Descrição</th> -->
                  <th class="vert-align text-center">Tipo</th>
                  <th class="vert-align text-center">Marca</th>
                  <th class="vert-align text-center">Modelo</th>
                  <th class="vert-align text-center">MAC</th>
                  <th class="vert-align text-center">Serial</th>
                  <th class="vert-align text-center">Alocado</th>
                  <th class="vert-align text-center">Data Alocação</th>
                  <th class="vert-align text-center" authorize="['materiais.write']">Ação</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="material in PLC.materiais">
                    <td class="vert-align text-center"><a href="/materiais/patrimonios/{{material.patrimonio}}">{{material.patrimonio}}</a> <i class="glyphicon glyphicon-warning-sign" ng-if="material.verificar==1"></i> <a href="/materiais/romaneios/{{material.romaneio}}" ng-if="material.romaneio"><i class="glyphicon glyphicon-transfer" style="font-size: 13px;color:cornflowerblue;" tooltip="Romaneio: {{material.romaneio}}" tooltip-placement="right" ></i></a></td>
                    <td class="vert-align text-center">{{material.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <!--<td class="vert-align text-center">{{material.descricao}}</td>-->
                    <td class="vert-align text-center">{{material.tipo}}</td>
                    <td class="vert-align text-center">{{material.marca}}</td>
                    <td class="vert-align text-center">{{material.modelo}}</td>
                    <td class="vert-align text-center">{{material.mac}}</td>
                    <td class="vert-align text-center">{{material.serial}}</td>
                    <td class="vert-align text-center"><span ng-if="material.centro_distrib != '' && material.centro_distrib != null">{{material.centro_distrib}} ({{material.tipoalocacao}})</span></td>
                    <td class="vert-align text-center">{{material.data_alocacao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center" authorize="['materiais.write']"><button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#frmAlocacao" title="Alocar Patrimônio" ng-click="PLC.alocar(material)" ng-if="!material.romaneio"><i class="glyphicon glyphicon-share-alt"></i></button> <a href="" ng-if="material.romaneio"><i class="glyphicon glyphicon-question-sign" style="font-size: 13px;color:cornflowerblue;" uib-tooltip="Este equipamento está incluído em um romaneio({{material.romaneio}}) e não pode ser alocado manualmente" tooltip-placement="left"></i></a></td>
                </tr>
              </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="PLC.pagination.size" ng-model="PLC.pagination.page" ng-change="PLC.pageChanged()" items-per-page="PLC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Página <span class="badge">{{ PLC.pagination.page}}</span> de  <span class="badge">{{ PLC.pagination.pages}}</span> de <span class="badge">{{ PLC.pagination.size}}</span> registro(s)</span>
            </div>
          </div>
<div ng-include="'app/materiais/alocacao.form.html'"></div>
