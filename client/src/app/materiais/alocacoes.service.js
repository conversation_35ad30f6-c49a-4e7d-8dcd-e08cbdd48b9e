'use strict';

angular.module('app')

    .factory('AlocacoesService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/materiais/:id/alocacoes', {},
            {
                getAlocacoes: {
                    method: 'GET', isArray: true
                },
                insertAlocacao: {
                    method: 'POST',
                    params: {id: '@patrimonio'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateAlocacao: {
                    method: 'PUT',
                    params: {
                        id: '@patrimonio'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteAlocacao: {
                    method: 'DELETE',
                    params: {
                        id: '@patrimonio'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }

            });
    });
