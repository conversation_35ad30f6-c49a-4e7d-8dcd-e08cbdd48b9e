'use strict';

angular.module('app')

    .factory('PatrimoniosTiposService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/tipos', {},
            {
                getTipos: {
                    method: 'GET', isArray: true
                }
            });
    })

    .factory('PatrimoniosTiposMenuService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/tipos.menu', {},
            {
                getTipos: {
                    method: 'GET', isArray: false
                }
            });
    });
