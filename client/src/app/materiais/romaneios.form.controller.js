(function () {
    'use strict';

    angular
        .module('app')
        .controller('RomaneiosFormController', RomaneiosFormController);

    /** @ngInject */
    function RomaneiosFormController($rootScope, RomaneiosService, $location, 
      toastr, formType, romaneio_status, romaneio, usuarios, focus, $http, API_CONFIG, $route, $filter) {

        var vm = this;

        vm.save = save;

        vm.tipo = 'patrimonio';
        vm.termos = '';
        vm.resultado = {
          'texto' : ''
        };  

        vm.busca = busca;
        vm.adicionar = adicionar;
        vm.status = status;
        vm.excluir = excluir; 

        vm.romaneio_status = romaneio_status;
        vm.usuarios = usuarios;

        vm.dateFormat = "DD/MM/YYYY HH:mm:ss";
        //vm.usuarios = $filter('filter')(usuarios, '!'+$rootScope.operador.username, true);
        
        vm.origem_temp = {novolocal : "", tipoalocacao: ""};
        vm.destino_temp = {novolocal : "", tipoalocacao: ""};
        
        vm.seleciona_origem = seleciona_origem;
        vm.seleciona_destino = seleciona_destino;
        vm.typeahead = typeahead;

        activate();

        function activate() {
          
          if (formType === 'create') {
            vm.romaneio = {"status":"Aberto", "usuario":$rootScope.operador.username};
            vm.itens = [];
            vm.inserindo = true;
          } else {
            vm.romaneio = romaneio.dados[0];
            vm.inserindo = false;
            vm.itens = vm.romaneio.itens;
          }
          

        }


        function save() {
          if(vm.inserindo){
            vm.romaneio.usuario = $rootScope.operador.username;
            RomaneiosService.save(vm.romaneio, function (response) {
              if (response.status === 'OK') {
                toastr.success("Romaneio adicionado", "Romaneio adicionado com sucesso!");
                $location.path('/materiais/romaneios/' + response.id);
              } else {
                toastr.error("Ocorreu um erro", response.dados);
              }
            });
          } else {
            RomaneiosService.updateRomaneio(vm.romaneio, function (response) {
              if (response.status === 'OK') {
                toastr.success('Romaneio atualizado', 'Romaneio atualizado com sucesso!');
              } else {
                toastr.error("Ocorreu um erro", response.dados);
              }
            });
          }
        }

        function seleciona_origem(){
          vm.romaneio.origem = vm.origem_temp.novolocal;
          vm.romaneio.tipoorigem = vm.origem_temp.tipoalocacao;
        } 

        function seleciona_destino() {
           vm.romaneio.destino = vm.destino_temp.novolocal;
           vm.romaneio.tipodestino = vm.destino_temp.tipoalocacao;
        }

        function busca(){
          if(vm.tipo=='patrimonio'){
            var s = 'patrimonio=' + vm.termos;
          } else {
            var s = 'serial=' + vm.termos;
          } 
          $http.get(API_CONFIG.url + '/materiais?' + s, {}).then(function(response){
            if(response.data.rows.length > 0){
              vm.resultado = response.data.rows[0];
              vm.resultado.texto = response.data.rows[0].patrimonio + ' - '+ response.data.rows[0].tipo + ' ' + 
                response.data.rows[0].marca + ' '+response.data.rows[0].modelo;  
              vm.resultado.localizado = true;  
              focus('adicionar');
            } else {
              vm.resultado = {
                "localizado" : false,
                "texto" : '-- EQUIPAMENTO NÃO LOCALIZADO --'
              };
              vm.termos = ''
              focus('termos');
            }
            
          });

        }

        function adicionar(){
          if(vm.resultado.localizado){
            vm.resultado.usuario = $rootScope.operador.username;
            $http.post(API_CONFIG.url + '/romaneios/' + vm.romaneio.id + '/itens', vm.resultado).then(function(response){
               if (response.data.status === 'Erro') {
                 toastr.error("Ocorreu um erro", response.data.mensagem); 
               } else {
                 RomaneiosService.get({id: $route.current.params.id}, function (response) {
                  vm.romaneio = response.dados[0];
                  vm.itens = vm.romaneio.itens;
                 }); 
                 toastr.success('Ítem adicionado', 'Ítem adicionado com sucesso!');
                 

               } 
            });
          }  
          limpa(); 
          focus('termos');
        }

        function status(id, status){

          $http.put(API_CONFIG.url + '/romaneios/' + vm.romaneio.id + '/itens/'+id, {"status":status, "usuario":$rootScope.operador.username}).then(function(response){
               if (response.data.status === 'Erro') {
                 toastr.error("Ocorreu um erro", response.data.mensagem); 
               } else {
                 RomaneiosService.get({id: $route.current.params.id}, function (response) {
                  vm.romaneio = response.dados[0];
                  vm.itens = vm.romaneio.itens;
                 }); 
                 toastr.success('Ítem atualizado', 'Ítem atualizado com sucesso!');
               } 
            });
        }

        function excluir(id){
          $http.delete(API_CONFIG.url + '/romaneios/' + vm.romaneio.id + '/itens/'+id, {}).then(function(response){
               if (response.data.status === 'Erro') {
                 toastr.error("Ocorreu um erro", response.data.mensagem); 
               } else {
                 RomaneiosService.get({id: $route.current.params.id}, function (response) {
                  vm.romaneio = response.dados[0];
                  vm.itens = vm.romaneio.itens;
                 }); 
                 toastr.success('Ítem excluído', 'Ítem excluído com sucesso!');
               } 
            });
        }

        function limpa(){
          vm.termos = '';
          vm.resultado = {
            'texto' : ''
          }; 
        }

/*
        function typeahead(busca){
          return $http.jsonp(API_CONFIG.url + "/materiais/typeahead?q="+busca).then(function(response){
          //return $http.jsonp("http://gd.geobytes.com/AutoCompleteCity?callback=JSON_CALLBACK &filter=US&q="+busca).then(function(response){
            return response.data;
        });
          */

        function typeahead(busca){
          return $http.get(API_CONFIG.url + '/materiais/typeahead', {
            params: {
              q: busca
            }
          })
       };
    }

})();
