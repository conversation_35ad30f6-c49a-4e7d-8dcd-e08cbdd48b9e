<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-book"></i> Materiais</li>
  <li class="active"><i class="glyphicon glyphicon-transfer"></i> Romaneios</li>
</ol>

<div class="barra">
  <div class="form-group">

    <a href="/materiais/romaneios/novo" type="button" class="btn btn-success btn-incluir text-center"
      authorize="['materiais.write']"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>

    <div class="form-group pull-right">
      <form class="form-inline" role="form">
        <div class="form-group">
          <select class="form-control" ng-model="RLC.tipo" ng-init="RLC.tipo = 'id'">
            <option value="id">ID</option>
          </select>
        </div>
        <div class="form-group">
          <input class="form-control" type="text" ng-model="RLC.termos">
        </div><!-- form group [search] -->
        <div class="form-group">
          <button class="btn btn-default filter-col" ng-click="RLC.busca(RLC.termos)">
            <span class="glyphicon glyphicon-search"></span> Pesquisar
          </button>
          <button class="btn btn-default filter-col" ng-click="RLC.limpar()">
            <span class="glyphicon glyphicon-refresh"></span> Limpar
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="table-responsive">

  <table class="table table-striped table-hover table-bordered table-condensed">
    <thead>
      <tr>
        <th class="vert-align text-center">ID</th>
        <th class="vert-align text-center">Data Cadastro</th>
        <th class="vert-align text-center">Criado por</th>
        <th class="vert-align text-center">Usuário (destino)</th>
        <th class="vert-align text-center">Local Origem</th>
        <th class="vert-align text-center">Local Destino</th>
        <th class="vert-align text-center">Status</th>
        <th class="vert-align text-center">Ítens</th>
        <th class="vert-align text-center">Observação</th>
        <th class="vert-align text-center">Ação</th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="romaneio in RLC.romaneios">
        <td class="vert-align text-center"><a href="/materiais/romaneios/{{romaneio.id}}">{{romaneio.id}}</a></td>
        <td class="vert-align text-center">{{romaneio.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
        <td class="vert-align text-center">{{romaneio.usuario}}</td>
        <td class="vert-align text-center">{{romaneio.usuario_destino}}</td>
        <td class="vert-align text-center">{{romaneio.origem}} ({{romaneio.tipoorigem}})</td>
        <td class="vert-align text-center">{{romaneio.destino}} ({{romaneio.tipodestino}})</td>
        <td class="vert-align text-center">{{romaneio.status}}</td>
        <td class="vert-align text-center">{{romaneio.itens}} <a href=""><i class="glyphicon glyphicon-question-sign"
              style="font-size: 13px;color:cornflowerblue;" uib-tooltip-html="'{{romaneio.itens_detalhes}}'"
              tooltip-placement="bottom"></i></a></td>
        <td class="vert-align text-center">{{romaneio.observacao}}</td>
        <td class="vert-align text-center">
          <a href="https://noc.telemidia.net.br:4000/romaneios/{{romaneio.id}}/relatorio" class="btn btn-sm btn-default"
            target="_blank" title="Imprimir"><i class="glyphicon glyphicon-print"></i></a>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="text-center">
    <uib-pagination total-items="RLC.pagination.size" ng-model="RLC.pagination.page" ng-change="RLC.pageChanged()"
      items-per-page="20" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true"
      first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
  </div>
  <div class="text-center">
    Página <span class="badge">{{RLC.pagination.page}}</span> de <span class="badge">{{RLC.pagination.pages}}</span> de
    <span class="badge">{{RLC.pagination.size}}</span> registro(s)</span>
  </div>
</div>