(function () {
    'use strict';

    angular
        .module('app')
        .controller('RomaneiosListController', RomaneiosListController);

    /** @ngInject */
    function RomaneiosListController($http, API_CONFIG, UsuariosDominioService) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.romaneios = [];
        vm.selecionado = {};
        vm.sortBy = 'id, data';
        vm.sortOrder = 'dsc';
        vm.pagination = {
            page: 1
        };

        vm.tipo='id';
        vm.termos = '';
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.limpar = limpar;
        vm.getUserDetail = getUserDetail;

        vm.usuario = {
            'nome':'',
            'email':'',
            'usuario':''
        };

        activate();

        function activate() {
            getData();
        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.tipo = 'id';
          vm.termos = '';
          getData();

        }

        function getData() {

            var urlApi = API_CONFIG.url + '/romaneios?page=' + vm.pagination.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.romaneios);
                angular.copy(response.data.pagination, vm.pagination);
            });
        }

        function pageChanged() {
            getData();
        }

        function busca(termos) {

            vm.currentPage = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            getData();

        }

        function getUserDetail(user){
            vm.usuario = {
                'nome':'',
                'email':'',
                'usuario':''
            };
            UsuariosDominioService.postUsuarios({"username": user}, function (response) {
                vm.usuario = response;
            });
        }


    }

})();
