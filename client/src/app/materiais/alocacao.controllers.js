(function () {
    'use strict';

    angular
        .module('app')
        .controller('AlocacaoController', AlocacaoController);

    /** @ngInject */
    function AlocacaoController($rootScope, toastr, AlocacoesService) {

        var vm = this;

        vm.save = save;

        function save(material) {

          material.usuario = $rootScope.operador.username;

          AlocacoesService.insertAlocacao(material, function (response) {
                if (response.status === 'OK') {
                    toastr.success("Alocação executada", "Alocação executada com sucesso!");
                    material.centro_distrib = material.novolocal;
                    material.data_alocacao = Date.now();
                } else {
                    toastr.error("Ocorreu um erro", response.mensagem);
                }
          });

        }

    }

})();
