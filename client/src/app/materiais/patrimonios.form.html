<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-book"></i> Materiais</li>
    <li class="active"><a href="/materiais/patrimonios"><i class="glyphicon glyphicon-barcode"></i> Patrimônios</a></li>
    <li ng-if="PFC.inserindo">Novo Patrimônio</li>
    <li ng-if="!PFC.inserindo">Patrimônio: <b>{{PFC.material.patrimonio}}</b></li>
</ol>

<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
    <li>
      <a data-target="#alocacoes"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-share"></i> Alocações </a>
    </li>
</ul>

<div class="tab-content">
<div id="dados" class="tab-pane fade in active">
<div class="barra">
    <div class="form-group">
    <a href="/materiais/patrimonios/novo" type="button" class="btn btn-success btn-incluir text-center" ng-if="!PFC.inserindo" authorize="['materiais.write']"><i class="glyphicon glyphicon-plus-sign"></i><br> Incluir</a>

    <button ng-click="PFC.save()" class="btn btn-primary btn-info btn-incluir text-center" type="submit" ng-disabled="frmMaterial.$invalid" authorize="['materiais.write']">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
    </button>



    </div>
</div>
  <div class="row top-buffer">
    <form class="form-horizontal" name="frmMaterial">
      <!--
      <div class="form-group" ng-class="{ 'has-error': frmMaterial.descricao.$invalid && frmMaterial.descricao.$dirty }">
        <label class="col-md-2 control-label">Descrição</label>
          <div class="col-md-4">
            <input id="descricao" name="descricao" class="form-control input-md"
              ng-model="PFC.material.descricao" type="text" required>
          </div>
      </div>
      -->

<div class="form-group alert alert-warning" ng-if="PFC.material.verificar==1">
<p>Este patrimônio foi cadastrado a partir de um equipamento sem etiqueta, ilegível ou o operador não possuia tal informação. </p>
<p>Será necessário uma verificação inloco para confirmação. Caso possua em mãos o patrimônio correto, favor digitá-lo abaixo.</p>
<p><strong>Motivo: </strong> {{PFC.material.motivo}}</p>

</div>


<div class="form-group" ng-if="PFC.material.verificar==1" ng-class="{ 'has-error': frmMaterial.patrimonio.$invalid && frmMaterial.material.$dirty }">
  <label class="col-md-2 control-label">Patrimônio</label>
    <div class="col-md-2">
      <input id="patrimonio" name="patrimonio" class="form-control input-md"
        ng-model="PFC.material.patrimonio" type="text" required authorize-edit="['materiais.write']">
    </div>
</div>
<div class="form-group" ng-class="{ 'has-error': frmMaterial.tipo.$invalid && frmMaterial.tipo.$dirty }">
    <label class="col-md-2 control-label" for="tipo"> Tipo</label>
    <div class="col-md-3">
        <ol class="nya-bs-select form-control"
              title="Tipo"
              id="tipo"
              name="tipo"
              ng-model="PFC.material.tipo"
              ng-change="PFC.getMarcas()"
              data-live-search="true"
              data-size="8"
              required
          >
          <li nya-bs-option="tipo in PFC.tipos">
                  <a>
                      {{ tipo }}
                      <span class="glyphicon glyphicon-ok check-mark"></span>
                  </a>
              </li>
          </ol>
    </div>
    <!--
    <div class="col-md-3" authorize="['materiais.read']">
      <input id="tipo" class="form-control input-md"
        ng-model="PFC.material.tipo" type="text" disabled>
    </div>
    -->
  </div>

      <div class="form-group" ng-class="{ 'has-error': frmMaterial.marca.$invalid && frmMaterial.marca.$dirty }" ng-if="PFC.material.tipo != undefined">
    <label class="col-md-2 control-label" for="marca"> Marca</label>
    <div class="col-md-3">
        <ol class="nya-bs-select form-control"
              title="Marca"
              id="marca"
              name="marca"
              ng-model="PFC.material.marca"
              ng-change="PFC.getModelos()"
              data-live-search="true"
              data-size="8"
              required
          >
          <li nya-bs-option="marca in PFC.marcas">
                  <a>
                      {{ marca }}
                      <span class="glyphicon glyphicon-ok check-mark"></span>
                  </a>
              </li>
          </ol>
    </div>
    <!--
    <div class="col-md-3" authorize="['materiais.read']">
          <input id="marca" class="form-control input-md"
        ng-model="PFC.material.marca" type="text" disabled>
    </div>
    -->
  </div>
  <div class="form-group" ng-class="{ 'has-error': frmMaterial.modelo.$invalid && frmMaterial.modelo.$dirty }" ng-if="PFC.material.marca != undefined">
<label class="col-md-2 control-label" for="modelo"> Modelo</label>
<div class="col-md-3">
    <ol class="nya-bs-select form-control"
          title="Modelo"
          id="modelo"
          name="modelo"
          ng-model="PFC.material.modelo"
          ng-change="PFC.changeModelo()"
          data-live-search="true"
          data-size="8"
          required
      >
      <li nya-bs-option="modelo in PFC.modelos" data-value="modelo.modelo">
              <a>
                  {{ modelo.modelo }}
                  <span class="glyphicon glyphicon-ok check-mark"></span>
              </a>
          </li>
      </ol>
 </div>
 <!--
    <div class="col-md-3" authorize="['materiais.read']">
      <input id="modelo" class="form-control input-md"
        ng-model="PFC.material.modelo" type="text" disabled>
    </div>
 -->
</div>

<div class="form-group">
  <label class="col-md-2 control-label">Nota Fiscal</label>
    <div class="col-md-2">
      <input id="nf" name="nf" class="form-control input-md"
        ng-model="PFC.material.nf" type="text" authorize-edit="['materiais.write']">
    </div>
</div>


<div class="form-group">
  <label class="col-md-2 control-label">Data Nota Fiscal</label>
    <div class="col-md-2">
      <div class="input-group">
      <input class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="PFC.material.nf_data" is-open="PFC.opened" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="nf_data" type="text" ng-required="false">
      <span class="input-group-btn">
        <button type="button" class="btn btn-default" ng-click="PFC.open()"><i class="glyphicon glyphicon-calendar"></i></button>
      </span>
    </div>
    </div>
</div>


<div class="form-group">
<label class="col-md-2 control-label" for="fornecedor"> Empresa Compradora</label>
<div class="col-md-3">
  <ol class="nya-bs-select form-control"
        title="Empresa"
        id="fornecedor"
        ng-model="PFC.material.empresa"
        data-live-search="true"
        data-size="8"
    >
    <li nya-bs-option="empresa in PFC.empresas" data-value="empresa.id" >
            <a>
                {{ empresa.empresa }}
                <span class="glyphicon glyphicon-ok check-mark"></span>
            </a>
        </li>
    </ol>
  </div>
  <!--
   <div class="col-md-3" authorize="['materiais.read']">
    <input id="fornecedor" class="form-control input-md"
        ng-model="PFC.material.fornecedor" type="text" disabled>
    </div>
    -->
</div>

<div class="form-group">
<label class="col-md-2 control-label" for="fornecedor"> Fornecedor</label>
<div class="col-md-3">
  <ol class="nya-bs-select form-control"
        title="Fornecedor"
        id="fornecedor"
        ng-model="PFC.material.fornecedor"
        data-live-search="true"
        data-size="8"

    >
    <li nya-bs-option="fornecedor in PFC.fornecedores">
            <a>
                {{ fornecedor }}
                <span class="glyphicon glyphicon-ok check-mark"></span>
            </a>
        </li>
    </ol>
  </div>
  <!--
   <div class="col-md-3" authorize="['materiais.read']">
    <input id="fornecedor" class="form-control input-md"
        ng-model="PFC.material.fornecedor" type="text" disabled>
    </div>
    -->
</div>
<div class="form-group" ng-class="{ 'has-error': frmMaterial.mac.$invalid && frmMaterial.mac.$dirty }" ng-if="PFC.mac_enabled==1">

  <label class="col-md-2 control-label">MAC</label>
    <div class="col-md-2">
      <input style=""
        class="form-control input-md" id="mac" name="mac"
        placeholder=""
        authorize-edit="['materiais.write']"
        ng-required="PFC.mac_enabled==1"
        ng-model="PFC.material.mac" mask="ww:ww:ww:ww:ww:ww"
        ng-pattern="/^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/"
        type="text">
    </div>
</div>

<div class="form-group" ng-if="PFC.serial_enabled!=0" ng-class="{ 'has-error': frmMaterial.serial.$invalid && frmMaterial.serial.$dirty }">
  <label class="col-md-2 control-label">Serial</label>
    <div class="col-md-2">
      <input id="serial" name="serial" class="form-control input-md"
        ng-model="PFC.material.serial" type="text" ng-required="PFC.serial_enabled==1" authorize-edit="['materiais.write']">
    </div>
</div>

<div class="form-group">
  <label class="col-md-2 control-label">Observação</label>
    <div class="col-md-3">
      <textarea id="observacao" class="form-control"
         ng-model="PFC.material.observacao"
         style="min-height: 200px;" authorize-edit="['materiais.write']"></textarea>
    </div>
</div>



  <hr />
  <div class="form-group top-buffer">
    <div class="col-md-2"></div>
    <div class="col-md-6">
      <button ng-click="PFC.save()" class="btn btn-md btn-primary btn-info" type="submit"
              ng-disabled="frmMaterial.$invalid" ng-if="operador.gravacao=='1'">
          <i class="glyphicon glyphicon-check" ></i> Salvar
      </button>
    </div>
  </div>
</form>

</div>
</div>
  <div id="alocacoes" class="tab-pane">
    <table class="table table-striped table-hover table-bordered table-condensed">
      <thead>
        <tr>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Usuário</th>
          <th class="vert-align text-center">Alocação Anterior</th>
          <th></th>
          <th class="vert-align text-center">Alocação Atual</th>
          <th class="vert-align text-center">Romaneio</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="a in PFC.alocacoes">
          <td class="vert-align text-center">{{a.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center">{{a.usuario}}</td>
          <td class="vert-align text-center">{{a.alocacao_anterior}} ({{a.tipoalocacao_anterior}})</td>
          <td class="vert-align text-center"><i class="glyphicon glyphicon-arrow-right"></i></td>
          <td class="vert-align text-center">{{a.alocacao_atual}} ({{a.tipoalocacao_atual}})</td>
          <td class="vert-align text-center"><a href="/romaneios/{{a.romaneio_id}}" ng-if="a.romaneio_id != ''">{{a.romaneio_id}}</a></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
