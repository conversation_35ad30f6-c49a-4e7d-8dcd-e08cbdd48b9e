(function () {
    'use strict';

    angular
        .module('app')
        .controller('CentrosDistribController', CentrosDistribController);

    /** @ngInject */
    function CentrosDistribController(CentrosDistribService) {

        var vm = this;

        vm.centrosdistrib = [];
        vm.seleciona = seleciona;

        activate();

        function activate() {

          var Centros = CentrosDistribService.getCentrosDistrib();
          Centros.$promise.then(function (data) {
            vm.centrosdistrib = data;
          });
        }

        function seleciona(tipo, destino, valor){
          destino.novolocal = valor;
          destino.tipoalocacao = tipo;
        }

    }

})();
