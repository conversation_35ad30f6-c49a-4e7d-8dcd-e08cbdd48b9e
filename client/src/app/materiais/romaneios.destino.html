<div class="modal" id="frmRomaneioDestino" tabindex="-1" role="dialog"
aria-labelledby="frmromaneiodestinolabel" aria-hidden="true" modal="showModal" close="cancel()"
>
 <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close"
           data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmromaneiodestinolabel">
            Selecione o destino</b>
        </h4>
      </div>
      <div class="modal-body">
        <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#centro_destino"  data-toggle="tab" style="cursor: pointer;">
                Centro de Distribuição</a>
            </li>
            <li>
              <a data-target="#pop_destino"  data-toggle="tab" style="cursor: pointer;">
                POP</a>
            </li>
            <li>
              <a data-target="#usuario_destino"  data-toggle="tab" style="cursor: pointer;">
                Usuário</a>
            </li>
        </ul>

        <div class="tab-content">
          <div id="centro_destino" class="tab-pane active" ng-controller="CentrosDistribController as CDC">
            <h5>Selecione um Centro de Distribuição:</h5>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>Centro</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="centro in CDC.centrosdistrib">
                      <td class="vert-align text-center"><input type="radio" ng-change="CDC.seleciona('centrodistrib', RFC.destino_temp, centro)" ng-model="RFC.destino_temp.novolocal" value="{{centro}}"></td>
                      <td class="vert-align text-center">{{centro}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div id="pop_destino" class="tab-pane" ng-controller="PopsController as PC">
            <h5>Selecione um POP</h5>
            <form class="form-inline" role="form">
              <div class="form-group">
                  <input class="form-control" ng-model="PC.termos" type="text">
              </div>
              <div class="form-group">
                <button class="btn btn-default filter-col" ng-click="PC.busca(PC.termos)">
                  <span class="glyphicon glyphicon-search"></span> Pesquisar
                </button>
              </div>
            </form>
            <br>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>POP</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="pop in PC.pops">
                      <td class="vert-align text-center"><input type="radio" ng-change="PC.seleciona('pop', RFC.destino_temp, pop)" ng-model="RFC.destino_temp.novolocal" value="{{pop}}"></td>
                      <td class="vert-align text-center">{{pop}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="usuario_destino" class="tab-pane" ng-controller="UsuariosController as UC">
            <h5>Selecione um Usuário</h5>
            <form class="form-inline" role="form">
              <div class="form-group">
                  <input class="form-control" ng-model="UC.termos" type="text">
              </div>
              <div class="form-group">
                <button class="btn btn-default filter-col" ng-click="UC.busca(UC.termos)">
                  <span class="glyphicon glyphicon-search"></span> Pesquisar
                </button>
              </div>
            </form>
            <br>
            <div class="pre-scrollable">
              <div class="table-responsive">
                <table class="table table-condensed table-bordered table-hover">
                  <thead>
                    <tr>
                      <th></th>
                      <th>Usuário</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="usuario in UC.usuarios">
                      <td class="vert-align text-center"><input type="radio" ng-change="UC.seleciona('usuario', RFC.destino_temp, usuario)" ng-model="RFC.destino_temp.novolocal" value="{{usuario}}"></td>
                      <td class="vert-align text-center">{{usuario}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
       </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default"
                data-dismiss="modal">
                    Cancelar
        </button>
        <button type="button" class="btn btn-primary" ng-click="RFC.seleciona_destino()" ng-disabled="frmRomaneioDestino.$invalid" data-dismiss="modal">
            Selecionar</button>
    </div>
</div>
</div>
</div>
