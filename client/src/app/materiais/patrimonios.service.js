'use strict';

angular.module('app')

    .factory('PatrimoniosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/materiais/:id', {},
            {
                getMateriais: {
                    method: 'GET', isArray: false
                },
                insertMaterial: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateMaterial: {
                    method: 'PUT',
                    params: {
                        id: '@patrimonio_original'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteMaterial: {
                    method: 'DELETE',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }

            });
    });
