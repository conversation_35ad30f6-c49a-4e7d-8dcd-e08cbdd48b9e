(function () {
    'use strict';

    angular
        .module('app')
        .controller('UsuariosController', UsuariosController);

    /** @ngInject */
    function UsuariosController(UsuariosService) {

        var vm = this;

        vm.usuarios = [];

        vm.busca = busca;
        vm.seleciona = seleciona;

        function busca(termos) {

          var Usuarios = UsuariosService.getUsuarios({nome : termos});
          Usuarios.$promise.then(function (data) {
            vm.usuarios = data;
          });
        }

        function seleciona(tipo, destino, valor){
          destino.novolocal = valor;
          destino.tipoalocacao = tipo;
        }
    }

})();
