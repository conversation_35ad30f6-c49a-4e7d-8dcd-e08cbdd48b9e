(function () {
    'use strict';

    angular
        .module('app')
        .controller('PatrimoniosFormController', PatrimoniosFormController);

    /** @ngInject */
    function PatrimoniosFormController($rootScope, $location, toastr, formType, material,
      PatrimoniosService, tipos, fornecedores, empresas, alocacoes, $http, API_CONFIG, $filter,
      LogAlteracaoService) {

        var vm = this;

        vm.save = save;
        vm.marcas = [];
        vm.modelos = [];
        vm.empresas = [];
        vm.getMarcas = getMarcas;
        vm.getModelos = getModelos;
        vm.changeModelo = changeModelo;
        vm.mac_enabled = 0;
        vm.serial_enabled = 0;
        vm.opened = false;
        vm.open = open;

        activate();

        function activate() {
          var data = new Date();
          if (formType === 'create') {
            vm.material = {};
            vm.inserindo = true;
            vm.material.nf_data = new Date();
          } else {
            vm.material = material.dados[0];
            vm.material.patrimonio_original = vm.material.patrimonio;
            vm.marcas = material.marcas;
            vm.modelos = material.modelos;
            vm.inserindo = false;
            vm.alocacoes = alocacoes;
            vm.changeModelo();
            vm.valores_anteriores = angular.copy(material.dados[0]);
            vm.material.nf_data = new Date(vm.material.nf_data);
            //data.setDate(vm.material.nf_data);

          }

          vm.tipos = tipos;
          vm.fornecedores = fornecedores;
          vm.empresas = empresas;

        }


        function open() {
          vm.opened = true;
        };

        function save() {
          if(vm.inserindo){
            vm.material.usuario = $rootScope.operador.username;
            PatrimoniosService.save(vm.material, function (response) {
              if (response.status === 'OK') {
                toastr.success("Patrimônio adicionado", "Patrimônio adicionado com sucesso!");
                $location.path('/materiais/patrimonios/' + response.id);
              } else {
                toastr.error("Ocorreu um erro", response.dados);
              }
            });
          } else {
            PatrimoniosService.updateMaterial(vm.material, function (response) {
              if (response.status === 'OK') {
                toastr.success('Patrimônio atualizado', 'Patrimônio atualizado com sucesso!');
                // Remove : do campo MAC
                //vm.material.mac_original = vm.material.mac;
                vm.material_copia = {};
                angular.copy(vm.material, vm.material_copia);
                vm.material_copia.mac = vm.material_copia.mac.replace(/:/g , "");

                //Insere log da alteração
                var log = {
                      idevento: null,
                      username: $rootScope.operador.username,
                      tabela: 'materiais',
                      nomeregistro: vm.valores_anteriores.patrimonio,
                      campos: findDiff(vm.material_copia, vm.valores_anteriores)
                };

                LogAlteracaoService.insertLog(log);
                angular.copy(vm.material_copia, vm.valores_anteriores);
                //vm.material.mac = vm.material.mac_original;
                $location.path('/materiais/patrimonios/' + vm.material.patrimonio);
              } else {
                toastr.error("Ocorreu um erro", response.dados);
              }
            });
          }
        }

        function getMarcas(){
          $http.get(API_CONFIG.url + '/marcas?tipo=' + vm.material.tipo).then(function (response, status) {
             vm.marcas = response.data;
             vm.modelos = [];
             vm.material.marca=null;
             vm.material.modelo=null;
          });
        }

        function getModelos(){
          $http.get(API_CONFIG.url + '/modelos?tipo=' + vm.material.tipo+'&marca='+vm.material.marca).then(function (response, status) {
             vm.modelos = response.data;
             vm.material.modelo=null;
          });
        }

        function changeModelo(){

          var result = $filter('filter')(vm.modelos, {modelo: vm.material.modelo}, true);
          if(result[0] !== undefined) {
            vm.mac_enabled = result[0].mac;
            if(result[0].mac==1){
              vm.serial_enabled=2;
            } else {
              vm.serial_enabled = result[0].serial;
            }

          }
        }

        function findDiff(original, edited) {
            var campos = [];
            var diff = {};
            var registro = {};
            for (var key in original) {
                if (original[key] !== edited[key]) {
                    registro = {};
                    diff[key] = edited[key];
                    registro.campo = key;
                    registro.valor_anterior = diff[key];
                    registro.valor_atual = original[key];
                    campos.push(registro);
                }
            }
            return campos;
        }

    }

})();
