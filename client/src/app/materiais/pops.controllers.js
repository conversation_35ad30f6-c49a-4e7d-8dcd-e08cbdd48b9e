(function () {
    'use strict';

    angular
        .module('app')
        .controller('PopsController', PopsController);

    /** @ngInject */
    function PopsController(PopsJSONService) {

        var vm = this;

        vm.pops = [];

        vm.busca = busca;
        vm.seleciona = seleciona;

        function busca(termos) {

          var Pops = PopsJSONService.getPops({nome : termos});
          Pops.$promise.then(function (data) {
            vm.pops = data;
          });
        }

        function seleciona(tipo, destino, valor){
          destino.novolocal = valor;
          destino.tipoalocacao = tipo;
        }
    }

})();
