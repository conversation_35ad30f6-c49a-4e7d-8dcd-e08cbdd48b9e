<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-book"></i> Materiais</li>
  <li class="active"><a href="/materiais/romaneios"><i class="glyphicon glyphicon-transfer"></i> Romaneios</a></li>
  <li ng-if="RFC.inserindo">Novo Romaneio</li>
  <li ng-if="!RFC.inserindo">Romaneio: <b>{{RFC.romaneio.id}}</b></li>
</ol>


<ul class="nav nav-tabs">
  <li class="active">
    <a data-target="#dados" data-toggle="tab" style="cursor: pointer;">
      <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
  </li>
  <li ng-if="!RFC.inserindo">
    <a data-target="#itens" data-toggle="tab" style="cursor: pointer;">
      <i class="glyphicon glyphicon-share"></i> Ítens <span class="badge badge-default">{{RFC.itens.length}}</span></a>
  </li>
</ul>

<div class="tab-content">
  <div id="dados" class="tab-pane active">
    <div class="barra">
      <div class="form-group">
        <a href="/materiais/romaneios/novo" type="button" class="btn btn-success btn-incluir text-center"
          ng-if="!RFC.inserindo" authorize="['materiais.write']"><i class="glyphicon glyphicon-plus-sign"></i><br>
          Incluir</a>

        <button ng-click="RFC.save()" class="btn btn-primary btn-info btn-incluir text-center" type="submit"
          ng-disabled="frmRomaneio.$invalid" authorize="['materiais.write']">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
        </button>

        <a href="https://noc.telemidia.net.br:4000/romaneios/{{RFC.romaneio.id}}/relatorio"
          class="btn btn-default btn-incluir text-center" target="_blank" title="Imprimir" ng-if="!RFC.inserindo"><i
            class="glyphicon glyphicon-print"></i><br> Imprimir</a>
      </div>
    </div>
    <div class="row top-buffer">
      <form class="form-horizontal" name="frmRomaneio">
        <div class="form-group">
          <label class="col-md-2 control-label">Número</label>
          <div class="col-md-1">
            <input id="id" name="id" class="form-control input-md" ng-model="RFC.romaneio.id" type="text"
              disabled="disabled">
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-2 control-label">Criado por</label>
          <div class="col-md-2">
            <input id="usuario" name="usuario" class="form-control input-md" ng-model="RFC.romaneio.usuario" type="text"
              disabled="disabled">
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-2 control-label">Data</label>
          <div class="col-md-2">
            <input id="data" name="data" class="form-control input-md" ng-model="RFC.romaneio.data" type="text"
              disabled="disabled" mo-date-input="{{RFC.dateFormat}}" />
          </div>
        </div>
        <div class="form-group" ng-class="{ 'has-error': frmRomaneio.origem.$invalid && frmRomaneio.origem.$dirty }">
          <label class="col-md-2 control-label">Local Origem</label>
          <div class="col-md-4">
            <input id="origem" name="origem" class="form-control input-md" ng-model="RFC.romaneio.origem" type="text"
              required disabled="disabled">
          </div>
          <div class="col-md-1"><button class="btn btn-warning" data-toggle="modal" data-target="#frmRomaneioOrigem"
              title="Selecionar origem" ng-if="operador.username == RFC.romaneio.usuario"><i
                class="glyphicon glyphicon-pencil"></i></button>
          </div>
        </div>

        <div class="form-group" ng-class="{ 'has-error': frmRomaneio.destino.$invalid && frmRomaneio.destino.$dirty }">
          <label class="col-md-2 control-label">Local Destino</label>
          <div class="col-md-4">
            <input id="destino" name="destino" class="form-control input-md" ng-model="RFC.romaneio.destino" type="text"
              required disabled="disabled">
          </div>
          <div class="col-md-1"><button class="btn btn-warning" data-toggle="modal" data-target="#frmRomaneioDestino"
              title="Selecionar destino" ng-if="operador.username == RFC.romaneio.usuario"><i
                class="glyphicon glyphicon-pencil"></i></button>
          </div>
        </div>

        <div class="form-group"
          ng-class="{ 'has-error': frmRomaneio.usuario_destino.$invalid && frmRomaneio.usuario_destino.$dirty }">
          <label class="col-md-2 control-label">Usuário Destino</label>
          <div class="col-md-4">
            <ol class="nya-bs-select form-control" title="Selecione um usuário" id="usuario_destino"
              name="usuario_destino" ng-model="RFC.romaneio.usuario_destino" data-live-search="true" data-size="8"
              disabled="(operador.username != RFC.romaneio.usuario)" required>
              <li nya-bs-option="usuario in RFC.usuarios">
                <a>
                  {{ usuario }}
                  <span class="glyphicon glyphicon-ok check-mark"></span>
                </a>
              </li>
            </ol>
          </div>
        </div>


        <div class="form-group" ng-class="{ 'has-error': frmRomaneio.status.$invalid && frmRomaneio.status.$dirty }">
          <label class="col-md-2 control-label" for="marca"> Status</label>
          <div class="col-md-6">
            <ol class="nya-bs-select form-control" title="Status" id="status" name="status"
              ng-model="RFC.romaneio.status" data-live-search="true" data-size="8"
              disabled="(operador.username != RFC.romaneio.usuario && operador.username != RFC.romaneio.usuario_destino)"
              required>
              <li nya-bs-option="status in RFC.romaneio_status">
                <a>
                  {{ status }}
                  <span class="glyphicon glyphicon-ok check-mark"></span>
                </a>
              </li>
            </ol>
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-2 control-label" for="modelo"> Observações</label>
          <div class="col-md-6">
            <textarea style="min-height: 200px;" id="observacao" class="form-control input-md"
              ng-model="RFC.romaneio.observacao"
              ng-disabled="(operador.username != RFC.romaneio.usuario && operador.username != RFC.romaneio.usuario_destino)"></textarea>

          </div>
        </div>

      </form>

    </div>
  </div>

  <div id="itens" class="tab-pane">
    <br>

    <div class="form-group pull-left" ng-if="operador.username == RFC.romaneio.usuario" authorize="materiais.write">
      <form class="form-inline ng-pristine ng-valid" role="form">
        <div class="form-group">
          <select style="" class="form-control ng-pristine ng-valid ng-touched" ng-model="RFC.tipo"
            ng-init="RFC.tipo = 'patrimonio'">
            <option value="patrimonio">Patrimonio</option>
            <option value="serial">Serial</option>
          </select>
        </div> <!-- form group [rows] -->
        <div class="form-group">
          <input type="text" id="termos" class="form-control ng-pristine ng-untouched ng-valid" ng-model="RFC.termos"
            ng-keyup="$event.keyCode == 13 &amp;&amp; RFC.busca()" type="text" placeholder="Digite um valor">
        </div><!-- form group [search] -->
        <button class="btn btn-default" ng-click="RFC.busca()">
          <span class="glyphicon glyphicon-search"></span>
        </button>
        <input type="text" class="form-control" ng-model="RFC.resultado.texto" style="width:400px;" disabled>
        <button class="btn btn-success filter-col" id="adicionar" ng-click="RFC.adicionar()"
          ng-show="RFC.resultado.localizado">

          <span class="glyphicon glyphicon-plus"></span>
        </button>
      </form>
    </div>
    <table class="table table-striped table-hover table-bordered table-condensed">
      <thead>
        <tr>
          <th class="vert-align text-center">Status</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Tipo</th>
          <th class="vert-align text-center">Marca</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">Conferido por</th>

          <th class="vert-align text-center" ng-if="operador.username==RFC.romaneio.usuario">Ação</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="a in RFC.romaneio.itens">
          <td class="vert-align text-center">
            <div style="" class="btn-group dropdown" dropdown="">
              <button id="split-button" type="button" class="btn btn-sm"
                ng-class="{'btn-warning' : a.status=='', 'btn-success': a.status=='Conferido', 'btn-danger' : a.status=='Defeito' || a.status=='Extravio' || a.status=='Faltando'}"
                ng-click="RFC.status(a.id, 'Conferido')"
                ng-disabled="RFC.romaneio.usuario_destino != operador.username"><span
                  ng-if="a.status==''">Não-Conferido</span><span ng-if="a.status!=''">{{a.status}}</span></button>
              <button aria-expanded="false" aria-haspopup="true" type="button" class="btn btn-sm dropdown-toggle"
                data-toggle="dropdown"
                ng-class="{'btn-warning' : a.status=='', 'btn-success': a.status=='Conferido', 'btn-danger' : a.status=='Defeito' || a.status=='Extravio' || a.status=='Faltando'}"
                ng-disabled="RFC.romaneio.usuario_destino != operador.username">
                <span class="caret"></span>
                <span class="sr-only">Botão</span>
              </button>
              <ul class="dropdown-menu" role="menu" aria-labelledby="split-button">
                <li role="menuitem"><a href="" ng-click="RFC.status(a.id, 'Conferido')">Conferido</a></li>
                <li role="menuitem"><a href="" ng-click="RFC.status(a.id, '')">Não-Conferido</a></li>
                <li class="divider"></li>
                <li role="menuitem"><a href="" ng-click="RFC.status(a.id, 'Faltando')">Faltando</a></li>
                <li role="menuitem"><a href="" ng-click="RFC.status(a.id, 'Defeito')">Defeito</a></li>
                <li role="menuitem"><a href="" ng-click="RFC.status(a.id, 'Extravio')">Extravio</a></li>
              </ul>
            </div>
          </td>
          <td class="vert-align text-center">{{a.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center"><a href="/materiais/patrimonios/{{a.patrimonio}}">{{a.patrimonio}}</a></td>
          <td class="vert-align text-center">{{a.tipo}}</td>
          <td class="vert-align text-center">{{a.marca}}</td>
          <td class="vert-align text-center">{{a.modelo}}</td>
          <td class="vert-align text-center">{{a.mac}}</td>
          <td class="vert-align text-center">{{a.serial}}</td>
          <td class="vert-align text-center"><span ng-if="a.usuario_status !=''">{{a.usuario_status}} em
              {{a.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</span></td>
          <td class="vert-align text-center" ng-if="operador.username==RFC.romaneio.usuario"><a href=""
              class="btn btn-danger btn-sm" title="Excluir ítem"
              ng-really-message="Tem certeza que deseja excluir este ítem ?" ng-really-click="RFC.excluir(a.id)"
              item="a"><i class="glyphicon glyphicon-remove"></i></a></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div ng-include="'app/materiais/romaneios.origem.html'"></div>
<div ng-include="'app/materiais/romaneios.destino.html'"></div>