
<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><a href="/notas">Notas</a></li>
    <li ng-show="inserindo">Nova Nota</li>
    <li ng-show="!inserindo"># {{::nota.id}}</li>
</ol>



<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
</ul>

<div class="tab-content">
  <div id="dados" class="tab-pane fade in active">
      <div class="barra">
    <div class="form-group">
    <button ng-click="save(nota)" class="btn btn-primary btn-info btn-incluir text-center" type="submit" ng-disabled="frmNota.$invalid" ng-if="eventoSelecionado.id != ''">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
      </button>    
    </div>
</div>   
    <div class="row top-buffer">
        <form class="form-horizontal" name="frmEvento">
      <div class="form-group">
           <label class="col-md-2 control-label" for="tipo"> Tipo</label>
            <div class="col-md-2">
                <select class="form-control" ng-model="nota.idnotatipo"
                     ng-options="item.id as item.tipo_nota for item in tipos" ng-disabled="!inserindo" required></select>
            </div>    
      </div> 
     <div class="form-group">
    <label class="col-md-2 control-label" for="observacao">Nota</label>
    <div class="col-md-6">
      <textarea id="observacao" class="form-control input-md" ng-model="nota.nota" ng-disabled="!inserindo" required style="min-height: 200px;"></textarea>
    </div>
   </div>
   </form>            
   </div>    
  </div>      
</div>    

