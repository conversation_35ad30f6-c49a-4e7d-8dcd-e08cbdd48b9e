'use strict';

angular.module('app')

    .controller('NotasListCtrl', ['$scope', '$rootScope', 'dadosAPI', function ($scope, $rootScope, dadosAPI) {

        $scope.notas = dadosAPI[0].dados;

        $scope.$on('eventoSelecionado', function () {
          

        });

    }])

    .controller('NotasFormCtrl', ['$scope', '$rootScope', '$cookies', '$location', 'dadosAPI', 'formType', 'NotasService', 'EventosService', 'toaster', '$localStorage', function ($scope, $rootScope, $cookies, $location, dadosAPI, formType, NotasService, EventosService, toaster, $localStorage) {

        if (formType === 'create') {

            $scope.tipos = dadosAPI[0];
            $scope.usuarios = dadosAPI[1];
            $scope.evento = dadosAPI[2].dados[0];

            $scope.inserindo = true;

            $scope.save = function (nota) {

                if ($rootScope.eventoSelecionado === '') {
                    nota.idevento = null;
                } else {
                    nota.idevento = $rootScope.eventoSelecionado.id;
                }

                nota.usuario_operador = $rootScope.operador.username;
                nota.usuario_tecnico = 'reginaldo';


                NotasService.save(nota, function (response) {
                    if (response.status === 'OK') {
                        //Se for nota de fechamento de evento, fecha e sai do evento
                        if (nota.idnotatipo === 1) {

                            $scope.evento.status = 'Fechado';

                            EventosService.updateEvento($scope.evento, function (response) {
                                if (response.status === 'OK') {
                                    toaster.pop('success', "Evento atualizado", "O evento foi fechado com sucesso!");
                                    delete $localStorage.evento;
                                    $rootScope.eventoSelecionado = {id: ''};
                                    $cookies.put('eventoSelecionado.id', '');
                                    $cookies.put('eventoSelecionado.descricao', '');
                                    $location.path("/eventos");
                                } else {
                                    toaster.pop('error', "Ocorreu um erro", response.dados);
                                }
                            });


                        }

                        toaster.pop('success', "Nota adicionada", "Nota adicionada com sucesso!");
                        $location.path('/notas/' + response.id);
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }
                });

            };
        } else {
            $scope.nota = dadosAPI[0].dados[0];
            $scope.tipos = dadosAPI[1];
            $scope.usuarios = dadosAPI[2];

            $scope.inserindo = false;

            $scope.save = function (nota) {

                NotasService.updateNota(nota, function (response) {
                    if (response.status === 'OK') {
                        toaster.pop('success', "Nota atualizada", "Nota atualizada com sucesso!");
                    } else {
                        toaster.pop('error', "Ocorreu um erro", response.dados);
                    }
                });
            };
        }


    }]);
