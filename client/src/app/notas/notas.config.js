'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/notas', {
                templateUrl: 'app/notas/notas.list.html',
                controller: 'NotasListCtrl',
                title: 'Notas',
                resolve: {
                    dadosAPI: function ($q, NotasService, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }
                        //$rootScope.eventoSelecionado = 1;
                        var Notas = NotasService.getNotas({evento: $rootScope.eventoSelecionado.id});
                        return $q.all([Notas.$promise]);
                    }
                },
                authorize: ['eventos.read', 'eventos.write']
            })

            .when('/eventos/:evento/notas/novo', {
                templateUrl: 'app/notas/notas.form.html',
                controller: 'NotasFormCtrl',
                title: 'Notas',
                resolve: {
                    dadosAPI: function ($q, NotasService, NotasTiposService, UsuariosService, EventosService, $route) {

                        var Tipos = NotasTiposService.getNotasTipos();
                        var Usuarios = UsuariosService.getUsuarios();
                        var Evento = EventosService.get({id: $route.current.params.evento});
                        return $q.all([Tipos.$promise, Usuarios.$promise, Evento.$promise]);
                    },

                    formType: function () {

                        return 'create';

                    }

                },
                authorize: ['eventos.write']
            })

            .when('/notas/:id', {
                templateUrl: 'app/notas/notas.form.html',
                controller: 'NotasFormCtrl',
                title: 'Notas',
                resolve: {
                    dadosAPI: function ($q, NotasService, NotasTiposService, UsuariosService, $route) {
                        var Nota = NotasService.get({id: $route.current.params.id});
                        var Tipos = NotasTiposService.getNotasTipos();
                        var Usuarios = UsuariosService.getUsuarios();

                        return $q.all([Nota.$promise, Tipos.$promise, Usuarios.$promise]);

                    },

                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['eventos.read', 'eventos.write']
            });

    });


