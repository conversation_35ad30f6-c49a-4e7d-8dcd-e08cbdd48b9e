'use strict';

angular.module('app')

    .factory('NotasService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/notas/:id', {},
            {
                getNotas: {
                    method: 'GET',
                    isArray: false
                },
                insertNota: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateNota: {
                    method: 'PUT',
                    params: {id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },

                deleteNota: {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('NotasTiposService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/notas/tipos', {},
            {
                getNotasTipos: {method: 'GET', isArray: true}
            });
    });