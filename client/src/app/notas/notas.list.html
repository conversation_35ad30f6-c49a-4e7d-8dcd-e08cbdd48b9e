<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-tags"></i> Notas</li>

</ol>

<div class="barra">
    <div class="form-group">
    
    <a href="/eventos/{{::eventoSelecionado.id}}/notas/novo" type="button" class="btn btn-success btn-incluir text-center" ng-if="(eventoSelecionado.id != '' && currentPath == 'notas')"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>
        
    <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                  <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar...">
                </div>
              </form>
        </div>
</div>
</div>  


 <div class="table-responsive">

<span class="counter pull-right"></span>
            <table class="table table-striped table-hover table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center">Evento</th>
                  <th class="vert-align text-center">Data Cadastro</th>
                    <th class="vert-align text-center" style="width: 390px;">Nota</th>
                  <th class="vert-align text-center">Tipo</th>
                    <th class="vert-align text-center">Operador</th>
                  <!--<th class="vert-align text-center">Técnico</th>-->
                  <th class="vert-align text-center">Ação</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="nota in notas | filter:filtro " ng-class="{'success': nota.evento_id == eventoSelecionado.id}">
                    <td class="vert-align text-center"><a href="/eventos/{{::nota.evento_id}}"># {{::nota.evento_id}}</a></td>
                    <td class="vert-align text-center">{{::nota.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{::nota.nota}}</td>
                    <td class="vert-align text-center">{{::nota.tipo_nota}}</td>
                    <td class="vert-align text-center">{{::nota.usuario_operador}}</td>
                    <!--<td class="vert-align text-center">{{nota.usuario_tecnico}}</td>-->
                   
                    <td class="vert-align text-center"><a href="/notas/{{::nota.id}}" class="btn btn-default btn-sm" title="Visualizar Nota"><i class="glyphicon glyphicon-search"></i></a></td>
                </tr>
                  
              </tbody>
            </table>
          </div>