(function () {
  'use strict';

  angular
    .module('app')
    .config(config);

  /** @ngInject */
  function config($provide, $logProvider, toastr, cfpLoadingBarProvider, $httpProvider,
    nyaBsConfigProvider, $locationProvider, $sceDelegateProvider) {


    $locationProvider.html5Mode(true);
    // Enable log
    $logProvider.debugEnabled(true);
    

    $httpProvider.defaults.useXDomain = true;

    $sceDelegateProvider.resourceUrlWhitelist([
      'self',
      'http://pocos-net.com.br/**',
      'https://www.pocos-net.com.br/**'
    ]);

    delete $httpProvider.defaults.headers.common['X-Requested-With'];

    $httpProvider.interceptors.push('httpRequestInterceptor');

    cfpLoadingBarProvider.includeSpinner = true;

    // Set options third-party lib
    toastr.options.timeOut = 3000;
    toastr.options.positionClass = 'toast-top-right';
    toastr.options.preventDuplicates = true;
    toastr.options.progressBar = true;

    var configObj = {
      defaultNoneSelection: 'Nada selecionado',
      noSearchResult: 'NENHUM RESULTADO ENCONTRADO',
      numberItemSelected: '%d item(s) selecionado'
    };

    nyaBsConfigProvider.setLocalizedText('pt-BR', configObj);
    nyaBsConfigProvider.useLocale('pt-BR');

    $provide.decorator('$exceptionHandler', function ($delegate, $injector, $window, SCRIPT_ERROR_MSG, LOGGING_URL) {
      return function (exception, cause) {
        // Using injector to get around cyclic dependencies
        $injector.get('$rootScope').$broadcast('error', SCRIPT_ERROR_MSG);

        console.log(
          angular.toJson({
            message: exception.stack || exception.message || exception || '',
            source: cause || '',
            url: $window.location.href
          }));
        /*
        // Bypassing angular's http abstraction to avoid infinite exception loops
        $injector.get('$httpBackend')('POST', LOGGING_URL, angular.toJson({
                message: exception.stack || exception.message || exception || '',
                source: cause || '',
                url: $window.location.href
        }), angular.noop, { 'content-type': 'application/json' });
        */
        $delegate(exception, cause);
      };
    });

  }

})();