'use strict';

angular.module('app')


    .controller('EventosFormCtrl', ['$scope', '$location', 'dadosAPI', 'formType',
    'EventosService', 'EventosCategoriasService', 'TarefasService', 'EventosTarefasService',
    'toaster', '$rootScope', '$localStorage', '$http', 'API_CONFIG', 'limitToFilter',
      function ($scope, $location, dadosAPI, formType, EventosService,
        EventosCategoriasService, TarefasService, EventosTarefasService, toaster, $rootScope, $localStorage, $http, API_CONFIG, limitToFilter) {

/*
        $scope.loadCat = function(tipo) {
          var Categorias = EventosCategoriasService.get({tipo : tipo});
          Categorias.$promise.then(function (data) {
            $scope.categorias = data.dados;
          });
        };

        
        $scope.loadTarefas = function(evento) {
          var Tarefas = EventosTarefasService.get({id : evento});
          Tarefas.$promise.then(function (data) {
            $scope.tarefas = data.dados;
          });
        };
*/

        $scope.opened = false;

        $scope.open = function() {
            $scope.opened = true;
        };

        $scope.loadPops = function(val) {
            return $http.get(API_CONFIG.url + "/pops.json?nome="+val).then(function(response){
              return limitToFilter(response.data, 15);
            });
        };

        $scope.loadHosts = function(val) {
            return $http.get(API_CONFIG.url + "/hosts.json?q="+val).then(function(response){
              return limitToFilter(response.data, 15);
            });
        };

        $scope.loadServicos = function(val) {
            return $http.get(API_CONFIG.url + "/servicos.json?q="+val).then(function(response){
              return limitToFilter(response.data, 15);
            });
        };

        $scope.abrangenciaChange = function(){
            $scope.evento.alerta.pops = [];
            $scope.evento.alerta.hosts = [];
            $scope.evento.alerta.servicos = [];
        }

        if (formType === 'create') {

            $scope.tipos = dadosAPI[0].dados;
            $scope.status = dadosAPI[1].dados;
            $scope.evento = {status: 'Aberto', username: $rootScope.operador.username, datacad: Date.now(), exibir_alerta: 0};
            $scope.evento.alerta = {'pops': [], 'hosts': [], 'servicos': [], 'abrangencia': 'global'};
            $scope.inserindo = true;
     
            $scope.save = function (evento) {

                EventosService.save(evento, function (response) {
                    if (response.status === 'OK') {
                        toaster.pop('success', "Evento adicionado", "Evento adicionado com sucesso!");
                        $location.path('/eventos/' + response.id);
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }
                });

            };


        } else {

            $scope.trabalhar = function (evento) {
          
                //Só permite trabalhar em um evento se não estiver fechado
                if (evento.status === "Fechado") {
                    toaster.pop('error', "Não permitido", "Não é possível trabalhar em um evento com status 'Fechado'.");
                } else {

                    $rootScope.eventoSelecionado = evento;
                    $localStorage.evento = evento;
                    $rootScope.$broadcast("eventoSelecionado");
                }
            };

            $scope.inserindo = false;

            $scope.evento = dadosAPI[0].dados[0];
            $scope.evento.alerta = dadosAPI[0].alerta;
            $scope.tipos = dadosAPI[1].dados;
            $scope.status = dadosAPI[2].dados;
            $scope.historicos = dadosAPI[3].historico;
            $scope.hosts = dadosAPI[3].hosts;
            $scope.servicos = dadosAPI[3].hostservicos;

            //$scope.loadCat($scope.evento.tipo);
            //$scope.loadTarefas($scope.evento.id);

            $scope.save = function (evento) {

                EventosService.updateEvento(evento, function (response) {
                    if (response.status === 'OK') {
                        toaster.pop('success', "Evento atualizado", "Evento atualizado com sucesso!");
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }
                });
            };

        }


    }]);
