<ol class="breadcrumb">
  <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><a href="/eventos"><i class="glyphicon glyphicon-calendar"></i> Eventos</a></li>
  <li class="active">{{::evento.descricao}}</li>
</ol>

<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
    <li><a data-target="#hosts" data-toggle="tab" style="cursor: pointer;"><i class="glyphicon glyphicon-cog"></i> Hosts Trabalhados</a></li>
     <li><a data-toggle="tab" data-target="#historico" style="cursor: pointer;"><i class="glyphicon glyphicon-random"></i>  Histórico</a></li>
</ul>

<div class="tab-content">
  <div id="dados" class="tab-pane fade in active">
      <div class="barra">
    <div class="form-group">
    <button ng-click="save(evento)" class="btn btn-primary btn-info btn-incluir text-center" type="submit" ng-disabled="frmEvento.$invalid" authorize="['eventos.write']">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
      </button>    
     <button ng-click="trabalhar(evento)" class="btn btn-primary btn-success btn-incluir text-center" type="submit" authorize="['eventos.write']">
          <span class="glyphicon glyphicon-play-circle"></span><br>Trabalhar
      </button>  
    </div>
</div>    
    <div class="row top-buffer">
        <form class="form-horizontal" name="frmEvento">
      <div class="form-group">
           <label class="col-md-2 control-label" for="data"> Data</label>
            <div class="col-md-2">
                <input id="data" name="data" type="text" class="form-control input-md" disabled value="{{evento.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">
            </div>    
      </div>   
      <div class="form-group">
           <label class="col-md-2 control-label" for="descricao"> Descrição</label>
            <div class="col-md-4">
                <input id="descricao" name="descricao" type="text" class="form-control input-md"
                       ng-model="evento.descricao" ng-disabled="!inserindo">
            </div>    
      </div> 
      <div class="form-group">
           <label class="col-md-2 control-label" for="usuario"> Operador</label>
            <div class="col-md-2">
                <input id="usuario" name="usuario" type="text" class="form-control input-md"
                       ng-model="evento.username" disabled>
            </div>    
      </div>     
      <div class="form-group">
           <label class="col-md-2 control-label" for="tipo"> Tipo</label>
            <div class="col-md-2">
                <select ng-model="evento.tipo" ng-options="obj for obj in tipos track by obj" class="form-control" name="tipo" required ng-disabled="!inserindo"></select>
            </div>    
      </div> 
      <div class="form-group">
           <label class="col-md-2 control-label" for="status"> Status</label>
            <div class="col-md-2">
                <select ng-model="evento.status" ng-options="obj for obj in status track by obj" class="form-control" name="status" required disabled></select>
            </div>    
      </div> 
      <hr>
      <div class="form-group">
        <label class="col-md-2 control-label" for="alerta"> Alerta</label>
         <div class="col-md-3">
             <select ng-model="evento.exibir_alerta" class="form-control">
                 <option ng-value="0">NÃO EXIBIR alerta para o HelpDesk</option>
                 <option ng-value="1">EXIBIR alerta para o HelpDesk</option>
             </select>    
         </div>    
       </div> 
       <div class="form-group" ng-if="evento.exibir_alerta==1">
            <label class="col-md-2 control-label" for="alerta"> Título do alerta</label>
             <div class="col-md-6">
                    <input id="titulo" name="titulo" type="text" class="form-control input-md"
                    ng-model="evento.alerta.titulo">
             </div>    
        </div> 
        <div class="form-group" ng-if="evento.exibir_alerta==1">
                <label class="col-md-2 control-label" for="mensagem">Mensagem do alerta</label>
                <div class="col-md-6">
                  <textarea id="mensagem" name="mensagem" class="form-control input-md" ng-model="evento.alerta.mensagem"
                  style="min-height: 200px;"></textarea>
                </div>
              </div>
               <div class="form-group" ng-if="evento.exibir_alerta==1">
                    <div class="form-inline">
            <label class="col-md-2 control-label" for="abrangencia"> Abrangência</label>
             <div class="col-md-5">
                    <select ng-model="evento.alerta.abrangencia" ng-change="abrangenciaChange()" class="form-control">
                            <option value="global">Global</option>
                            <option value="elementos">POPs/Hosts/Serviços</option>
                    </select>  

             </div>  
             </div>  
        </div> 

        <div class="form-group" ng-if="evento.exibir_alerta==1 && evento.alerta.abrangencia!=='global'">
            <label class="col-md-2 control-label" for="elementos"> POPs</label>
             <div class="col-md-6">
                <tags-input 
                    ng-model="evento.alerta.pops" 
                    replace-spaces-with-dashes="false"
                    add-from-autocomplete-only="true"
                    placeholder="Adicionar POP">
                    <auto-complete source="loadPops($query)"></auto-complete>
                 </tags-input>
                </div>    
        </div> 
        <div class="form-group" ng-if="evento.exibir_alerta==1 && evento.alerta.abrangencia!=='global'">
                <label class="col-md-2 control-label" for="elementos"> Hosts</label>
                 <div class="col-md-6">
                        <tags-input 
                        ng-model="evento.alerta.hosts" 
                        replace-spaces-with-dashes="false"
                        add-from-autocomplete-only="true"
                        placeholder="Adicionar Host">
                         <auto-complete source="loadHosts($query)"></auto-complete>
                      </tags-input>
                    </div>    
            </div> 
            <div class="form-group" ng-if="evento.exibir_alerta==1 && evento.alerta.abrangencia!=='global'">
                    <label class="col-md-2 control-label" for="elementos"> Serviços</label>
                     <div class="col-md-6">
                            <tags-input 
                            ng-model="evento.alerta.servicos" 
                            add-from-autocomplete-only="true"
                            replace-spaces-with-dashes="false"
                            placeholder="Adicionar Serviço">
                             <auto-complete source="loadServicos($query)"></auto-complete>
                          </tags-input>
                        </div>    
                </div> 
                <br>
                 <br>
                 <br><br>
                 <br>
                 <br><br>
                 <br><br>
    </form>        
    </div>    
    </div>  
    
    <div id="hosts" class="tab-pane fade in">
        <table class="table table-striped table-hover table-bordered">
       <thead>
         <th class="vert-align text-center">Hosts</th>
       </thead>
       <tbody>
         <tr ng-repeat="host in hosts">
             <td class="vert-align text-center" ng-bind-html="host"></td>
         </tr>
     </tbody>
   </table>
        
        <table class="table table-striped table-hover table-bordered">
     <thead>
       <th class="vert-align text-center">Serviços</th>
     </thead>
     <tbody>
       <tr ng-repeat="servico in servicos">
           <td class="vert-align text-center" ng-bind-html="servico"></td>
       </tr>
   </tbody>
 </table>
    </div>
    
    <div id="historico" class="tab-pane fade in">
        <div class="form-group">
    <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar no histórico...">
</div>
<span class="counter pull-right"></span>

<div class="panel panel-default" ng-repeat="historico in historicos | filter:filtro ">
    <div class="panel-heading"><span ng-bind-html="historico.titulo"></span> <span class="label label-info">Há <span am-time-ago="historico.data"></span></span></div>
  <div class="panel-body">
  <div class="table" ng-show="historico.tabela=='log_alteracao'">
      <table class="table-condensed">
        <thead>
            <tr>
                <th class="vert-align text-center">Parâmetro</th>
                <th class="vert-align text-center">Valor Anterior</th>
                <th class="vert-align text-center">Valor Atual</th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="item in historico.conteudo">
                <td class="vert-align text-center">{{::item.parametro}}</td>
                <td class="vert-align text-center">{{::item.valor_anterior}}</td>
                <td class="vert-align text-center">{{::item.valor_atual}}</td>
            </tr>
        </tbody>
      </table>
    </div>

    <div class="table" ng-show="historico.tabela=='calculos_ptp'">
        <table class="table table-condensed" style="width:350px">
         <tbody>
            <tr>
                <td class="vert-align text-right">Distância:</td>
                <td class="vert-align text-left">{{::historico.subtitulo.distancia}}</td>
            </tr>
            <tr>
                <td class="vert-align text-right">Perdas:</td>
                <td class="vert-align text-left">{{::historico.subtitulo.perdas}}</td>
            </tr>
            <tr>
                <td class="vert-align text-right">Frequência:</td>
                <td class="vert-align text-left">{{::historico.subtitulo.frequencia}}</td>
            </tr>
        </tbody>
        </table>

      <table class="table table-condensed" style="width:500px">
            <tbody>
               <thead>
                <tr>
                    <th class="vert-align text-right">Host 1</th>
                    <th class="vert-align text-right"></th>
                    <th class="vert-align text-left"></th>
                    <th class="vert-align text-left">Host 2</th>
                </tr>
                </thead>
                <tr>
                    <td class="text-right"><b>Host</b></td>
                    <td class="text-left">{{::historico.conteudo[0].host}}</td>
                    <td class="text-right"><b>Host</b></td>
                    <td class="text-left">{{::historico.conteudo[1].host}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Potência</b></td>
                    <td class="text-left">{{::historico.conteudo[0].potencia}}</td>
                    <td class="text-right"><b>Potência</b></td>
                    <td class="text-left">{{::historico.conteudo[1].potencia}}</td>
                </tr>

                <tr>
                    <td class="text-right"><b>Ganho</b></td>
                    <td class="text-left">{{::historico.conteudo[0].ganho}}</td>
                    <td class="text-right"><b>Ganho</b></td>
                    <td class="text-left">{{::historico.conteudo[1].ganho}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Elevação</b></td>
                    <td class="text-left">{{::historico.conteudo[0].elevacao}}</td>
                    <td class="text-right"><b>Elevação</b></td>
                    <td class="text-left">{{::historico.conteudo[1].elevacao}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Altura (antena)</b></td>
                    <td class="text-left">{{::historico.conteudo[0].altura}}</td>
                    <td class="text-right"><b>Altura (antena)</b></td>
                    <td class="text-left">{{::historico.conteudo[1].altura}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Sinal (calculado)</b></td>
                    <td class="text-left">{{::historico.conteudo[0].sinalcalc}}</td>
                    <td class="text-right"><b>Sinal (calculado)</b></td>
                    <td class="text-left">{{::historico.conteudo[1].sinalcalc}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Sinal (real)</b></td>
                    <td class="text-left">{{::historico.conteudo[0].sinalreal}}</td>
                    <td class="text-right"><b>Sinal (real)</b></td>
                    <td class="text-left">{{::historico.conteudo[1].sinalreal}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Tilt (calculado)</b></td>
                    <td class="text-left">{{::historico.conteudo[0].tiltcalc}}</td>
                    <td class="text-right"><b>Tilt (calculado)</b></td>
                    <td class="text-left">{{::historico.conteudo[1].tiltcalc}}</td>
                </tr>
                <tr>
                    <td class="text-right"><b>Tilt (real)</b></td>
                    <td class="text-left">{{::historico.conteudo[0].tiltreal}}</td>
                    <td class="text-right"><b>Tilt (real)</b></td>
                    <td class="text-left">{{::historico.conteudo[1].tiltreal}}</td>
                </tr>

            </tbody>
        </table>
    </div>


    <div class="table" ng-show="historico.tabela=='hosts_parametros'">
      <table class="table table-condensed" style="width:350px">
            <tbody>
               <thead>
                <tr>
                    <th class="vert-align text-right">Anterior</th>
                    <th class="vert-align text-right"></th>
                    <th class="vert-align text-left"></th>
                    <th class="vert-align text-left">Atual</th>

                </tr>
                </thead>
                <tr ng-class="{'success': historico.conteudo[0].hardware != historico.conteudo[1].hardware}">
                    <td class="text-right"><b>Hardware</b></td>
                    <td class="text-left">{{::historico.conteudo[0].hardware}}</td>
                    <td class="text-right"><b>Hardware</b></td>
                    <td class="text-left">{{::historico.conteudo[1].hardware}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].ip != historico.conteudo[1].ip}">
                    <td class="text-right"><b>IP</b></td>
                    <td class="text-left">{{::historico.conteudo[0].ip}}</td>
                    <td class="text-right"><b>IP</b></td>
                    <td class="text-left">{{::historico.conteudo[1].ip}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].mac != historico.conteudo[1].mac}">
                    <td class="text-right"><b>MAC</b></td>
                    <td class="text-left">{{::historico.conteudo[0].mac}}</td>
                    <td class="text-right"><b>MAC</b></td>
                    <td class="text-left">{{::historico.conteudo[1].mac}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].interface != historico.conteudo[1].interface}">
                    <td class="text-right"><b>Interface</b></td>
                    <td class="text-left">{{::historico.conteudo[0].interface}}</td>
                    <td class="text-right"><b>Interface</b></td>
                    <td class="text-left">{{::historico.conteudo[1].interface}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].modooperacao != historico.conteudo[1].modooperacao}">
                    <td class="text-right"><b>Modo Operação</b></td>
                    <td class="text-left">{{::historico.conteudo[0].modooperacao}}</td>
                    <td class="text-right"><b>Modo Operação</b></td>
                    <td class="text-left">{{::historico.conteudo[1].modooperacao}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].local != historico.conteudo[1].local}">
                    <td class="text-right"><b>Local</b></td>
                    <td class="text-left">{{::historico.conteudo[0].local}}</td>
                    <td class="text-right"><b>Local</b></td>
                    <td class="text-left">{{::historico.conteudo[1].local}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].observacao != historico.conteudo[1].observacao}">
                    <td class="text-right"><b>Observação</b></td>
                    <td class="text-left">{{::historico.conteudo[0].observacao}}</td>
                    <td class="text-right"><b>Observação</b></td>
                    <td class="text-left">{{::historico.conteudo[1].observacao}}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table" ng-show="historico.tabela=='hosts_radio_parametros'">

      <table class="table table-condensed" style="width:350px">
            <tbody>
               <thead>
                <tr>
                    <th class="vert-align text-right">Anterior</th>
                    <th class="vert-align text-right"></th>
                    <th class="vert-align text-left"></th>
                    <th class="vert-align text-left">Atual</th>

                </tr>
                </thead>
                <tr ng-class="{'success': historico.conteudo[0].ssid != historico.conteudo[1].ssid}">
                    <td class="text-right"><b>SSID</b></td>
                    <td class="text-left">{{::historico.conteudo[0].ssid}}</td>
                    <td class="text-right"><b>SSID</b></td>
                    <td class="text-left">{{::historico.conteudo[1].ssid}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].mac != historico.conteudo[1].mac}">
                    <td class="text-right"><b>MAC</b></td>
                    <td class="text-left">{{::historico.conteudo[0].mac}}</td>
                    <td class="text-right"><b>MAC</b></td>
                    <td class="text-left">{{::historico.conteudo[1].mac}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].interface != historico.conteudo[1].interface}">
                    <td class="text-right"><b>Interface</b></td>
                    <td class="text-left">{{::historico.conteudo[0].interface}}</td>
                    <td class="text-right"><b>Interface</b></td>
                    <td class="text-left">{{::historico.conteudo[1].interface}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].protocolo != historico.conteudo[1].protocolo}">
                    <td class="text-right"><b>Protocolo</b></td>
                    <td class="text-left">{{::historico.conteudo[0].protocolo}}</td>
                    <td class="text-right"><b>Protocolo</b></td>
                    <td class="text-left">{{::historico.conteudo[1].protocolo}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].criptografia != historico.conteudo[1].criptografia}">
                    <td class="text-right"><b>Criptografia</b></td>
                    <td class="text-left">{{::historico.conteudo[0].criptografia}}</td>
                    <td class="text-right"><b>Criptografia</b></td>
                    <td class="text-left">{{::historico.conteudo[1].criptografia}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].alturaantena != historico.conteudo[1].alturaantena}">
                    <td class="text-right"><b>Altura Antena</b></td>
                    <td class="text-left">{{::historico.conteudo[0].alturaantena}}</td>
                    <td class="text-right"><b>Altura Antena</b></td>
                    <td class="text-left">{{::historico.conteudo[1].alturaantena}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].polarizacao != historico.conteudo[1].polarizacao}">
                    <td class="text-right"><b>Polarização</b></td>
                    <td class="text-left">{{::historico.conteudo[0].polarizacao}}</td>
                    <td class="text-right"><b>Polarização</b></td>
                    <td class="text-left">{{::historico.conteudo[1].polarizacao}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].frequencia != historico.conteudo[1].frequencia}">
                    <td class="text-right"><b>Frequência</b></td>
                    <td class="text-left">{{::historico.conteudo[0].frequencia}}</td>
                    <td class="text-right"><b>Frequência</b></td>
                    <td class="text-left">{{::historico.conteudo[1].frequencia}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].potencia != historico.conteudo[1].potencia}">
                    <td class="text-right"><b>Potência</b></td>
                    <td class="text-left">{{::historico.conteudo[0].potencia}}</td>
                    <td class="text-right"><b>Potência</b></td>
                    <td class="text-left">{{::historico.conteudo[1].potencia}}</td>
                </tr>

                <tr ng-class="{'success': historico.conteudo[0].ganho != historico.conteudo[1].ganho}">
                    <td class="text-right"><b>Ganho</b></td>
                    <td class="text-left">{{::historico.conteudo[0].ganho}}</td>
                    <td class="text-right"><b>Ganho</b></td>
                    <td class="text-left">{{::historico.conteudo[1].ganho}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].perdas != historico.conteudo[1].perdas}">
                    <td class="text-right"><b>Perdas</b></td>
                    <td class="text-left">{{::historico.conteudo[0].perdas}}</td>
                    <td class="text-right"><b>Perdas</b></td>
                    <td class="text-left">{{::historico.conteudo[1].perdas}}</td>
                </tr>
                <tr ng-class="{'success': historico.conteudo[0].observacao != historico.conteudo[1].observacao}">
                    <td class="text-right"><b>Observação</b></td>
                    <td class="text-left">{{::historico.conteudo[0].observacao}}</td>
                    <td class="text-right"><b>Observação</b></td>
                    <td class="text-left">{{::historico.conteudo[1].observacao}}</td>
                </tr>

            </tbody>
        </table>
    </div>

    <div ng-show="historico.tabela == 'calculos_ptmp'">


    </div>

    <div class="table" ng-repeat="item in historico.conteudo" ng-show="historico.tabela!='log_alteracao' && historico.tabela!='hosts_radio_parametros' && historico.tabela!='hosts_parametros' && historico.tabela!='calculos_ptp' && historico.tabela !='calculos_ptmp'">
        <table class="table-condensed">
            <tbody>

                <tr ng-repeat="(key, value) in item">
                    <td class="text-right"><b>{{::key}}:</b></td>
                    <td class="text-left">{{::value}}</td>
                </tr>


            </tr>
            </tbody>
        </table>
    </div>
</div>
    </div>    
    
</div>    

    

<!-- Ainda não implementado
  <div class="col-md-8">
    <div class="panel panel-primary">
      <div class="panel-heading">Tarefas</div>
      <div class="panel-body">
        <ul class="list-group">
          <li class="list-group-item" ng-repeat="tarefa in tarefas">
            <a href="{{tarefa.link}}"><i class="glyphicon" ng-class="{'glyphicon-remove-circle text-danger': tarefa.status=='pendente', 'glyphicon-ok-circle text-success': tarefa.status=='ok'}"></i> {{tarefa.tarefa}}</a></li>
            </ul>
      </div>
    </div>
  </div>
  -->





</div>
