'use strict';

angular.module('app')

    .factory('EventosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/:id', {},
            {
                getEventos: {
                    method: 'GET',
                    isArray: false
                },
                insertEvento: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateEvento: {
                    method: 'PUT',
                    params: {id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },

                deleteEvento: {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })


    .factory('EventosStatusService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/status/:status_id', {},
            {
                getEventosStatus: {
                    method: 'GET',
                    isArray: false
                },
                insertEventoStatus: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateEventoStatus: {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteEventoStatus: {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('EventosTiposService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/tipos/:tipo_id', {},
            {
                getEventosTipos: {
                    method: 'GET',
                    isArray: false
                },
                insertEventoTipo: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateEventoTipo: {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteEventoTipo: {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('EventosCategoriasService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/categorias', {},
            {
                getEventosCategorias: {
                    method: 'GET',
                    isArray: false
                }
            }
        );
    })

    .factory('TarefasService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/tarefas', {},
            {
                getTarefas: {
                    method: 'GET',
                    isArray: false
                }
            }
        );
    })

    .factory('EventosTarefasService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/:id/tarefas', {},
            {
                getTarefas: {
                    method: 'GET',
                    isArray: false
                }
            }
        );
    })

    .factory('EventosHistoricoService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/eventos/:id/historico', {},
            {
                getEventosHistorico: {
                    method: 'GET',
                    isArray: false
                }
            }
        );
    });
