(function () {
    'use strict';

    angular
        .module('app')
        .controller('EventosListController', EventosListController);

    /** @ngInject */
    function EventosListController($http, API_CONFIG, cfpLoadingBar, $rootScope, toaster, $localStorage) {

        var vm = this;

        vm.limit = 58;
        vm.filtro = '';
        vm.eventos = [];
        vm.sortBy = 'datacad';
        vm.sortOrder = 'dsc';
        
        vm.tipo = 'descricao';
        vm.username = $rootScope.operador.username;
        vm.pagination = {
            page: 1
        };              

        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.status = '';
        vm.setStatus = setStatus;
        vm.operador = '';
        vm.setOperador = setOperador;
        vm.trabalhar = trabalhar;

        activate();

        function activate() {
            getData();
        }

        function getData() {
           // cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/eventos?page=' + vm.pagination.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
            if (vm.status !== '') {
                urlApi += '&status=' + vm.status;
            }
            
            switch(vm.operador){
                case 'outros':
                    urlApi += '&username=!' + vm.username; 
                    break;
                case 'meus':
                    urlApi += '&username=' + vm.username; 
                    break;
            }
            
            if (vm.status !== '') {
                urlApi += '&status=' + vm.status;
            }
            
            $http.get(urlApi).then(function (response) {
                angular.copy(response.data.rows, vm.eventos);
                angular.copy(response.data.pagination, vm.pagination);

            });
        }

        function pageChanged() {
            getData();
        }

        function setStatus(status) {
            vm.status = status;
            getData();
        }
        
        function setOperador(operador) {
            vm.operador = operador;
            getData();
        }

        function busca(termos) {
            
            vm.currentPage = 1;
            if(termos!=undefined){
                vm.filtro = '&' + vm.tipo + '=|' + termos + '|';    
            }
            

            getData();

        }

        function trabalhar(evento) {

            //Só permite trabalhar em um evento se não estiver fechado
            if (evento.status === "Fechado") {
                toaster.pop('error', "Não permitido", "Não é possível trabalhar em um evento com status 'Fechado'.");
            } else {

                $rootScope.eventoSelecionado = evento;
                $localStorage.evento = evento;
                $rootScope.$broadcast("eventoSelecionado");
             }

        }

    }

})();
