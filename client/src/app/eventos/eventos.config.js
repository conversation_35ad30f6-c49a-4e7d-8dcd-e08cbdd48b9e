'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/eventos', {
                templateUrl: 'app/eventos/eventos.list.html',
                controller: 'EventosListController',
                controllerAs: 'ELC',
                title: 'Eventos',
                authorize: ['eventos.read', 'eventos.write']
            })

            .when('/eventos/novo', {
                templateUrl: 'app/eventos/eventos.form.html',
                controller: 'EventosFormCtrl',
                title: 'Eventos',
                resolve: {
                    dadosAPI: function ($q, EventosService, EventosTiposService,
                      EventosStatusService) {
                        var Tipos = EventosTiposService.getEventosTipos();
                        var Status = EventosStatusService.getEventosStatus();
                        //$scope.evento = {};
                        return $q.all([Tipos.$promise, Status.$promise]);

                    },

                    formType: function () {

                        return 'create';

                    }
                },
                authorize: ['eventos.write']
            })

            .when('/eventos/:id_evento', {
                templateUrl: 'app/eventos/eventos.form.html',
                controller: 'EventosFormCtrl',
                title: 'Eventos',
                resolve: {
                    dadosAPI: function ($q, EventosService, EventosTiposService, EventosStatusService, EventosHistoricoService, $route) {
                        var Evento = EventosService.get({id: $route.current.params.id_evento});
                        var Tipos = EventosTiposService.getEventosTipos();
                        var Status = EventosStatusService.getEventosStatus();
                        var EventoHistorico = EventosHistoricoService.getEventosHistorico({id: $route.current.params.id_evento});
                        return $q.all([Evento.$promise, Tipos.$promise, Status.$promise, EventoHistorico.$promise]);


                    },

                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['eventos.read', 'eventos.write']
            });
    });
