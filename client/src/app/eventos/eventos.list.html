<ol class="breadcrumb">
    <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-calendar"></i> Eventos</li>

</ol>

<div class="barra">
<div class="form-group">
       <a href="/eventos/novo" type="button" class="btn btn-success btn-incluir text-center" ng-show="eventoSelecionado.id == ''" authorize="['eventos.write']"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>
    <div class="form-group pull-right">
        <form class="form-inline" role="form">
            <div class="form-group">
                Exibir:
                <select class="form-control" ng-model="ELC.operador" ng-init="ELC.operador = ''" ng-change="ELC.setOperador(ELC.operador)" authorize="['eventos.write']">
                <option value="">Todos Operadores</option>
                <option value="outros">Outros Operadores</option>
                <option value="meus">Meus Eventos</option>
            </select>
                <select class="form-control" ng-model="ELC.status" ng-init="ELC.status = ''" ng-change="ELC.setStatus(ELC.status)">
                <option value="">Todos Status</option>
                <option value="Aberto">Abertos</option>
                <option value="Fechado">Fechados</option>
            </select>

            </div>
            <div class="form-group">
                Pesquisar por:
                 <select class="form-control" ng-model="ELC.tipo" ng-init="ELC.tipo = 'descricao'">
                <option value="descricao">Descrição</option>
                <option value="id">ID</option>

            </select>
            </div>
            <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="ELC.busca(termos)">Pesquisar</button>
            </div>
             </form>
        </div>
</div>
</div>


<div class="table-responsive">
  <!--
  <div class="form-group pull-right">
    <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar...">
  </div>
-->

<span class="counter pull-right"></span>
            <table class="table table-striped table-hover table-bordered tabela-eventos">

              <thead>
                <tr>
                  <th class="vert-align text-center" width="3%">#</th>
                  <th class="vert-align text-center" width="6%">Status</th>
                  <th class="vert-align text-center" width="12%">Data</th>
                  <th class="vert-align text-center">Evento</th>
                  <th class="vert-align text-center" width="8%" authorize="['eventos.write']">Ação</th>
                  <th class="vert-align text-center" width="9%">Tipo</th>
                  <th class="vert-align text-center" width="10%">Operador</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="evento in ELC.eventos">
                  <td class="vert-align text-center">{{::evento.id}}</td>
                  <td class="vert-align text-center" ><span class="label" ng-class="[{'label-success': evento.status == 'Aberto'}, {'label-danger': evento.status == 'Fechado'}, {'label-default': evento.status !== 'Fechado' && evento.status !== 'Aberto'}]"> {{evento.status}}</span></td>
                  <td class="vert-align text-center">{{::evento.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center" title="{{evento.descricao}}"><a href="/eventos/{{evento.id}}">{{::evento.descricao}}</a></td>
                    <td class="vert-align text-center" title="Trabalhar" authorize="['eventos.write']">
                    <button class="btn btn-warning btn-sm" title="Trabalhar no Evento" ng-click="ELC.trabalhar(evento)" ng-show="eventoSelecionado.id != evento.id"><i class="glyphicon glyphicon-play-circle"></i> Trabalhar</button> <button class="btn btn-success btn-sm" ng-show="eventoSelecionado.id == evento.id" ng-click="ELC.trabalhar(evento)" title="Clique para sair do evento"><i class="glyphicon glyphicon-check"></i> Trabalhando</button>
                    </td>


                    <td class="vert-align text-center"><i class="glyphicon glyphicon-wrench" ng-show="evento.tipo == 'Manutenção'"></i> <i class="glyphicon glyphicon-folder-close" ng-show="evento.tipo == 'Projeto'"></i> <i class="glyphicon glyphicon-exclamation-sign" ng-show="evento.tipo == 'Ocorrência'"></i> {{::evento.tipo}}</td>
                    <td class="vert-align text-center" title="{{::evento.username}}">{{::evento.username}}</td>
                                    </tr>
              </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="ELC.pagination.size" ng-model="ELC.pagination.page" ng-change="ELC.pageChanged()" items-per-page="ELC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Página <span class="badge">{{ELC.pagination.page}}</span> de  <span class="badge">{{ELC.pagination.pages}}</span> de <span class="badge">{{ELC.pagination.size}}</span> registro(s)</span>
            </div>
          </div>
