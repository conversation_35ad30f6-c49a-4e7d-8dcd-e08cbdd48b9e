<div class="barra">
    <div class="form-group">
    <!-- Parâmetros -->    
    <a href="/hosts/{{host.host}}/servicos/{{::servico.nomeservico}}/calculos/ptmp/novo" type="button" class="btn btn-success btn-incluir text-center" ng-if="eventoSelecionado.id != ''" authorize="['servicos.write']"><i class="glyphicon glyphicon-plus"></i> <br>Incluir</a>
    </div>
</div>    

<table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align">Evento</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">AP</th>
          <th class="vert-align text-center">Observação</th> 
          <th class="vert-align text-center">Ação</th>     
        </tr>
      </thead>
      <tbody>                  
        <tr ng-repeat="calculoptmp in calculosptmp">
          <td class="vert-align"><a href="/eventos/{{::calculoptmp.idevento}}">#{{::calculoptmp.idevento}}</a></td>
          <td class="vert-align text-center">{{::calculoptmp.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align">{{::calculoptmp.username}}</td>
          <td class="vert-align text-center">{{::calculoptmp.ap}}</td>                    
          <td class="vert-align text-center">{{::calculoptmp.observacao}}</td>
          <td class="vert-align text-center">
          <a href="/hosts/{{host.host}}/servicos/{{::servico.nomeservico}}/calculos/ptmp/{{::calculoptmp.id}}" class="btn btn-default btn-sm" title="Visualizar Cálculo"><i class="glyphicon glyphicon-search"></i></a>
</td>
        </tr>
      </tbody>
    </table> 