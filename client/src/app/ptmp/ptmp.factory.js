(function() {
  'use strict';

  angular
    .module('app')
    .factory('CalculoPtmp', CalculoPtmp);

  /** @ngInject */
  function CalculoPtmp() {

    //var calculo = calculo;

    function calculo(cliente, dados){
      var altura_cliente = Number(cliente.altura);
      var altura_ap = parseFloat(dados.altura_ant_ap);
      var elevacao_ap = dados.elevacao_ap;
      var elevacao_ap_total = elevacao_ap + altura_ap;
      var distancia = Number(cliente.distancia);
      var tilt_calc_ap = dados.tilt_calc_ap;
      var ant_ang_sup = distancia * (Math.tan(Math.PI * tilt_calc_ap / 180));
      var dif_m = altura_cliente - elevacao_ap_total;

      // Antena pequena
      var ant_peq_ang_inf = distancia * (Math.tan(Math.PI * (tilt_calc_ap - 8) / 180));

      // Valores utilizados na abertura da antena no gráfico
      var ant_peq_superior = (distancia + 100) * (Math.tan(Math.PI * (tilt_calc_ap / 180))) + elevacao_ap_total;
      var ant_peq_meio = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 4) / 180) + elevacao_ap_total;
      var ant_peq_inferior = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 8) / 180) + elevacao_ap_total;
      //////////////////////

      //var ant_peq_dif_graus = Math.atan(((altura_cliente-ant_peq_meio)/distancia))*180/Math.PI;
      var ant_peq_atende = (dif_m <= ant_ang_sup) && (dif_m >= ant_peq_ang_inf);

      // Antena grande

      var ant_grd_ang_inf = distancia * (Math.tan(Math.PI * (tilt_calc_ap - 4) / 180));
      // Valores utilizados na abertura da antena no gráfico
      var ant_grd_superior = (distancia + 100) * (Math.tan(Math.PI * (tilt_calc_ap / 180))) + elevacao_ap_total;
      var ant_grd_meio = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 2) / 180) + elevacao_ap_total;
      var ant_grd_inferior = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 4) / 180) + elevacao_ap_total;
      //////////////////////

      //var ant_grd_dif_graus = Math.atan(((altura_cliente-ant_grd_meio)/distancia))*180/Math.PI;
      var ant_grd_atende = (dif_m <= ant_ang_sup) && (dif_m >= ant_grd_ang_inf);

      var resultado_cliente = {ant_peq_atende:'Não', ant_grd_atende:'Não'};

      if(ant_peq_atende){
        resultado_cliente.ant_peq_atende = 'Sim';
      } else {
        resultado_cliente.ant_peq_atende = 'Não';
      }

      if(ant_grd_atende){
        resultado_cliente.ant_grd_atende = 'Sim';
      } else {
        resultado_cliente.ant_grd_atende = 'Não';
      }

      var resultado = {
        dados_grafico: [
            {
              distancia : (distancia + 100),
              superior  : ant_peq_superior,
              inferior  : ant_peq_inferior,
              meio      : ant_peq_meio
            },
            {
              distancia : (distancia + 100),
              superior  : ant_grd_superior,
              inferior  : ant_grd_inferior,
              meio      : ant_grd_meio
            }
        ],
        cliente: resultado_cliente
      };

      return resultado;
    }

  }
})();
