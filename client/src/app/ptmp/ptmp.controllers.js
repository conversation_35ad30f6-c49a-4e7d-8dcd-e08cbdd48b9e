'use strict';

angular.module('app')

    .controller('CalculosPtmpCtrl', ['$scope', '$rootScope', '$route', '$location',
        'toaster', 'CalculosPtmpService', 'ParametrosUltimoService', 'formType', 'dadosAPI',
        function ($scope, $rootScope, $route, $location, toaster, CalculosPtmpService, ParametrosUltimoService, formType, dadosAPI) {

            var parametros = dadosAPI[0].dados[0];

            if (formType === 'create') {


                $scope.inserindo = true;

                $scope.calculoptmp = {
                    ap: parametros.hostservicos,
                    ganho_dbi: parametros.ganho_dbi,
                    elevacao_ap: parseFloat(parametros.elevacao),
                    altura_ant_ap: parseFloat(parametros.alturaantena_m),
                    host: parametros.host,
                    hostservicos: parametros.hostservicos,
                    idhost: parametros.idhost,
                    tilt_calc_ap: 0,
                    tilt_real_ap: 0,
                    clientes: []
                };


            } else {
                $scope.calculoptmp = dadosAPI[0].dados[0];
            }


            $scope.saveCalculoPtmp = function (calculo) {

                var novoCalculo = {};
                novoCalculo = angular.copy(calculo);
                novoCalculo.host = parametros.host;
                novoCalculo.hostservicos = parametros.hostservicos;
                novoCalculo.idevento = $rootScope.eventoSelecionado.id;
                novoCalculo.username = $rootScope.operador.username;
                novoCalculo.grafico_ant_peq = JSON.stringify($scope.graficoAntPeqConfig);

                CalculosPtmpService.insertCalculoPtmp(novoCalculo, function (response) {
                    if (response.status === 'OK') {
                        toaster.pop('success', "Cálculo salvo", "Cálculo adicionado com sucesso!");
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }
                });


            };

            $scope.novoCliente = {};
            $scope.visualizandoCalculo = false;

            $scope.dados_grafico = [{distancia: 0, superior: 0, meio: 0, inferior: 0},
                {distancia: 0, superior: 0, meio: 0, inferior: 0}];

            $scope.visualizarCalculo = function (calculo) {
                $scope.calculoptmp = angular.copy(calculo);
                $scope.visualizandoCalculo = true;
            };

            $scope.$watch('calculoptmp.altura_ant_ap', function () {
                $scope.atualizaCalculoPtmp();
            });

            $scope.$watch('calculoptmp.elevacao_ap', function () {
                $scope.atualizaCalculoPtmp();
            });

            $scope.$watch('calculoptmp.tilt_calc_ap', function () {
                $scope.atualizaCalculoPtmp();
            });

            $scope.atualizaCalculoPtmp = function () {
                function calculo_ptmp(cliente) {
                    var altura_cliente = Number(cliente.altura);
                    var altura_ap = parseFloat($scope.calculoptmp.altura_ant_ap);
                    var elevacao_ap = Number($scope.calculoptmp.elevacao_ap);
                    var elevacao_ap_total = elevacao_ap + altura_ap;
                    var distancia = Number(cliente.distancia);
                    var tilt_calc_ap = $scope.calculoptmp.tilt_calc_ap;
                    var ant_ang_sup = distancia * (Math.tan(Math.PI * tilt_calc_ap / 180));
                    var dif_m = altura_cliente - elevacao_ap_total;

                    // Antena pequena
                    var ant_peq_ang_inf = distancia * (Math.tan(Math.PI * (tilt_calc_ap - 8) / 180));

                    // Valores utilizados na abertura da antena no gráfico
                    var ant_peq_superior = (distancia + 100) * (Math.tan(Math.PI * (tilt_calc_ap / 180))) + elevacao_ap_total;
                    var ant_peq_meio = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 4) / 180) + elevacao_ap_total;
                    var ant_peq_inferior = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 8) / 180) + elevacao_ap_total;
                    //////////////////////

                    //var ant_peq_dif_graus = Math.atan(((altura_cliente - ant_peq_meio) / distancia)) * 180 / Math.PI;
                    var ant_peq_atende = (dif_m <= ant_ang_sup) && (dif_m >= ant_peq_ang_inf);

                    // Antena grande

                    var ant_grd_ang_inf = distancia * (Math.tan(Math.PI * (tilt_calc_ap - 4) / 180));
                    // Valores utilizados na abertura da antena no gráfico
                    var ant_grd_superior = (distancia + 100) * (Math.tan(Math.PI * (tilt_calc_ap / 180))) + elevacao_ap_total;
                    var ant_grd_meio = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 2) / 180) + elevacao_ap_total;
                    var ant_grd_inferior = (distancia + 100) * Math.tan(Math.PI * (tilt_calc_ap - 4) / 180) + elevacao_ap_total;
                    //////////////////////

                    $scope.dados_grafico = [
                        {
                            distancia: (distancia + 100),
                            superior: ant_peq_superior,
                            inferior: ant_peq_inferior,
                            meio: ant_peq_meio
                        },
                        {
                            distancia: (distancia + 100),
                            superior: ant_grd_superior,
                            inferior: ant_grd_inferior,
                            meio: ant_grd_meio
                        }
                    ];

                   // var ant_grd_dif_graus = Math.atan(((altura_cliente - ant_grd_meio) / distancia)) * 180 / Math.PI;
                    var ant_grd_atende = (dif_m <= ant_ang_sup) && (dif_m >= ant_grd_ang_inf);

                    var resultado = {ant_peq_atende: 'Não', ant_grd_atende: 'Não'};

                    if (ant_peq_atende) {
                        resultado.ant_peq_atende = 'Sim';
                    } else {
                        resultado.ant_peq_atende = 'Não';
                    }

                    if (ant_grd_atende) {
                        resultado.ant_grd_atende = 'Sim';
                    } else {
                        resultado.ant_grd_atende = 'Não';
                    }

                    return resultado;

                }

                function montaGrafico() {

                    var elevacao_total = parseFloat($scope.calculoptmp.altura_ant_ap) + Number($scope.calculoptmp.elevacao_ap);

                    var series = {
                        name: 'Abertura da antena',
                        data: [
                            [0, elevacao_total],
                            [$scope.dados_grafico[0].distancia, $scope.dados_grafico[0].superior],
                            [0, elevacao_total],
                            [$scope.dados_grafico[0].distancia, $scope.dados_grafico[0].inferior],
                            [0, elevacao_total],
                            [$scope.dados_grafico[0].distancia, $scope.dados_grafico[0].meio]
                        ],
                        color: '#357ebd'
                    };
                    $scope.graficoAntPeqConfig.series = [];
                    $scope.graficoAntPeqConfig.series.push(series);


                    series = {
                        name: 'Abertura da antena',
                        data: [
                            [0, elevacao_total],
                            [$scope.dados_grafico[1].distancia, $scope.dados_grafico[1].superior],
                            [0, elevacao_total],
                            [$scope.dados_grafico[1].distancia, $scope.dados_grafico[1].inferior],
                            [0, elevacao_total],
                            [$scope.dados_grafico[1].distancia, $scope.dados_grafico[1].meio]
                        ],
                        color: '#357ebd'
                    };
                    $scope.graficoAntGrdConfig.series = [];
                    $scope.graficoAntGrdConfig.series.push(series);

                    angular.forEach($scope.calculoptmp.clientes, function (value) {
                        var serie = {
                            name: 'Cliente ' + value.cliente_id,
                            data: [
                                [value.distancia, value.altura]
                            ],
                            antena_peq : value.antena_peq,
                            antena_grd : value.antena_grd
                            
                        };
                        $scope.graficoAntPeqConfig.series.push(serie);
                        $scope.graficoAntGrdConfig.series.push(serie);
                    });

                }

                // Atualiza as informações de cobertura na lista de clientes
                angular.forEach($scope.calculoptmp.clientes, function (value, key) {
                    var resultado = calculo_ptmp(value);
                    $scope.calculoptmp.clientes[key].antena_peq = resultado.ant_peq_atende;
                    $scope.calculoptmp.clientes[key].antena_grd = resultado.ant_grd_atende;
                });

                montaGrafico();


            };

            function dynamicSort(property) {
                var sortOrder = 1;
                if (property[0] === "-") {
                    sortOrder = -1;
                    property = property.substr(1);
                }
                return function (a, b) {
                    var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
                    return result * sortOrder;
                };
            }

            $scope.incluirCliente = function (cliente) {
                var id;
                if ($scope.calculoptmp.clientes.length === 0) {
                    id = 1;
                } else {
                    id = Math.max.apply(Math,$scope.calculoptmp.clientes.map(function(o){return o.cliente_id})) + 1;
                    
                }
                

                $scope.calculoptmp.clientes.push({
                    cliente_id: id,
                    altura: cliente.altura,
                    distancia: cliente.distancia,
                });
                
                
                $scope.calculoptmp.clientes.sort(dynamicSort("distancia"));
                $scope.atualizaCalculoPtmp();
                cliente.altura = '';
                cliente.distancia = '';    

            };

            $scope.excluirCliente = function (cliente) {
                var index = $scope.calculoptmp.clientes.indexOf(cliente);
                $scope.calculoptmp.clientes.splice(index, 1);
                $scope.atualizaCalculoPtmp();
            };


            $scope.graficoAntPeqConfig = {
                options: {
                    chart: {
                        type: 'line',
                        zoomType: 'x'
                    },
                    tooltip: {
                        useHTML: true,
                        formatter: function () {
                            var antena_peq = this.series.options.antena_peq;
                            var antena_grd = this.series.options.antena_grd;
                            var cobertura_peq = '';
                            var cobertura_grd = '';
                            
                            
                            if(antena_peq != undefined){
                                cobertura_peq = (antena_peq === 'Sim') ? '<span class="label label-success">Sim</span>' : '<span class="label label-danger">Não</span>';
                            }
                            
                            if(antena_grd != undefined){
                                cobertura_grd = (antena_grd === 'Sim') ? '<span class="label label-success">Sim</span>' : '<span class="label label-danger">Não</span>';
                            }
                            
                            
                            return '<b>' + this.point.series.name + '</b><br>Distância: <b>' + this.x +
                                'm</b><br>Elevação: <b>' + this.y + 'm</b><br>Ant. Peq.: '+cobertura_peq+'<br>Ant. Grd.: '+cobertura_grd;
                            }
                    },
                },
                series: [],
                title: {
                    text: 'Antena Pequena',
                    style: {
                        fontWeight: 'bold',
                        fontSize: '14px'
                    }
                },
                
                subtitle: {
                    text: 'Abertura 8°',
                    style: {
                        fontSize: '11px'
                    }
                },
                xAxis: {
                    title: {text: 'Distância (m)'},
                    tickAmount: 10
                },
                yAxis: {
                    title: {text: 'Elevação (m)'},
                    tickAmount: 11
                },
                loading: false
            };


            $scope.graficoAntGrdConfig = {
                options: {
                    chart: {
                        type: 'line',
                        zoomType: 'x'
                    },
                    tooltip: {
                        useHTML: true,
                        formatter: function () {
                            var antena_peq = this.series.options.antena_peq;
                            var antena_grd = this.series.options.antena_grd;
                            var cobertura_peq = '';
                            var cobertura_grd = '';
                            
                            
                            if(antena_peq != undefined){
                                cobertura_peq = (antena_peq === 'Sim') ? '<span class="label label-success">Sim</span>' : '<span class="label label-danger">Não</span>';
                            }
                            
                            if(antena_grd != undefined){
                                cobertura_grd = (antena_grd === 'Sim') ? '<span class="label label-success">Sim</span>' : '<span class="label label-danger">Não</span>';
                            }
                            
                            
                            return '<b>' + this.point.series.name + '</b><br>Distância: <b>' + this.x +
                                'm</b><br>Elevação: <b>' + this.y + 'm</b><br>Ant. Peq.: '+cobertura_peq+'<br>Ant. Grd.: '+cobertura_grd;
                            }
                    },
                },
                series: [],
                title: {
                    text: 'Antena Grande',
                    style: {
                        fontWeight: 'bold',
                        fontSize: '14px'
                    }
                },
                subtitle: {
                    text: 'Abertura 4°',
                    style: {
                        fontSize: '11px'
                    }
                },
                

                xAxis: {
                    title: {text: 'Distância (m)'},
                    tickAmount: 10
                },
                yAxis: {
                    title: {text: 'Elevação (m)'},
                    tickAmount: 11
                },
                loading: false
            };


        }]);
