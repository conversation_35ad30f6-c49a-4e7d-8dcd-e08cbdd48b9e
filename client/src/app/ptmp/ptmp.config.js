'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/hosts/:host/servicos/:id_servico/calculos/ptmp/novo', {
                templateUrl: 'app/ptmp/ptmp.form.html',
                controller: 'CalculosPtmpCtrl',
                title: 'Cálculos PTMP',
                resolve: {
                    dadosAPI: function ($q, ParametrosUltimoService, $rootScope, $route) {

                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }
                        var Parametros = ParametrosUltimoService.getParametros({servico: $route.current.params.id_servico});
                        return $q.all([Parametros.$promise]);


                    },
                    formType: function () {

                        return 'create';

                    }
                },
                authorize: ['servicos.write']
            })

            .when('/hosts/:host/servicos/:id_servico/calculos/ptmp/:id', {
                templateUrl: 'app/ptmp/ptmp.form.html',
                controller: 'CalculosPtmpCtrl',
                title: 'Cálculos PTMP',
                resolve: {
                    dadosAPI: function ($q, CalculosPtmpService, $rootScope, $route) {

                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }
                        var Calculo = CalculosPtmpService.get(
                            {
                                host: $route.current.params.id_servico,
                                servico: $route.current.params.id_servico,
                                calculo: $route.current.params.id
                            });
                        return $q.all([Calculo.$promise]);

                    },
                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['servicos.read', 'servicos.write']
            });

    });
