'use strict';

angular.module('app')

    .factory('CalculosPtmpService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:host/servicos/:servico/calculos/ptmp/:calculo', {},
            {
                getCalculosPtmp: {method: 'GET', isArray: false},
                insertCalculoPtmp: {
                    method: 'POST',
                    params: {host: '@host', servico: '@hostservicos'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    });