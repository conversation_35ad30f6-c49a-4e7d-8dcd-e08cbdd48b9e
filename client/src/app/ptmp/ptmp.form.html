{{Host}}
<ol class="breadcrumb">
  <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-random"></i> Serviços</li>
  <li>{{calculoptmp.ap}}</li>
  <li>Calculos PTMP</li>
  <li class="active" ng-show="inserindo"><b>Novo</b></li>
  <li class="active" ng-show="!inserindo"><b>#{{calculoptmp.id}}</b></li>
</ol>

<div class="barra">
        <div class="form-group">
            <button ng-click="saveCalculoPtmp(calculoptmp)" ng-show="!visualizandoCalculo" class="btn btn-primary btn-info btn-incluir text-center" type="submit"  title="Salvar Cálculo">
                <span class="glyphicon glyphicon-check"></span><br><PERSON>var
            </button>      
        </div>
    </div>   

<div class="row">
    <div class="col-md-6">
        <form class="form-horizontal" id="frmcalculo">
<fieldset>
<div class="form-group">
  <label class="col-md-4 control-label" for="ap">AP</label>
  <div class="col-md-6">
    <input type="text" class="form-control" ng-model="calculoptmp.ap" disabled>
  </div>
</div>
<div class="form-group">
  <label class="col-md-4 control-label" for="elevacao_ap">Elevação AP</label>
    <div class="col-md-4">
      <input id="elevacao_ap" class="form-control input-md" ng-model="calculoptmp.elevacao_ap" ng-disabled="visualizandoCalculo">
    </div>
</div>

<div class="form-group">
  <label class="col-md-4 control-label" for="ganho_dbi">Ganho Antena</label>
  <div class="col-md-4">
    <input id="ganho_dbi" name="ganho_dbi" class="form-control input-md" required="required" ng-model="calculoptmp.ganho_dbi" ng-disabled="visualizandoCalculo">
  </div>
</div>

<div class="form-group">
  <label class="col-md-4 control-label" for="altura_ant_ap">Altura Antena</label>
  <div class="col-md-4">
    <input id="altura_ant_ap" name="altura_ant_ap" class="form-control input-md" required="required" ng-model="calculoptmp.altura_ant_ap" ng-disabled="visualizandoCalculo">
  </div>
</div>

<div class="form-group">
  <label class="col-md-4 control-label" for="tilt_calc_ap">Tilt Calculado</label>
  <div class="col-md-4">
    <input id="tilt_calc_ap" name="tilt_calc_ap" class="form-control input-md" required="" ng-model="calculoptmp.tilt_calc_ap" ng-disabled="visualizandoCalculo">
  </div>
</div>
<div class="form-group">
  <label class="col-md-4 control-label" for="tilt_real_ap">Tilt Real</label>
  <div class="col-md-4">
  <input id="tilt_real_ap" name="tilt_real_ap" class="form-control input-md" required="" ng-model="calculoptmp.tilt_real_ap" ng-disabled="visualizandoCalculo">
  </div>
</div>
</fieldset>
</form>
</div>
    <div class="col-md-6">
    <form class="form-inline" ng-show="!visualizandoCalculo">

        <caption><b>Inclusão de cliente</b></caption>
    <fieldset>
    <div class="form-group">
            <div class="col-xs-4">
              <input type="number" placeholder="Distância" class="form-control" ng-model="novoCliente.distancia"/>
            </div>
        </div>
        <div class="form-group">
            <div class="col-xs-4">
                <input type="number" placeholder="Elevação" class="form-control" ng-model="novoCliente.altura"/>
            </div>
        </div>
        <div class="form-group">
            <div class="col-xs-2">
                <button type="button" class="btn btn-md btn-success" id="btnincluircliente" ng-click="incluirCliente(novoCliente)">Incluir Cliente</button>
            </div>
       </div>
       </fieldset>
</form>

        <caption ng-show="visualizandoCalculo"><b>Clientes</b></caption>
<div class="pre-scrollable" style="min-height:180px; max-height:180px;">
        <table class="table table-bordered table-striped table-hover">
     <thead>

                <tr>
                  <th class="vert-align text-center">#</th>
                  <th class="vert-align text-center">Distância</th>
                  <th class="vert-align text-center">Elevação</th>
                  <th class="vert-align text-center">Ant. Pequena</th>
                  <th class="vert-align text-center">Ant. Grande</th>
                  <th class="vert-align text-center" ng-show="!visualizandoCalculo"></th>
                </tr>
      </thead>
    
     <tbody>
                <tr ng-repeat="cliente in calculoptmp.clientes track by $index">
                    <td class="vert-align text-center"><b>{{cliente.cliente_id}}</b></td>
                    <td class="vert-align text-center">{{cliente.distancia}}</td>
                    <td class="vert-align text-center">{{cliente.altura}}</td>
                    <td class="vert-align text-center"><span class="label" ng-class="{'label-success' : cliente.antena_peq == 'Sim', 'label-danger': cliente.antena_peq == 'Não'}">{{cliente.antena_peq}}</span></td>
                    <td class="vert-align text-center"><span class="label" ng-class="{'label-success' : cliente.antena_grd == 'Sim', 'label-danger': cliente.antena_grd == 'Não'}">{{cliente.antena_grd}}</span></td>
                    <td class="vert-align text-center" ng-show="!visualizandoCalculo"><a class="btn btn-danger btn-sm" title="Excluir Cliente" ng-click="excluirCliente(cliente)" ><i class="glyphicon glyphicon-trash"></i></a></td>
                </tr>
              </tbody>
    
</table>
    </div>    
</div>
</div>
           <div class="row">
              <div class="col-md-6">
                 <highchart id="graficoAntPeq" config="graficoAntPeqConfig"></highchart>
              </div>

              <div class="col-md-6">
<highchart id="graficoAntGrd" config="graficoAntGrdConfig"></highchart>
              </div>
          </div>

