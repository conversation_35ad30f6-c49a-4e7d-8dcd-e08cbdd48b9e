'use strict';

angular.module('app')

    /*
    .factory('BackupsService', function ($resource, API_CONFIG) {

        return $resource(API_CONFIG.url + '/hosts/:id/backups', {},
            {
                getBackups: {method: 'GET', isArray: true}
            }
        );
    })
    

    .factory('BackupsLogsService', function ($resource, API_CONFIG) {

        return $resource(API_CONFIG.url + '/hosts/:id/backups/logs', {},
            {
                getLogs: {method: 'GET', isArray: true}
            }
        );
    })
    */

    .factory('BackupsService', ['$http', 'API_CONFIG', function ($http, API_CONFIG){

        var url = API_CONFIG.url;
        var dataFactory = {};

        dataFactory.getBackups = function (id) {
            return $http.get(url + '/hosts/'+id+'/backups', {cache: true});
        };

        return dataFactory;
    }])

    .factory('BackupsLogsService', ['$http', 'API_CONFIG', function ($http, API_CONFIG){

        var url = API_CONFIG.url;
        var dataFactory = {};

        dataFactory.getLogs = function (id) {
            return $http.get(url + '/hosts/'+id+'/backups/logs', {cache: true});
        };

        return dataFactory;
    }]);

    
    

