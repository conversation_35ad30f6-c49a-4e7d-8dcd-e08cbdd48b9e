<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><a href="/hosts"><i class="glyphicon glyphicon-tasks"></i> Hosts</a></li>
    <li ng-show="HFC.inserindo">Novo Host</li>
    <li ng-show="!HFC.inserindo">{{::HFC.host.host}}</li>
</ol>

<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
    <li ng-if="!HFC.inserindo"><a data-target="#parametros" data-toggle="tab" style="cursor: pointer;" ><i class="glyphicon glyphicon-cog" ></i> Parâmetros</a></li>
     <li ng-if="!HFC.inserindo"><a data-toggle="tab" data-target="#servicos" style="cursor: pointer;"><i class="glyphicon glyphicon-random" ></i>  Serviços</a></li>
     <li ng-show="!HFC.inserindo" authorize="['backups.read']"><a data-toggle="tab" data-target="#backups" style="cursor: pointer;" ng-click="HFC.getBackups()"><i class="glyphicon glyphicon-floppy-disk"></i>  Backups</a></li>
</ul>

<div class="tab-content">

<div id="dados" class="tab-pane fade in active">
    <div class="barra">
        <div class="form-group">
            <button ng-click="HFC.save(HFC.host)" class="btn btn-primary btn-info btn-incluir text-center" type="submit" ng-disabled="frmHost.$invalid" ng-if="eventoSelecionado.id != ''" title="Salvar Host" authorize="['hosts.write']">
                <span class="glyphicon glyphicon-check"></span><br>Salvar
            </button>

            <a href="http://lab.pocos-net.com.br/maps.php?sysmapid={{HFC.host.sysmapid}}"
                         target="_blank"
                         class="btn btn-default btn-incluir text-center"
                         ng-class="{disabled : HFC.host.sysmapid == null}"
                         title="Visualizar Mapa"><i class="glyphicon glyphicon-search"></i><br>Visualizar Mapa</a>
                      <a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{HFC.host.sysmapid}}"
                         ng-if="eventoSelecionado.id != ''"
                         target="_blank"
                         class="btn btn-warning btn-incluir text-center"
                         ng-class="{disabled : host.sysmapid == null}"
                         title="Editar Mapa"
                         authorize="['hosts.write']"
                         ><i class="glyphicon glyphicon-edit"></i><br>Editar Mapa</a>

        </div>
    </div>

  <div class="row top-buffer">
    <form class="form-horizontal" name="frmHost">
      <div class="form-group">
        <label class="col-md-2 control-label" for="nome"> Nome <i
                                  class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i></label>
    <div class="col-md-9">
      <div class="form-inline">
            <div class="form-group">
                         <ol class="nya-bs-select form-control"
                                id="tipo"
                                name="tipo"
                                title="Tipo"
                                ng-model="HFC.host.tipo"
                                ng-change="HFC.atualizaTela()"
                                data-live-search="true"
                                data-size="8"
                                required
                            >
                                <li nya-bs-option="tipo in HFC.hoststipos" data-value="tipo.id" ng-class="{disabled: eventoSelecionado.id == '' }">
                                    <a>
                                        {{ ::tipo.tipo }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign"></i>
                            <ol class="nya-bs-select form-control"
                                id="pop"
                                name="pop"
                                title="Selecione um POP"
                                ng-model="HFC.host.pop"
                                ng-change="HFC.atualizaTela()"
                                data-live-search="true"
                                data-size="8"
                                required
                            >
                                <li nya-bs-option="pop in HFC.pops" data-value="pop.id" ng-class="{disabled: eventoSelecionado.id == '' }">
                                    <a>
                                        {{::pop.tipo}} {{ ::pop.cidade_sigla }} {{ ::pop.nome }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign"  ng-show="HFC.popdest !== 0"></i>
                            <ol class="nya-bs-select form-control animate-show"
                                ng-show="HFC.popdest !== 0"
                                id="popdest"
                                name="popdest"
                                title="Selecione o POP de destino"
                                ng-model="HFC.host.popdest"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                ng-required="HFC.popdest == 2"
                            >

                                <li nya-bs-option="pop in HFC.pops_dest" data-value="pop.id">
                                    <span class="dropdown-header">{{$group}}</span>
                                    <a>
                                        {{::pop.tipo}} {{ ::pop.cidade_sigla }} {{ ::pop.nome }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign" ng-show="HFC.tecnologia !== 0"></i>
                            <ol class="nya-bs-select form-control"
                                id="tecnologia"
                                name="tecnologia"
                                ng-show="HFC.tecnologia !== 0"
                                title="Tecnologia"
                                ng-model="HFC.host.tecnologia"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                ng-required="HFC.tecnologia == 2"
                                ng-change="HFC.tecnologiaChange()"
                            >
                                <li nya-bs-option="tec in HFC.hoststecnologias" data-value="tec.id">
                                    <a>
                                        {{ tec.tecnologia }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign" ng-show="HFC.identificador !== 0"></i>
                            <input type="text" class="form-control"
                                   id="identificacao"
                                   name="identificacao"
                                   ng-show="HFC.identificador == 2"
                                   placeholder="Identificação (Obrigatório)"
                                   ng-model="HFC.host.identificacao"
                                   ng-disabled="eventoSelecionado.id == ''"
                                   ng-required="HFC.identificador == 2"
                                   required
                            >
                            <input type="text" class="form-control"
                                   id="identificacao"
                                   name="identificacao"
                                   ng-if="HFC.identificador == 1"
                                   placeholder="Identificação (Opcional)"
                                   ng-model="HFC.host.identificacao"
                                   ng-disabled="eventoSelecionado.id == ''"
                                   ng-required="HFC.identificador == 2"
                                   required
                            >
                            <i class="glyphicon glyphicon-question-sign"
                               style="font-size: 13px; color:cornflowerblue;"
                               uib-tooltip="Digite apenas um número ou sigla que identifica o host (Ex.: 1, 1-2-3, A)"
                               tooltip-placement="right"
                               ng-show="HFC.identificador !== 0"
                            ></i>
            </div>
        </div>
    </div>
  </div>

  <div class="form-group">
    <label class="col-md-2 control-label" for="telamapa"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        uib-tooltip="Especifica a tela do mapa em que este host será inserido"
                                        tooltip-placement="bottom"></i> Tela do mapa</label>
                                <div class="col-md-1">
                                    <select id="telamapa" name="telamapa" class="form-control input-md"
                                           ng-model="HFC.host.telamapa" ng-disabled="eventoSelecionado.id == ''"
                                           ng-options="o for o in HFC.telasmapa">
                                    </select>
                                </div>
  </div>
  <div class="form-group">
    <label class="col-md-2 control-label">Status</label>
    <div class="col-md-3">
      <span class="label label-success" ng-if="HFC.host.ativo==1">Ativo</span>
      <span class="label label-warning" ng-if="HFC.host.ativo==0">Inativo</span>
      <span class="label label-danger" ng-if="HFC.host.ativo==2">Retirado</span>
      {{::HFC.host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
    </div>
  </div>

  <hr />
  <div class="form-group">
    <label class="col-md-2 control-label" for="observacao">Observação</label>
    <div class="col-md-6">
      <textarea id="observacao" name="observacao" class="form-control input-md" ng-model="HFC.host.observacao"
                ng-disabled="eventoSelecionado.id == ''" style="min-height: 200px;"></textarea>
    </div>
  </div>

</form>


</div>
</div>

<div id="parametros" class="tab-pane fade in" ng-controller="ParametrosCtrl as PC" ng-if="HFC.host.tipo!==15 && HFC.host.tipo!==28">
  <div ng-include="'app/parametros/parametros.list.html'"></div>
  <div ng-include="'app/parametros/parametros.form.html'"></div>
</div>

<div id="parametros" class="tab-pane fade in" ng-controller="ParametrosCtrl as PC" ng-if="HFC.host.tipo===15">
  <div ng-include="'app/parametros/parametros.ftth.list.html'"></div>
  <div ng-include="'app/parametros/parametros.ftth.form.html'"></div>
</div>

<div id="parametros" class="tab-pane fade in" ng-controller="ParametrosCtrl as PC" ng-if="HFC.host.tipo===28">
    <div ng-include="'app/parametros/parametros.ftta.list.html'"></div>
    <div ng-include="'app/parametros/parametros.ftta.form.html'"></div>
  </div>

<div id="servicos" class="tab-pane fade" ng-controller="ServicosCtrl">
  <div ng-include="'app/servicos/servicos.host.list.html'"></div>
  <div ng-include="'app/servicos/servicos.form.html'"></div>
</div>
<div id="backups" class="tab-pane fade">
<div ng-include="'app/hosts/hosts.backup.list.html'" authorize="['backups.read']"></div>
</div>

</div>
