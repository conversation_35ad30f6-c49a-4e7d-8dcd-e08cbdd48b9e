'use strict';

angular.module('app')

    .factory('HostsService', function ($resource, API_CONFIG) {

        return $resource(API_CONFIG.url + '/hosts/:id', {},
            {
                getHosts: {method: 'GET', isArray: false},
                insertHost: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateHost: {
                    method: 'PUT',
                    params: {id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteHost: {
                    method: 'DELETE',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                enableHost: {
                    method: 'PATCH',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('HostsServiceJSON', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts.json', {},
            {
                getHostsJSON: {method: 'GET', isArray: true}
            }
        );
    })

    .factory('HostsByPopServiceJSON', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pop/:idpop/hosts.json', {},
            {
                getHostsJSON: {method: 'GET', isArray: true}
            }
        );
    })



    .factory('HostsTipos', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/tipos', {},
            {
                getHostsTipos: {method: 'GET', isArray: true, cache: true}
            }
        );
    })


    /*
    .factory('HostsTecnologias', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/tecnologias', {},
            {
                getHostsTecnologias: {method: 'GET', isArray: true, cache: true}
            }
        );
    })
    */

    .factory('HostsTecnologias', ['$http', 'API_CONFIG', function ($http, API_CONFIG){
        var json = [];
        json.get = $http.get(API_CONFIG.url + '/hosts/tecnologias', {cache: true})
            .then(function(resp){
                return resp.data;
            });
        json.all = function(){
            return json.get;
        };
        return json;
    }])

    .service('Host', function () {
        return {};
    });
