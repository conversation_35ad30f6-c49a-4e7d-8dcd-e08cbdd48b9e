'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/hosts', {
                templateUrl: 'app/hosts/hosts.list.html',
                controller: 'HostsListController',
                controllerAs: 'HLC',
                title: 'Hosts',
                authorize: ['hosts.read', 'hosts.write']
            })

            .when('/hosts/v2', {
                templateUrl: 'app/hosts/hosts.list.html',
                controller: 'HostsController',
                title: 'Hosts',
                authorize: ['hosts.read', 'hosts.write']
            })


            .when('/hosts/novo', {
                templateUrl: 'app/hosts/hosts.form.html',
                controller: 'HostsFormController',
                controllerAs: 'HFC',
                title: 'Hosts',
                model: 'hosts',
                resolve: {
                  host: function(){
                    return {};
                  },
                  pops: function(PopsNomesService){
                    return PopsNomesService.getNomes().$promise;
                  },
                  
                  hoststipos: function(HostsTipos){
                    return HostsTipos.getHostsTipos().$promise;
                  },
                  

                  /*
                  hoststecnologias: function(HostsTecnologias){
                    return HostsTecnologias.getHostsTecnologias().$promise;
                  },
                  */
                  servicos: function(){
                    return [];
                  },
                  //servicostipos: function(){
                  //  return [];
                  //},
                  //hardwares: function(){
                  //  return [];
                  //},
                  //modos: function(){
                  //  return [];
                  //},
                  /*
                  backups: function(){
                    return [];
                  },
                  backupslogs: function(){
                    return [];
                  },
                  */
                  /*
                    dadosAPI: function ($q, HostsService, PopsNomesService, HostsTipos, HostsTecnologias) {

                        var Pops = PopsNomesService.getNomes();
                        HostsTipos = HostsTipos.getHostsTipos();
                        HostsTecnologias = HostsTecnologias.getHostsTecnologias();

                        //var Status = EventosStatusService.getEventosStatus();
                        return $q.all([Pops.$promise, HostsTipos.$promise, HostsTecnologias.$promise]);
                    },
                    */

                    formType: function () {

                        return 'create';

                    }
                },
                authorize: ['hosts.write']
            })
            /*
            .when('/hosts/:id', {
                templateUrl: 'app/hosts/hosts.form.html',
                controller: 'HostsFormController',
                controllerAs: 'HFC',
                title: 'Hosts',
                resolve: {
                  
                  host: function($route, HostsService) {
                    return HostsService.get({id: $route.current.params.id}).$promise;
                  },
                  pops: function(PopsNomesService) {
                    return PopsNomesService.getNomes().$promise;
                  },
                  
                  hoststipos: function(HostsTipos) {
                    return HostsTipos.getHostsTipos().$promise;
                  },

                  formType: function () {

                      return 'edit';

                  }
                },
                authorize: ['hosts.read', 'hosts.write']
            });
            */

            .when('/hosts/:id/:host', {
                templateUrl: 'app/hosts/hosts.form.html',
                //controller: 'HostsFormCtrl',
                controller: 'HostsFormController',
                controllerAs: 'HFC',
                title: 'Hosts',
                resolve: {

                  host: function($route, HostsService) {
                    return HostsService.get({id: $route.current.params.id}).$promise;
                  },
                  pops: function(PopsNomesService) {
                    return PopsNomesService.getNomes().$promise;
                  },
                  
                  hoststipos: function(HostsTipos) {
                    return HostsTipos.getHostsTipos().$promise;
                  },
                  
                  /*
                  hoststecnologias: function(HostsTecnologias){
                    return HostsTecnologias.getHostsTecnologias().$promise;
                  },
                  */
                  servicos: function($route, ServicosService){
                    return ServicosService.get({host: $route.current.params.host}).$promise;
                  },
                 // servicostipos: function(ServicosTiposJSONService){
                 //   return ServicosTiposJSONService.getServicosTipos().$promise;
                 // },
                  //hardwares: function(HardwareJSONService){
                  //  return HardwareJSONService.getHardwares().$promise;
                  //},
                  //modos: function(ModoJSONService){
                  //  return ModoJSONService.getModos().$promise;
                  //},
                  
                  /*
                  backups: function($route, BackupsService){
                    return BackupsService.getBackups({id: $route.current.params.id}).$promise;
                  },
                  backupslogs: function($route, BackupsLogsService){
                    return BackupsLogsService.getLogs({id: $route.current.params.id}).$promise;
                  },
                  */

                /*
                    dadosAPI: function ($q, HostsService, PopsNomesService,
                      ServicosService, ServicosTiposJSONService, HardwareJSONService,
                      ModoJSONService, HostsTipos, HostsTecnologias, BackupsService, $route) {
                        var Host = HostsService.get({id: $route.current.params.id});
                        var Pops = PopsNomesService.getNomes();
                        HostsTipos = HostsTipos.getHostsTipos();
                        HostsTecnologias = HostsTecnologias.getHostsTecnologias();
                        var Servicos = ServicosService.get({host: $route.current.params.host});
                        var ServicosTipos = ServicosTiposJSONService.getServicosTipos();
                        var Hardwares = HardwareJSONService.getHardwares();
                        var Modos = ModoJSONService.getModos();
                        var Backups = BackupsService.getBackups({id: $route.current.params.id});

                        return $q.all([Host.$promise, Pops.$promise,
                          Servicos.$promise, ServicosTipos.$promise,
                          Hardwares.$promise, Modos.$promise,
                          HostsTipos.$promise, HostsTecnologias.$promise,
                          Backups.$promise]);

                    },
*/
                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['hosts.read', 'hosts.write']
            });

    });

  /*
  .config(function ($stateProvider) {
        $stateProvider

          state('hosts', { 
            url: '/hosts',
            templateUrl: 'app/hosts/hosts.list.html',
            controller: 'HostsListController'
          })

});      
*/