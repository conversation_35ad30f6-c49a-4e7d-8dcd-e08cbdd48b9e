<div class="sub-barra">
    <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar..." style="max-width:200px;">
</div>

<ul class="nav nav-tabs">
 <li class="active"><a data-target="#arquivos" data-toggle="tab" style="cursor: pointer;"><i class="glyphicon glyphicon-folder-open"></i> Arquivos</a></li>
 <li><a data-toggle="tab" data-target="#logs" style="cursor: pointer;" ng-click="HFC.getLogs()"><i class="glyphicon glyphicon-exclamation-sign"></i>  Logs</a></li>
</ul>

<div class="tab-content" >
    <div id="arquivos" class="tab-pane fade in active">

<div class="pre-scrollable">

      <div class="table-responsive">
<table class="table table-condensed table-bordered table-hover">
    <thead>
      <tr>
        <th>Pasta</a></th>
        <th>Arquivo</a></th>
        <th>Data modificação</a></th>
        <th>Tamanho</a></th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="backup in HFC.backups | filter:filtro">
          <td>{{::backup.folder}}</td>
          <td>
          <!--
          <a href="http://noc2.telemidia.net.br/api/download.php?tipo=backup&pasta={{::backup.folder}}&host={{::HFC.host.id}}&arquivo={{::backup.file}}"
                class="btn btn-primary btn-xs"
              download><i class="glyphicon glyphicon-floppy-disk"></i> {{backup.file}}</a> -->

          <a href="" ng-click="HFC.download('backup', backup.folder, HFC.host.id, backup.file)" class="btn btn-primary btn-xs"><i class="glyphicon glyphicon-floppy-disk"></i> {{backup.file}}</a>

          </td>
          <td>{{::backup.modified}}</td>
          <td>{{::backup.size}}</td>
      </tr>
    </tbody>
  </table>
</div>
</div>
</div>
<div id="logs" class="tab-pane fade in">
  <div class="pre-scrollable">
  <div class="table-responsive">
  <table class="table table-condensed table-bordered table-hover">
  <thead>
  <tr>
    <th style="width: 155px;">Data</a></th>
    <th>Status</a></th>
  </tr>
  </thead>
  <tbody>
  <tr ng-repeat="log in HFC.backupslogs">
      <td>{{::log.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
      <td>{{::log.status}}</td>
  </tr>
  </tbody>
  </table>
  </div>
</div>
</div>
</div>
