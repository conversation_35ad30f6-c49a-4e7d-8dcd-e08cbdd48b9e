(function () {
    'use strict';

    angular
        .module('app')
        .controller('HostsFormController', HostsFormController);

    /** @ngInject */
    function HostsFormController($http, API_CONFIG, formType, host, pops,
        hoststipos, servicos,
        PopsMapasComboService, cfpLoadingBar,
        $rootScope, HostsService, $location,
        LogExclusaoService, LogAlteracaoService, toaster, $filter,
        Host, Parametro, BackupsService, BackupsLogsService, $route,
        HostsTecnologias, AuthorizationService, DownloadService) {

        var vm = this;

        vm.atualizaTela = atualizaTela;
        vm.save = save;
        vm.aba = 'dados';
        vm.atualizaBarra = atualizaBarra;
        vm.getBackups = getBackups;
        vm.getLogs = getLogs;
        vm.download = download;
        vm.tecnologiaChange = tecnologiaChange;

        vm.ultimoparametro = {};

        vm.backups = [];
        vm.hoststecnologias = null;

        activate();

        function activate() {
            console.log('activate');

            getTecnologias();
        }

        if (formType === 'create') {

            vm.host = {};
            vm.pops = pops;
            vm.pops_dest = [];
            vm.hoststipos = hoststipos;
            vm.popdest = 0;
            vm.tecnologia = 0;
            vm.identificador = 0;
            vm.inserindo = true;
            atualizaTela();

        } else {

             vm.host = host.dados;
             //Host não localizado
             if(vm.host === undefined){
                $location.path('/404');
             } else {


                //Copio o objeto vm.host para o serviço Host, compartilhado com outros controllers
                angular.copy(vm.host, Host);
                vm.pops = pops;
                vm.pops_dest = [];
                vm.servicos = servicos.dados;

                //vm.servicostipos = servicostipos;
                //vm.hardwares = hardwares;
                //vm.modos = modos;
                vm.hoststipos = hoststipos;

                vm.inserindo = false;
                vm.popdest = 0;
                vm.tecnologia = 0;
                vm.identificador = 0;
                //vm.valores_anteriores = angular.copy(host.dados[0]);
                vm.valores_anteriores = vm.host;
                atualizaTela();
             }
        }

        function atualizaTela(){

            vm.telasmapa = [1];
            var result = $filter('filter')(vm.hoststipos, {id: vm.host.tipo}, true);
            if(result[0] !== undefined) {
                vm.popdest = result[0].pop_dest;
                vm.tecnologia = result[0].tecnologia;
                vm.identificador = result[0].identificador;
                result = $filter('filter')(vm.pops, {id: vm.host.pop}, true);
                if(result[0] !== undefined) {
                    PopsMapasComboService.getMapas({popid: result[0]["popid"]}, function (response) {
                        vm.telasmapa = vm.telasmapa.concat(response);
                    });
                    vm.pops_dest = [];
                    var pops = $filter('filter')(vm.pops, {pop_pai: result[0]["pop"]}, true);
                    vm.pops_dest = vm.pops_dest.concat(pops);
                    pops = $filter('filter')(vm.pops, {pop_pai_backup: result[0]["pop"]}, true);
                    vm.pops_dest = vm.pops_dest.concat(pops);
                    pops = $filter('filter')(vm.pops, {pop: result[0]["pop_pai"]}, true);
                    vm.pops_dest = vm.pops_dest.concat(pops);
                    pops = $filter('filter')(vm.pops, {pop: result[0]["pop_pai_backup"]}, true);
                    vm.pops_dest = vm.pops_dest.concat(pops);
                }
            }
            if(vm.inserindo){
              vm.host.telamapa = 1;
            }

        };

        function save(host, acao) {

            if(vm.inserindo){
                if ($rootScope.eventoSelecionado === '') {
                    host.idevento = null;
                } else {
                    host.idevento = $rootScope.eventoSelecionado.id;
                }

                host.username = $rootScope.operador.username;

                HostsService.save(host, function (response) {

                    if (response.status === 'OK') {
                        toaster.pop('success', "Host adicionado", "Host adicionado com sucesso!");
                        $location.path('/hosts/' + response.id + '/' + response.host);
                        $rootScope.$broadcast("eventoSelecionado");
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }
                });
            } else {
                HostsService.updateHost(host, function (response) {

                    if (response.status === 'OK') {
                        toaster.pop('success', "Host atualizado", "Host atualizado com sucesso!");

                        //Insere log da alteração
                        var log = {
                              idevento: $rootScope.eventoSelecionado.id,
                              username: $rootScope.operador.username,
                              tabela: 'hosts',
                              nomeregistro: vm.valores_anteriores.host,
                              campos: findDiff(host, vm.valores_anteriores)
                        };

                        LogAlteracaoService.insertLog(log);
                        $location.path('/' + $rootScope.currentPath + '/' + response.dados.id + '/' + response.dados.host);
                    } else {
                       toaster.pop('error', response.mensagem, response.dados);
                    }
                });
            }

        };

        function findDiff(original, edited) {
            var campos = [];
            var diff = {};
            var registro = {};
            for (var key in original) {
                if (original[key] !== edited[key]) {
                    registro = {};
                    diff[key] = edited[key];
                    registro.campo = key;
                    registro.valor_anterior = diff[key];
                    registro.valor_atual = original[key];
                    campos.push(registro);
                }
            }
            return campos;
        }

        function atualizaBarra(aba){
            vm.aba = aba;
        }

        function getBackups(){
            BackupsService.getBackups(vm.host.id)
                .then(function (response) {
                    vm.backups = response.data;
                }, function (error) {
                    console.log('Não foi possível recuperar os dados: ' + error.message);
                });
        }

        function getLogs(){
            BackupsLogsService.getLogs(vm.host.id)
                .then(function (response) {
                    vm.logs = response.data;
                }, function (error) {
                    console.log('Não foi possível recuperar os dados: ' + error.message);
                });
        }

        function getTecnologias(){
          HostsTecnologias.all().then(function(resp) {
            vm.hoststecnologias = resp;
            vm.hoststecnologias.unshift({"id":0,"datacad":null,"tecnologia":"-- Nenhuma --"});
          });
        }

        function download(tipo, pasta, host, arquivo){
            DownloadService.startDownload(tipo, pasta, host, arquivo);
        }

        function tecnologiaChange(){
            console.log(vm.host.tecnologia);
            if(vm.host.tecnologia == 0) vm.host.tecnologia = null;
            console.log(vm.host.tecnologia);
        }

        

  }
})();
