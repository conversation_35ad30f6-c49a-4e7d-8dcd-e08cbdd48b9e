<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-tasks"></i> Hosts</li>

</ol>
<div class="barra">
  <div class="form-group">

    <a href="/hosts/novo" type="button" class="btn btn-success btn-incluir text-center"
      ng-if="eventoSelecionado.id != ''" authorize="['hosts.write']"><span
        class="glyphicon glyphicon-plus"></span><br>Incluir</a>

    <div class="form-group pull-right">
      <form class="form-inline" role="form">
        <div class="form-group">
          <select class="form-control" ng-model="HLC.status" ng-change="HLC.pageChanged()">
            <option value="">Todos</option>
            <option value="1">Ativos</option>
            <option value="0">Inativos</option>
            <option value="2">Retirados</option>
          </select>
        </div>
        <div class="form-group">
          <select class="form-control" ng-model="HLC.tipo">
            <option value="host">Nome</option>
            <option value="ip">IP/Faixa IP</option>
            <option value="pop">POP</option>
            <option value="username">Operador</option>
            <option value="idevento">Evento</option>
            <option value="id">ID</option>

          </select>
        </div>
        <div class="form-group">
          <input size="30" maxlength="30" class="form-control" type="text" ng-model="HLC.termos">
          <button class="btn btn-default" title="Pesquisar" ng-click="HLC.busca(HLC.termos)">Pesquisar</button>
          <button class="btn btn-default filter-col" ng-click="HLC.limpar()">
            <span class="glyphicon glyphicon-refresh"></span> Limpar
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="table-responsive">
  <span class="counter pull-right"></span>

  <table class="table table-striped table-hover table-bordered">
    <thead>
      <tr>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('ativo')"> Status
            <span ng-show="HLC.sortBy == 'ativo' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'ativo' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('host')"> Host
            <span ng-show="HLC.sortBy == 'host' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'host' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('tipo')"> Tipo
            <span ng-show="HLC.sortBy == 'tipo' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'tipo' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('ip')"> IP
            <span ng-show="HLC.sortBy == 'ip' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'ip' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('pop')"> POP
            <span ng-show="HLC.sortBy == 'pop' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'pop' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('datacad')"> Data
            <span ng-show="HLC.sortBy == 'datacad' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'datacad' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('username')"> Operador
            <span ng-show="HLC.sortBy == 'username' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'username' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
        <th class="vert-align text-center"><a href="#" ng-click="HLC.sort('idevento')"> Evento
            <span ng-show="HLC.sortBy == 'idevento' && HLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
            <span ng-show="HLC.sortBy == 'idevento' && HLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></a></th>
        <th class="vert-align text-center">Mapa Zabbix</th>
        <th class="vert-align text-center">Ação</th>
      </tr>
    </thead>
    <tbody>

      <tr ng-repeat="host in HLC.hosts" ng-class="{'success': data.idevento == eventoSelecionado.id}">
        <td class="vert-align text-center"><span class="label label-success" style="cursor: default;"
            ng-if="host.ativo==1" uib-tooltip="{{:: host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}"
            tooltip-placement="top">Ativo</span>
          <span class="label label-warning" style="cursor: default;" ng-if="host.ativo==0"
            uib-tooltip="{{:: host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}"
            tooltip-placement="top">Inativo</span>
          <span class="label label-danger" style="cursor: default;" ng-if="host.ativo==2"
            tooltip="{{host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Retirado</span></td>
        <td class="vert-align text-center"><a href="/hosts/{{:: host.id}}/{{:: host.host}}">{{:: host.host}}</a></td>
        <!-- <td class="vert-align text-center"><a href="/hosts/{{:: host.id}}">{{:: host.host}}</a></td> -->
        <td class="vert-align text-center">{{:: host.tipo}}</td>

        <td class="vert-align text-center">{{:: host.ip}}</td>
        <td class="vert-align text-center">{{:: host.pop}}</td>
        <td class="vert-align text-center">{{:: host.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
        <td class="vert-align text-center">{{:: host.username}}</td>
        <td class="vert-align text-center"><a href="/eventos/{{:: host.idevento}}">#{{:: host.idevento}}</a></td>
        <td class="vert-align text-center">
          <a href="http://lab.pocos-net.com.br/zabbix.php?action=map.view&sysmapid={{:: host.sysmapid}}" target="_blank"
            class="btn btn-default btn-sm" ng-class="{disabled : host.sysmapid == null}" title="Visualizar Mapa"><i
              class="glyphicon glyphicon-search"></i></a>
          <a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{:: host.sysmapid}}"
            ng-if="eventoSelecionado.id != ''" target="_blank" class="btn btn-default btn-sm"
            ng-class="{disabled : host.sysmapid == null}" title="Editar Mapa" authorize="['hosts.write']"><i
              class="glyphicon glyphicon-edit"></i></a>
        </td>
        <td class="vert-align text-center" ng-show="eventoSelecionado.id == ''"><a
            href="/hosts/{{:: host.id}}/{{host.host}}" class="btn btn-default btn-sm" title="Visualizar Host"><i
              class="glyphicon glyphicon-search"></i></a></td>
        <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''" authorize="['hosts.write']"><a
            href="/hosts/{{:: host.id}}/{{:: host.host}}" class="btn btn-default btn-sm" title="Editar Host"><i
              class="glyphicon glyphicon-edit"></i></a> <a href=""
            ng-really-message="Tem certeza que deseja desativar este host ?" ng-really-click="HLC.desativa(host)"
            item="host" class="btn btn-warning btn-sm" title="Desabilitar Host" ng-show="host.ativo==1"><i
              class="glyphicon glyphicon-remove"></i></a> <a href=""
            ng-really-message="Tem certeza que deseja ativar este host ?" ng-really-click="HLC.ativa(host)" item="host"
            class="btn btn-success btn-sm" title="Habilitar Host" ng-show="host.ativo!==1"><i
              class="glyphicon glyphicon-check"></i></a>
          <a href="" ng-really-message="Tem certeza que deseja retirar este host ?" ng-really-click="HLC.retira(host)"
            item="host" class="btn btn-danger btn-sm" title="Retirar Host" ng-show="host.ativo==0"><i
              class="glyphicon glyphicon-trash"></i></a>
        </td>
      </tr>

    </tbody>
  </table>
  <div class="text-center">
    <uib-pagination total-items="HLC.pagination.size" ng-model="HLC.pagination.page" ng-change="HLC.pageChanged()"
      items-per-page="HLC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
      boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
    </uib-pagination>
  </div>
  <div class="text-center">
    Página <span class="badge">{{ HLC.pagination.page}}</span> de <span class="badge">{{ HLC.pagination.pages}}</span>
    de <span class="badge">{{ HLC.pagination.size}}</span> registro(s)</span>
  </div>
</div>