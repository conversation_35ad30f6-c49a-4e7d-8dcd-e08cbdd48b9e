(function () {
    'use strict';

    angular
        .module('app')
        .controller('HostsListController', HostsListController);

    /** @ngInject */
    function HostsListController($http, API_CONFIG, cfpLoadingBar, $rootScope, HostsService,
        LogExclusaoService, LogAlteracaoService, toaster, $routeParams, $location) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo='host';
        vm.termos='';
        vm.hosts = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.desativa = desativa;
        vm.retira = retira;
        vm.ativa = ativa;
        vm.sort = sort;
        vm.limpar = limpar;

        activate();

        function activate() {


            if($routeParams.ativo !== undefined){
              vm.status = $routeParams.ativo;
            } else {
              vm.status = '';
            }

            if($routeParams.sortby !== undefined){
              vm.sortBy = $routeParams.sortby;
            } else {
              vm.sortBy = 'host';
            }

            if($routeParams.sortorder !== undefined){
              vm.sortOrder = $routeParams.sortorder;
            } else {
              vm.sortOrder = 'asc';
            }

            if($routeParams.host !== undefined){
              vm.tipo='host';
              vm.termos=$routeParams.host;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.ip !== undefined){
              vm.tipo='ip';
              vm.termos=$routeParams.ip;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.pop !== undefined){
              vm.tipo='pop';
              vm.termos=$routeParams.pop;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.username !== undefined){
              vm.tipo='username';
              vm.termos=$routeParams.username;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.idevento !== undefined){
              vm.tipo='idevento';
              vm.termos=$routeParams.idevento;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.id !== undefined){
              vm.tipo='id';
              vm.termos=$routeParams.id;
              vm.filtro = '&' + vm.tipo + '=' + vm.termos;
            }
            getData();
        }

        function getData() {

            var urlApi = API_CONFIG.url + '/hosts?page=' + $routeParams.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;

            if(vm.status !=='') urlApi += '&ativo=' + vm.status;
            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.hosts);
                angular.copy(response.data.pagination, vm.pagination);
            });
        }

        function pageChanged() {
            var urlApi = '/hosts?page=' + vm.pagination.page +
                '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos;
            if(vm.status !=='') urlApi += '&ativo=' + vm.status;
            $location.url(urlApi);

        }

        function sort(field){
            if(field == vm.sortBy){
                if(vm.sortOrder == 'asc'){
                    vm.sortOrder = 'dsc';
                } else {
                    vm.sortOrder = 'asc';
                }
            }

            vm.sortBy = field;
            pageChanged();
        }

        function busca(termos) {

            vm.pagination.page = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            pageChanged();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.tipo = 'host';
          vm.termos = '';
          $location.url('/hosts');
        }

        function desativa(host) {

            host.idevento = $rootScope.eventoSelecionado.id;
            host.username = $rootScope.operador.username;

            HostsService.deleteHost(host, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'hosts',
                        id: host.id,
                        nomeregistro: host.host,
                        idevento: host.idevento,
                        username: host.username
                    };

                    LogExclusaoService.insertLog(log, function () {



                    });

                    toaster.pop('success', "Host desativado", "Host desativado com sucesso!");
                    host.ativo = 0;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        };

        function retira(host){
          host.idevento = $rootScope.eventoSelecionado.id;
          host.username = $rootScope.operador.username;

          var urlApi = API_CONFIG.url + '/hosts/' + host.id + "/retirar";
          $http.delete(urlApi).then(function (response) {
            if (response.data.status === 'OK') {

              //Insere log da alteração
              var log = {
                        idevento: $rootScope.eventoSelecionado.id,
                        username: $rootScope.operador.username,
                        tabela: 'hosts',
                        nomeregistro: host.host,
                        campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 2}]
              };

              LogAlteracaoService.insertLog(log, function (response) {
              });

                toaster.pop('success', "Host retirado", "Host retirado com sucesso!");
                host.ativo = 2;
              };
        });
      };

        function ativa(host) {

            host.idevento = $rootScope.eventoSelecionado.id;
            host.username = $rootScope.operador.username;

            HostsService.enableHost(host, function (response) {

              if (response.status === 'OK') {
                    //Insere log da alteração
                    var log = {
                              idevento: $rootScope.eventoSelecionado.id,
                              username: $rootScope.operador.username,
                              tabela: 'hosts',
                              nomeregistro: host.host,
                              campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 1}]
                    };

                    LogAlteracaoService.insertLog(log, function (response) {
                    });

                    toaster.pop('success', "Host ativado", "Host ativado com sucesso!");
                    host.ativo = 1;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

    }

})();
