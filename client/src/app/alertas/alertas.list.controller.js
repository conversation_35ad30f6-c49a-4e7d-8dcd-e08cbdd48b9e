(function() {
  'use strict';

  angular
    .module('app')
    .controller('AlertasListController', AlertasListController);

  /** @ngInject */

  function AlertasListController($http, API_CONFIG) {

    var vm = this;
    vm.alertas = [];

    activate();

    function activate() {
      getResource();
    }

    function getResource() {
      var urlApi = API_CONFIG.url + '/alertas/todos';
        $http.get(urlApi).then(function (response) {
          vm.alertas = response.data.dados;
          vm.total = response.data.total;
            });
    }

  }

})();