'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/dedicados', {
                templateUrl: 'app/pops/pops.list.html',
                //controller: 'DedicadosListController',
                controller: 'PopsListController',
                controllerAs: 'PLC',
                title: 'Dedicados',
                authorize: ['pops.read', 'pops.write']
            })

            .when('/pops', {
                templateUrl: 'app/pops/pops.list.html',
                controller: 'PopsListController',
                controllerAs: 'PLC',
                title: 'POPs',
                authorize: ['pops.read', 'pops.write']
            })

            /*
            .when('/pops/v2', {
                templateUrl: 'app/pops/pops.form.v2.html',
                controller: 'PopsCtrl',
                title: 'POPs',
                resolve: {

                    dadosAPI: function ($q, PopsJSONService, CidadesService,
                      $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();
                        return $q.all([PopsJSON.$promise, Cidades.$promise]);
                    },

                    formType: function () {

                        return 'create';

                    }

                }
            })

            .when('/pops/v2/:id', {
                templateUrl: 'app/pops/pops.form.v2.html',
                controller: 'PopsCtrl',
                title: 'POPs',
                resolve: {

                    dadosAPI: function ($q, PopsService, PopsJSONService,
                                        CidadesService, PopsMapasService,
                                        $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        var Pops = PopsService.get({id: $route.current.params.id});
                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();
                        var Mapas = PopsMapasService.getMapas({popid: $route.current.params.id});

                        return $q.all([Pops.$promise, PopsJSON.$promise,
                          Cidades.$promise, Mapas.$promise]);
                    },
                    formType: function () {

                        return 'edit';

                    }

                }
            })
*/
            .when('/pops/novo', {
                templateUrl: 'app/pops/pops.form.html',
                controller: 'PopsCtrl',
                title: 'POPs',
                resolve: {

                    dadosAPI: function ($q, PopsJSONService, CidadesService,
                      $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();
                        return $q.all([PopsJSON.$promise, Cidades.$promise]);
                    },

                    formType: function () {

                        return 'create';

                    }

                },
                authorize: ['pops.write']
            })

            .when('/dedicados/novo', {
                templateUrl: 'app/pops/pops.form.html',
                controller: 'PopsCtrl',
                title: 'Dedicados',
                resolve: {

                    dadosAPI: function ($q, PopsJSONService, CidadesService, $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();

                        return $q.all([PopsJSON.$promise, Cidades.$promise]);
                    },

                    formType: function () {

                        return 'create';

                    }

                },
                authorize: ['pops.write']
            })

            .when('/dedicados/:id', {
                templateUrl: 'app/pops/pops.form.html',
                controller: 'PopsCtrl',
                title: 'Dedicados',
                resolve: {

                    dadosAPI: function ($q, PopsService, PopsJSONService,
                                        CidadesService, PopsMapasService,
                                        $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        var Pops = PopsService.get({id: $route.current.params.id});
                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();
                        var Mapas = PopsMapasService.getMapas({popid: $route.current.params.id});

                        return $q.all([Pops.$promise, PopsJSON.$promise, Cidades.$promise, Mapas.$promise]);
                    },
                    formType: function () {

                        return 'edit';

                    }

                },
                authorize: ['pops.read', 'pops.write']
            })

            .when('/pops/:id', {
                templateUrl: 'app/pops/pops.form.html',
                controller: 'PopsCtrl',
                title: 'POPs',
                resolve: {

                    dadosAPI: function ($q, PopsService, PopsJSONService,
                                        CidadesService, PopsMapasService,
                                        $route, $rootScope) {
                        if ($rootScope.eventoSelecionado === undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        var Pops = PopsService.get({id: $route.current.params.id});
                        //var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();
                        var Cidades = CidadesService.getCidades();
                        var Mapas = PopsMapasService.getMapas({popid: $route.current.params.id});

                        return $q.all([Pops.$promise, PopsJSON.$promise,
                          Cidades.$promise, Mapas.$promise]);
                    },
                    formType: function () {

                        return 'edit';

                    }

                },
                authorize: ['pops.read', 'pops.write']
            });

            /*

            .when('/pops/:id/:pop', {
                templateUrl: 'app/pops/pops.form.html',
                controller: 'PopsCtrl',
                title: 'POPs',
                resolve: {

                    dadosAPI: function ($q, PopsService, PopsHostsService, PopsJSONService, BackhaulJSONService, $route, $rootScope) {
                        if ($rootScope.eventoSelecionado == undefined) {
                            $rootScope.eventoSelecionado = {id: ''};
                        }

                        var Pops = PopsService.get({id: $route.current.params.id});
                        var Hosts = PopsHostsService.getPopsHosts({pop: $route.current.params.pop});
                        var Backhaul = BackhaulJSONService.getBackhaul();
                        var PopsJSON = PopsJSONService.getPops();

                        return $q.all([Pops.$promise, Hosts.$promise, Backhaul.$promise, PopsJSON.$promise]);
                    },
                    formType: function () {

                        return 'edit';

                    }

                }
            })
            */

    });
