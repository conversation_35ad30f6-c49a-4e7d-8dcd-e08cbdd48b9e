<ol class="breadcrumb">
  <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li ng-show="currentPath == 'pops'"><a href="/pops"><i class="glyphicon glyphicon-record"></i> POPs</a></li>
  <li ng-show="currentPath == 'dedicados'"><a href="/dedicados"><i class="glyphicon glyphicon-map-marker"></i>
      Dedicados</a></li>
  <li ng-show="inserindo && currentPath == 'pops'">Novo POP</li>
  <li ng-show="!inserindo && currentPath == 'pops'">{{pop.pop}}</li>
  <li ng-show="inserindo && currentPath == 'dedicados'">Novo Dedicado</li>
  <li ng-show="!inserindo && currentPath == 'dedicados'">{{pop.pop}}</li>

</ol>

<div map-lazy-load="https://maps.google.com/maps/api/js" map-lazy-load-params="{{googleMapsUrl}}">

  <ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
    <li>
      <a data-target="#contrato" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-folder-close"></i> Contrato </a>
    </li>
    <li>
      <a data-target="#parametros" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-cog"></i> Parâmetros </a>
    </li>
    <li ng-if="colunas.length > 0">
      <a data-target="#servicos" data-toggle="tab" style="cursor: pointer;"><i class="glyphicon glyphicon-random"></i>
        Serviços</a>
    </li>
    <li ng-show="hosts.length > 0">
      <a data-target="#hosts" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-tasks"></i> Hosts </a>
    </li>
    <li>
      <a data-toggle="tab" data-target="#mapas" style="cursor: pointer;">
        <i class="glyphicon glyphicon-globe"></i> Mapas</a>
    </li>
    <li>
      <a data-toggle="tab" data-target="#anexos" style="cursor: pointer;">
        <i class="glyphicon glyphicon-paperclip"></i> Anexos</a>
    </li>
  </ul>

  <div class="tab-content">
    <div id="dados" class="tab-pane fade in active">

      <div class="barra">
        <div class="form-group">
          <button ng-click="save(pop)" class="btn btn-primary btn-info btn-incluir text-center" type="submit"
            ng-disabled="frmPop.$invalid" ng-if="eventoSelecionado.id != '' && pop.ativo == 1"
            authorize="['pops.write']">
            <span class="glyphicon glyphicon-check"></span><br>Salvar
          </button>
        </div>
      </div>
      <div class="row top-buffer">
        <form class="form-horizontal" name="frmPop">
          <div class="form-group">
            <label class="col-md-2 control-label" for="nome"><i class="glyphicon glyphicon-question-sign"
                style="font-size: 13px; color:cornflowerblue;"
                uib-tooltip="Digite apenas o nome do POP/Dedicado sem utilizar a palavra POP/Dedicado ou sigla da cidade. Apenas letras e/ou números, _ ou espaço são permitidos."
                tooltip-placement="right"></i> Nome <i class="glyphicon glyphicon-asterisk text-danger"
                style="font-size: 11px;"></i></label>
            <div class="col-md-6">
              <div class="form-inline">
                <div class="form-group">
                  <label for="sigla"> <span ng-show="currentPath == 'pops'">POP</span><span
                      ng-show="currentPath == 'dedicados'">Dedicado</span> <i class="glyphicon glyphicon-plus-sign"></i>
                  </label>
                  <select class="form-control" id="sigla" ng-model="pop.cidade_sigla"
                    ng-options="cidade.sigla as cidade.sigla for cidade in cidades"
                    ng-disabled="eventoSelecionado.id == ''" required>
                  </select>
                  <i class="glyphicon glyphicon-plus-sign"></i>
                  <div class="form-group" ng-class="{ 'has-error': frmPop.nome.$invalid && frmPop.nome.$dirty }">
                    <input type="text" class="form-control" style="width:400px;" placeholder="Nome" name="nome"
                      ng-model="pop.nome" ng-pattern="/^[a-zA-Z0-9\_ ]*$/" ng-disabled="eventoSelecionado.id == ''"
                      required>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="pop_pai"><i class="glyphicon glyphicon-question-sign"
                style="font-size: 13px;color:cornflowerblue;"
                uib-tooltip="Ponto principal de onde o POP cadastrado recebe link" tooltip-placement="bottom"></i> POP
              (Pai)<i class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i> </label>
            <div class="col-md-3">
              <ol class="nya-bs-select form-control" title="Selecione um POP Pai" id="pai" ng-model="pop.pop_pai"
                ng-change="changeMapasPai()" disabled="eventoSelecionado.id == ''" data-live-search="true" data-size="8"
                required>
                <li nya-bs-option="pop in pops">
                  <a>
                    {{ ::pop }}
                    <span class="glyphicon glyphicon-ok check-mark"></span>
                  </a>
                </li>
              </ol>
              <a ng-click="deselect('pop_pai')" ng-hide="eventoSelecionado.id == ''" class="btn btn-danger btn-xs"><i
                  class="glyphicon glyphicon-trash"></i></a>

            </div>
            <label class="col-md-1 control-label" for="pop_pai_mapa">Tela mapa</label>
            <div class="col-md-1">
              <select id="pop_pai_mapa" class="form-control" ng-model="pop.pop_pai_telamapa"
                ng-options="o for o in pop_pai_mapas" ng-disabled="eventoSelecionado.id == ''"></select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="textInput3">POP (Backup)</label>
            <div class="col-md-3">
              <ol class="nya-bs-select form-control" title="(Opcional)" id="pai_backup" ng-model="pop.pop_pai_backup"
                ng-change="changeMapasBackup()" disabled="eventoSelecionado.id == ''" data-live-search="true"
                data-size="8">
                <li nya-bs-option="p in pops" ng-class="{disabled: p === pop.pop_pai }">
                  <a>
                    {{ ::p }}
                    <span class="glyphicon glyphicon-ok check-mark"></span>
                  </a>
                </li>
              </ol>
              <a ng-click="deselect('pop_pai_backup')" ng-hide="eventoSelecionado.id == ''"
                class="btn btn-danger btn-xs"><i class="glyphicon glyphicon-trash"></i></a>
            </div>
            <label class="col-md-1 control-label" for="pop_backup_mapa">Tela mapa</label>
            <div class="col-md-1">
              <select id="pop_backup_mapa" class="form-control" ng-model="pop.pop_backup_telamapa"
                ng-options="o for o in pop_backup_mapas" ng-disabled="eventoSelecionado.id == ''"></select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="elevacao">Elevação <i
                class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i></label>
            <div class="col-md-1">
              <input id="elevacao" type="number" class="form-control input-md" required ng-model="pop.elevacao"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label">Infraestrutura</label>
            <div class="col-md-3">
              <input id="infraestrutura" type="text" class="form-control input-md" ng-model="pop.infraestrutura_pop"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label">Status</label>
            <div class="col-md-3">
              <span class="label label-success" ng-if="pop.ativo==1">Ativo</span>
              <span class="label label-danger" ng-if="pop.ativo==0">Inativo</span>
              {{::pop.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
            </div>
          </div>
          <hr />

          <div class="form-group">
            <label class="col-md-2 control-label" for="cidade">Pesquisa Endereço</label>
            <div class="col-md-6">
              <input size="100" class="form-control" name="enderecogoogle" id="enderecogoogle" places-auto-complete
                types="['geocode']" on-place-changed="autocompleteCallback()" strict-bounds="true"
                circle-bounds="{{limite}}" ng-model="pop.endereco_pop"
                ng-disabled="eventoSelecionado.id == '' || pop.api_ok == '0'" d />
            </div>
            <input type="checkbox" ng-true-value="1" ] ng-false-value="0" ng-model="pop.api_ok"
              ng-disabled="eventoSelecionado.id == ''" ng-change="changeApiOk" /> Utilizar campo de pesquisa da API
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="endereco">Logradouro</label>
            <div class="col-md-4">
              <input id="logradouro" type="text" class="form-control input-md" ng-model="pop.api_logradouro"
                ng-disabled="eventoSelecionado.id == '' || pop.api_ok == '1'">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="numero">Número</label>
            <div class="col-md-1">
              <input size="5" class="form-control" id="numero" ng-model="pop.api_numero"
                ng-disabled="eventoSelecionado.id == ''" required />
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="latitude">Latitude</label>
            <div class="col-md-2">
              <input id="latitude" type="text" class="form-control input-md" ng-model="pop.latitude"
                ng-disabled="eventoSelecionado.id == ''" mask="-99.9999999?" restrict="reject" validate="false"
                ng-pattern="/^(\+|-)?(?:90(?:(?:\.0{1,7})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,7})?))$/" required>
              Latitude original: {{pop.latitude_original}}
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="longitude">Longitude</label>
            <div class="col-md-2">
              <input id="longitude" type="text" class="form-control input-md" ng-model="pop.longitude"
                ng-disabled="eventoSelecionado.id == ''" mask="-99.9999999?" restrict="reject" validate="false"
                ng-pattern="/^(\+|-)?(?:180(?:(?:\.0{1,7})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,7})?))$/"
                required> Longitude original: {{pop.longitude_original}}
            </div>
          </div>

          <!--
  <div class="form-group">
    <label class="col-md-2 control-label" for="endereco">Endereço</label>
    <div class="col-md-4">
      <input id="endereco" type="text" class="form-control input-md"
             ng-model="pop.endereco_pop" ng-disabled="eventoSelecionado.id == ''">
    </div>
  </div>
-->

          <div class="form-group">
            <label class="col-md-2 control-label" for="bairro">Bairro</label>
            <div class="col-md-3">
              <input id="bairro" type="text" class="form-control input-md" ng-model="pop.bairro_pop"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="cidade">Cidade</label>
            <div class="col-md-2">
              <select class="form-control" id="cidade" ng-model="pop.cidade_sigla"
                ng-options="cidade.sigla as cidade.cidade for cidade in cidades" disabled>
              </select>
            </div>
          </div>
          <hr />
          <div class="form-group">
            <label class="col-md-2 control-label" for="observacao">Observação</label>
            <div class="col-md-6">
              <textarea id="observacao" class="form-control input-md" ng-model="pop.observacao"
                ng-disabled="eventoSelecionado.id == ''" style="min-height: 200px;"></textarea>
            </div>
          </div>

        </form>


      </div>
    </div>


    <div id="parametros" class="tab-pane fade in" ng-controller="PopParametrosController as PPC">
      <div ng-include="'app/pop_parametros/pop.parametros.list.html'"></div>
      <div ng-include="'app/pop_parametros/pop.parametros.form.html'"></div>
    </div>

    <div id="contrato" class="tab-pane fade in">
      <div class="barra">
        <div class="form-group">
          <button ng-click="save(pop)" class="btn btn-primary btn-info btn-incluir text-center" type="submit"
            ng-disabled="frmPop.$invalid" ng-if="eventoSelecionado.id != '' && pop.ativo == 1">
            <span class="glyphicon glyphicon-check"></span><br>Salvar
          </button>
        </div>
      </div>
      <div class="row top-buffer">
        <form class="form-horizontal">
          <div class="form-group">
            <label class="col-md-2 control-label" for="dataativacao">Data Ativação</label>
            <div class="col-md-2">
              <input id="dataativacao" type="date" class="form-control input-md" ng-model="pop.dataativacao"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="datadesativacao">Data Desativação</label>
            <div class="col-md-2">
              <input id="datadesativacao" type="date" class="form-control input-md" ng-model="pop.datadesativacao"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <hr />
          <div class="form-group">
            <label class="col-md-2 control-label" for="contato">Nome Responsável</label>
            <div class="col-md-4">
              <input id="contato" type="text" class="form-control input-md" ng-model="pop.contato_responsavel"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="telefone_responsavel">Telefone</label>
            <div class="col-md-2">
              <input id="telefone_responsavel" type="text" class="form-control input-md"
                ng-model="pop.telefone_responsavel" ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="email_responsavel">E-mail</label>
            <div class="col-md-4">
              <input id="email_responsavel" type="text" class="form-control input-md" ng-model="pop.email_responsavel"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="endereco_responsavel">Endereço</label>
            <div class="col-md-3">
              <input id="endereco_responsavel" type="text" class="form-control input-md"
                ng-model="pop.endereco_responsavel" ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="bairro_responsavel">Bairro</label>
            <div class="col-md-2">
              <input id="bairro_responsavel" type="text" class="form-control input-md" ng-model="pop.bairro_responsavel"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="cidade_responsavel">Cidade</label>
            <div class="col-md-2">
              <input id="cidade_responsavel" type="text" class="form-control input-md" ng-model="pop.cidade_responsavel"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <hr />
          <div class="form-group">
            <label class="col-md-2 control-label" for="numcliente_energia">Núm. Cliente Energia</label>
            <div class="col-md-2">
              <input id="numcliente_energia" type="text" class="form-control input-md" ng-model="pop.numcliente_energia"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="numinstalacao_energia">Núm. Instalação</label>
            <div class="col-md-2">
              <input id="numinstalacao_energia" type="text" class="form-control input-md"
                ng-model="pop.numinstalacao_energia" ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="titular_energia">Titular</label>
            <div class="col-md-3">
              <input id="titular_energia" type="text" class="form-control input-md" ng-model="pop.titular_energia"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>
          <div class="form-group">
            <label class="col-md-2 control-label" for="aberturachamado">Abertura Chamado</label>
            <div class="col-md-2">
              <input id="aberturachamado" type="text" class="form-control input-md" ng-model="pop.aberturachamado"
                ng-disabled="eventoSelecionado.id == ''">
            </div>
          </div>

        </form>
      </div>
    </div>

    <div id="servicos" class="tab-pane fade in">

      <br>
      <div class="btn-group">
        <label class="btn btn-primary" ng-model="visao" uib-btn-radio="'colunas'">Visão por Colunas</label>
        <label class="btn btn-primary" ng-model="visao" uib-btn-radio="'linhas'">Visão por Linhas</label>
      </div>
      <p></p>

      <div class="table-responsive" ng-if="visao=='colunas'">
        <table class="table table-condensed table-bordered table-hover">
          <thead>
            <tr>
              <th class="vert-align text-center" ng-repeat="(key, value) in colunas_links">
                <small><a href="{{::value}}" ng-show="key != 'Parâmetro'">{{::key}}</a></small>
                <small ng-show="key == 'Parâmetro'">{{::key}}</small>
              </th>
              <!-- <th class="vert-align text-center" ng-repeat="coluna in colunas"><small>{{coluna}}</small></th> -->
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="linha in linhas">
              <td ng-repeat="coluna in colunas">
                <strong ng-show="coluna == 'Parâmetro'">
                  <small>{{::linha[coluna]}}</small>
                </strong>
                <small ng-show="coluna != 'Parâmetro' && linha['Parâmetro'] != 'IP'">{{::linha[coluna]}}</small>
                <small ng-show="linha['Parâmetro'] == 'IP' && coluna != 'Parâmetro'"><a
                    href="http://{{linha[coluna]}}:8922" target="_blank">{{::linha[coluna]}}</a></small>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="table-responsive" ng-if="visao=='linhas'">
        <table class="table table-condensed table-bordered table-hover">
          <thead>
            <tr>
              <th class="vert-align text-center">Serviço</th>
              <th class="vert-align text-center">Tipo</th>
              <th class="vert-align text-center">IP</th>
              <th class="vert-align text-center">MAC</th>
              <th class="vert-align text-center">Marca</th>
              <th class="vert-align text-center">Modelo</th>
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">Proprietário</th>
              <th class="vert-align text-center">Monitoramento</th>
              <th class="vert-align text-center">SSID</th>
              <th class="vert-align text-center">Interface</th>
              <th class="vert-align text-center">Protocolo</th>
              <th class="vert-align text-center">Criptografia</th>
              <th class="vert-align text-center">Altura Antena</th>
              <th class="vert-align text-center">Polarização</th>
              <th class="vert-align text-center">Frequência</th>
              <th class="vert-align text-center">Potência</th>
              <th class="vert-align text-center">Ganho</th>
              <th class="vert-align text-center">Perdas</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="(key, value) in tabela">
              <td class="vert-align text-center">{{::value['nomeservico']}}</td>
              <td class="vert-align text-center">{{::value['Tipo']}}</td>
              <td class="vert-align text-center">{{::value['IP']}}</td>
              <td class="vert-align text-center">{{::value['MAC']}}</td>
              <td class="vert-align text-center">{{::value['Marca']}}</td>
              <td class="vert-align text-center">{{::value['Modelo']}}</td>
              <td class="vert-align text-center"><a
                  href="materiais/patrimonios/{{::value['Patrimônio']}}">{{::value['Patrimônio']}}</a></td>
              <td class="vert-align text-center">{{::value['Proprietário']}}</td>
              <td class="vert-align text-center">{{::value['Monitoramento']}}</td>
              <td class="vert-align text-center">{{::value['SSID']}}</td>
              <td class="vert-align text-center">{{::value['Interface']}}</td>
              <td class="vert-align text-center">{{::value['Protocolo']}}</td>
              <td class="vert-align text-center">{{::value['Criptografia']}}</td>
              <td class="vert-align text-center">{{::value['Altura Antena']}}</td>
              <td class="vert-align text-center">{{::value['Polarização']}}</td>
              <td class="vert-align text-center">{{::value['Frequência']}}</td>
              <td class="vert-align text-center">{{::value['Potência']}}</td>
              <td class="vert-align text-center">{{::value['Ganho']}}</td>
              <td class="vert-align text-center">{{::value['Perdas']}}</td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>

    <div id="hosts" class="tab-pane fade in">
      <div class="row top-buffer">
        <div class="col-md-2">
          <select class="form-control" ng-model="filtro.ativo">
            <option value="">Exibir Todos</option>
            <option value="1">Exibir Ativos</option>
            <option value="0">Exibir Inativos</option>
            <option value="2">Exibir Retirados</option>


          </select>
        </div>
      </div>
      <br>
      <div class="table-responsive">
        <table class="table table-condensed table-bordered table-hover">
          <thead>
            <tr>
              <th class="vert-align text-center">Status</th>
              <th class="vert-align text-center">Host</th>
              <th class="vert-align text-center">IP</th>
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">LegÍvel</th>
              <th class="vert-align text-center">Marca</th>
              <th class="vert-align text-center">Modelo</th>
              <th class="vert-align text-center">Proprietário</th>
              <th class="vert-align text-center">Monitoramento</th>
              <th class="vert-align text-center">Local</th>
              <th class="vert-align text-center" authorize="['hosts.write']">Ação</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="host in hosts | filter:filtro:strict">
              <td class="vert-align text-center"><span class="label label-success" style="cursor: default;"
                  ng-if="host.ativo==1"
                  tooltip="{{host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Ativo</span><span
                  class="label label-warning" style="cursor: default;" ng-if="host.ativo==0"
                  tooltip="{{host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Inativo</span>
                <span class="label label-danger" style="cursor: default;" ng-if="host.ativo==2"
                  tooltip="{{host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Retirado</span>
              </td>
              <td class="vert-align text-center"><a href="/hosts/{{host.id}}/{{host.host}}">{{host.host}}</a></td>
              <td class="vert-align text-center">{{host.ip}}</td>
              <td class="vert-align text-center"><a
                  href="materiais/patrimonios/{{host.patrimonio}}">{{host.patrimonio}}</a></td>
              <td class="vert-align text-center"><span style="color:green;" class="glyphicon glyphicon-ok"
                  ng-if="host.verificar == 0 && host.patrimonio !== ''"></span><span style="color:red;"
                  class="glyphicon glyphicon-remove" ng-if="host.verificar == 1 && host.patrimonio !== ''"></span></td>
              <td class="vert-align text-center">{{host.marca}}</td>
              <td class="vert-align text-center">{{host.modelo}}</td>
              <td class="vert-align text-center">{{host.modooperacao}}</td>
              <td class="vert-align text-center">{{host.monitoramento}}</td>
              <td class="vert-align text-center">{{host.local}}</td>
              <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''" authorize="['hosts.write']"><a
                  href="/hosts/{{ host.id}}/{{ host.host}}" class="btn btn-default btn-sm" title="Editar Host"><i
                    class="glyphicon glyphicon-edit"></i></a> <a href=""
                  ng-really-message="Tem certeza que deseja desativar este host ?" ng-really-click="desativaHost(host)"
                  item="host" class="btn btn-warning btn-sm" title="Desabilitar Host" ng-show="host.ativo==1"><i
                    class="glyphicon glyphicon-remove"></i></a>
                <a href="" ng-really-message="Tem certeza que deseja ativar este host ?"
                  ng-really-click="ativaHost(host)" item="host" class="btn btn-success btn-sm" title="Habilitar Host"
                  ng-show="host.ativo!=1"><i class="glyphicon glyphicon-check"></i></a>
                <a href="" ng-really-message="Tem certeza que deseja retirar este host ?"
                  ng-really-click="retiraHost(host)" item="host" class="btn btn-danger btn-sm" title="Retirar Host"
                  ng-show="host.ativo==0"><i class="glyphicon glyphicon-trash"></i></a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>

    <div id="mapas" class="tab-pane fade in">

      <div class="barra">
        <div class="form-group">
          <button class="btn btn-success btn-incluir text-center" ng-if="eventoSelecionado.id != '' && pop.ativo == 1"
            title="Inserir tela do mapa" data-toggle="modal" data-target="#frmmapa" data-placement="top" rel="tooltip"
            authorize="['pops.write']">
            <span class="glyphicon glyphicon-plus"></span><br>Incluir
          </button>
        </div>
      </div>

      <table class="table table-condensed table-hover table-bordered" style="width:580px;">
        <thead>
          <tr>
            <th class="vert-align text-center">Identificador do mapa</th>
            <th class="vert-align text-center">Nome do mapa</th>
            <th class="vert-align text-center" ng-show="eventoSelecionado.id != ''">Ação</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="mapa in mapas | orderBy:'identificador' track by mapa.identificador">
            <td class="vert-align text-center">{{::mapa.identificador}}</td>
            <td class="vert-align text-center">{{::mapa.mapa}}</td>
            <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''"><a class="btn btn-danger btn-sm"
                ng-really-message="Tem certeza que deseja excluir este mapa ?" ng-really-click="removeMapa(mapa)"
                title="Excluir"><i class="glyphicon glyphicon-remove"></i></a>
              <a href="http://lab.pocos-net.com.br/maps.php?sysmapid={{::mapa.sysmapid}}" target="_blank"
                class="btn btn-default btn-sm" title="Visualizar Mapa" ng-class="{'disabled' : mapa.sysmapid == ''}"><i
                  class="glyphicon glyphicon-eye-open"></i></a>
              <a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{::mapa.sysmapid}}" target="_blank"
                class="btn btn-default btn-sm" title="Editar Mapa" ng-class="{disabled : mapa.sysmapid == ''}"
                ng-show="eventoSelecionado.id != ''"><i class="glyphicon glyphicon-globe"></i></a>
            </td>

          </tr>
        </tbody>
      </table>

    </div>

    <div id="anexos" class="tab-pane fade in">

      <div ng-include="'app/pops/anexos/anexosViewList.html'" ng-controller="PopsAnexoController"></div>

    </div>
  </div>
</div>

<div class="modal" id="frmmapa" tabindex="-1" role="dialog" aria-labelledby="frmmapalabel" aria-hidden="true"
  modal="showModal" close="cancel()">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmparametroslabel">
          Mapa
        </h4>
      </div>
      <div class="modal-body">
        <form role="form" name="frmmapa">
          <div class="form-group">
            <label for="identificador">Identificador</label>
            <select class="form-control" ng-model="novomapa.identificador"
              ng-init="identificadores = [2,3,4,5,6,7,8,9,10]" ng-options="o as o for o in identificadores"></select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">
          Fechar
        </button>
        <button type="button" class="btn btn-primary" ng-click="saveMapa(novomapa);" ng-disabled="frmmapa.$invalid"
          data-dismiss="modal">
          Salvar</button>
      </div>
    </div>
  </div>
</div>