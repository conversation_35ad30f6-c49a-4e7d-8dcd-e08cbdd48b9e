(function () {
    'use strict';

    angular
        .module('app')
        .controller('DedicadosListController', DedicadosListController);

    /** @ngInject */
    function DedicadosListController($http, API_CONFIG, cfpLoadingBar,$rootScope, PopsService,
        LogExclusaoService, LogAlteracaoService, toaster) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo='pop';
        vm.pops = [];
        vm.sortBy = 'pop';
        vm.sortOrder = 'asc';
        vm.pagination = {
            page: 1
        };

        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.desativa = desativa;
        vm.ativa = ativa;

        activate();

        function activate() {
            getData();
        }

        function getData() {
            cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/pops?page=' + vm.pagination.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder+"&tipo=Dedicado";
            $http.get(urlApi).then(function (response) {
                //vm.totalItems = response.data.pagination.total;
                //vm.start = response.data.pagination.start;
                //vm.end = response.data.pagination.end;
                angular.copy(response.data.rows, vm.pops);
                angular.copy(response.data.pagination, vm.pagination);
                cfpLoadingBar.complete();
            });
        }

        function pageChanged() {
            getData();
        }

        function busca(termos) {

            vm.currentPage = 1;

            // if(vm.tipo == 'nome'){
            //  vm.filtro = '&nome=' + termos;
            // }

            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            getData();

        }

        function desativa(pop) {

            pop.idevento = $rootScope.eventoSelecionado.id;
            pop.username = $rootScope.operador.username;

            PopsService.deletePop(pop, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'pops',
                        id: pop.id,
                        nomeregistro: pop.pop,
                        idevento: pop.idevento,
                        username: pop.username
                    };

                    LogExclusaoService.insertLog(log, function () {

                        

                    });

                    toaster.pop('success', "POP desativado", "POP desativado com sucesso!");
                    pop.ativo = 0;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        }

        function ativa(pop) {

            pop.idevento = $rootScope.eventoSelecionado.id;
            pop.username = $rootScope.operador.username;

            PopsService.enablePop(pop, function (response) {
              if (response.status === 'OK') {
                    //Insere log da alteração
                    var log = {
                              idevento: $rootScope.eventoSelecionado.id,
                              username: $rootScope.operador.username,
                              tabela: 'pops',
                              nomeregistro: pop.pop,
                              campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 1}]
                    };

                    LogAlteracaoService.insertLog(log, function () {
                    });    

                    toaster.pop('success', "POP ativado", "POP ativado com sucesso!");
                    pop.ativo = 1;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        }

    }

})();
