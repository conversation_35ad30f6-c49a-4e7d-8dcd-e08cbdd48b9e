'use strict';

angular.module('app')

    .controller('PopsCtrl', ['$scope', '$window', '$rootScope', 'dadosAPI',
        'PopsService', 'toaster', '$route', 'formType', '$location',
        'DIR_ANEXOS', 'Upload', '$uibModal', '$http', 'API_CONFIG', 'PopsHostsService',
        'PopsMapasService', 'HostsService', 'LogExclusaoService', 'LogAlteracaoService',
        function ($scope, $window, $rootScope, dadosAPI, PopsService, toaster, $route, formType,
                  $location, DIR_ANEXOS, Upload, $uibModal, $http, API_CONFIG, PopsHostsService,
                  PopsMapasService, HostsService, LogExclusaoService, LogAlteracaoService) {

            $scope.visao = 'colunas';

            $scope.limite = {center: {lat: -21.8533921, lng: -46.5688472}, radius: 150000};

            $scope.loadAnexos = function () {

                $http.get(API_CONFIG.url + '/pops/' + $scope.pop.id + '/' + $scope.pop.pop + '/anexos').then(function (data) {
                    $scope.anexos = data.dados;

                });

                $http.get(API_CONFIG.url + '/anexos/tipos').then(function (data) {
                    $scope.anexos_tipos = data;

                });


            };

          
            $scope.googleMapsUrl="https://maps.googleapis.com/maps/api/js?key=AIzaSyCQKwsTMK2VoL5toABFONpKe7ORJrMrLOA&libraries=places";

            $scope.autocompleteCallback = function(){
                $scope.place = this.getPlace();

                $scope.pop.api_logradouro = $scope.place.name;   

                angular.forEach($scope.place.address_components, function(value, key) {
                  if(value.types[0] == 'street_number'){
                     $scope.pop.api_numero = value.long_name;
                  }

                  if(value.types[0] == 'route'){
                     $scope.pop.api_logradouro = value.long_name;
                  }

                  if(value.types[0] == 'sublocality_level_1'){
                    $scope.pop.api_bairro = value.long_name;
                    $scope.pop.bairro_pop = value.long_name;
                  }

                  if(value.types[0] == 'administrative_area_level_2'){
                    $scope.pop.api_cidade = value.long_name;
                  }
                });

                $scope.pop.api_resultado = $scope.place;

                $scope.pop.latitude = $scope.place.geometry.location.lat();
                $scope.pop.longitude = $scope.place.geometry.location.lng();

              }

            $scope.deselect = function(pop){
              if(pop==='pop_pai'){
                $scope.pop.pop_pai = null;
                $scope.pop_pai_mapas = [1];
                $scope.pop.pop_pai_telamapa = 1;
              }

              if(pop==='pop_pai_backup'){
                $scope.pop.pop_pai_backup = null;
                $scope.pop_backup_mapas = [1];
                $scope.pop.pop_backup_telamapa = 1;
              }
            };


            $scope.loadMapasPai = function(pop){

              $scope.pop_pai_mapas = [1];
              $http.get(API_CONFIG.url + '/pops/' + pop + '/mapas.popnome').then(function (data) {
                  $scope.pop_pai_mapas = $scope.pop_pai_mapas.concat(data);
              });
            };

            $scope.loadMapasBackup = function(pop){
              $scope.pop_backup_mapas = [1];
              $http.get(API_CONFIG.url + '/pops/' + pop + '/mapas.popnome').then(function (data) {
                  $scope.pop_backup_mapas = $scope.pop_backup_mapas.concat(data);
              });
            };

            $scope.changeMapasPai = function(){
              $scope.loadMapasPai($scope.pop.pop_pai);
              $scope.pop.pop_pai_telamapa = 1;
            };

            $scope.changeMapasBackup = function(){
              $scope.loadMapasbackup($scope.pop.pop_pai_backup);
              $scope.pop.pop_backup_telamapa = 1;
            };

            $scope.lista = 'colunas';

          if (formType === 'create') {
                $scope.mapas = [];
                $scope.pop = {ativo: 1, qtdmapas: 1, api_ok: 1};
                //$scope.backhauls = dadosAPI[0];
                //$scope.pops = dadosAPI[1];
                $scope.pops = dadosAPI[0];
                //$scope.pops.splice(0, 0, ' '); // Adiciona um ítem vazio
                //$scope.cidades = dadosAPI[2];
                $scope.cidades = dadosAPI[1];
                $scope.inserindo = true;
                $scope.colunas = [];
                $scope.pop.latitude_original = $scope.pop.latitude;
                $scope.pop.longitude_original = $scope.pop.longitude;
                if($rootScope.currentPath === 'pops'){
                    $scope.pop.tipo = 'POP';
                } else {
                    $scope.pop.tipo = 'Dedicado';
                }


            }

            if (formType === 'edit') {
                $scope.mapas = [];
                $scope.pop = dadosAPI[0].dados[0];

                $scope.pop.dataativacao = new Date($scope.pop.dataativacao);
                $scope.pop.datadesativacao = new Date($scope.pop.datadesativacao);
                $scope.pop.latitude_original = $scope.pop.latitude;
                $scope.pop.longitude_original = $scope.pop.longitude;


                //Pop não localizado
                if($scope.pop === undefined){
                    $location.path('/404');
                } else {

                    //$scope.backhauls = dadosAPI[1];
                    $scope.pops = dadosAPI[1];
                    //$scope.pops = dadosAPI[1];
                    //$scope.cidades = dadosAPI[3];
                    $scope.cidades = dadosAPI[2];
                    $scope.mapas = dadosAPI[3];
                    $scope.colunas = [];
                    $scope.inserindo = false;
                    $scope.loadAnexos();
                    $scope.loadMapasPai($scope.pop.pop_pai);
                    $scope.loadMapasBackup($scope.pop.pop_pai_backup);

                    $scope.anexo = {
                        tabela: 'pop',
                        idevento: $rootScope.eventoSelecionado.id,
                        username: $rootScope.operador.username,
                        campo: $scope.pop.pop,
                        observacao: '',
                        tipo: ''
                    };

                    var Hosts = PopsHostsService.getPopsHosts({pop: $scope.pop.pop});
                    Hosts.$promise.then(function (data) {
                        $scope.hosts = data.hosts;
                        $scope.colunas = data.colunas;
                        $scope.colunas_links = data.colunas_links;
                        $scope.linhas = data.linhas;
                        $scope.tabela = data.tabela;

                        if ($scope.colunas === undefined) {
                            $scope.colunas = [];
                        }
                    });
                }
            }

            //Converte coordenadas de graus para decimais
            $scope.convertLatLon = function (lat, lon) {

                var point = new GeoPoint(lon, lat);
                return ('-' + point.getLatDec() + ',' + '-' + point.getLonDec());
            };


            $scope.save = function (pop) {

                if (formType === 'create') {

                    pop.idevento = $rootScope.eventoSelecionado.id;
                    pop.username = $rootScope.operador.username;

                    if(pop.dataativacao !== null && pop.dataativacao !== undefined){
                      pop.dataativacao = new Date(pop.dataativacao);
                    }

                    if(pop.datadesativacao !== null && pop.datadesativacao !== undefined){
                      pop.datadesativacao = new Date(pop.datadesativacao);
                    }



                    PopsService.insertPop(pop, function (response) {

                        if (response.status === 'OK') {
                            toaster.pop('success', "POP adicionado", "POP adicionado com sucesso!");
                            $location.path('/' + $rootScope.currentPath + '/' + response.id);
                            $rootScope.$broadcast("eventoSelecionado");
                        } else {
                            toaster.pop('error', response.mensagem, response.dados);
                        }

                    });

                } else if (formType === 'edit') {

                    if(pop.dataativacao !== null && pop.dataativacao !== undefined){
                      pop.dataativacao = new Date(pop.dataativacao);
                    }

                    if(pop.datadesativacao !== null && pop.datadesativacao !== undefined){
                      pop.datadesativacao = new Date(pop.datadesativacao);
                    }


                    PopsService.updatePop(pop, function (response) {
                        if (response.status === 'OK') {
                            toaster.pop('success', "POP atualizado", "POP atualizado com sucesso!");
                            $route.reload();
                        } else {
                            toaster.pop('error', response.mensagem, response.dados);
                        }

                    });

                }

            };

            $scope.saveMapa = function(mapa){
              var novoMapa = angular.copy(mapa);
              novoMapa.popid = $route.current.params.id;
              PopsMapasService.insertMapa(novoMapa, function(response){
                if(response.status === 'OK'){
                  novoMapa.id = response.id;
                  novoMapa.mapa = response.mapa;
                  novoMapa.sysmapid = response.sysmapid;
                  $scope.mapas.push(novoMapa);
                  toaster.pop('succes', "Mapa incluído", "Mapa incluído com sucesso!");
                } else {
                  toaster.pop('error', response.mensagem, response.dados);
                }
              });
            };

            $scope.removeMapa = function(mapa){
              PopsMapasService.deleteMapa(mapa, function (response) {
                  if (response.status === 'OK') {
                      $scope.mapas.splice($scope.mapas.indexOf(mapa),1);

                      toaster.pop('success', "Mapa excluído", "Mapa excluído com sucesso!");
                  } else {
                      toaster.pop('error', response.mensagem, response.dados);
                  }
              });
            };

            ////////////////////////// ANEXOS ////////////////////////////////////////////////

            $scope.dir_anexos = DIR_ANEXOS.url + '/pop/';

            var ModalInstanceCtrl = function ($scope, $uibModalInstance, $uibModal, item) {

                $scope.item = item;

                $scope.ok = function () {
                    $uibModalInstance.close();
                };

                $scope.cancel = function () {
                    $uibModalInstance.dismiss('cancel');
                };
            };

            $scope.showModal = function (selecionado) {

                $scope.opts = {
                    backdrop: true,
                    backdropClick: true,
                    dialogFade: false,
                    keyboard: true,
                    templateUrl: 'app/pops/anexos/modal.html',
                    controller: ModalInstanceCtrl,
                    size: 'lg',
                    windowClass: 'xx-dialog',
                    resolve: {} // empty storage
                };


                $scope.opts.resolve.item = function () {
                    return angular.copy({
                        username: selecionado.username,
                        data: selecionado.datacad,
                        imagem: 'http://noc2.telemidia.net.br/api/download.php?tipo=anexo&pasta=pop&arquivo=' + selecionado.arquivo,
                        observacao: selecionado.observacao,
                        tipo: selecionado.tipo
                    }); // pass name to Dialog
                };

                var modalInstance = $uibModal.open($scope.opts);

                modalInstance.result.then(function () {
                    //on ok button press
                }, function () {
                    //on cancel button press

                });
            };

            $scope.uploadPic = function (file) {

                $scope.anexo.anexo = file;

                file.upload = Upload.upload({
                    url: API_CONFIG.url + '/anexos',
                    data: $scope.anexo,
                });

                file.upload.then(function (response) {
                    if (response.data.status === 'OK') {
                        toaster.pop('success', "Arquivo anexado", "Arquivo anexado com sucesso!");
                        $scope.loadAnexos();
                    } else {
                        toaster.pop('error', response.mensagem, response.dados);
                    }

                });

            };



            $scope.setFile = function (element) {
                $scope.currentFile = element.files[0];
                var reader = new FileReader();

                reader.onload = function (event) {
                    $scope.image_source = event.target.result;
                    $scope.$apply();

                };
                // when the file is read it triggers the onload event above.
                reader.readAsDataURL(element.files[0]);
            };

            $scope.delete = function(anexo){

              $http.delete(API_CONFIG.url + '/anexos/pop/' + anexo.id).then(function (response) {

                if (response.status === 'OK') {
                    toaster.pop('success', "Anexo excluído", "Anexo excluído com sucesso!");
                    $scope.loadAnexos();
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


          };

          $scope.desativaHost = function(host){
            host.idevento = $rootScope.eventoSelecionado.id;
            host.username = $rootScope.operador.username;

            HostsService.deleteHost(host, function (response) {
                if (response.status === 'OK') {

                  var log = {
                      tabela: 'hosts',
                      id: host.id,
                      nomeregistro: host.host,
                      idevento: host.idevento,
                      username: host.username
                  };

                  LogExclusaoService.insertLog(log, function () {



                  });

                    toaster.pop('success', "Host desativado", "Host desativado com sucesso!");
                    host.ativo = 0;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
          };

          $scope.retiraHost = function(host){
            host.idevento = $rootScope.eventoSelecionado.id;
            host.username = $rootScope.operador.username;

            var urlApi = API_CONFIG.url + '/hosts/' + host.id + "/retirar";
            $http.delete(urlApi).then(function (response) {
              if (response.data.status === 'OK') {

                //Insere log da alteração
                var log = {
                          idevento: $rootScope.eventoSelecionado.id,
                          username: $rootScope.operador.username,
                          tabela: 'hosts',
                          nomeregistro: host.host,
                          campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 2}]
                };

                LogAlteracaoService.insertLog(log, function (response) {
                });

                  toaster.pop('success', "Host retirado", "Host retirado com sucesso!");
                  host.ativo = 2;
                };
          });

/*
            HostsService.retiraHost(host, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'hosts',
                        id: host.id,
                        nomeregistro: host.host,
                        idevento: host.idevento,
                        username: host.username
                    };

                    LogExclusaoService.insertLog(log, function () {



                    });

                    toaster.pop('success', "Host desativado", "Host desativado com sucesso!");
                    host.ativo = 0;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
            */

          };

          $scope.ativaHost = function(host) {

              host.idevento = $rootScope.eventoSelecionado.id;
              host.username = $rootScope.operador.username;

              HostsService.enableHost(host, function (response) {

                if (response.status === 'OK') {
                      //Insere log da alteração
                      var log = {
                                idevento: $rootScope.eventoSelecionado.id,
                                username: $rootScope.operador.username,
                                tabela: 'hosts',
                                nomeregistro: host.host,
                                campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 1}]
                      };

                      LogAlteracaoService.insertLog(log, function (response) {
                      });

                      toaster.pop('success', "Host ativado", "Host ativado com sucesso!");
                      host.ativo = 1;
                  } else {
                      toaster.pop('error', response.mensagem, response.dados);
                  }
              });


          };

        }]);
