(function () {
    'use strict';

    angular
        .module('app')
        .controller('PopsListController', PopsListController);

    /** @ngInject */
    function PopsListController($http, API_CONFIG, cfpLoadingBar, $rootScope, PopsService,
        LogExclusaoService, LogAlteracaoService, toaster, $routeParams, $location) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo = 'pop';
        vm.termos = '';
        vm.pops = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.desativa = desativa;
        vm.ativa = ativa;
        vm.sort = sort;
        vm.limpar = limpar;

        activate();

        function activate() {
            if($routeParams.ativo !== undefined){
              vm.status = $routeParams.ativo;
            } else {
              vm.status = '';
            }

            if($routeParams.sortby !== undefined){
              vm.sortBy = $routeParams.sortby;
            } else {
              vm.sortBy = 'pop';
            }

            if($routeParams.sortorder !== undefined){
              vm.sortOrder = $routeParams.sortorder;
            } else {
              vm.sortOrder = 'asc';
            }

            if($routeParams.pop !== undefined){
              vm.tipo='pop';
              vm.termos=$routeParams.pop;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.pai !== undefined){
              vm.tipo='pop_pai';
              vm.termos=$routeParams.pai;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.backup !== undefined){
              vm.tipo='pop_pai_backup';
              vm.termos=$routeParams.backup;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.bairro !== undefined){
              vm.tipo='bairro_pop';
              vm.termos=$routeParams.bairro;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.cidade !== undefined){
              vm.tipo='cidade_pop';
              vm.termos=$routeParams.cidade;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.username !== undefined){
              vm.tipo='username';
              vm.termos=$routeParams.username;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.idevento !== undefined){
              vm.tipo='idevento';
              vm.termos=$routeParams.idevento;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.id !== undefined){
              vm.tipo='id';
              vm.termos=$routeParams.id;
              vm.filtro = '&' + vm.tipo + '=' + vm.termos;
            }
            getData();
        }

        function getData() {
            cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/pops?page=' + $routeParams.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
            if(vm.status !=='') urlApi += '&ativo=' + vm.status;
            ($rootScope.currentPath=='pops') ? urlApi += '&tipo=POP' : urlApi += '&tipo=Dedicado';
            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.pops);
                angular.copy(response.data.pagination, vm.pagination);
                cfpLoadingBar.complete();
            });
        }

        function pageChanged() {
             var urlApi = '/'+ $rootScope.currentPath + '?page=' + vm.pagination.page +
                '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos;
            if(vm.status !=='') urlApi += '&ativo=' + vm.status;
            $location.url(urlApi);
        }

        function sort(field){
            if(field == vm.sortBy){
                if(vm.sortOrder == 'asc'){
                    vm.sortOrder = 'dsc';
                } else {
                    vm.sortOrder = 'asc';
                }
            }

            vm.sortBy = field;
            pageChanged();
        }

        function busca(termos) {

            vm.pagination.page = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            getData();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.tipo = 'pop';
          vm.termos = '';
          $location.url('/' + $rootScope.currentPath);
        }

        function desativa(pop) {

            pop.idevento = $rootScope.eventoSelecionado.id;
            pop.username = $rootScope.operador.username;

            $http({
              url: API_CONFIG.url + '/pops/' + pop.id,
              method: "DELETE",
            }).then(function(response) {
              console.log(response);

            //PopsService.deletePop(pop, function (response) {
                if (response.data.status === 'OK') {

                    var log = {
                        tabela: 'pops',
                        id: pop.id,
                        nomeregistro: pop.pop,
                        idevento: pop.idevento,
                        username: pop.username
                    };

                    LogExclusaoService.insertLog(log, function () {



                    });

                    toaster.pop('success', "POP desativado", "POP desativado com sucesso!");
                    pop.ativo = 0;
                } else {
                    toaster.pop('error', response.data.mensagem, response.data.dados);
                }
            });
        }

        function ativa(pop) {

            pop.idevento = $rootScope.eventoSelecionado.id;
            pop.username = $rootScope.operador.username;

            PopsService.enablePop(pop, function (response) {
              if (response.status === 'OK') {
                    //Insere log da alteração
                    var log = {
                              idevento: $rootScope.eventoSelecionado.id,
                              username: $rootScope.operador.username,
                              tabela: 'pops',
                              nomeregistro: pop.pop,
                              campos: [{'campo' :'ativo', 'valor_anterior': 0, 'valor_atual' : 1}]
                    };

                    LogAlteracaoService.insertLog(log, function (response) {
                    });

                    toaster.pop('success', "POP ativado", "POP ativado com sucesso!");
                    pop.ativo = 1;
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        }

    }

})();
