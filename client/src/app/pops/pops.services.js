'use strict';

angular.module('app')

    .factory('PopsService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:id', {},
            {
                getPops: {
                    method: 'GET', isArray: false
                },
                insertPop: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updatePop: {
                    method: 'PUT',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deletePop: {
                    method: 'DELETE',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                enablePop: {
                    method: 'PATCH',
                    params: {
                        id: '@id'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }

            });
    })

    .factory('PopsJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops.json', {},
            {
                getPops: {method: 'GET', isArray: true}
            });
    })

    .factory('PopsComboService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops.combo', {},
            {
                getPops: {method: 'GET', isArray: true}
            });
    })

    .factory('PopsNomesService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops.nomes', {},
            {
                getNomes: {method: 'GET', isArray: true}
            });
    })

    .factory('PopsHostsJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:pop/hosts.json', {},
            {
                getHosts: {method: 'GET', isArray: true}
            });
    })

    .factory('PopsHostsService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:pop/hosts', {},
            {
                getPopsHosts: {method: 'GET', isArray: false}
            });
    })

    .factory('BackhaulJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/backhaul.json', {},
            {
                getBackhaul: {method: 'GET', isArray: true}
            });
    });
