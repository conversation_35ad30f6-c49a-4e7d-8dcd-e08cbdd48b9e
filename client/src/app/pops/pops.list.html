<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li ng-if="currentPath == 'pops'"><i class="glyphicon glyphicon-record"></i> POPs</li>
    <li ng-if="currentPath == 'dedicados'"><i class="glyphicon glyphicon-map-marker"></i> Dedicados</li>
</ol>

<div class="barra">
    <div class="form-group">
    
    <a href="/pops/novo" type="button" class="btn btn-success btn-incluir text-center" ng-if="eventoSelecionado.id != '' && currentPath == 'pops'"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>
        
    <a href="/dedicados/novo" type="button" class="btn btn-success btn-incluir text-center" ng-if="eventoSelecionado.id != '' && currentPath == 'dedicados'"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>
        
        
    <div class="form-group pull-right">
        <form class="form-inline" role="form">
            <div class="form-group">
                  <select class="form-control" ng-model="PLC.status" ng-change="PLC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">Ativos</option>
                    <option value="0">Inativos</option>
                      
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control" ng-model="PLC.tipo">
                    <option value="pop">Nome</option>
                    <option value="pop_pai">POP Pai</option>
                    <option value="pop_pai_backup">POP Pai Backup</option>
                    <option value="bairro_pop">Bairro</option>
                    <option value="cidade_pop">Cidade</option>
                    <option value="username">Operador</option>
                    <option value="idevento">Evento</option>
                    <option value="id">ID</option>

                </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="PLC.termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="PLC.busca(PLC.termos)">Pesquisar</button>
                    <button class="btn btn-default filter-col" ng-click="PLC.limpar()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
        </div>
</div>
</div>  


   <div class="table-responsive">
   
    <span class="counter pull-right"></span>
            <table class="table table-striped table-hover table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('ativo')"> Status <span ng-show="PLC.sortBy == 'ativo' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'ativo' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('pop')"> POP <span ng-show="PLC.sortBy == 'pop' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'pop' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('pop_pai')"> POP Pai <span ng-show="PLC.sortBy == 'pop_pai' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'pop_pai' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('bairro_pop')"> Bairro <span ng-show="PLC.sortBy == 'bairro_pop' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'bairro_pop' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cidade_pop')"> Cidade <span ng-show="PLC.sortBy == 'cidade_pop' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cidade_pop' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('datacad')"> Data <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('username')"> Operador <span ng-show="PLC.sortBy == 'username' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'username' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('idevento')"> Evento <span ng-show="PLC.sortBy == 'idevento' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'idevento' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center">Mapa Zabbix</th>
                  <th class="vert-align text-center">Ação</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="pop in PLC.pops">
                    <td class="vert-align text-center"><span class="label label-success" style="cursor: default;" ng-if="pop.ativo==1" tooltip-placement="top" uib-tooltip="{{::pop.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Ativo</span><span class="label label-danger" style="cursor: default;" ng-if="pop.ativo==0" tooltip-placement="top" uib-tooltip="{{::pop.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}">Inativo</span></td>
                    <td class="vert-align text-center" ng-show="pop.tipo=='POP'"><a href="/pops/{{::pop.id}}">{{::pop.pop}}</a></td>
                    <td class="vert-align text-center" ng-show="pop.tipo=='Dedicado'"><a href="/dedicados/{{::pop.id}}">{{::pop.pop}}</a></td>
                    
                    <td class="vert-align text-center">{{::pop.pop_pai}}</td>
                    <td class="vert-align text-center">{{::pop.bairro_pop}}</td>
                    <td class="vert-align text-center">{{::pop.cidade_pop}}</td>
                    <td class="vert-align text-center">{{::pop.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{::pop.username}}</td>
                    <td class="vert-align text-center"><a href="/eventos/{{::pop.idevento}}">#{{::pop.idevento}}</a></td>

                    <td class="vert-align text-center">
                      <span class="dropdown" ng-if="pop.mapas.length > 1">
                        <a href="" class="dropdown-toggle btn btn-default btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Visualizar Mapa"><i class="glyphicon glyphicon-search"></i> <span class="caret"></span></a>
                        <ul class="dropdown-menu" style="min-width: 10px;">
                          <li ng-repeat="mapa in pop.mapas" ng-class="{disabled : mapa.sysmapid == null}"><a href="http://lab.pocos-net.com.br/zabbix.php?action=map.view&sysmapid={{::mapa.sysmapid}}" target="_blank" ng-disabled="mapa.sysmapid==''">{{::mapa.identificador}}</a></li>
                        </ul>
                      </span>
                      <span class="dropdown" ng-if="pop.mapas.length > 1 && eventoSelecionado.id != ''">
                        <a href="" class="dropdown-toggle btn btn-warning btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Editar Mapa"><i class="glyphicon glyphicon-edit"></i> <span class="caret"></span></a>
                        <ul class="dropdown-menu" style="min-width: 10px;">
                          <li ng-repeat="mapa in pop.mapas" ng-class="{disabled : mapa.sysmapid == null}"><a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{::mapa.sysmapid}}" target="_blank">{{::mapa.identificador}}</a></li>
                        </ul>
                      </span>
                      <a href="http://lab.pocos-net.com.br/zabbix.php?action=map.view&sysmapid={{::pop.sysmapid}}"
                         ng-if="pop.mapas.length == 1"
                         target="_blank"
                         class="btn btn-default btn-sm"
                         ng-class="{disabled : pop.sysmapid == null}"
                         title="Visualizar Mapa"><i class="glyphicon glyphicon-search"></i></a>
                      <a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{::pop.sysmapid}}"
                         ng-if="pop.mapas.length == 1 && eventoSelecionado.id != ''"
                         target="_blank"
                         class="btn btn-warning btn-sm"
                         ng-class="{disabled : pop.sysmapid == null}"
                         title="Editar Mapa"
                         ><i class="glyphicon glyphicon-edit"></i></a>
                    </td>
                    <td class="vert-align text-center" ng-show="eventoSelecionado.id == ''"><a href="/hosts/{{::host.id}}/{{::host.host}}" class="btn btn-default btn-sm" title="Visualizar Host"><i class="glyphicon glyphicon-search"></i></a></td>
                    <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''"> <a href="" ng-really-message="Tem certeza que deseja desativar este POP ? Todos os hosts associados serão desabilitados." ng-really-click="PLC.desativa(pop)" item="pop" class="btn btn-danger btn-sm" title="Desabilitar POP" ng-if="pop.ativo==1"><i class="glyphicon glyphicon-remove"></i></a> <a href="" ng-really-message="Tem certeza que deseja ativar este POP ?" ng-really-click="PLC.ativa(pop)" item="pop" class="btn btn-success btn-sm" title="Habilitar POP" ng-if="pop.ativo==0"><i class="glyphicon glyphicon-check"></i></a></td>
                </tr>
              </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="PLC.pagination.size" ng-model="PLC.pagination.page" ng-change="PLC.pageChanged()" items-per-page="PLC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Página <span class="badge">{{PLC.pagination.page}}</span> de  <span class="badge">{{PLC.pagination.pages}}</span> de <span class="badge">{{PLC.pagination.size}}</span> registro(s)</span>
            </div>
          </div>
