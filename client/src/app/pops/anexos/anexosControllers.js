'use strict';

angular.module('app')

    .controller('PopsAnexoController', ['$scope', '$rootScope', '$route', '$location',
    '$http', '$timeout', 'API_CONFIG', 'DIR_ANEXOS', 'Upload', 'toaster', '$uibModal',
    'DownloadService',
        function ($scope, $rootScope, $route, $location, $http, $timeout, API_CONFIG, 
            DIR_ANEXOS, Upload, toaster, $uibModal, DownloadService) {


    $scope.anexo = {
        tabela: 'pops',
        idevento: $rootScope.eventoSelecionado.id,
        username: $rootScope.operador.username,
        campo: $route.current.params.id,
        observacao: '',
        tipo: '',
        id: $route.current.params.id
    };

    $scope.dir_anexos = DIR_ANEXOS.url + '/pops/';

    $scope.download = function(tipo, pasta, host, arquivo){
       DownloadService.startDownload(tipo, pasta, host, arquivo);
    }  

    var ModalInstanceCtrl = function ($scope, $uibModalInstance, item) {

        $scope.item = item;

        $scope.ok = function () {
            $uibModalInstance.close();
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };
    };

    ModalInstanceCtrl.$inject = ['$scope', '$uibModalInstance', 'item'];
    
    $scope.showModal = function (selecionado) {

        $scope.opts = {
            backdrop: true,
            backdropClick: true,
            dialogFade: false,
            keyboard: true,
            templateUrl: 'app/pops/anexos/modal.html',
            controller: ModalInstanceCtrl,
            size: 'lg',
            windowClass: 'xx-dialog',
            resolve: {} // empty storage
        };


        $scope.opts.resolve.item = function () {
            return angular.copy({
                username: selecionado.username,
                data: selecionado.datacad,
                imagem: 'http://noc2.telemidia.net.br/api/download.php?tipo=anexo&pasta=pop&arquivo=' + selecionado.arquivo,
                observacao: selecionado.observacao,
                tipo: selecionado.tipo
            }); // pass name to Dialog
        };

        var modalInstance = $uibModal.open($scope.opts);

        modalInstance.result.then(function () {
            //on ok button press
        }, function () {
            //on cancel button press
            
        });
    };

    $scope.uploadPic = function (file) {

        $scope.anexo.anexo = file;

        file.upload = Upload.upload({
            url: API_CONFIG.url + '/anexos',
            data: $scope.anexo,
        });

        file.upload.then(function (response) {
            if (response.data.status === 'OK') {
                toaster.pop('success', "Arquivo anexado", "Arquivo anexado com sucesso!");
                $scope.load();
            } else {
                toaster.pop('error', "Ocorreu um erro", response.data);
            }

        });

    };

    $scope.load = function () {

        $http.get(API_CONFIG.url + $location.path() + '/anexos').then(function (data) {
            $scope.anexos = data.dados;

        });

        $http.get(API_CONFIG.url + '/anexos/tipos').then(function (data) {
            $scope.anexos_tipos = data;
            
        });


    };

    $scope.setFile = function (element) {
        $scope.currentFile = element.files[0];
        var reader = new FileReader();

        reader.onload = function (event) {
            $scope.image_source = event.target.result;
            $scope.$apply();

        };
        // when the file is read it triggers the onload event above.
        reader.readAsDataURL(element.files[0]);
    };


    $scope.delete = function(anexo){
            $http.delete(API_CONFIG.url + '/anexos/pop/' + anexo.id).then(function (data) {

                if (data.status === 'OK') {
                    toaster.pop('success', "Anexo excluído", "Anexo excluído com sucesso!");
                    $scope.load();
                } else {
                    toaster.pop('error', "Ocorreu um erro", data.dados);
                }
            });

    };

    $scope.load();

    }
]);

