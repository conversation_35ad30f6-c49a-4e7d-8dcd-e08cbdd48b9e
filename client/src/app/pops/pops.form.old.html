<ol class="breadcrumb">
    <li><a href="/">Dashboard</a></li>
    <li ng-show="currentPath == 'pops'"><a href="/pops">POPs</a></li>
    <li ng-show="currentPath == 'dedicados'"><a href="/dedicados">Dedicados</a></li>
    <li ng-show="inserindo && currentPath == 'pops'"><a href="/pops/novo">Novo POP</a></li>
    <li ng-show="!inserindo && currentPath == 'pops'"><a href="/pops/{{pop.id}}">{{pop.pop}}</a></li>
    <li ng-show="inserindo && currentPath == 'dedicados'"><a href="/dedicados/novo">Novo Dedicado</a></li>
    <li ng-show="!inserindo && currentPath == 'dedicados'"><a href="/dedicados/{{pop.id}}">{{pop.pop}}</a></li>

</ol>
<!--
<div class="alert alert-warning" ng-show="eventoSelecionado.id == ''">
    <p>Você não está trabalhando em nenhum <b>evento</b>. Clique <a href="/eventos" class="alert-link">aqui</a> para
        selecionar um evento.</p>
</div>

<div class="alert alert-success dropdown" ng-show="eventoSelecionado.id != ''" ng-controller="MenuController">
    <strong>Você está trabalhando</strong> em
 <span class="dropdown">
 <a href="" class="alert-link dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">[#{{eventoSelecionado.id}}]
     {{eventoSelecionado.descricao}} <span class="caret"></span></a>
  <ul class="dropdown-menu">
      <li><a href="/eventos/{{eventoSelecionado.id}}">Visualizar Evento</a></li>
      <li role="separator" class="divider"></li>
      <li><a href="" ng-click="sair()">Sair do evento</a></li>
  </ul>
    </span>
</div>
-->

<div class="panel panel-default">
    <div class="panel-body">
        <p class="text-danger"><i class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i> Campos
            obrigatórios</p>

        <form name="frmPop">
            <input type="hidden" ng-model="pop.tipo">
            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-tag"></i> <strong>Nomenclatura</strong></div>
                <div class="panel-body">
                    <div class="form-inline">
                        <div class="form-group">
                            <label for="sigla"><i class="glyphicon glyphicon-question-sign"
                                                  style="font-size: 13px; color:cornflowerblue;"
                                                  tooltip="Digite apenas o nome do POP/Dedicado sem utilizar a palavra POP/Dedicado ou sigla da cidade"
                                                  tooltip-placement="right"></i> <span ng-show="currentPath == 'pops'">POP</span><span ng-show="currentPath == 'dedicados'">Dedicado</span><i
                                    class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i> <i class="glyphicon glyphicon-plus-sign"></i>
                            </label>
                            <select class="form-control"
                                    id="sigla"
                                    ng-model="pop.cidade_sigla"
                                    ng-options="cidade.sigla as cidade.sigla for cidade in cidades"
                                    ng-disabled="eventoSelecionado.id == ''"
                                    required>
                            </select>
                            <i class="glyphicon glyphicon-plus-sign"></i>
                            <input type="text" class="form-control" style="width:400px;" placeholder="Nome"
                                   ng-model="pop.nome" ng-disabled="eventoSelecionado.id == ''">
                        </div>
                    </div>
                </div>
            </div>


            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-list-alt"></i> <strong>Dados</strong>
                </div>
                <div class="panel-body">

                    <div class="form-horizontal">

                        <div class="row">
                            <div class="form-group">
                              <!--
                                <label class="col-md-2 control-label" for="backhaul"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        tooltip="Ligação entre o núcleo da rede (backbone) e as sub-redes periféricas"
                                        tooltip-placement="bottom"></i> Backhaul<i
                                        class="glyphicon glyphicon-asterisk text-danger"
                                        style="font-size: 11px;"></i></label>

                                <div class="col-md-2">
                                    <ol class="nya-bs-select form-control"
                                        title="Selecione um Backhaul"
                                        id="backhaul"
                                        ng-model="pop.backhaul"
                                        disabled="eventoSelecionado.id == ''"
                                        data-live-search="true"
                                        data-size="8"
                                        required
                                    >
                                        <li nya-bs-option="backhaul in backhauls">
                                            <a>
                                                {{ backhaul }}
                                                <span class="glyphicon glyphicon-ok check-mark"></span>
                                            </a>
                                        </li>
                                    </ol>
                                </div>
                              -->
                                <label class="col-md-2 control-label" for="pai"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        tooltip="Ponto principal de onde o POP cadastrado recebe link"
                                        tooltip-placement="bottom"></i> Pop Pai<i
                                        class="glyphicon glyphicon-asterisk text-danger"
                                        style="font-size: 11px;"></i></label>

                                <div class="col-md-3">
                                    <ol class="nya-bs-select form-control"
                                        title="Selecione um POP Pai"
                                        id="pai"
                                        ng-model="pop.pop_pai"
                                        disabled="eventoSelecionado.id == ''"
                                        data-live-search="true"
                                        data-size="8"
                                        required
                                    >
                                        <li nya-bs-option="pop in pops">
                                            <a>
                                                {{ pop }}
                                                <span class="glyphicon glyphicon-ok check-mark"></span>
                                            </a>
                                        </li>
                                    </ol>
                                    <a ng-click="deselect('pop_pai')" ng-hide="eventoSelecionado.id == ''" class="btn btn-danger btn-xs"><i class="glyphicon glyphicon-trash"></i></a>
                                </div>
                                <label class="col-md-2 control-label" for="pai_backup"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        tooltip="Ponto de contingência ou balanceamento de onde o POP cadastrado recebe link"
                                        tooltip-placement="bottom"></i> Pop Pai Backup</label>

                                <div class="col-md-3">
                                    <ol class="nya-bs-select form-control"
                                        title="(Opcional)"
                                        id="pai_backup"
                                        ng-model="pop.pop_pai_backup"
                                        disabled="eventoSelecionado.id == ''"
                                        data-live-search="true"
                                        data-size="8"
                                    >
                                        <li nya-bs-option="p in pops" ng-class="{disabled: p === pop.pop_pai }">
                                            <a>
                                                {{ p }}
                                                <span class="glyphicon glyphicon-ok check-mark"></span>
                                            </a>
                                        </li>
                                    </ol>
                                    <a ng-click="deselect('pop_pai_backup')" ng-hide="eventoSelecionado.id == ''" class="btn btn-danger btn-xs"><i class="glyphicon glyphicon-trash"></i></a>
                                </div>

                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">

                                <label class="col-md-2 control-label" for="elevacao">Elevação<i
                                        class="glyphicon glyphicon-asterisk text-danger"
                                        style="font-size: 11px;"></i></label>

                                <div class="col-md-2">
                                    <input id="elevacao" type="text" class="form-control input-md" required
                                           ng-model="pop.elevacao" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="infraestrutura">Infraestrutura</label>

                                <div class="col-md-2">
                                    <input id="infraestrutura" type="text" class="form-control input-md"
                                           ng-model="pop.infraestrutura_pop" ng-disabled="eventoSelecionado.id == ''">
                                </div>
<!--
                                <label class="col-md-1 control-label" for="qtdmapas"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        tooltip="Quantidade de telas necessárias para este POP"
                                        tooltip-placement="bottom"></i> Qtd. Mapas</label>
                                <div class="col-md-1">
                                    <select id="qtdmapas" class="form-control input-md"
                                           ng-model="pop.qtdmapas" ng-disabled="eventoSelecionado.id == ''"
                                           ng-init="qtd = [1,2,3,4,5]"
                                           ng-options="o as o for o in qtd">
                                    </select>
                                </div>
                              -->

                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-globe"></i> <strong>Localização</strong><a
                        href="http://maps.google.com/maps?q={{convertLatLon(pop.latitude,pop.longitude)}}&t=k"
                        class="badge pull-right" target="_blank"
                        ng-show="pop.latitude !== undefined && pop.latitude != '' && pop.longitude !== undefined && pop.longitude != ''">Ver
                    mapa</a></div>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-1 control-label" for="latitude">Latitude</label>

                                <div class="col-md-2">
                                    <input id="latitude" type="text" class="form-control input-md"
                                           ng-model="pop.latitude" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="longitude">Longitude</label>

                                <div class="col-md-2">
                                    <input id="longitude" type="text" class="form-control input-md"
                                           ng-model="pop.longitude" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-1 control-label" for="endereco">Endereço</label>

                                <div class="col-md-3">
                                    <input id="endereco" type="text" class="form-control input-md"
                                           ng-model="pop.endereco_pop" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="bairro">Bairro</label>

                                <div class="col-md-2">
                                    <input id="bairro" type="text" class="form-control input-md"
                                           ng-model="pop.bairro_pop" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="cidade">Cidade</label>

                                <div class="col-md-3">
                                    <select class="form-control"
                                            id="cidade"
                                            ng-model="pop.cidade_sigla"
                                            ng-options="cidade.sigla as cidade.cidade for cidade in cidades"
                                            disabled
                                    >
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-earphone"></i> <strong>Contato
                    (Responsável)</strong></div>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-1 control-label" for="contato">Nome Responsável</label>

                                <div class="col-md-2">
                                    <input id="contato" type="text" class="form-control input-md"
                                           ng-model="pop.contato_responsavel" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="telefone_responsavel">Telefone</label>

                                <div class="col-md-2">
                                    <input id="telefone_responsavel" type="text" class="form-control input-md"
                                           ng-model="pop.telefone_responsavel" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-1 control-label" for="endereco_responsavel">Endereço</label>

                                <div class="col-md-3">
                                    <input id="endereco_responsavel" type="text" class="form-control input-md"
                                           ng-model="pop.endereco_responsavel" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="bairro_responsavel">Bairro</label>

                                <div class="col-md-2">
                                    <input id="bairro_responsavel" type="text" class="form-control input-md"
                                           ng-model="pop.bairro_responsavel" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="cidade_responsavel">Cidade</label>

                                <div class="col-md-2">
                                    <input id="cidade_responsavel" type="text" class="form-control input-md"
                                           ng-model="pop.cidade_responsavel" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-flash"></i> <strong>Dados (Energia)</strong>
                </div>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-1 control-label" for="numcliente_energia">Núm. Cliente</label>

                                <div class="col-md-2">
                                    <input id="numcliente_energia" type="text" class="form-control input-md"
                                           ng-model="pop.numcliente_energia" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="numinstalacao_energia">Núm.
                                    Instalação</label>

                                <div class="col-md-2">
                                    <input id="numinstalacao_energia" type="text" class="form-control input-md"
                                           ng-model="pop.numinstalacao_energia"
                                           ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="titular_energia">Titular</label>

                                <div class="col-md-2">
                                    <input id="titular_energia" type="text" class="form-control input-md"
                                           ng-model="pop.titular_energia" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-1 control-label" for="aberturachamado">Abertura Chamado</label>

                                <div class="col-md-2">
                                    <input id="aberturachamado" type="text" class="form-control input-md"
                                           ng-model="pop.aberturachamado" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-folder-close"></i> <strong>Dados (Contrato)</strong>
                </div>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="dataativacao">Data Ativação</label>

                                <div class="col-md-2">
                                    <input id="dataativacao" type="date" class="form-control input-md"
                                           ng-model="pop.dataativacao" ng-disabled="eventoSelecionado.id == ''">
                                </div>
                                <label class="col-md-2 control-label" for="datadesativacao">Data Desativação</label>

                                <div class="col-md-2">
                                    <input id="datadesativacao" type="date" class="form-control input-md"
                                           ng-model="pop.datadesativacao"
                                           ng-disabled="eventoSelecionado.id == ''">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading"><i class="glyphicon glyphicon-info-sign"></i> <strong>Observação</strong>
                </div>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <textarea id="observacao" class="form-control input-md" ng-model="pop.observacao"
                                              ng-disabled="eventoSelecionado.id == ''"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form" ng-show="eventoSelecionado.id != ''">
                <button ng-click="save(pop)" class="btn btn-md btn-primary btn-info" type="submit"
                        ng-disabled="frmPop.$invalid">
                    <i class="glyphicon glyphicon-check"></i> Salvar
                </button>
            </div>
        </form>
    </div>
</div>


<div class="panel" ng-show="!inserindo">

    <ul class="nav nav-tabs">

        <li ng-class="{'active' : colunas.length > 0}" ng-show="colunas.length > 0"><a data-target="#servicos"
                                                                                       data-toggle="tab" style="cursor: pointer;"><i
                class="glyphicon glyphicon-random"></i> Serviços do POP <span
                class="badge">{{colunas.length - 1}}</span></a></li>
        <li ng-show="hosts.length > 0"><a data-target="#hosts" data-toggle="tab" style="cursor: pointer;"><i
                        class="glyphicon glyphicon-tasks"></i> Hosts do POP <span
                        class="badge">{{hosts.length}}</span></a></li>
        <li ng-class="{'active' : colunas.length == 0}"><a data-toggle="tab" data-target="#anexos" style="cursor: pointer;"><i
                class="glyphicon glyphicon-paperclip"></i> Anexos</a></li>
        <li><a data-toggle="tab" data-target="#mapas" style="cursor: pointer;"><i
                        class="glyphicon glyphicon-globe"></i> Mapas</a></li>
    </ul>
</div>

<div class="tab-content" ng-show="!inserindo">
    <div id="servicos" class="tab-pane fade in" ng-class="{'active' : colunas.length > 0}">
        <div class="table-responsive">
            <table class="table table-condensed table-bordered table-hover">
                <thead>
                <tr>
                    <th class="vert-align text-center" ng-repeat="(key, value) in colunas_links">
                        <small><a href="{{value}}" ng-show="key != 'Parâmetro'">{{key}}</a></small>
                        <small ng-show="key == 'Parâmetro'">{{key}}</small>
                    </th>
                    <!-- <th class="vert-align text-center" ng-repeat="coluna in colunas"><small>{{coluna}}</small></th> -->
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="linha in linhas">
                    <td ng-repeat="coluna in colunas">
                        <strong ng-show="coluna == 'Parâmetro'">
                            <small>{{linha[coluna]}}</small>
                        </strong>
                        <small ng-show="coluna != 'Parâmetro' && linha['Parâmetro'] != 'IP'">{{linha[coluna]}}</small>
                        <small ng-show="linha['Parâmetro'] == 'IP' && coluna != 'Parâmetro'"><a
                                href="http://{{linha[coluna]}}:8922" target="_blank">{{linha[coluna]}}</a></small>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="hosts" class="tab-pane fade in">
        <div class="table-responsive">
            <table class="table table-condensed table-bordered table-hover">
                <thead>
                <tr>
                  <th class="vert-align text-center">Host</th>
                  <th class="vert-align text-center">IP</th>
                  <th class="vert-align text-center">Hardware</th>
                  <th class="vert-align text-center">Proprietário</th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="host in hosts">
                    <td><a href="/hosts/{{host.id}}/{{host.host}}">{{host.host}}</a></td>
                    <td>{{host.ip}}</td>
                    <td>{{host.hardware}}</td>
                    <td>{{host.modooperacao}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="anexos" class="tab-pane fade in" ng-class="{'active' : colunas.length == 0}">
        <div ng-include="'app/pops/anexos/anexosViewList.html'"></div>
    </div>

    <div id="mapas" class="tab-pane fade in">
      <div class="panel">
         <button type="button" class="btn btn-success" data-toggle="modal" data-target="#frmmapa" data-placement="top" rel="tooltip" ng-show="eventoSelecionado.id != ''"><i class="glyphicon glyphicon-plus"></i> Inserir tela do mapa...</button>
      </div>

      <table class="table table-condensed table-hover table-bordered" style="width:580px;">
        <thead>
          <tr>
            <th class="vert-align text-center">Identificador do mapa</th>
            <th class="vert-align text-center">Nome do mapa</th>
            <th class="vert-align text-center" ng-show="eventoSelecionado.id != ''">Ação</th>
          </tr>
        </thead>
        <tbody>
         <tr ng-repeat="mapa in mapas | orderBy:'identificador' track by mapa.identificador">
           <td class="vert-align text-center">{{mapa.identificador}}</td>
           <td class="vert-align text-center">{{mapa.mapa}}</td>
           <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''"><a class="btn btn-danger btn-sm"  ng-really-message="Tem certeza que deseja excluir este mapa ?" ng-really-click="removeMapa(mapa)" title="Excluir"><i class="glyphicon glyphicon-remove"></i></a>
             <a href="http://lab.pocos-net.com.br/maps.php?sysmapid={{mapa.sysmapid}}" target="_blank" class="btn btn-default btn-sm" title="Visualizar Mapa" ng-class="{'disabled' : mapa.sysmapid == ''}"><i class="glyphicon glyphicon-eye-open"></i></a>
             <a href="http://lab.pocos-net.com.br/sysmap.php?sysmapid={{mapa.sysmapid}}" target="_blank" class="btn btn-default btn-sm" title="Editar Mapa" ng-class="{disabled : mapa.sysmapid == ''}" ng-show="eventoSelecionado.id != ''"><i class="glyphicon glyphicon-globe"></i></a>
           </td>

         </tr>
       </tbody>
     </table>
    </div>
    <div class="modal" id="frmmapa" tabindex="-1" role="dialog"
 aria-labelledby="frmmapalabel" aria-hidden="true" modal="showModal" close="cancel()">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close"
               data-dismiss="modal">
                   <span aria-hidden="true">&times;</span>
                   <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title" id="frmparametroslabel">
                Mapa
            </h4>
        </div>
        <div class="modal-body">
            <form role="form" name="frmmapa">
              <div class="form-group">
              <label for="identificador">Identificador</label>
              <select
                class="form-control"
                ng-model="novomapa.identificador"
                ng-init="identificadores = [2,3,4,5]"
                ng-options="o as o for o in identificadores"></select>
              </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default"
                    data-dismiss="modal">
                        Fechar
            </button>
            <button type="button" class="btn btn-primary" ng-click="saveMapa(novomapa);" ng-disabled="frmmapa.$invalid" data-dismiss="modal">
                Salvar</button>
        </div>
    </div>
  </div>
</div>

</div>
