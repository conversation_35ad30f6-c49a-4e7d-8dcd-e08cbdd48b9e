<nav class="navbar navbar-default navbar-fixed-top" ng-if="MC.mobile==false">
  <div class="container-fluid">
    <div class="navbar-header">

      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main-navbar"
        aria-expanded="false" aria-controls="navbar">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>

      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
        aria-expanded="false" aria-controls="navbar">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>

      <a class="navbar-brand" href="/dashboard"><i class="glyphicon glyphicon-cloud" style="top:3px;"></i> NOC</a>
    </div>
    <div id="navbar" class="navbar-collapse collapse">
      <ul class="nav navbar-nav navbar-left" ng-if="currentPath!='login'">
        <li authorize="['dashboard.read', 'dashboard.write']" ng-class="{'active': currentPath=='dashboard'}"><a
            href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
        <li authorize="['eventos.read', 'eventos.write']" ng-class="{'active': currentPath=='eventos'}"><a
            href="/eventos"><i class="glyphicon glyphicon-calendar"></i> Eventos </a></li>
        <li authorize="['ferramentas.read', 'ferramentas.write']" class="dropdown"
          ng-class="{'active': currentPath=='ferramentas'}">
          <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
              class="glyphicon glyphicon-wrench"></i> Ferramentas <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="/ferramentas/systemlog"><i class="glyphicon glyphicon-cog"></i> System Log</a></li>
            <li authorize="['develop.write']"><a href="/network/bgpfilters"><i class="glyphicon glyphicon-filter"></i>
                Filtros BGP</a></li>
            <li authorize="['ferramentas.write', 'develop.read']"><a href="/network/cgnat"><i class="glyphicon glyphicon-random"></i>
                Remoção de CGNAT</a></li>
            <li authorize="['ferramentas.write', 'develop.read']"><a href="/ftth/signal-collect"><i class="glyphicon glyphicon-signal"></i>
                Coleta de sinal ONUs</a></li>
            <li authorize="['ferramentas.write', 'ferramentas.read', 'develop.read']"><a href="/ferramentas/provision"><i class="glyphicon glyphicon-cloud-upload"></i> Serviço de
                Provisionamento</a></li>
            <!--<li authorize="['ferramentas.write', 'ferramentas.read', 'develop.read']"><a href="/ferramentas/auth-workers"><i class="glyphicon glyphicon-cloud-upload"></i> Serviço de
                  Provisionamento (Workers)</a></li>    -->
            <li authorize="['concentrador.read', 'concentrador.write']"><a href="/ferramentas/blacklist"><i class="glyphicon glyphicon-eye-close"></i> OLT Blacklist</a></li>
            <li authorize="['concentrador.read', 'concentrador.write']"><a href="/ferramentas/zteadmin"><i class="glyphicon glyphicon-modal-window"></i> ZTE Admin</a></li>
          </ul>
        </li>
        <li><a href="https://noc.telemidia.net.br/projetos" target="_blank"><i class="glyphicon glyphicon-list-alt"></i>
            Projetos </a></li>


      </ul>
      <ul class="nav navbar-nav navbar-right" ng-if="currentPath!='login'">
        <li class="dropdown" authorize="['auxiliares.write']">
          <a class="dropdown-toggle" data-toggle="dropdown" aria-expanded="true"><i
              class="glyphicon glyphicon-bell"></i> <small><span class="label info-number">{{
                MC.alertas.total}}</span></small></a>
          <ul class="dropdown-menu alert-dropdown">
            <li ng-repeat="alerta in MC.alertas.dados">
              <a href="" ng-href="{{ alerta.url}}"><span class="glyphicon {{ alerta.icon}}"></span> {{ alerta.registro}}
                <span class="label label-warning">{{ alerta.regra}}</span></a>
            </li>

            <li class="divider"></li>
            <li>
              <a href="/alertas">Exibir Todos</a>
            </li>
          </ul>
        </li>
        <li class="dropdown" ng-if="currentPath!='login'">
          <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
              class="glyphicon glyphicon-user"></i> {{ operador.username}} <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="" ng-click="MC.logout()"> Sair</a></li>
          </ul>
        </li>
        <li class="dropdown" ng-if="currentPath!='login'" authorize="develop.write">
          <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
              class="glyphicon glyphicon-console"></i> <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="/develop/logs"><i class="glyphicon glyphicon-exclamation-sign"></i> Logs de Erros</a></li>
          </ul>
        </li>

      </ul>

    </div>
  </div>

</nav>
<nav class="navbar sub-nav-laranja navbar-fixed-top" ng-if="currentPath!='login' && MC.mobile==false">
</nav>
<nav class="navbar navbar-inverse sub-nav navbar-collapse ng-scope collapse" id="main-navbar" role="navigation"
  ng-if="MC.mobile==false">
  <div class="container-fluid">
    <!--
    <div class="pull-right" style="padding-top: 6px;
  padding-bottom: 6px;padding-left:10px;">
      <a href="" data-toggle="modal" data-target="#frmabout"><span app-version="" class="label label-info"></span></a>
    </div>-->
    <ul class="nav navbar-nav navbar-left" style="margin-left:5px;" ng-if="currentPath!='login'" role="navigation">

      <li authorize="['eventos.read', 'eventos.write']" ng-class="{'active': currentPath=='notas'}"><a href="/notas"
          style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-tags"></i> Notas</a></li>
      <li authorize="['pops.read', 'pops.write']" ng-class="{'active': currentPath=='pops'}"><a href="/pops"
          style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-record"></i> POPs</a></li>
      <li authorize="['pops.read', 'pops.write']" ng-class="{'active': currentPath=='dedicados'}"><a href="/dedicados"
          style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-map-marker"></i> Dedicados</a>
      </li>
      <li authorize="['hosts.read', 'hosts.write']" ng-class="{'active': currentPath=='hosts'}"><a href="/hosts"
          style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-tasks"></i> Hosts</a></li>
      <li authorize="['servicos.read', 'servicos.write']" ng-class="{'active': currentPath=='servicos'}"><a
          href="/servicos" style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-random"></i>
          Serviços</a></li>
      <li authorize="['enlaces.read', 'enlaces.write']" ng-class="{'active': currentPath=='enlaces'}"><a href="/enlaces"
          style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-link"></i> Enlaces</a></li>


      <li authorize="['concentrador.read', 'concentrador.write', 'auxiliares.read', 'auxiliares.write', 'marketing.read', 'marketing.write']"
        class="dropdown" ng-class="{'active': currentPath=='relatorios'}">
        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-move"></i> Relatórios <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="/relatorios/capacidade"><i class="glyphicon glyphicon-stats"></i> Capacidade de APs</a></li>
          <li><a href="/relatorios/pppoe"><i class="glyphicon glyphicon-import"></i> Sessões PPPoE</a></li>
          <li><a href="/relatorios/velocidade"><i class="glyphicon glyphicon-dashboard"></i> Medições de Velocidade</a>
          </li>
          <li authorize="['relatorios.read', 'relatorios.write']"><a href="/relatorios/ipfixo"><i
                class="glyphicon glyphicon-list"></i> Reservas de IP</a></li>
          <li authorize="['comercial.read', 'comercial.write']"><a href="/comercial/prospects"><i
                class="glyphicon glyphicon-calendar"></i> Prospects</a></li>
          <li authorize="['comercial.read', 'comercial.write']"><a href="/comercial/pedidos"><i
                class="glyphicon glyphicon-check"></i> Pedidos</a></li>
          <li authorize="['relatorios.read', 'relatorios.write']"><a href="" ng-click="MC.download_clientesap()"><i
                class="glyphicon glyphicon-list"></i> Clientes por AP</a></li>
          <li authorize="['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']"><a
              href="/relatorios/capacidade-olts"><i class="glyphicon glyphicon-list"></i> Capacidade dos OLTs</a></li>
          <li authorize="['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']"><a href="/relatorios/ordens-servico"><i
                class="glyphicon glyphicon-list"></i> Ordens de serviço</a></li>
          <li authorize="['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']"><a href="/relatorios/auditorias-os"><i
                class="glyphicon glyphicon-list"></i> Auditorias de OS</a></li>
          <li authorize="['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']"><a href="/relatorios/contratos-cancelados"><i
                class="glyphicon glyphicon-ban-circle"></i> Contratos cancelados</a></li>
          <li authorize="['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']"><a href="/relatorios/pedidos-reservados"><i
                class="glyphicon glyphicon-lock"></i> Pedidos reservados</a></li>

          <li role="separator" class="divider"></li>

          <li><a href="/relatorios/personalizados"><i class="glyphicon glyphicon-cog"></i> Personalizados</a></li>

          <!-- <li authorize="['suporte.read', 'suporte.write']"><a href="/helpdesk/analises"><i class="glyphicon glyphicon-phone"></i> Análises Assistidas</a></li> -->
        </ul>
      </li>

      <li authorize="['materiais.read', 'materiais.write']" class="dropdown"
        ng-class="{'active': currentPath=='materiais'}">
        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-book"></i> Materiais <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="/materiais/patrimonios"><i class="glyphicon glyphicon-barcode"></i> Patrimônios</a></li>
          <li><a href="/materiais/romaneios"><i class="glyphicon glyphicon-transfer"></i> Romaneios</a></li>
        </ul>
      </li>
      <li authorize="['graficos.read', 'graficos.write']" ng-class="{'active': currentPath=='graficos'}"><a
          href="/graficos" style="padding-top: 5px; padding-bottom: 5px;"><i class="glyphicon glyphicon-signal"></i>
          Gráficos</a></li>

      <li authorize="['comercial.read', 'comercial.write']" class="dropdown"
        ng-class="{'active': currentPath=='comercial'}">
        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-usd"></i> Comercial <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="/comercial/pedidos"><i class="glyphicon glyphicon-pencil"></i> Pedidos</a></li>
          <li authorize="['gerencia.write']"><a href="/comercial/planos"><i
                class="glyphicon glyphicon-shopping-cart"></i>
              Planos</a></li>
          <li authorize="['gerencia.write']"><a href="/comercial/grupos-vendedores"><i
                class="glyphicon glyphicon-usd"></i>
              Grupos de vendedores</a></li>
            <li><a href="/comercial/campanhas"><i class="glyphicon glyphicon-bullhorn"></i> Campanhas de Marketing</a></li>
            <li><a href="/comercial/sky"><i class="glyphicon glyphicon-hd-video"></i> SKY+</a></li>
        </ul>
      </li>


      <li authorize="['gerencia.read', 'gerencia.write']" class="dropdown"
        ng-class="{'active': currentPath=='gerencia'}">

        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-briefcase"></i> Gerência <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="/gerencia/desempenho-tecnicos"><i class="glyphicon glyphicon-stats"></i> Análise de desempenho: Técnicos de Campo</a>
          </li>
          <li><a href="/gerencia/desempenho-comercial"><i class="glyphicon glyphicon-stats"></i> Análise de desempenho: Comercial</a>
          </li>
          <li><a href="/gerencia/mailing"><i class="glyphicon glyphicon-envelope"></i> Mailing</a></li>
        </ul>
      </li>

      <li authorize="['concentrador.read', 'concentrador.write', 'comercial.read', 'comercial.write']" class="dropdown">
        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-edit"></i> Atendimento <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li authorize="['concentrador.read', 'concentrador.write']"><a href="/helpdesk"><i
                class="glyphicon glyphicon-warning-sign"></i> Helpdesk</a></li>

          <li authorize="['concentrador.read', 'concentrador.write', 'comercial.read', 'comercial.write']"><a
              href="/emails"><i class="glyphicon glyphicon-envelope"></i>
              Gerenciar e-mails</a></li>

          <li authorize="['comercial.read', 'comercial.write']"><a href="/comercial/cobertura"><i
                class="glyphicon glyphicon-usd"></i> Comercial</a></li>
        </ul>
      </li>

      <li authorize="['comercial.write']" class="dropdown">
        <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
            class="glyphicon glyphicon-pencil"></i> Cadastros <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li authorize="['comercial.write']"><a href="/comercial/logradouros"><i class="glyphicon glyphicon-road"></i>
              Logradouros (Substituições)</a></li>
          <li authorize="['wallpaper.write']"><a href="/gerencia/ramais"><i class="glyphicon glyphicon-earphone"></i>
              Ramais (wallpaper)</a></li>
        </ul>
      </li>


    </ul>
  </div>
</nav>
<nav class="alert sub-nav-evento"
  ng-class="{'alert-success' : eventoSelecionado.id != '', 'alert-warning' : eventoSelecionado.id == ''}"
  role="navigation" ng-if="currentPath!='login' && MC.mobile==false">

  <div class="container-fluid pull-right">
    <div style="padding:3px; color: #c09853;" ng-show="eventoSelecionado.id == ''" authorize="['eventos.write']">Você
      não está trabalhando em nenhum <b>evento</b>. Clique <a href="/eventos" class="alert-link">aqui</a> para
      selecionar um evento.</span></div>
    <div class="dropdown" ng-show="eventoSelecionado.id != ''" style="margin-left:95px;padding:3px; color: #468847">
      <i class="glyphicon glyphicon-check"></i><strong> Você está trabalhando</strong> no evento
      <span class="dropdown">
        <a href="" class="dropdown-toggle" data-toggle="dropdown" style="color: #356635;" aria-haspopup="true"
          aria-expanded="false"><span class="label label-success">#{{ eventoSelecionado.id}}</span>
          {{ eventoSelecionado.descricao}}
          <!--[{{eventoSelecionado.categoria}}--><span class="caret"></span>
        </a>

        <ul class="dropdown-menu multi-level">
          <!-- Ainda não implementado
    <li class="dropdown-header">Tarefas ({{MC.eventostarefasresumo.ok}}/{{MC.eventostarefasresumo.total}})<div class="progress">
  <div class="progress-bar progress-bar-success" style="width: {{MC.eventostarefasresumo.perc}}%"></div>
</div></li>
    <li ng-repeat="tarefa in MC.eventostarefas" class="dropdown-submenu"><a href="{{tarefa.link}}">
      <i class="glyphicon" ng-class="{
        'glyphicon-remove-circle text-danger': tarefa.status=='pendente',
        'glyphicon-ok-circle text-success': tarefa.status=='ok',
        'glyphicon-info-sign text-warning': tarefa.status=='alerta',
      }"></i> {{tarefa.tarefa}}</a>
      <ul class="dropdown-menu">
          <li class="dropdown-header">Alertas desta tarefa ({{tarefa.alertas.length}})</li>
          <li ng-repeat="alerta in tarefa.alertas"><a tabindex="-1" href="#">{{alerta.registro}} <span class="label label-warning">{{alerta.regra}}</span></a></li>
      </ul>
    </li>



   <li role="separator" class="divider"></li>
   -->
          <li><a href="/eventos/{{ eventoSelecionado.id}}"><i class="glyphicon glyphicon-search"></i> Visualizar
              Evento</a></li>
          <li><a href="" ng-click="MC.sair()"><i class="glyphicon glyphicon-new-window"></i> Sair do evento</a></li>
        </ul>
      </span>
    </div>

  </div>
</nav>

<div ng-include="'app/basicos/about.html'" ng-if="MC.mobile==false"></div>

<!--
    <nav class="navbar navbar-default navbar-fixed subnav">
      <div class="container-fluid">
        <div id="navbar" class="navbar-collapse collapse">
        </div>
      </div>
    </nav>
       -->


<nav class="navbar navbar-default navbar-fixed-top" ng-if="MC.mobile==true">

  <div class="container-fluid">

    <div class="navbar-header">
      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
        aria-expanded="false" aria-controls="navbar">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <a class="navbar-brand" href="/comercial/cobertura"><i class="glyphicon glyphicon-cloud" style="top:3px;"></i>
        NOC</a>

    </div>
    <div id="navbar" class="navbar-collapse collapse">

      <ul class="nav navbar-nav navbar-left" ng-if="currentPath!='login'">
        <li authorize="['terceiro.write']"><a href="/comercial/prospects/externo"><i
              class="glyphicon glyphicon-home"></i> Prospects</a></li>

      </ul>

      <ul class="nav navbar-nav navbar-right" ng-if="currentPath!='login'">
        <li class="dropdown" ng-if="currentPath!='login'">
          <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i
              class="glyphicon glyphicon-user"></i> {{ operador.username}} <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="" ng-click="MC.logout()"> Sair</a></li>
          </ul>
        </li>
      </ul>

    </div>
  </div>

</nav>
