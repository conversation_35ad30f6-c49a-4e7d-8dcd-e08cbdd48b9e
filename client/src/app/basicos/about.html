<div class="modal" id="frmabout" tabindex="-1" role="dialog"
     aria-labelledby="frmaboutlabel" aria-hidden="true" modal="showModal" close="cancel()">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close"
                        data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmaboutlabel">
                    <i class="glyphicon glyphicon-info-sign"></i> Sobre
                </h4>
            </div>
            <div class="modal-body">
              <p>Versão: <strong><span app-version=""></span></strong></p>
              <div class="pre-scrollable">
                  <!-- git log --pretty="%cd | <span style='color: #c09853;'>%h</span> - %s<br>" --date=format:'%d/%m/%Y %H:%M:%S' -->
                  16/11/2018 10:16:06 | <span style='color: #c09853;'>8be7f6b</span> - Merge branch 'feature/ftth-api' into develop<br>
16/11/2018 10:15:43 | <span style='color: #c09853;'>5972faf</span> - Implementada nova interface de atendimento Helpdesk<br>
23/10/2018 10:22:37 | <span style='color: #c09853;'>d0ff4ac</span> - Merge branch 'feature/ftth-api' into develop<br>
23/10/2018 10:22:19 | <span style='color: #c09853;'>2730227</span> - corrigido exibicao da fila<br>
16/10/2018 09:57:30 | <span style='color: #c09853;'>2173331</span> - Merge branch 'feature/ftth-api' into develop<br>
16/10/2018 09:57:06 | <span style='color: #c09853;'>e330c01</span> - versao de testes do modulo de liberação ftth<br>
05/10/2018 08:59:38 | <span style='color: #c09853;'>33b9310</span> - resolvido conflitos<br>
05/10/2018 08:51:42 | <span style='color: #c09853;'>47fd186</span> - Merge branch 'feature/helpdeskpesquisamac' into develop<br>
05/10/2018 08:51:18 | <span style='color: #c09853;'>543ba90</span> - implementado busca por mac em helpdesk<br>
04/10/2018 16:48:22 | <span style='color: #c09853;'>cbc33d9</span> - funcoes de liberacao de onus implementadas<br>
27/09/2018 13:40:48 | <span style='color: #c09853;'>55536f2</span> - Merge branch 'bugfix/getanminfo' into feature/ftth-api<br>
27/09/2018 13:40:00 | <span style='color: #c09853;'>0853523</span> - Merge branch 'bugfix/getanminfo' into develop<br>
27/09/2018 13:37:40 | <span style='color: #c09853;'>1af1a7b</span> - corrigida consulta das informações de olts<br>
27/09/2018 12:21:25 | <span style='color: #c09853;'>f243925</span> - Merge branch 'feature/ampliacaogooglemapsenderecos' into feature/ftth-api<br>
27/09/2018 12:21:13 | <span style='color: #c09853;'>6ff3830</span> - Merge branch 'feature/ampliacaogooglemapsenderecos' into develop<br>
27/09/2018 12:20:52 | <span style='color: #c09853;'>83c9b22</span> - ampliado raio da busca na API do google para pops<br>
25/09/2018 15:58:58 | <span style='color: #c09853;'>c83e200</span> - Merge branch 'feature/vlanservicos' into develop<br>
25/09/2018 15:58:40 | <span style='color: #c09853;'>a401d32</span> - adicionado campo vlanid em hosts_servicos<br>
17/09/2018 16:56:43 | <span style='color: #c09853;'>07352fd</span> - Merge branch 'feature/ipv6info' into develop<br>
17/09/2018 16:56:26 | <span style='color: #c09853;'>934c3b4</span> - alterado rotulos ipv4 ipv6<br>
17/09/2018 14:57:41 | <span style='color: #c09853;'>0ba6ea2</span> - Merge branch 'hotfix/anmlogout' into develop<br>
17/09/2018 14:57:05 | <span style='color: #c09853;'>039eb3f</span> - logout no anm server<br>
17/09/2018 14:34:33 | <span style='color: #c09853;'>f39176d</span> - Merge branch 'feature/ipv6info' into develop<br>
17/09/2018 14:34:07 | <span style='color: #c09853;'>22e26b8</span> - adicionado informações de ipv6<br>
11/09/2018 14:51:27 | <span style='color: #c09853;'>f19fd64</span> - Merge branch 'hotfix/integracaounm' into develop<br>
11/09/2018 14:45:34 | <span style='color: #c09853;'>7f1237e</span> - Merge branch 'hotfix/integracaounm'<br>
11/09/2018 14:45:08 | <span style='color: #c09853;'>6099991</span> - Alterado IP server UNM2000<br>
30/08/2018 17:22:50 | <span style='color: #c09853;'>a19b33a</span> - Merge branch 'bugfix/retornoapilogin' into develop<br>
30/08/2018 17:22:35 | <span style='color: #c09853;'>8f2b334</span> - Merge branch 'feature/helpdesksenhauser' into develop<br>
30/08/2018 17:21:59 | <span style='color: #c09853;'>2a340af</span> - corrigido retorno da api na rota login<br>
30/08/2018 17:20:43 | <span style='color: #c09853;'>109885f</span> - implementado exibição da senha em atendimento helpdesk<br>
27/08/2018 16:32:41 | <span style='color: #c09853;'>8349ca4</span> - Merge branch 'release/1.5.9'<br>
27/08/2018 16:32:18 | <span style='color: #c09853;'>09fbc14</span> - Merge branch 'release/1.5.9' into develop<br>
27/08/2018 16:31:50 | <span style='color: #c09853;'>39cccd4</span> - bump versão 1.5.9<br>
27/08/2018 16:10:34 | <span style='color: #c09853;'>df48c2a</span> - Merge branch 'feature/historicofiltrospgb' into develop<br>
27/08/2018 16:10:08 | <span style='color: #c09853;'>bfa86a6</span> - implementado filtro por timestamp<br>
24/08/2018 09:55:34 | <span style='color: #c09853;'>5eb1b78</span> - Merge branch 'bugfix/backups' into develop<br>
24/08/2018 09:55:17 | <span style='color: #c09853;'>c6d6640</span> - corrigido retorno da função FileSizeConvert para arquivos nulos<br>
20/08/2018 10:28:17 | <span style='color: #c09853;'>40941af</span> - Merge branch 'bugfix/bgplistfilters' into develop<br>
20/08/2018 10:26:35 | <span style='color: #c09853;'>00a2297</span> - query truncada<br>
17/08/2018 14:36:13 | <span style='color: #c09853;'>7da816d</span> - Merge branch 'feature/bgpetiquetas' into develop<br>
17/08/2018 14:19:50 | <span style='color: #c09853;'>1bf2507</span> - etiquetas de action<br>
17/08/2018 11:54:18 | <span style='color: #c09853;'>ba72495</span> - Merge branch 'hotfix/paginacaobgp' into develop<br>
17/08/2018 11:53:48 | <span style='color: #c09853;'>df75be5</span> - corrigida paginação em filtros bgp<br>
17/08/2018 11:33:56 | <span style='color: #c09853;'>87c4ecb</span> - Merge branch 'feature/corbgp' into develop<br>
17/08/2018 11:33:29 | <span style='color: #c09853;'>862255c</span> - linha vermelha em filtros bgp desabilitados<br>
17/08/2018 11:16:45 | <span style='color: #c09853;'>bfabd7c</span> - Merge branch 'release/1.5.8'<br>
17/08/2018 11:16:33 | <span style='color: #c09853;'>9d094a6</span> - Merge branch 'release/1.5.8' into develop<br>
17/08/2018 11:16:15 | <span style='color: #c09853;'>59188d8</span> - faltando ; em analises.services<br>
17/08/2018 10:51:30 | <span style='color: #c09853;'>9590ee6</span> - Merge branch 'release/1.5.8'<br>
17/08/2018 10:50:32 | <span style='color: #c09853;'>460594c</span> - Merge branch 'release/1.5.8' into develop<br>
17/08/2018 10:47:43 | <span style='color: #c09853;'>0574c32</span> - Bump versão 1.5.8<br>
                  17/08/2018 10:37:43 | <span style='color: #c09853;'>6bb7eaa</span> - Merge branch 'feature/bgpfilters' into develop<br>
                  17/08/2018 10:37:25 | <span style='color: #c09853;'>e4a3327</span> - Alterado ícone e posição do menu Filtros BGP<br>
                  17/08/2018 10:30:15 | <span style='color: #c09853;'>6c05e27</span> - Adicionado campo set bgp communities<br>
                  16/08/2018 17:42:02 | <span style='color: #c09853;'>f085be8</span> - Incluida opção de operador 'pertence' ao filtro prefix<br>
                  16/08/2018 16:55:39 | <span style='color: #c09853;'>d91c703</span> - Multi filtros na interface<br>
                  16/08/2018 13:39:54 | <span style='color: #c09853;'>cb42181</span> - Merge branch 'feature/apimkmonitoramento' into feature/bgpfilters<br>
                  15/08/2018 15:55:27 | <span style='color: #c09853;'>eeda7b3</span> - Implementada API para filtros BGP<br>
                  13/08/2018 10:23:30 | <span style='color: #c09853;'>d04bbaa</span> - Resolução de conflitos<br>
                  13/08/2018 10:21:31 | <span style='color: #c09853;'>5c33970</span> - Merge branch 'feature/autoteste' into develop<br>
                  13/08/2018 10:21:05 | <span style='color: #c09853;'>c62fc7e</span> - Implementado módulo para listagem de filtros BGP<br>
                  10/08/2018 09:23:11 | <span style='color: #c09853;'>00e2eaa</span> - Pesquisa por ip em analises<br>
                  09/08/2018 14:30:51 | <span style='color: #c09853;'>b457904</span> - Corrigido gravacao de log de erro<br>
                  08/08/2018 17:25:26 | <span style='color: #c09853;'>2823753</span> - Implementado módulo de análises assistidas<br>
                  07/08/2018 14:20:45 | <span style='color: #c09853;'>e9c720a</span> - Merge branch 'hotfix/addpatrimonioredes' into develop<br>
                  06/08/2018 16:54:27 | <span style='color: #c09853;'>a34b263</span> - Insere mac sem : no banco<br>
                  06/08/2018 11:00:33 | <span style='color: #c09853;'>9b54c74</span> - Merge branch 'hotfix/chaveapi' into develop<br>
                  06/08/2018 10:57:12 | <span style='color: #c09853;'>8f0a60a</span> - Merge branch 'hotfix/chaveapi'<br>
                  06/08/2018 10:56:38 | <span style='color: #c09853;'>4def081</span> - Atualizada chave da API do Google<br>
                  28/06/2018 14:04:43 | <span style='color: #c09853;'>124bfb1</span> - Merge branch 'master' of http://git.telemidia.net.br/Telemidia/service-noc<br>
                  28/06/2018 14:04:14 | <span style='color: #c09853;'>01d86f5</span> - Merge branch 'hotfix/atendimentocomercial'<br>
                  28/06/2018 14:03:50 | <span style='color: #c09853;'>485d577</span> - Não permitir verificar cobertura com endereço não localizado no Google<br>
                  19/06/2018 08:44:53 | <span style='color: #c09853;'>486d899</span> - Merge branch 'hotfix/notas' of Telemidia/service-noc into master<br>
                  19/06/2018 08:44:12 | <span style='color: #c09853;'>1528e6e</span> - adicionado prefixo fdwtables<br>
                  04/06/2018 11:38:33 | <span style='color: #c09853;'>baeaa96</span> - Merge branch 'hotfix/prefixos' into develop<br>
                  04/06/2018 11:37:54 | <span style='color: #c09853;'>47edb35</span> - Merge branch 'hotfix/prefixos'<br>
                  04/06/2018 11:37:24 | <span style='color: #c09853;'>252a3e1</span> - Alterado schema da tabela prefixos<br>
                  04/06/2018 09:20:50 | <span style='color: #c09853;'>0bf2141</span> - Merge branch 'bugfix/desempenho'<br>
                  04/06/2018 09:20:33 | <span style='color: #c09853;'>fea3fa7</span> - Merge branch 'bugfix/desempenho' into develop<br>
                  04/06/2018 09:19:57 | <span style='color: #c09853;'>951d0e4</span> - Retiradas opções de Mudança de Plano e Endereço<br>
                  25/05/2018 09:34:15 | <span style='color: #c09853;'>c7aed77</span> - Merge branch 'feature/noclog' into develop<br>
                  25/05/2018 09:33:58 | <span style='color: #c09853;'>3954130</span> - Permissões alteradas para acesso ao log<br>
                  25/05/2018 09:30:00 | <span style='color: #c09853;'>0380a1b</span> - Merge branch 'bugfix/desempenho' into develop<br>
                  25/05/2018 09:29:47 | <span style='color: #c09853;'>bba7d8e</span> - Estrutura de consultas corrigidas<br>
                  23/05/2018 11:55:00 | <span style='color: #c09853;'>7a23ed2</span> - Merge branch 'hotfix/streetview' into develop<br>
                  23/05/2018 11:50:43 | <span style='color: #c09853;'>393a83c</span> - Merge branch 'hotfix/streetview'<br>
                  23/05/2018 11:50:21 | <span style='color: #c09853;'>b672d1e</span> - Corrigido bug de controle do zoom devido a atualizacao da API<br>
                  09/05/2018 09:50:10 | <span style='color: #c09853;'>895ecb5</span> - Merge branch 'hotfix/bindad' into develop<br>
                  09/05/2018 09:49:51 | <span style='color: #c09853;'>6e83e81</span> - Merge branch 'hotfix/bindad'<br>
                  09/05/2018 09:49:10 | <span style='color: #c09853;'>fa0147f</span> - Alterado nome para bind no servidor AD<br>
                  02/05/2018 12:35:05 | <span style='color: #c09853;'>d40e134</span> - Merge branch 'hotfix/cdmsativos' into develop<br>
                  02/05/2018 12:34:44 | <span style='color: #c09853;'>0684781</span> - Merge branch 'hotfix/cdmsativos'<br>
                  02/05/2018 12:34:24 | <span style='color: #c09853;'>ddb5896</span> - Consulta apenas CDMs ativos<br>
                  30/04/2018 09:31:43 | <span style='color: #c09853;'>59b2b72</span> - Merge branch 'feature/logconsultacobertura' into develop<br>
                  30/04/2018 09:31:27 | <span style='color: #c09853;'>dee07f7</span> - Merge branch 'feature/logconsultacobertura'<br>
                  30/04/2018 09:31:02 | <span style='color: #c09853;'>003b82b</span> - Gravação de log de consultas<br>
                  26/04/2018 11:07:08 | <span style='color: #c09853;'>2e0d1b8</span> - Merge branch 'feature/melhoriaconsultacdm'<br>
                  26/04/2018 11:06:27 | <span style='color: #c09853;'>4be2f6e</span> - Merge branch 'feature/melhoriaconsultacdm' into develop<br>
                  26/04/2018 11:05:54 | <span style='color: #c09853;'>f89a8d0</span> - Pesquisa utilizando place_id<br>
                  24/04/2018 09:23:04 | <span style='color: #c09853;'>d1ec9f6</span> - Merge branch 'hotfix/camponumero' into develop<br>
                  24/04/2018 09:22:46 | <span style='color: #c09853;'>4ac2885</span> - Merge branch 'hotfix/camponumero'<br>
                  24/04/2018 09:22:19 | <span style='color: #c09853;'>94982d8</span> - Campo numero só permite inteiro<br>
                  19/04/2018 15:14:27 | <span style='color: #c09853;'>69bdd00</span> - Merge branch 'hotfix/streetview' into develop<br>
                  19/04/2018 15:14:08 | <span style='color: #c09853;'>ac4c17f</span> - Merge branch 'hotfix/streetview'<br>
                  19/04/2018 15:13:33 | <span style='color: #c09853;'>da55095</span> - Corrigida exibição do Street View quando é alternado para apenas mapa<br>
                  19/04/2018 14:53:44 | <span style='color: #c09853;'>1ee074c</span> - Merge branch 'hotfix/streetview' into develop<br>
                  19/04/2018 14:53:19 | <span style='color: #c09853;'>ac7a87e</span> - Merge branch 'hotfix/streetview'<br>
                  19/04/2018 14:53:00 | <span style='color: #c09853;'>09dc7f0</span> - Corrigida exibição do Street View<br>
                  16/04/2018 14:21:53 | <span style='color: #c09853;'>b697037</span> - Merge branch 'hotfix/formulariocomercial'<br>
                  16/04/2018 14:21:36 | <span style='color: #c09853;'>a910f4a</span> - Merge branch 'hotfix/formulariocomercial' into develop<br>
                  16/04/2018 14:21:15 | <span style='color: #c09853;'>7f5569d</span> - Informações corrigidas e aumento da margem 5G para 10m<br>
                  16/04/2018 08:48:46 | <span style='color: #c09853;'>bdb56c0</span> - Merge branch 'hotfix/formulariocomercial'<br>
                  16/04/2018 08:48:19 | <span style='color: #c09853;'>6ea0004</span> - Merge branch 'hotfix/formulariocomercial' into develop<br>
                  16/04/2018 08:47:52 | <span style='color: #c09853;'>c41e6d5</span> - Corrigida mensagem de disponibilidade de cobertura<br>
                  13/04/2018 16:55:35 | <span style='color: #c09853;'>110ccf8</span> - Merge branch 'hotfix/formulariocomercial' into develop<br>
                  13/04/2018 16:54:45 | <span style='color: #c09853;'>ebef51e</span> - Merge branch 'hotfix/formulariocomercial'<br>
                  13/04/2018 16:54:23 | <span style='color: #c09853;'>6b32ac8</span> - Corrigido alerta para cdm radio em area fibra e campos obrigatorios<br>
                  12/04/2018 10:48:03 | <span style='color: #c09853;'>6814071</span> - Merge branch 'release/1.5.3'<br>
                  12/04/2018 10:47:37 | <span style='color: #c09853;'>deaf3db</span> - Merge branch 'release/1.5.3' into develop<br>
                  12/04/2018 10:47:16 | <span style='color: #c09853;'>29934f5</span> - Bump versão 1.5.3<br>
                  12/04/2018 10:43:15 | <span style='color: #c09853;'>9ec6bd7</span> - Merge branch 'feature/correcaoenderecos' into develop<br>
12/04/2018 10:36:17 | <span style='color: #c09853;'>b69364d</span> - Implementado modulo para correção de logradouros<br>
10/04/2018 15:45:20 | <span style='color: #c09853;'>d5ae0f5</span> - Merge branch 'release/v1.5.2' into develop<br>
10/04/2018 15:44:59 | <span style='color: #c09853;'>adaddb4</span> - Adicionado alerta para criação de cdm<br>
10/04/2018 14:53:56 | <span style='color: #c09853;'>6cde1ef</span> - Merge branch 'release/v1.5.2' into develop<br>
10/04/2018 14:49:14 | <span style='color: #c09853;'>074f9dd</span> - Bump versão 1.5.2<br>
10/04/2018 14:46:21 | <span style='color: #c09853;'>6b84d2d</span> - Merge branch 'feature/coberturacomercialv2' into develop<br>
10/04/2018 14:46:09 | <span style='color: #c09853;'>5022bbf</span> - Corrigida consulta de numero de clientes no mesmo endereço<br>
10/04/2018 14:17:36 | <span style='color: #c09853;'>b577b69</span> - Merge branch 'feature/coberturacomercialv2' into develop<br>
10/04/2018 14:17:07 | <span style='color: #c09853;'>3b7c1b3</span> - Finalizada v2 do módulo atendimento comercial<br>
04/04/2018 14:49:31 | <span style='color: #c09853;'>8458ec9</span> - Corrigido consulta de plano caso o CDM não possua atributos de latitude/longitude<br>
03/04/2018 15:10:17 | <span style='color: #c09853;'>3915fa2</span> - Merge branch 'feature/informacoeseletricaspops' into develop<br>
03/04/2018 15:09:59 | <span style='color: #c09853;'>ad549b5</span> - Implementado parametros elétricos em pops<br>
02/04/2018 12:17:42 | <span style='color: #c09853;'>9215c1b</span> - Merge branch 'feature/enderecopop' into develop<br>
02/04/2018 12:14:09 | <span style='color: #c09853;'>a4c2981</span> - Atributo api_ok habilitado por padrão<br>
02/04/2018 10:25:42 | <span style='color: #c09853;'>2725920</span> - Merge branch 'feature/enderecopop' into develop<br>
02/04/2018 10:25:26 | <span style='color: #c09853;'>5a89f11</span> - Implementado checkbox informando se endereço não foi localizado na API do Google<br>
29/03/2018 11:39:29 | <span style='color: #c09853;'>cf7c0ce</span> - Merge branch 'feature/enderecopops' into develop<br>
29/03/2018 11:38:55 | <span style='color: #c09853;'>5734e84</span> - Implementado pesquisa de endereço na API do Google<br>
28/03/2018 16:22:21 | <span style='color: #c09853;'>e2c6781</span> - Merge branch 'release/1.5.1' into develop<br>
28/03/2018 16:22:05 | <span style='color: #c09853;'>91e87c0</span> - Corrigido relatório de prospects externos<br>
28/03/2018 15:45:29 | <span style='color: #c09853;'>172bacc</span> - Merge branch 'release/1.5.1' into develop<br>
28/03/2018 15:45:11 | <span style='color: #c09853;'>933ffa3</span> - Bump versao 1.5.1<br>
28/03/2018 15:40:31 | <span style='color: #c09853;'>88dc11b</span> - Corrigida permissao para terceiros<br>
28/03/2018 15:21:49 | <span style='color: #c09853;'>34d9ae4</span> - Merge branch 'feature/vendasmobile' into develop<br>
28/03/2018 15:21:28 | <span style='color: #c09853;'>3f8ea93</span> - Implementada lista de prospects para terceirizados<br>
28/03/2018 10:30:04 | <span style='color: #c09853;'>674293a</span> - Merge branch 'feature/vendasmobile' into develop<br>
28/03/2018 10:29:33 | <span style='color: #c09853;'>fdc71f8</span> - Primeira versão do módulo para terceirizados<br>
27/03/2018 09:47:55 | <span style='color: #c09853;'>eb61ed7</span> - Alterado campo cobertura do prospect<br>
26/03/2018 17:33:41 | <span style='color: #c09853;'>2c71e2a</span> - Merge branch 'hotfix/consultacomercialcdmsemlatlng' into develop<br>
26/03/2018 17:32:56 | <span style='color: #c09853;'>d9b719c</span> - Corrigido alerta de cdm sem lat lng<br>
26/03/2018 17:19:04 | <span style='color: #c09853;'>c406a0a</span> - Implementada versão mobile<br>
16/03/2018 10:44:00 | <span style='color: #c09853;'>0b05300</span> - Modificada interface para melhor usabilidade mobile<br>
09/03/2018 13:09:39 | <span style='color: #c09853;'>7cd3bd4</span> - Tela de login responsiva<br>
15/12/2017 15:51:56 | <span style='color: #c09853;'>f0a281e</span> - Merge branch 'release/1.5.0'<br>
15/12/2017 15:51:37 | <span style='color: #c09853;'>45346ee</span> - Merge branch 'release/1.5.0' into develop<br>
15/12/2017 15:51:18 | <span style='color: #c09853;'>35a59c9</span> - bump versão 1.5.0<br>
15/12/2017 15:40:57 | <span style='color: #c09853;'>ad5414e</span> - Merge branch 'feature/campospatrimonio' into develop<br>
15/12/2017 15:40:31 | <span style='color: #c09853;'>47a752b</span> - Adicionado campos em materiais<br>
04/12/2017 15:23:45 | <span style='color: #c09853;'>02eea36</span> - Merge branch 'hotfix/prospectcdmlatlng' of telemidia/noc into develop<br>
04/12/2017 11:31:23 | <span style='color: #c09853;'>d0ae2e3</span> - Somente verifica CDMs rádio caso possua lat lng<br>
01/12/2017 11:15:16 | <span style='color: #c09853;'>5aa44da</span> - Merge branch 'hotfix/prospectcdm' of telemidia/noc into develop<br>
01/12/2017 11:13:21 | <span style='color: #c09853;'>2c4706c</span> - Merge branch 'hotfix/prospectcdm' of telemidia/noc into master<br>
01/12/2017 11:12:21 | <span style='color: #c09853;'>66605c1</span> - Corrigido consulta de cobertura cdm<br>
30/11/2017 12:48:56 | <span style='color: #c09853;'>d5f30e8</span> - Merge branch 'hotfix/prospecttelefone' of telemidia/noc into master<br>
30/11/2017 12:47:53 | <span style='color: #c09853;'>d5850a2</span> - Corrigido validação de telefone<br>
29/11/2017 17:21:19 | <span style='color: #c09853;'>d7147b4</span> - Merge branch 'hotfix/prospects' into develop<br>
29/11/2017 17:20:37 | <span style='color: #c09853;'>e3b0112</span> - Merge branch 'hotfix/prospects'<br>
29/11/2017 17:20:12 | <span style='color: #c09853;'>6497c57</span> - Corrigida persistência de dados de prospects<br>
23/11/2017 08:27:58 | <span style='color: #c09853;'>40617a8</span> - Atualizada versão do Gulp<br>
23/11/2017 08:26:08 | <span style='color: #c09853;'>7d567b6</span> - Merge branch 'bugfix/monitor' into develop<br>
23/11/2017 08:25:50 | <span style='color: #c09853;'>871159c</span> - Diminuido periodo de alertas para 1 hora<br>
21/11/2017 15:09:48 | <span style='color: #c09853;'>3a501f8</span> - Merge branch 'feature/monitoramento' into develop<br>
21/11/2017 12:58:07 | <span style='color: #c09853;'>a786bc2</span> - Finalizado rota de monitoramento<br>
17/11/2017 13:53:06 | <span style='color: #c09853;'>bf0824c</span> - Adicionado campo intervalo de quanto tempo o alerta foi gerado<br>
17/11/2017 13:36:26 | <span style='color: #c09853;'>988df3b</span> - Retorna apenas alertas das últimas 6 horas<br>
14/11/2017 08:58:20 | <span style='color: #c09853;'>613782d</span> - Substituido JSON por CSV<br>
13/11/2017 17:22:58 | <span style='color: #c09853;'>1269d10</span> - Merge branch 'hotfix/velocidade' into develop<br>
13/11/2017 17:22:28 | <span style='color: #c09853;'>ff7c485</span> - Merge branch 'hotfix/velocidade'<br>
13/11/2017 17:22:14 | <span style='color: #c09853;'>ac42dd7</span> - Retirado coluna username da lista<br>
13/11/2017 17:19:06 | <span style='color: #c09853;'>0c4c691</span> - Merge branch 'hotfix/velocidade'<br>
13/11/2017 17:18:44 | <span style='color: #c09853;'>299d791</span> - Retirado consulta por username provisoriamente<br>
13/11/2017 17:03:18 | <span style='color: #c09853;'>7846254</span> - Merge branch 'hotfix/velocidade' of http://git.telemidia.net.br:3000/telemidia/noc into feature/monitoramento<br>
13/11/2017 16:57:12 | <span style='color: #c09853;'>72f29c7</span> - Retirado filtro por usuário<br>
13/11/2017 09:25:28 | <span style='color: #c09853;'>9f35260</span> - Merge branch 'hotfix/pqserver' into develop<br>
13/11/2017 09:24:53 | <span style='color: #c09853;'>ebf8c82</span> - Merge branch 'hotfix/pqserver'<br>
13/11/2017 09:24:31 | <span style='color: #c09853;'>75cef76</span> - Alteradas conexões bancos PG e mail<br>
07/11/2017 11:07:47 | <span style='color: #c09853;'>bef5165</span> - Implementado rotas na api para aplicação monitor-rede<br>
07/11/2017 08:53:05 | <span style='color: #c09853;'>1bc54fa</span> - Merge branch 'hotfix/userad' into develop<br>
03/11/2017 15:20:38 | <span style='color: #c09853;'>cbe48db</span> - Merge branch 'hotfix/userad'<br>
03/11/2017 15:19:32 | <span style='color: #c09853;'>a465dd3</span> - Alterado usuario de bind do AD<br>
27/10/2017 09:53:41 | <span style='color: #c09853;'>7e5bcd7</span> - Merge branch 'hotfix/iprsyslog' into develop<br>
27/10/2017 09:53:10 | <span style='color: #c09853;'>691151f</span> - Merge branch 'hotfix/iprsyslog'<br>
27/10/2017 09:52:51 | <span style='color: #c09853;'>30eab4f</span> - Alterado ip do servidor de log<br>
24/10/2017 10:20:06 | <span style='color: #c09853;'>f4fadb3</span> - Merge branch 'feature/relatorioaps' into develop<br>
24/10/2017 10:19:51 | <span style='color: #c09853;'>800c068</span> - Implementado relatório de qtd clientes por ap<br>
24/10/2017 10:19:51 | <span style='color: #c09853;'>800c068</span> - Implementado relatório de qtd clientes por ap<br>
23/10/2017 17:13:56 | <span style='color: #c09853;'>ed688c0</span> - Merge branch 'hotfix/atendimentohelpdesk' into develop<br>
23/10/2017 17:13:36 | <span style='color: #c09853;'>f31ad9b</span> - Merge branch 'hotfix/atendimentohelpdesk'<br>
23/10/2017 17:13:23 | <span style='color: #c09853;'>8049a4a</span> - Simplificada tabela da ONU<br>
23/10/2017 16:49:30 | <span style='color: #c09853;'>c7681b0</span> - Merge branch 'hotfix/systemlog' into develop<br>
23/10/2017 16:49:07 | <span style='color: #c09853;'>8e1ec08</span> - Merge branch 'hotfix/systemlog'<br>
23/10/2017 16:48:32 | <span style='color: #c09853;'>ad73850</span> - Corrigido rota para systemlog<br>
23/10/2017 13:04:37 | <span style='color: #c09853;'>bfb30f1</span> - Merge branch 'hotfix/permissoesrede' into develop<br>
23/10/2017 13:04:06 | <span style='color: #c09853;'>f1ff4f9</span> - Merge branch 'hotfix/permissoesrede'<br>
23/10/2017 13:03:42 | <span style='color: #c09853;'>5392484</span> - Corrigida permissão de escrita em materiais para redes<br>
23/10/2017 13:00:33 | <span style='color: #c09853;'>2acf38b</span> - Merge branch 'hotfix/tipopatrimonio'<br>
23/10/2017 13:00:11 | <span style='color: #c09853;'>e6a0129</span> - Corrigido tipo em patrimonio ilegivel<br>
23/10/2017 11:45:47 | <span style='color: #c09853;'>df8611b</span> - Merge branch 'hotfix/downloadcobertura' into develop<br>
23/10/2017 11:45:21 | <span style='color: #c09853;'>573701f</span> - Merge branch 'hotfix/downloadcobertura'<br>
23/10/2017 11:45:00 | <span style='color: #c09853;'>2b2cfb2</span> - Corrigido download cobertura<br>
23/10/2017 11:26:19 | <span style='color: #c09853;'>596f90a</span> - Merge branch 'hotfix/downloadbackup' into develop<br>
23/10/2017 11:25:58 | <span style='color: #c09853;'>ca167cb</span> - Merge branch 'hotfix/downloadbackup'<br>
23/10/2017 11:25:35 | <span style='color: #c09853;'>9a92e8e</span> - Corrigido download de backups<br>
19/10/2017 17:48:21 | <span style='color: #c09853;'>c083f9b</span> - Merge branch 'hotfix/insertservico' into develop<br>
19/10/2017 17:42:59 | <span style='color: #c09853;'>7486158</span> - Merge branch 'hotfix/insertservico'<br>
19/10/2017 17:42:47 | <span style='color: #c09853;'>d6ae494</span> - Corigido combo tipo do serviço<br>
18/10/2017 17:37:51 | <span style='color: #c09853;'>0bea188</span> - Merge branch 'hotfix/formparametros' into develop<br>
18/10/2017 17:37:30 | <span style='color: #c09853;'>2b61cb6</span> - Merge branch 'hotfix/formparametros'<br>
18/10/2017 17:37:08 | <span style='color: #c09853;'>f4b78b9</span> - Corrigido combo proprietário<br>
18/10/2017 17:31:49 | <span style='color: #c09853;'>5fda8b0</span> - Merge branch 'hotfix/ipdbserver' into develop<br>
18/10/2017 17:31:27 | <span style='color: #c09853;'>7dbc203</span> - Merge branch 'hotfix/ipdbserver'<br>
18/10/2017 17:30:33 | <span style='color: #c09853;'>17bd7c5</span> - Alterado ip do postgres<br>
18/10/2017 14:24:48 | <span style='color: #c09853;'>f64bb96</span> - Merge branch 'hotfix/materiais' into develop<br>
18/10/2017 14:24:23 | <span style='color: #c09853;'>18c8900</span> - Merge branch 'hotfix/materiais'<br>
18/10/2017 14:24:09 | <span style='color: #c09853;'>5beb358</span> - Corrigido combo marca/modelo<br>
18/10/2017 09:32:33 | <span style='color: #c09853;'>aed6bda</span> - Merge branch 'hotfix/ipdatabase' into develop<br>
18/10/2017 09:32:06 | <span style='color: #c09853;'>d1edf88</span> - Merge branch 'hotfix/ipdatabase'<br>
18/10/2017 09:31:28 | <span style='color: #c09853;'>f721107</span> - Alterado ip do postgres<br>
17/10/2017 17:46:22 | <span style='color: #c09853;'>419847a</span> - Revogada permissão redes em gerencia.read<br>
17/10/2017 17:16:48 | <span style='color: #c09853;'>62f5197</span> - Merge branch 'hotfix/parametrohost' into develop<br>
17/10/2017 17:16:28 | <span style='color: #c09853;'>9fde83d</span> - Corrigido service de modos de operação<br>
17/10/2017 17:04:34 | <span style='color: #c09853;'>119c401</span> - Implementada rota na API<br>
17/10/2017 16:34:09 | <span style='color: #c09853;'>1fa3065</span> - Merge branch 'hotfix/permissaomailing' into develop<br>
17/10/2017 16:33:41 | <span style='color: #c09853;'>ef83137</span> - Corrigida permissão para grupo gerencia em mailing<br>
17/10/2017 15:08:17 | <span style='color: #c09853;'>29d970b</span> - Concedida permissão para leitura em materiais para helpdesk<br>
17/10/2017 11:44:02 | <span style='color: #c09853;'>5f9b43e</span> - Merge branch 'release/1.4.3'<br>
17/10/2017 11:43:36 | <span style='color: #c09853;'>6826ebe</span> - Merge branch 'release/1.4.3' into develop<br>
17/10/2017 11:43:16 | <span style='color: #c09853;'>2b77e05</span> - Consulta informações do contrato mesmo sem um mac válido<br>
                  17/10/2017 09:18:11 | <span style='color: #c09853;'>de0d9cc</span> - Merge branch 'release/1.4.3'<br>
                  16/10/2017 17:58:17 | <span style='color: #c09853;'>42acfbc</span> - Corrigido permissão módulo mailing<br>
                  16/10/2017 17:49:28 | <span style='color: #c09853;'>c9104c0</span> - Merge branch 'release/1.4.3'<br>
                  16/10/2017 17:49:11 | <span style='color: #c09853;'>ae6fc09</span> - Bump versão 1.4.3<br>
                  16/10/2017 17:25:52 | <span style='color: #c09853;'>beb350d</span> - Merge branch 'feature/sinalftth' into develop<br>
                  16/10/2017 17:22:00 | <span style='color: #c09853;'>b7244aa</span> - Finalizada atualização do módulo de atendimento helpdesk<br>
                  11/10/2017 17:42:10 | <span style='color: #c09853;'>b858321</span> - Implementado sinal na interface<br>
                  11/10/2017 09:44:44 | <span style='color: #c09853;'>83b8266</span> - Merge branch 'hotfix/update' into develop<br>
                  11/10/2017 09:22:39 | <span style='color: #c09853;'>960f92a</span> - Bibliotecas atualizadas<br>
                  10/10/2017 15:25:43 | <span style='color: #c09853;'>bf47bcd</span> - Implementado classe para consulta no ANM Server<br>
                  04/10/2017 10:58:44 | <span style='color: #c09853;'>85cb24e</span> - Merge branch 'release-1.4'<br>
                  04/10/2017 10:58:00 | <span style='color: #c09853;'>a55571d</span> - Merge branch 'release-1.4' into develop<br>
                  04/10/2017 10:55:42 | <span style='color: #c09853;'>085175f</span> - Bump versão 1.4<br>
                  04/10/2017 10:12:59 | <span style='color: #c09853;'>a48dc1a</span> - Merge branch 'feature/mailing' into develop<br>
                  04/10/2017 10:12:23 | <span style='color: #c09853;'>cfcd6ab</span> - Finalizado módulo mailing<br>
                  21/09/2017 15:30:31 | <span style='color: #c09853;'>f864ba3</span> - Inicio do módulo mailing<br>
                  18/09/2017 17:11:24 | <span style='color: #c09853;'>c820ab2</span> - Merge branch 'hotfix/importkml' into develop<br>
                  18/09/2017 17:11:02 | <span style='color: #c09853;'>8d2556e</span> - Permite importar kml com múltiplos polígonos<br>
                  05/09/2017 15:13:15 | <span style='color: #c09853;'>36db460</span> - Merge branch 'hotfix/coberturacomercial' into develop<br>
                  05/09/2017 15:01:20 | <span style='color: #c09853;'>3aa323a</span> - Otimizado consulta endereço<br>
                  12/06/2017 14:30:38 | <span style='color: #c09853;'>224fba8</span> - Merge branch 'feature/listapatrimoniosalocados' into develop<br>
                  12/06/2017 11:10:48 | <span style='color: #c09853;'>e4138fb</span> - Adicionado coluna Local na lista de hosts do pop<br>
                  02/06/2017 17:19:38 | <span style='color: #c09853;'>abe5fe4</span> - Adicionado consulta de planos CDM-HT<br>
                  01/06/2017 10:57:58 | <span style='color: #c09853;'>c4e278a</span> - Merge branch 'hotfix/parametroszabbix' into develop<br>
                  01/06/2017 10:56:58 | <span style='color: #c09853;'>a297e32</span> - Corrigido integração com Zabbix<br>
                  31/05/2017 09:45:36 | <span style='color: #c09853;'>c6873bf</span> - Merge branch 'feature/cdmfibra' into develop<br>
                  31/05/2017 09:45:11 | <span style='color: #c09853;'>03057ec</span> - Implementado exibição de cdm na cobertura fibra<br>
                  18/05/2017 17:41:31 | <span style='color: #c09853;'>17b9413</span> - Melhorias no módulo comercial<br>
                  05/05/2017 09:30:49 | <span style='color: #c09853;'>43b5e24</span> - Adicionado link para patrimonio em parametros e motivo em materiais<br>
                  05/05/2017 09:03:33 | <span style='color: #c09853;'>0007fed</span> - Merge branch 'feature/correcaopatrimonio' into develop<br>
                  05/05/2017 09:03:03 | <span style='color: #c09853;'>49af8cb</span> - Implementado possibilidade para alteração do número de patrimônio<br>
                  24/04/2017 15:49:25 | <span style='color: #c09853;'>a4b7f74</span> - Adicionado consulta de prospects<br>
                  19/04/2017 16:47:53 | <span style='color: #c09853;'>a0e4c2b</span> - Campo telefone em atendimento comercial obrigatorio<br>
                  19/04/2017 10:34:07 | <span style='color: #c09853;'>3555660</span> - Merge branch 'feature/importkml' into develop<br>
                  19/04/2017 09:48:49 | <span style='color: #c09853;'>8231211</span> - Implementado importação de kml<br>
                  11/04/2017 11:10:00 | <span style='color: #c09853;'>8282a26</span> - Merge branch 'feature/atendimentocomercial' into develop<br>
                  11/04/2017 10:38:33 | <span style='color: #c09853;'>ee10a57</span> - Finalizado módulo atendimento comercial<br>
                  03/04/2017 17:38:23 | <span style='color: #c09853;'>49bac65</span> - Incorporado lista de cdms<br>
                  31/03/2017 08:38:47 | <span style='color: #c09853;'>edc70fe</span> - Adicionado consulta de cdms<br>
                  28/03/2017 09:55:31 | <span style='color: #c09853;'>663341e</span> - Corrigido campo ip e botão salvar em parametros do host<br>
                  27/03/2017 16:30:56 | <span style='color: #c09853;'>9bfa7b1</span> - Implementado módulo atendimento comercial<br>
                  21/03/2017 11:08:43 | <span style='color: #c09853;'>9c3f8ae</span> - Iniciado módulo atendimento comercial<br>
                  16/03/2017 17:08:49 | <span style='color: #c09853;'>c4c2608</span> - Seta ip 0.0.0.0 quando nulo em parametros ftth<br>
                  16/03/2017 10:09:44 | <span style='color: #c09853;'>aeb0af0</span> - Corrigido nome da cidade de poços em gerencia<br>
                  16/03/2017 09:07:39 | <span style='color: #c09853;'>766031b</span> - Merge branch 'release/1.3.82' into develop<br>
                  16/03/2017 08:59:36 | <span style='color: #c09853;'>cc7d165</span> - Bump versão<br>
                  16/03/2017 08:45:57 | <span style='color: #c09853;'>39d16fd</span> - Alterado url api para producao<br>
                  15/03/2017 17:49:38 | <span style='color: #c09853;'>7f2832d</span> - Adicionado recurso para inserir parâmetros em equipamentos sem patrimônio (cadastro)<br>
                  14/03/2017 16:41:24 | <span style='color: #c09853;'>06bb167</span> - Merge branch 'release/1.3.80' into develop<br>
                  14/03/2017 16:41:04 | <span style='color: #c09853;'>0210482</span> - Bump versao 1.3.80<br>
                  14/03/2017 11:05:44 | <span style='color: #c09853;'>7239568</span> - Merge branch 'feature/integracaomateriais' into develop<br>
                  14/03/2017 11:04:36 | <span style='color: #c09853;'>c78f0d8</span> - Adicionado integração com módulo materiais<br>
                  06/03/2017 15:44:56 | <span style='color: #c09853;'>ba05748</span> - Merge branch 'release/1.3.71' into develop<br>
                  06/03/2017 15:44:22 | <span style='color: #c09853;'>d969361</span> - Bump versão 1.3.71<br>
                  24/02/2017 10:54:41 | <span style='color: #c09853;'>be20a72</span> - Resolvido conflitos<br>
                  24/02/2017 10:28:31 | <span style='color: #c09853;'>f24a619</span> - Alterado servidor da API para produção<br>
                  21/02/2017 15:59:52 | <span style='color: #c09853;'>d2dcc27</span> - Merge branch 'feature/logmateriais' into develop<br>
                  21/02/2017 15:59:06 | <span style='color: #c09853;'>eb3493a</span> - Adicionado gravação de log de alteração em materiais<br>
                  21/02/2017 15:17:59 | <span style='color: #c09853;'>f98cf25</span> - Alteração automática no nome dos hosts ao renomear POP<br>
                  13/02/2017 16:57:49 | <span style='color: #c09853;'>ee130eb</span> - Integrado API Zabbix<br>
                  30/01/2017 17:44:19 | <span style='color: #c09853;'>c129da0</span> - Merge branch 'hotfix/validacaomac' into develop<br>
                  30/01/2017 17:43:32 | <span style='color: #c09853;'>7cd71b1</span> - Corrigido validação na API do campo mac<br>
                  30/01/2017 17:09:46 | <span style='color: #c09853;'>dabd15f</span> - Merge branch 'hotfix/validacaomac' into develop<br>
                  30/01/2017 17:08:25 | <span style='color: #c09853;'>c9928d7</span> - Merge branch 'hotfix/validacaomac'<br>
                  30/01/2017 17:08:04 | <span style='color: #c09853;'>45bb76a</span> - Corrigido validação de MAC e serial em branco<br>
                  23/01/2017 15:19:28 | <span style='color: #c09853;'>09d38ab</span> - Merge branch 'release/1.3.60'<br>
                  23/01/2017 15:18:57 | <span style='color: #c09853;'>42fc836</span> - Merge branch 'release/1.3.60' into develop<br>
                  23/01/2017 15:18:26 | <span style='color: #c09853;'>cac1d5f</span> - Bump versão 1.3.60<br>
                  17/01/2017 10:01:12 | <span style='color: #c09853;'>1110d62</span> - Merge branch 'feature/atalhonocprojetos' into develop<br>
                  17/01/2017 09:58:24 | <span style='color: #c09853;'>cb9f1f5</span> - Adicionado atalho para projetos<br>
                  16/01/2017 16:23:43 | <span style='color: #c09853;'>bb07afb</span> - Merge branch 'feature/reloadlogs' into develop<br>
                  16/01/2017 16:23:12 | <span style='color: #c09853;'>9a4ed73</span> - Adicionado botão reload em logs de atendimento<br>
                  16/01/2017 09:48:01 | <span style='color: #c09853;'>f692d31</span> - Merge branch 'hotfix/acumulados' into develop<br>
                  16/01/2017 09:47:41 | <span style='color: #c09853;'>025f34f</span> - Merge branch 'hotfix/acumulados'<br>
                  16/01/2017 09:47:14 | <span style='color: #c09853;'>dd9da52</span> - Corrigido acumulados e gráfico por tempo quando período menor que 1 mês<br>
                  13/01/2017 11:25:28 | <span style='color: #c09853;'>20db354</span> - Merge branch 'hotfix/agrupamentomes' into develop<br>
                  13/01/2017 11:24:37 | <span style='color: #c09853;'>e5408d8</span> - Merge branch 'hotfix/agrupamentomes'<br>
                  13/01/2017 11:23:52 | <span style='color: #c09853;'>cd41e05</span> - Corrigido agrupamento por mes<br>
                  13/01/2017 09:36:57 | <span style='color: #c09853;'>f51eac8</span> - Finalizando parametros em host<br>
                  12/01/2017 17:30:34 | <span style='color: #c09853;'>c7f4ef8</span> - Merge branch 'hotfix/graficodesempenho' into develop<br>
                  12/01/2017 17:30:12 | <span style='color: #c09853;'>b868964</span> - Merge branch 'hotfix/graficodesempenho'<br>
                  12/01/2017 17:29:23 | <span style='color: #c09853;'>33d9222</span> - Corrigido ordenação nos gráficos<br>
                  05/01/2017 14:37:38 | <span style='color: #c09853;'>7b1757e</span> - Adicionada permissão para retirada de hosts<br>
                  04/01/2017 14:10:22 | <span style='color: #c09853;'>c5f34a0</span> - Merge branch 'feature/retiradahosts' into develop<br>
                  04/01/2017 14:09:52 | <span style='color: #c09853;'>ff95255</span> - Adicionado funcionalidade de retirar host<br>
                  28/12/2016 15:31:10 | <span style='color: #c09853;'>2ddd8d9</span> - Merge branch 'release/1.3.56'<br>
                  28/12/2016 15:29:48 | <span style='color: #c09853;'>579faec</span> - Merge branch 'release/1.3.56' into develop<br>
                  28/12/2016 15:29:20 | <span style='color: #c09853;'>67bc495</span> - Bump versão 1.3.56<br>
                  28/12/2016 10:57:51 | <span style='color: #c09853;'>f54b40a</span> - Merge branch 'feature/relatorioipfixo' into develop<br>
                  28/12/2016 10:46:45 | <span style='color: #c09853;'>eee058d</span> - Adicionado consulta de reservas de ip<br>
                  27/12/2016 15:40:39 | <span style='color: #c09853;'>7f51fcb</span> - Adicionado integração com API Zabbix<br>
                  12/12/2016 14:21:56 | <span style='color: #c09853;'>058846c</span> - Adicionado filtro hora em systemlog<br>
                  09/12/2016 15:18:13 | <span style='color: #c09853;'>82e4230</span> - paginação em systemlog<br>
                  09/12/2016 10:11:59 | <span style='color: #c09853;'>cda7978</span> - Adicionado filtro por data em systemlog<br>
                  08/12/2016 11:06:25 | <span style='color: #c09853;'>874cd7c</span> - Merge branch 'feature/graficodesempenhoporcentagem' into develop<br>
                  08/12/2016 11:05:43 | <span style='color: #c09853;'>cff88cd</span> - Gráficos alterados para agrupamento por mes e implementado porcentagem - modulo gerencia<br>
                  06/12/2016 16:03:52 | <span style='color: #c09853;'>519d8ab</span> - Seleção de host em systemlog<br>
                  21/11/2016 16:48:17 | <span style='color: #c09853;'>c9b35c3</span> - Merge branch 'feature/masterlog' into develop<br>
                  21/11/2016 16:47:17 | <span style='color: #c09853;'>a98c69c</span> - Adicionado módulo Ferramentas/System Log<br>
                  18/11/2016 10:15:23 | <span style='color: #c09853;'>5892907</span> - Corrigida rota para inserção de enlace<br>
                  17/11/2016 16:15:11 | <span style='color: #c09853;'>e922585</span> - Adicionado campo tipo vlan em parametros ftth<br>
                  17/11/2016 14:43:32 | <span style='color: #c09853;'>f2d67b4</span> - Alterado legenda escopo para serviço<br>
                  17/11/2016 14:37:48 | <span style='color: #c09853;'>de5fd05</span> - Cores status contrato<br>
                  17/11/2016 11:17:31 | <span style='color: #c09853;'>b0eff75</span> - Adicionado campo escopo no atendimento<br>
                  17/11/2016 10:58:34 | <span style='color: #c09853;'>d7ce44d</span> - Corrigido botão limpar em atendimento<br>
                  17/11/2016 10:50:46 | <span style='color: #c09853;'>675ad37</span> - Pequena modificação na interface atendimento<br>
                  16/11/2016 16:02:03 | <span style='color: #c09853;'>ce5cf39</span> - Merge branch 'feature/syslog' into develop<br>
                  16/11/2016 16:01:40 | <span style='color: #c09853;'>3789102</span> - Adicionado consulta logs<br>
                  14/11/2016 10:33:26 | <span style='color: #c09853;'>88dec35</span> - Merge branch 'release/1.3.43'<br>
                  14/11/2016 10:32:36 | <span style='color: #c09853;'>9e3bddd</span> - Merge branch 'release/1.3.43' into develop<br>
                  14/11/2016 10:22:56 | <span style='color: #c09853;'>7d0a364</span> - Bump versão 1.3.43<br>
                  14/11/2016 10:01:13 | <span style='color: #c09853;'>70e592e</span> - Resolvido conflitos<br>
                  14/11/2016 09:55:13 | <span style='color: #c09853;'>ca3a8e3</span> - Adicionado módulo helpdek<br>
                  11/11/2016 08:31:50 | <span style='color: #c09853;'>8ea452b</span> - Adicionado concentrador *************<br>
                  09/11/2016 17:03:48 | <span style='color: #c09853;'>d3cd263</span> - Merge branch 'feature/olt' into develop<br>
                  09/11/2016 17:03:10 | <span style='color: #c09853;'>e5b1329</span> - Incuida seleção da OLT nos parametros do host<br>
                  09/11/2016 11:40:58 | <span style='color: #c09853;'>0efefd3</span> - Merge branch 'hotfix/parametroftth' into develop<br>
                  09/11/2016 11:07:00 | <span style='color: #c09853;'>d79e65e</span> - Merge branch 'hotfix/parametroftth'<br>
                  09/11/2016 11:06:27 | <span style='color: #c09853;'>cbb2f04</span> - Corrigido inclusão de parametros com busca por mac ou parcial<br>
                  03/11/2016 16:42:09 | <span style='color: #c09853;'>5b9e7d0</span> - Adicionado concentrador ************<br>
                  03/11/2016 10:39:09 | <span style='color: #c09853;'>c37279a</span> - Merge branch 'release/1.3.40' into develop<br>
                  03/11/2016 10:37:50 | <span style='color: #c09853;'>74bcd7f</span> - Merge branch 'release/1.3.40'<br>
                  27/10/2016 17:20:08 | <span style='color: #c09853;'>7dcc173</span> - Corrigido rota parametros<br>
                  25/10/2016 16:03:15 | <span style='color: #c09853;'>c78f6c7</span> - Bump versão 1.3.40<br>
                  25/10/2016 10:01:55 | <span style='color: #c09853;'>7b5019d</span> - Merge branch 'feature/ftth' into develop<br>
                  24/10/2016 17:31:53 | <span style='color: #c09853;'>83824b3</span> - Retorno da marca/modelo pelo patrimonio nos parametros<br>
                  20/10/2016 14:22:57 | <span style='color: #c09853;'>9ab48c4</span> - Adicionados recursos FTTH<br>
                  19/09/2016 10:15:43 | <span style='color: #c09853;'>6d265a2</span> - Merge branch 'release/1.3.36' into develop<br>
                  19/09/2016 10:14:19 | <span style='color: #c09853;'>dce074f</span> - Merge branch 'release/1.3.36'<br>
                  16/09/2016 16:07:54 | <span style='color: #c09853;'>fbbafc1</span> - Bump versão 1.3.36<br>
                  16/09/2016 15:48:15 | <span style='color: #c09853;'>d40823c</span> - Implementado graficos de desempenho por tempo de execução<br>
                  14/09/2016 16:24:34 | <span style='color: #c09853;'>47b2b7b</span> - Merge branch 'feature/desempenho122436' into develop<br>
                  14/09/2016 16:22:44 | <span style='color: #c09853;'>2cc96ef</span> - Implementado grafico por tempo de execução<br>
                  02/09/2016 10:18:42 | <span style='color: #c09853;'>5d9e883</span> - Merge branch 'hotfix/captiongrafico' into develop<br>
                  02/09/2016 10:17:58 | <span style='color: #c09853;'>d0781ba</span> - Merge branch 'hotfix/captiongrafico'<br>
                  02/09/2016 10:17:27 | <span style='color: #c09853;'>5b828b7</span> - Corrrigido caption nos graficos de desempenho<br>
                  31/08/2016 10:20:47 | <span style='color: #c09853;'>6207162</span> - Merge branch 'hotfix/botaosalvarpop' into develop<br>
                  31/08/2016 10:17:42 | <span style='color: #c09853;'>a8f4cb8</span> - Merge branch 'hotfix/botaosalvarpop'<br>
                  31/08/2016 10:17:26 | <span style='color: #c09853;'>69c479a</span> - Bump versão 1.3.31<br>
                  31/08/2016 10:14:02 | <span style='color: #c09853;'>7ebbe91</span> - Corrigido botão Salvar em POPs<br>
                  30/08/2016 17:00:26 | <span style='color: #c09853;'>f351cc0</span> - REsolvido conflitos<br>
                  30/08/2016 16:58:13 | <span style='color: #c09853;'>8e326fa</span> - Solucionado conflitos<br>
                  30/08/2016 16:56:06 | <span style='color: #c09853;'>170c381</span> - Merge branch 'release/1.3.30' into develop<br>
                  30/08/2016 16:55:22 | <span style='color: #c09853;'>125706b</span> - Bump versão 1.3.30<br>
                  30/08/2016 16:41:12 | <span style='color: #c09853;'>0ed3c88</span> - Merge branch 'feature/emailpop' into develop<br>
                  30/08/2016 16:40:53 | <span style='color: #c09853;'>fe61abc</span> - Adicionado campo e-mail em pop<br>
                  30/08/2016 16:29:40 | <span style='color: #c09853;'>bf8ced3</span> - Opção para remover seleção de tecnologia em hosts<br>
                  29/08/2016 17:42:44 | <span style='color: #c09853;'>cd2288b</span> - Unificado controller de lista pops/dedicados<br>
                  29/08/2016 16:51:28 | <span style='color: #c09853;'>8e31990</span> - Merge branch 'feature/filtrostatus' into develop<br>
                  29/08/2016 16:51:03 | <span style='color: #c09853;'>1435d6f</span> - Adicionado filtro ativos/inativos em pops/hosts e ordenação em pops<br>
                  29/08/2016 09:52:53 | <span style='color: #c09853;'>b0e23de</span> - Corrigido numero versao<br>
                  29/08/2016 09:34:22 | <span style='color: #c09853;'>13ff9fa</span> - Merge branch 'hotfix/popsinativos' into develop<br>
                  29/08/2016 09:32:30 | <span style='color: #c09853;'>b5590e1</span> - Bump versão 1.3.24<br>
                  29/08/2016 09:29:24 | <span style='color: #c09853;'>34fe505</span> - Merge branch 'hotfix/popsinativos'<br>
                  29/08/2016 09:28:43 | <span style='color: #c09853;'>add8fbf</span> - Filtro pops inativos no combo e bloqueio de alteração de inativos<br>
                  26/08/2016 16:37:31 | <span style='color: #c09853;'>3f786aa</span> - Merge branch 'feature/titulograficos' into develop<br>
                  26/08/2016 16:37:12 | <span style='color: #c09853;'>66418a2</span> - Adicionado titulo nos gráficos<br>
                  26/08/2016 16:04:59 | <span style='color: #c09853;'>ee00ff7</span> - Merge branch 'feature/melhoriaeventos' into develop<br>
                  26/08/2016 16:03:46 | <span style='color: #c09853;'>4807887</span> - Botão trabalhar na tela de eventos<br>
                  26/08/2016 15:02:26 | <span style='color: #c09853;'>76b9db9</span> - Merge branch 'feature/statusservicos' into develop<br>
                  26/08/2016 15:01:37 | <span style='color: #c09853;'>ffd283b</span> - Finalizado habilitar/desabilitar servicos<br>
                  26/08/2016 09:29:27 | <span style='color: #c09853;'>0b04e67</span> - Auto stash for revert of "Adicionado opção desativar em serviços"<br>
                  26/08/2016 09:24:18 | <span style='color: #c09853;'>82d7396</span> - Adicionado opção desativar em serviços<br>
                  25/08/2016 10:32:17 | <span style='color: #c09853;'>dad7cb5</span> - Merge branch 'release-1.3.23' into develop<br>
                  25/08/2016 10:29:13 | <span style='color: #c09853;'>de528a2</span> - Merge branch 'release-1.3.23'<br>
                  25/08/2016 10:28:26 | <span style='color: #c09853;'>c715c27</span> - Bump versão 1.3.23<br>
                  25/08/2016 10:21:49 | <span style='color: #c09853;'>efe1423</span> - Incluido menu develop<br>
                  25/08/2016 10:20:39 | <span style='color: #c09853;'>d4156f0</span> - Merge branch 'desempenhoinstalacao' into develop<br>
                  25/08/2016 10:19:22 | <span style='color: #c09853;'>c201aa2</span> - Implementado gráfico de instalação<br>
                  25/08/2016 08:45:38 | <span style='color: #c09853;'>4d8dc5a</span> - Merge branch 'logerros' into develop<br>
                  24/08/2016 17:55:53 | <span style='color: #c09853;'>1fc7205</span> - Adicionado consulta de log de erros<br>
                  24/08/2016 16:12:10 | <span style='color: #c09853;'>183196e</span> - Primeiro commit<br>
                  24/08/2016 16:10:25 | <span style='color: #c09853;'>8c5461a</span> - Initial commit<br>

              </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                    Fechar
                </button>
            </div>
        </div>
    </div>
</div>
