(function() {
  'use strict';

  angular
    .module('app')
    .controller('MenuController', MenuController);

  /** @ngInject */
  function MenuController($scope, $location, $rootScope, $localStorage, AuthenticationService,
    EventosTarefasService, API_CONFIG, $http, AuthorizationService) {

    var vm = this;

    vm.isActive = isActive;
    vm.logout = logout;
    vm.sair = sair;
    vm.eventostarefas = [];
    vm.download_clientesap = download_clientesap;


    activate();


    $scope.$on('eventoSelecionado', function() {

        //getTarefas();

    });

    function download_clientesap(){
      
      var url = API_CONFIG.url + '/relatorios/qtdclientesap';
      $http({
          method: 'GET',
          url: url,
          responseType: 'arraybuffer'
      }).then(function (response) {

          var headers = response.headers();
          var filename = 'qtd_clientes_ap.csv';
          var contentType = headers['content-type'];

          var blob = new Blob([response.data], {
              type: contentType
          });
          if (navigator.msSaveBlob)
           navigator.msSaveBlob(blob, filename);
          else {
              // Try using other saveBlob implementations, if available
              var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
              if (saveBlob === undefined) {
                  var linkElement = document.createElement('a');
                  try {
                      var blob = new Blob([response.data], { type: contentType });
                      var url = window.URL.createObjectURL(blob);
                      linkElement.setAttribute('href', url);
                      linkElement.setAttribute("download", filename);

                      var clickEvent = new MouseEvent("click", {
                          "view": window,
                          "bubbles": true,
                          "cancelable": false
                      });
                      linkElement.dispatchEvent(clickEvent);
                  } catch (ex) {
                      console.log(ex);
                  }
              }
              
          }
          
      }, function(error) {
          console.log(error);
      });
  };   

    /*
    $scope.$on('logout', function() {
        console.log('logout')  
        $rootScope.operador = {};
        $rootScope.eventoSelecionado = {id: ''};
        $consoleookies.put('eventoSelecionado.id', '');
        $cookies.put('eventoSelecionado.descricao', '');
        $cookies.put('eventoSelecionado.categoria', '');

    });
*/
     

    function activate() {

      vm.mobile = false;
      vm.relatorios = [];

      if($rootScope.operador.username === undefined){
        $rootScope.operador = {username:''};
      }

      //getAlertas();
      //getTarefas();
      //if($rootScope.operador.username !== undefined){
        //getAlertas();
      //}

      if(AuthorizationService.Authorize('["auxiliares.read", "auxiliares.write"]')){
        getAlertas();
      }

      if(AuthorizationService.Authorize('["terceiro.write"]')){
        vm.mobile = true;
      }

   }   

    function sair(){
      delete $localStorage.evento;
      $rootScope.eventoSelecionado = {id: ''};
      $rootScope.$broadcast("eventoSelecionado");
       //window.location.assign("#/eventos");
      $location.path('/eventos');
    }

    function logout(){
      AuthenticationService.Logout();
      /*
      LoginService.doLogout(function () {

      });

      $rootScope.operador = {};
      $cookies.put('operador.username', '');
      //window.location.assign("#/");
      */
      $rootScope.operador = {"username":""};
      $rootScope.eventoSelecionado = {id: ''};
      delete $localStorage.evento;
      $rootScope.$broadcast("eventoSelecionado");
      // Remove URL parameters before redirecting to /login
      $location.search({});
      $location.path('/login');
    }

    function isActive(path) {

      if ($rootScope.currentPath === path) {
        return "active";
      } else {
        return "";
      }

    }


    
    function getAlertas() {
      var urlApi = API_CONFIG.url + '/alertas';
        $http.get(urlApi).then(function (response) {
          vm.alertas = response.data;
          console.log(vm.alertas);
	        });
    }
    



    
    function getTarefas(){
      var Tarefas = EventosTarefasService.get({id : $rootScope.eventoSelecionado.id});
      Tarefas.$promise.then(function (data) {
        vm.eventostarefas = data.dados;
        vm.eventostarefasresumo = data.resumo;
      });
    }
    


  }

})();
