<div ng-controller="<PERSON><PERSON><PERSON><PERSON>roller as MC">
<ol class="breadcrumb">
  <li><a href="/">Dashboard</a></li>
    <li class="active"><a href="/{{::currentPath}}">{{::title}}</a></li>
</ol>
<a class="btn btn-md btn-success" href="/{{::currentPath}}/novo" ng-show="(eventoSelecionado.id != '' && currentPath != 'eventos' && currentPath != 'notas' && currentPath != 'servicos') || (currentPath == 'eventos' && eventoSelecionado.id == '')"><i class="glyphicon glyphicon-plus"></i> Incluir</a>

<a class="btn btn-md btn-success" href="/eventos/{{::eventoSelecionado.id}}/notas/novo" ng-show="(eventoSelecionado.id != '' && currentPath == 'notas')"><i class="glyphicon glyphicon-plus"></i> Incluir</a>

<p></p>
</div>
