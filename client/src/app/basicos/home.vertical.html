<nav class="navbar navbar-inverse navbar-fixed-top" ng-controller="MenuController">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="#">Sistema de Controle de Infraestrutura</a>
        </div>
        
        <div id="navbar" class="navbar-collapse collapse">
                     
           <ul class="nav navbar-nav navbar-left">
            <!-- <li><a href="#">Dashboard</a></li>
            <li><a href="#">Configuracoes</a></li>
            <li><a href="#">Perfil</a></li>
            <li><a href="#">Ajuda</a></li>
--><li class="dropdown" ng-show="eventoSelecionado.id !== ''">
               <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Trabalhando no Evento: <span class="badge">{{eventoSelecionado.descricao}}</span> <span class="caret"></span></a>
              <ul class="dropdown-menu">
               
                <li><a ng-click="sair()">Sair</a></li>
              </ul>
            </li>
          </ul>
          <form class="navbar-form navbar-right">
            <input type="text" ng-model="filtro" class="form-control" placeholder="Pesquisar...">
          </form>
          
        </div>
      </div>
    </nav>
    
    <div class="container-fluid" ng-controller="MenuController">
      <div class="row">
        <div class="col-sm-3 col-md-2 sidebar">
          <ul class="nav nav-sidebar">
            <li ng-class="{ active: isActive('/evento')}"><a href="/eventos"><i class="glyphicon glyphicon-asterisk"></i> Eventos <span class="sr-only">(current)</span></a></li>
            <li ng-class="{ active: isActive('/nota')}"><a href="/notas"><i class="glyphicon glyphicon-tags"></i> Notas<span class="sr-only">(current)</span></a></li>

          </ul>
           
           <ul class="nav nav-sidebar">
            <li ng-class="{ active: isActive('/host')}"><a href="/hosts"><i class="glyphicon glyphicon-flash"></i> Hosts<span class="sr-only">(current)</span></a></li>
            <li ng-class="{ active: isActive('/enlace')}"><a href="/enlaces"><i class="glyphicon glyphicon-link"></i> Enlaces</a></li>
          </ul>
        </div>
        <div class="col-sm-9 col-sm-offset-3 col-md-10 col-md-offset-2 main">
            
            <div ng-view></div>  
            <toaster-container toaster-options="
               {
                'time-out': 2000, 
                'close-button':true,
                'onclick': null,
                'showDuration': '200',
                'hideDuration': '1000',
                'extendedTimeOut': '1000',
                'showEasing': 'swing',
                'hideEasing': 'linear',
                'showMethod': 'fadeIn',
                'hideMethod': 'fadeOut'
                }
            ">
            </toaster-container>     
        </div>
      </div>
    </div>
    