(function () {
    'use strict';

    angular
        .module('app')
        .constant('toastr', toastr)
        .constant('API_CONFIG', {
            //url: 'https://noc.telemidia.net.br/api_new'
            //url: 'http://noc2.telemidia.net.br/api/v3'
            //url: 'http://noc2.telemidia.net.br/rc/api'
            url: 'http://noc-staging.telemidia.net.br:4000',
            api_juniper_url: 'https://api.telemidia.net.br',
            netconfws: 'https://api.telemidia.net.br:5000/test',
            //netconfws: 'https://api.telemidia.net.br:5010/test',
            authws: 'https://api.telemidia.net.br:5003/app',
            nocws: 'https://api.telemidia.net.br:5001/tl1',
            helpdeskws: 'https://api.telemidia.net.br:5001/helpdeskws',
            pingws: 'https://api.telemidia.net.br:5002/ping',
            authmonitor: 'https://api.telemidia.net.br:5004/monitor',
            //pingws: 'http://localhost:5002/ping',
            //juniper_user: 'noc',
            //juniper_pass: 'CQmqPtkISSgJsvN7Ub6MUI23j9FNVCd4xdxWfBk4BkNtV5UTEW'
            api_juniper_token: 'bm9jOkNRbXFQdGtJU1NnSnN2TjdVYjZNVUkyM2o5Rk5WQ2Q0eGR4V2ZCazRCa050VjVVVEVX'
        })

        .constant('ZTE_API', {
            url: 'https://api.telemidia.net.br:8000',
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NDI3ODAzNzIsImV4cCI6MzMxNjgxNTMxNzJ9.OgEbhlHCXVUhzT8DZ5amCpzJSCswODxSgCDYoDRllz0'
        })

        .constant('MIKROTIK_API', {url: 'https://api.telemidia.net.br:32000'})

        .constant('PORTAL_ASSINANTE_API', { url: 'https://portal.telemidia.net.br:8000' })

        .constant('GENIE_API', { url: 'https://genie-api.telemidia.net.br:9000' })

        .constant('GENIE_ACS', { url: 'http://acs.telemidia.net.br:3000' })

        .constant('SIGNAL_COLLECT_API', { url: 'https://api.telemidia.net.br:5005/signalcollect' })

        .constant('RAS_SERVER', {
            urlPrefix: 'https://',
            urlPostfix: '.ras.telemidia.net.br:8080'
        })

        .constant('DIR_ANEXOS', {
            //url: 'http://noc2.telemidia.net.br/app/anexos'
            url: 'https://noc.telemidia.net.br/anexos'
        })
        .constant('moment', moment)
        .constant('SCRIPT_ERROR_MSG', 'Ocorreu um erro na aplicação e os detalhes foram coletados.')
        .constant('LOGGING_URL', '/errors/javascript');

})();
