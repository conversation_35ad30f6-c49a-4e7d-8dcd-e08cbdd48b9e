<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-phone"></i> Analises Assistidas</li>

</ol>
<div class="barra">
    <div class="form-group">

    <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="ALC.termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="ALC.busca(ALC.termos)">Pesquisar</button>
                <button class="btn btn-default filter-col" ng-click="ALC.limpar()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
        </div>
      </div>
</div>

<div class="table-responsive">
    <span class="counter pull-right"></span>

            <table class="table table-striped table-hover table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center"> ID</th>
                  <th class="vert-align text-center"> Data</th>
                  <th class="vert-align text-center"> IP</th>
                  <th class="vert-align text-center"> Resultados</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="analise in ALC.analises">
                    <td class="vert-align text-center">{{:: analise.id}}</td>
                    <td class="vert-align text-center">{{:: analise.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{:: analise.ip}}</td>
                    <td class="vert-align" style="white-space:pre-wrap">{{:: analise.resultados}}</td>
                </tr>
              </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="ALC.pagination.size" ng-model="ALC.pagination.page" ng-change="ALC.pageChanged()" items-per-page="ALC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Página <span class="badge">{{ ALC.pagination.page}}</span> de  <span class="badge">{{ ALC.pagination.pages}}</span> de <span class="badge">{{ ALC.pagination.size}}</span> registro(s)</span>
            </div>
          </div>
