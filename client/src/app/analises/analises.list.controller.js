(function () {
    'use strict';

    angular
        .module('app')
        .controller('AnalisesListController', AnalisesListController);

    /** @ngInject */
    function AnalisesListController($http, API_CONFIG, cfpLoadingBar, $rootScope, AnalisesService,
        toaster, $routeParams, $location) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo='ip';
        vm.termos='';
        vm.analises = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.limpar = limpar;

        activate();

        function activate() {

            if($routeParams.id !== undefined){
              vm.tipo='id';
              vm.termos=$routeParams.id;
            }

            if($routeParams.ip !== undefined){
                vm.tipo='ip';
                vm.termos=$routeParams.ip;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
              }

            getData();
        }

        function getData() {

            var urlApi = API_CONFIG.url + '/helpdesk/analises?page=' + $routeParams.page + "&count=" +
              vm.limit + vm.filtro;

            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.analises);
                angular.copy(response.data.pagination, vm.pagination);
            });
        }

        function pageChanged() {
            var urlApi = '/helpdesk/analises?page=' + vm.pagination.page + '&' + vm.tipo + '=' + vm.termos;
            $location.url(urlApi);
        }

        function busca(termos) {

            vm.pagination.page = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            pageChanged();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.termos = '';
          $location.url('/helpdesk/analises');
        }
    }

})();
