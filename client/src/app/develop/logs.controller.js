(function () {
    'use strict';

    angular
        .module('app')
        .controller('LogsController', LogsController);

    /** @ngInject */
    function LogsController($http, API_CONFIG) {

        var vm = this;

        vm.resultados = [];

        activate();

        function activate(){
            atualiza();
        }

        function atualiza(){
            vm.resultados = [];
            $http.get(API_CONFIG.url+'/log/erros')
                .then(function(response) {
                    angular.copy(response.data, vm.resultados);
                });  
        }

    }

})();