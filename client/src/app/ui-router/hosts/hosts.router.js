'use strict';

angular.module('app')

    .config(function ($stateProvider) {
        $stateProvider

      .state('host', {
        url:         '/host/:hostId',
        controller: 'HostController',
        controllerAs: 'HC',
        resolve: {
          host: function($stateParams, HostsService) {
            return HostsService.get({id: $stateParams.hostId}).$promise;
          }
        },  
        templateUrl: 'app/ui-router/hosts/host.form.html',
        authorize: ['hosts.read', 'hosts.write']
      })
      .state('host.dados', {
        url:         '/dados',
        controller: 'HostDadosController',
        controllerAs: 'HDC',
        resolve: {
          pops: function(PopsNomesService){
            return PopsNomesService.getNomes().$promise;
          },
          hoststipos: function(HostsTipos){
            return HostsTipos.getHostsTipos().$promise;
          },
          hoststecnologias: function(HostsTecnologias){
              return HostsTecnologias.getHostsTecnologias().$promise;
          }
        },          
        templateUrl: 'app/ui-router/hosts/host.dados.html',
        authorize: ['hosts.read', 'hosts.write']
      })
      .state('host.parametros', {
        url:         '/parametros',
        controller: 'HostParametrosController',
        controllerAs: 'HPC',
        resolve: {
          hardwares: function(HardwareService){
            return HardwareService.getHardwares().$promise;
          }
        },          
        templateUrl: 'app/ui-router/hosts/host.parametros.html'
      });

    });  


    