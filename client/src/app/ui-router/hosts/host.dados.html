<div class="row top-buffer">
    <form class="form-horizontal" name="frmHost">
      <div class="form-group">
        <label class="col-md-2 control-label" for="nome"> Nome <i
                                  class="glyphicon glyphicon-asterisk text-danger" style="font-size: 11px;"></i></label>
    <div class="col-md-9">
      <div class="form-inline">
            <div class="form-group">
                         <ol class="nya-bs-select form-control"
                                id="tipo"
                                title="Tipo"
                                ng-model="HDC.host.tipo"
                                ng-change="HDC.atualizaTela()"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                required
                            >
                                <li nya-bs-option="tipo in HDC.hoststipos" data-value="tipo.id">
                                    <a>
                                        {{ ::tipo.tipo }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign"></i>
                            <ol class="nya-bs-select form-control"
                                id="pop"
                                title="Selecione um POP"
                                ng-model="HDC.host.pop"
                                ng-change="HDC.atualizaTela()"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                required
                            >
                                <li nya-bs-option="pop in HDC.pops" data-value="pop.id">
                                    <a>
                                        {{::pop.tipo}} {{ ::pop.cidade_sigla }} {{ ::pop.nome }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign"  ng-show="HDC.popdest !== 0"></i>
                            <ol class="nya-bs-select form-control animate-show"
                                ng-show="HDC.popdest !== 0"
                                id="popdest"
                                title="Selecione o POP de destino"
                                ng-model="HDC.host.popdest"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                ng-required="HDC.popdest == 2"
                            >

                                <li nya-bs-option="pop in HDC.pops_dest" data-value="pop.id">
                                    <span class="dropdown-header">{{$group}}</span>
                                    <a>
                                        {{::pop.tipo}} {{ ::pop.cidade_sigla }} {{ ::pop.nome }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign" ng-show="HDC.tecnologia !== 0"></i>
                            <ol class="nya-bs-select form-control"
                                id="tecnologia"
                                ng-show="HDC.tecnologia !== 0"
                                title="Tecnologia (Opcional)"
                                ng-model="HDC.host.tecnologia"
                                disabled="eventoSelecionado.id == ''"
                                data-live-search="true"
                                data-size="8"
                                ng-required="tecnologia == 2"
                            >
                                <li nya-bs-option="tec in HDC.hoststecnologias" data-value="tec.id">
                                    <a>
                                        {{ ::tec.tecnologia }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>
                            <i class="glyphicon glyphicon-plus-sign" ng-show="HDC.identificador !== 0"></i>
                            <input type="text" class="form-control"
                                   id="identificacao"
                                   ng-show="HDC.identificador == 2"
                                   placeholder="Identificação (Obrigatório)"
                                   ng-model="HDC.host.identificacao"
                                   ng-disabled="eventoSelecionado.id == ''"
                                   ng-required="HDC.identificador == 2"
                                   required
                            >
                            <input type="text" class="form-control"
                                   id="identificacao"
                                   ng-if="HDC.identificador == 1"
                                   placeholder="Identificação (Opcional)"
                                   ng-model="HDC.host.identificacao"
                                   ng-disabled="eventoSelecionado.id == ''"
                                   ng-required="HDC.identificador == 2"
                                   required
                            >
                            <i class="glyphicon glyphicon-question-sign"
                               style="font-size: 13px; color:cornflowerblue;"
                               uib-tooltip="Digite apenas um número ou sigla que identifica o host (Ex.: 1, 1-2-3, A)"
                               tooltip-placement="right"
                               ng-show="HDC.identificador !== 0"
                            ></i>
            </div>
        </div>
    </div>
  </div>
  
  <div class="form-group">
    <label class="col-md-2 control-label" for="telamapa"><i
                                        class="glyphicon glyphicon-question-sign"
                                        style="font-size: 13px;color:cornflowerblue;"
                                        uib-tooltip="Especifica a tela do mapa em que este host será inserido"
                                        tooltip-placement="bottom"></i> Tela do mapa</label>
                                <div class="col-md-1">
                                    <select id="telamapa" class="form-control input-md"
                                           ng-model="HDC.host.telamapa" ng-disabled="eventoSelecionado.id == ''"
                                           ng-options="o for o in HDC.telasmapa">
                                    </select>
                                </div>
  </div>
  <div class="form-group">
    <label class="col-md-2 control-label">Status</label>
    <div class="col-md-3">
      <span class="label label-success" ng-if="HDC.host.ativo==1">Ativo</span>
      <span class="label label-danger" ng-if="HDC.host.ativo==0">Inativo</span>
      {{::HDC.host.data_status | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
    </div>
  </div>

  <hr />
  <div class="form-group">
    <label class="col-md-2 control-label" for="observacao">Observação</label>
    <div class="col-md-6">
      <textarea id="observacao" class="form-control input-md" ng-model="HDC.host.observacao"
                ng-disabled="eventoSelecionado.id == ''"></textarea>
    </div>
  </div>
  <div class="form-group top-buffer" ng-show="eventoSelecionado.id != ''">
    <div class="col-md-2"></div>
    <div class="col-md-6">
      <button ng-click="HDC.save(HDC.host)" class="btn btn-md btn-primary btn-info" type="submit"
              ng-disabled="frmHost.$invalid">
          <i class="glyphicon glyphicon-check"></i> Salvar
      </button>
    </div>
  </div>
</form>


</div>