(function() {
  'use strict';

  angular
    .module('app')
    .controller('HostController', HostController);

  /** @ngInject */

  function HostController(host, Host) {

    var vm = this;

    vm.host = host.dados[0];

    angular.copy(vm.host, Host);

    vm.tabData   = [
      {
        heading: 'Dad<PERSON>',
        route:   'host.dados'
      },
      {
        heading: 'Parâmetros',
        route:   'host.parametros'
      },
      {
        heading: 'Serviços',
        route:   'host.servicos'
      },
      {
        heading: 'Backup',
        route:   'host.backup'
      }

    ];

  }

})();