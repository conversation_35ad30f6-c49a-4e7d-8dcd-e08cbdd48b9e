(function() {
  'use strict';

  angular
    .module('app')
    .controller('HostDadosController', HostDadosController);

  /** @ngInject */

  function HostDadosController($filter, host, pops, hoststipos, hoststecnologias, Host, PopsMapasComboService) {

    var vm = this;

    vm.atualizaTela = atualizaTela;

    activate();

    function activate(){
      vm.host = host.dados[0];
      vm.pops = pops;
      vm.hoststipos = hoststipos;
      vm.hoststecnologias = hoststecnologias;
      vm.pops_dest = [];
      vm.inserindo = false;
      vm.popdest = 0;
      vm.tecnologia = 0;
      vm.identificador = 0;
      vm.valores_anteriores = angular.copy(host.dados[0]);
      atualizaTela();
    }
    
    function atualizaTela(){

      vm.telasmapa = [1];
      var result = $filter('filter')(vm.hoststipos, {id: vm.host.tipo}, true);
      if(result[0] !== undefined) {
        vm.popdest = result[0].pop_dest;
        vm.tecnologia = result[0].tecnologia;
        vm.identificador = result[0].identificador;
        result = $filter('filter')(vm.pops, {id: vm.host.pop}, true);
        if(result[0] !== undefined) {
          PopsMapasComboService.getMapas({popid: result[0]["popid"]}, function (response) {
            vm.telasmapa = vm.telasmapa.concat(response);
          });
          vm.pops_dest = [];
          var pops = $filter('filter')(vm.pops, {pop_pai: result[0]["pop"]}, true);
          vm.pops_dest = vm.pops_dest.concat(pops);
          pops = $filter('filter')(vm.pops, {pop_pai_backup: result[0]["pop"]}, true);
          vm.pops_dest = vm.pops_dest.concat(pops);
          pops = $filter('filter')(vm.pops, {pop: result[0]["pop_pai"]}, true);
          vm.pops_dest = vm.pops_dest.concat(pops);
          pops = $filter('filter')(vm.pops, {pop: result[0]["pop_pai_backup"]}, true);
          vm.pops_dest = vm.pops_dest.concat(pops);
        }    
      }
      if(vm.inserindo){
        vm.host.telamapa = 1;
      }   
    };    

  }

})();