<span class="counter pull-right"></span>
<br>
  <div class="panel panel-default" ng-show="parametros.length > 0" >
  <div class="panel-heading">Parâmetro atual</div>
  <div class="panel-body">

      <table class="table">
        <thead>
          <tr>
            <th>Hardware</th>
            <th>IP</th>
            <th>MAC</th>
            <th>Proprietário</th>
            <th>Local</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ ultimoparametro.nomehardware}}</td>
            <td>{{ ultimoparametro.ip}}</td>
            <td>{{ ultimoparametro.mac}}</td>
            <td>{{ ultimoparametro.modooperacao}}</td>
            <td>{{ ultimoparametro.local}}</td>
          </tr>
        </tbody>
      </table>

  </div>
</div>

<div class="navbar">
    <form class="navbar-form navbar-left">
       <button type="button" class="btn btn-success" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-show="eventoSelecionado.id != ''" ng-click="novo()"><i class="glyphicon glyphicon-plus"></i> Adicionar Parâmetro</button>
       <button type="button" class="btn btn-warning" ng-show="eventoSelecionado.id != ''" ng-click="clone()" data-toggle="modal" data-target="#frmparametros"><i class="glyphicon glyphicon-copy"></i> Clonar Último Parâmetro</button>

    </form>
    <form class="navbar-form">
        <div class="form-group pull-right">
        <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar...">
        </div>
    </form>
  </div>

    <div class="well well-sm">Histórico de alterações <span class="badge">{{:: parametros.length}}</span></div>
    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Evento</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">Hardware</th>
          <th class="vert-align text-center">IP</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Proprietário</th>
          <th class="vert-align text-center">Local</th>
          <th class="vert-align text-center">Observação</th>
        </tr>
      </thead>
      <tbody>
               <tr ng-repeat="parametro in parametros | filter:filtro" ng-class="{'success': parametro.idevento == eventoSelecionado.id}">
            <td class="vert-align text-center"><a href="/eventos/{{:: parametro.idevento}}">#{{:: parametro.idevento}}</a></td>
            <td>{{:: parametro.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td>{{:: parametro.username}}</td>
            <td>{{:: parametro.nomehardware}}</td>
            <td>{{:: parametro.ip}}</td>
            <td>{{:: parametro.mac}}</td>
            <td>{{:: parametro.modooperacao}}</td>
            <td>{{:: parametro.local}}</td>
            <td>{{:: parametro.observacao}}</td>
      </tbody>
      </table>