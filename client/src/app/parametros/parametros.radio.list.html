<div class="barra">
    <div class="form-group">
    <!-- Parâmetros -->    
    <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmparametrosradio" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="novo()" authorize="['servicos.write']"><i class="glyphicon glyphicon-plus"></i> <br>Incluir</button>
           <button type="button" class="btn btn-warning btn-incluir text-center" ng-show="eventoSelecionado.id != ''" ng-click="clone()" data-toggle="modal" data-target="#frmparametrosradio" authorize="['servicos.write']"><i class="glyphicon glyphicon-copy"></i><br>Clonar</button>   
    </div>
</div>    

<div ng-if="parametros_radio.length > 0">
<h5>Parâmetro Atual</h5>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>SSID</th>
            <th>MAC</th>
            <th>Protocolo</th>
            <th>Criptografia</th>
            <th>Altura Antena</th>
            <th>Polarização</th>
            <th>Frequência</th>
            <th>Potência</th>
            <th>Ganho</th>
            <th>Perdas</th>
          </tr>
        </thead>
        <tbody>
          <tr>
                <td>{{parametros_radio[0].ssid}}</td>
                <td>{{parametros_radio[0].mac}}</td>
                <td>{{parametros_radio[0].protocoloradio}}</td>
                <td>{{parametros_radio[0].criptografia}}</td>
                <td>{{parametros_radio[0].alturaantena_m}}</td>
                <td>{{parametros_radio[0].polarizacao}}</td>
                <td>{{parametros_radio[0].frequencia_mhz}}</td>
                <td>{{parametros_radio[0].potencia_dbm}}</td>
                <td>{{parametros_radio[0].ganho_dbi}}</td>
                <td>{{parametros_radio[0].perdas_db}}</td>
          </tr>
        </tbody>
      </table>
 </div>



<h5>Histórico de alterações <span class="badge">{{parametros_radio.length}}</span></h5>

<table class="table table-striped table-hover table-bordered">

               <thead>
     <tr>
                  <th>Evento</th>
                  <th>Data</th>
                  <th>Operador</th>
                  <th>SSID</th>
                  <th>MAC</th>
                  <th>Protocolo</th>
                  <th>Criptografia</th>
                  <th>Altura Antena</th>
                  <th>Polarizacao</th>
                  <th>Frequência (Mhz)</th>
                  <th>Potência (dBm)</th>
                  <th>Ganho (dBi)</th>
                  <th>Perdas (dB)</th>
                  <th>Observação</th>
                </tr>
              
              <tbody>
                <tr ng-repeat="parametro_radio in parametros_radio | filter: filtro" ng-class="{'success': parametro_radio.idevento == eventoSelecionado.id}">
                   <td class="vert-align text-center"><a href="/eventos/{{::parametro_radio.idevento}}">#{{::parametro_radio.idevento}}</a></td>
                    <td>{{::parametro_radio.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td>{{::parametro_radio.username}}</td>
                    <td>{{::parametro_radio.ssid}}</td>
                    <td>{{::parametro_radio.mac}}</td>
                    <td>{{::parametro_radio.protocoloradio}}</td>
                    <td>{{::parametro_radio.criptografia}}</td>
                    <td>{{::parametro_radio.alturaantena_m}}</td>
                    <td>{{::parametro_radio.polarizacao}}</td>
                    <td>{{::parametro_radio.frequencia_mhz}}</td>
                    <td>{{::parametro_radio.potencia_dbm}}</td>
                    <td>{{::parametro_radio.ganho_dbi}}</td>
                    <td>{{::parametro_radio.perdas_db}}</td>
                    <td>{{::parametro_radio.observacao}}</td>
                </tr>
              </tbody>
            </table>
