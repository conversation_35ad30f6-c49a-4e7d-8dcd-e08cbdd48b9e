'use strict';

angular.module('app')

    .factory('ParametrosRadioService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:servico/parametros', {},

            {
                getParametrosRadio: {method: 'GET', isArray: false},
                insertParametrosRadio: {
                    method: 'POST',
                    params: {servico: '@servico'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('ParametrosUltimoService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:servico/ultimoparametro', {},

            {
                getParametros: {method: 'GET', isArray: false}

            }
        );
    });



