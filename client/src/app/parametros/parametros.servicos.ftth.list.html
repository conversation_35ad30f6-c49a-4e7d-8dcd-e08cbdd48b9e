<div class="barra">
    <div class="form-group">
    <!-- Parâmetros -->
    <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmparametrosftth" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="novo()" authorize="['servicos.write']"><i class="glyphicon glyphicon-plus"></i> <br>Incluir</button>
           <button type="button" class="btn btn-warning btn-incluir text-center" ng-show="eventoSelecionado.id != ''" ng-click="clone()" data-toggle="modal" data-target="#frmparametrosftth" authorize="['servicos.write']"><i class="glyphicon glyphicon-copy"></i><br>Clonar</button>
    </div>
</div>

<div ng-if="parametros_ftth.length > 0">
<h5>Parâmetro Atual</h5>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="vert-align text-center">Descrição</th>
            <th class="vert-align text-center">Porta ONU</th>
            <th class="vert-align text-center">VLAN Id</th>
            <th class="vert-align text-center">Tipo VLAN</th>
            <th class="vert-align text-center">ID Pacote</th>
          </tr>
        </thead>
        <tbody>
          <tr>
                <td class="vert-align text-center">{{parametros_ftth[0].descricao}}</td>
                <td class="vert-align text-center">{{parametros_ftth[0].porta_onu}}</td>
                <td class="vert-align text-center">{{parametros_ftth[0].vlan_id}}</td>
                <td class="vert-align text-center">{{parametros_ftth[0].tipo_vlan}}</td>
                <td class="vert-align text-center">{{parametros_ftth[0].id_pacote}}</td>
          </tr>
        </tbody>
      </table>
 </div>



<h5>Histórico de alterações <span class="badge">{{parametros_ftth.length}}</span></h5>

<table class="table table-striped table-hover table-bordered">

               <thead>
     <tr>
                  <th class="vert-align text-center">Evento</th>
                  <th class="vert-align text-center">Data</th>
                  <th class="vert-align text-center">Operador</th>
                  <th class="vert-align text-center">Descrição</th>
                  <th class="vert-align text-center">Porta ONU</th>
                  <th class="vert-align text-center">VLAN Id</th>
                  <th class="vert-align text-center">Tipo VLAN</th>
                  <th class="vert-align text-center">ID Pacote</th>
                </tr>

              <tbody>
                <tr ng-repeat="parametro_ftth in parametros_ftth | filter: filtro" ng-class="{'success': parametro_ftth.idevento == eventoSelecionado.id}">
                   <td class="vert-align text-center"><a href="/eventos/{{::parametro_ftth.idevento}}">#{{::parametro_ftth.idevento}}</a></td>
                    <td class="vert-align text-center">{{::parametro_ftth.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.username}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.descricao}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.porta_onu}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.vlan_id}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.tipo_vlan}}</td>
                    <td class="vert-align text-center">{{::parametro_ftth.id_pacote}}</td>
                </tr>
              </tbody>
            </table>
