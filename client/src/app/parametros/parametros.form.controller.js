(function () {
    'use strict';

    angular
        .module('app')
        .controller('ParametrosFormController', ParametrosFormController);

    /** @ngInject */
    function ParametrosFormController($filter, $scope, $rootScope, $route, HardwareService, ParametrosService, ModoService, toaster) {

        var vm = this;

        vm.gerenciavel = 0;
        vm.hardwares = [];
        vm.modos = [];
        vm.parametro = {};
        vm.atualizaTela = atualizaTela;
        vm.saveParametro = saveParametro;
        vm.novo = novo;
        vm.clone = clone;

        activate();

        function activate() {
            getHardwares();
            getModos();
        }
        
        function getHardwares() {
            HardwareService.getHardwares()
                .then(function(hardwares) {
                    vm.hardwares = hardwares;
                },
                function(data) {
                    console.log('erro ao recuperar lista de hadwares.')
                });
        };
        
        function getModos() {
            ModoService.getModos()
                .then(function(modos) {
                    vm.modos = modos;
                },
                function(data) {
                    console.log('erro ao recuperar lista de modos.')
                });
        };
        
        function saveParametro(parametro) {
            
            var novoParametro = {};
            var host = $route.current.params.host;
            novoParametro = angular.copy(parametro);
            novoParametro.host = host;

            novoParametro.idevento = $rootScope.eventoSelecionado.id;
            novoParametro.username = $rootScope.operador.username;

            ParametrosService.insertParametros(novoParametro, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");
                    console.log($scope);
                    
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        };
        
        function novo() {

            vm.parametro = {};
           

        };

        
        function clone() {
            angular.copy($scope.HFC.ultimoparametro, vm.parametro);
            atualizaTela();
        };
        
        function atualizaTela(){
           
            var result = $filter('filter')(vm.hardwares, {id: vm.parametro.hardware}, true);
            if(result[0] !== undefined) {
              vm.gerenciavel = result[0].gerenciavel;
            }
        };
    }

})();