
<!-- Parametros radio modal -->
<div class="modal fade" id="frmparametrosradio" tabindex="-1" role="dialog"
     aria-labelledby="frmparametrosradiolabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- <PERSON><PERSON>er -->
            <div class="modal-header">
                <button type="button" class="close"
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmparametroslabel">
                    Parâmetros Rádio
                </h4>
            </div>

            <!-- Modal Body -->

            <div class="modal-body">
                <form role="form" name="frmRadio">

                  <div class='row'>
                    <div class='col-sm-8'>
                        <div class='form-group'>
                            <label for="ssid">SSID</label>
                              <input type="text" class="form-control"
                          id="ssid" placeholder="" ng-model="parametro_radio.ssid" required="required"/>
                        </div>
                    </div>
                    <div class='col-sm-4'>
                        <div class='form-group'>
                            <label for="frequencia">Frequência (Mhz)</label>
                          <input type="text" class="form-control"
                          id="frequencia" ng-model="parametro_radio.frequencia_mhz" required="required"/>
                        </div>
                    </div>

                </div>

                   <div class='row'>
                    <div class='col-sm-6'>
                        <div class='form-group'>
                            <label for="mac">MAC</label>
                              <input type="text" class="form-control"
                          id="mac" placeholder="" ng-model="parametro_radio.mac"/>
                        </div>
                    </div>

                       <div class='col-sm-6'>
                        <div class='form-group'>
                            <label for="protocolo">Protocolo</label>
                              <input type="text" class="form-control"
                          id="protocolo" placeholder="" ng-model="parametro_radio.protocoloradio"/>
                        </div>
                    </div>


                </div>

                 <div class="row">
                    <div class='col-sm-4'>
                           <div class='form-group'>
                            <label for="interface">Interface</label>
                              <input type="text" class="form-control"
                          id="interface" ng-model="parametro_radio.interface"/>
                        </div>
                     </div>
                    <div class='col-sm-4'>
                        <div class='form-group'>
                            <label for="polarizacao">Polarizacao</label>
                              <select id="polarizacao" ng-model="parametro_radio.polarizacao" class="form-control">
                                  <option value="X">X</option>
                                  <option value="H">H</option>
                                  <option value="V">V</option>
                              </select>
                        </div>
                    </div>
                    <div class='col-sm-4'>
                        <div class='form-group'>
                            <label for="criptografia">Criptografia</label>
                              <input type="text" class="form-control"
                          id="criptografia" ng-model="parametro_radio.criptografia"/>
                        </div>
                    </div>
                </div>

                <div class='row'>
                    <div class='col-sm-3'>
                        <div class='form-group'>
                            <label for="alturaantena">Altura Antena</label>
                      <input type="text" class="form-control"
                          id="alturaantena" ng-model="parametro_radio.alturaantena_m" required="required"/>
                        </div>
                    </div>

                    <div class='col-sm-3'>
                        <div class='form-group'>
                             <label for="potencia">Potência (dBm)</label>
                      <input type="text" class="form-control"
                          id="potencia" ng-model="parametro_radio.potencia_dbm" required="required"/>
                        </div>
                     </div>
                     <div class='col-sm-3'>
                        <div class='form-group'>
                            <label for="ganho">Ganho (dBi)</label>
                      <input type="text" class="form-control"
                          id="ganho" ng-model="parametro_radio.ganho_dbi" required="required"/>
                        </div>
                     </div>
                     <div class='col-sm-3'>
                        <div class='form-group'>
                            <label for="perdas">Perdas (dB)</label>
                      <input type="text" class="form-control"
                          id="perdas" ng-model="parametro_radio.perdas_db" required="required"/>
                        </div>
                    </div>
                </div>


                <div class='row'>
                    <div class='col-sm-12'>
                        <div class='form-group'>
                            <label for="observacao">Observação</label>
                      <textarea class="form-control"
                                id="observacao" ng-model="parametro_radio.observacao"/></textarea>
                        </div>
                    </div>
                </div>                                                                                            </form>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>
                <button type="button" class="btn btn-primary" ng-click="saveParametroRadio(parametro_radio);" ng-disabled="frmRadio.$invalid" data-dismiss="modal">
                    Salvar</button>
            </div>
        </div>
    </div>
</div>
