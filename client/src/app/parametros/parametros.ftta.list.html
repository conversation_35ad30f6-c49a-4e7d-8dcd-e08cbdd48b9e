<div class="barra">
        <div class="form-group">
            <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="novo()" authorize="['hosts.write']"><i class="glyphicon glyphicon-plus"></i><br>Incluir</button>
            <button type="button" class="btn btn-warning btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="clone()" authorize="['hosts.write']"><i class="glyphicon glyphicon-copy"></i><br>Clonar</button>
        </div>
</div>

<div ng-if="parametros.length > 0">

<h5>Parâmetro Atual</h5>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="vert-align text-center">No. Portas</th>
            <th class="vert-align text-center">CDO</th>
            <th class="vert-align text-center">CAT</th>
            <th class="vert-align text-center">Porta ligação (CAT/CDO)</th>

          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="vert-align text-center">{{ ultimoparametro.portas}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.cdo}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.cat}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.porta}}</td>
          </tr>
        </tbody>
      </table>
</div>

    <h5>Histórico de alterações <span class="badge">{{parametros.length}}</span></h5>

    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Evento</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">No. Portas</th>
            <th class="vert-align text-center">CDO</th>
            <th class="vert-align text-center">CAT</th>
            <th class="vert-align text-center">Porta</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="parametro in parametros | filter:filtro" ng-class="{'success': parametro.idevento == eventoSelecionado.id}">
            <td class="vert-align text-center"><a href="/eventos/{{:: parametro.idevento}}">#{{:: parametro.idevento}}</a></td>
            <td class="vert-align text-center">{{ parametro.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{ parametro.username}}</td>
            <td class="vert-align text-center">{{ parametro.portas}}</td>
            <td class="vert-align text-center">{{ parametro.cdo}}</td>
            <td class="vert-align text-center">{{ parametro.cat}}</td>
            <td class="vert-align text-center">{{ parametro.porta}}</td>
      </tbody>
      </table>