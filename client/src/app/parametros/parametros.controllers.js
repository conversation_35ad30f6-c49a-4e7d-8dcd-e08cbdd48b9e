'use strict';

angular.module('app')

    .controller('ParametrosCtrl', function ($scope, $rootScope, $route, $filter,
        HardwareService, ParametrosService, ParametrosFtthService, ParametrosFttaService, ModoService,
        OltListService, $http, API_CONFIG, toaster, Parametro, focus, Host) {

        $scope.parametros = [];
        $scope.ultimoparametro = {};
        $scope.gerenciavel = 0;

        $scope.hardwares = [];
        $scope.modos = [];
        $scope.tipobusca = 'patrimonio';
        $scope.patrimonios = [];
        $scope.patrimonioselecionado = {};
        $scope.patrimoniolegivel = '1';
        $scope.tipos = [];
        $scope.material = {};

        $scope.getHardwares = function() {
            HardwareService.getHardwares()
                .then(function(hardwares) {
                    $scope.hardwares = hardwares;
                },
                function(data) {
                    console.log('erro ao recuperar lista de hadwares.')
                });
        };

        $scope.getHardwares();

        $scope.getModos = function() {
            ModoService.getModos()
                .then(function(modos) {
                    $scope.modos = modos;
                },
                function(data) {
                    console.log('erro ao recuperar lista de modos.')
                });
        };

        $scope.getModos();

        $scope.getOlts = function(){

          var OltList = OltListService.getAll();
            OltList.$promise
              .then(function (data) {
                $scope.olts = data;
            });
        };

        $scope.getOlts();



        //var Parametros = ParametrosService.get({id: $route.current.params.id});

       if(Host.tipo == 15){
         var Parametros = ParametrosFtthService.get({id: $route.current.params.id});
       } else if(Host.tipo == 28){
         var Parametros = ParametrosFttaService.get({id: $route.current.params.id});
       } else {
         var Parametros = ParametrosService.get({host: $route.current.params.host});
       }

        Parametros.$promise.then(function (data) {
            $scope.parametros = data.dados;
            $scope.ultimoparametro = angular.copy($scope.parametros[0]);
            $scope.$parent.HFC.parametro = $scope.ultimoparametro;
        });

        //var host_id = $route.current.params.id;
        var host = $route.current.params.host;

        $scope.saveParametro = function (parametro) {
            var novoParametro = {};
            novoParametro = angular.copy(parametro);
            //novoParametro.host_id = host_id;
            novoParametro.host = Host.host;

            novoParametro.idevento = $rootScope.eventoSelecionado.id;
            novoParametro.username = $rootScope.operador.username;
            novoParametro.mac = $scope.patrimonioselecionado.mac;
            novoParametro.patrimonio = $scope.patrimonioselecionado.patrimonio;
            novoParametro.material = $scope.material;

            ParametrosService.insertParametros(novoParametro, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");

                    //var Parametros = ParametrosService.get({id: $route.current.params.id});
                    var Parametros = ParametrosService.get({host: $route.current.params.host});
                    Parametros.$promise.then(function (data) {
                        $scope.parametros = angular.copy(data.dados);

                        $scope.ultimoparametro = angular.copy($scope.parametros[0]);
                        //angular.copy($scope.parametros[0], Parametro);
                        $scope.parametro = $scope.ultimoparametro;

                    });


                } else {
                    //alert('Ocorreu um erro: ' + response.dados);
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

        $scope.saveParametroFtth = function (parametro) {
            var novoParametro = {};
            novoParametro = angular.copy(parametro);

            novoParametro.host_id = Host.id;
            novoParametro.idevento = $rootScope.eventoSelecionado.id;
            novoParametro.username = $rootScope.operador.username;
            novoParametro.mac = $scope.patrimonioselecionado.mac;
            novoParametro.patrimonio = $scope.patrimonioselecionado.patrimonio;

            ParametrosFtthService.insertParametros(novoParametro, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");

                    var Parametros = ParametrosFtthService.get({id: $route.current.params.id});
                    Parametros.$promise.then(function (data) {
                        $scope.parametros = angular.copy(data.dados);
                        $scope.ultimoparametro = angular.copy($scope.parametros[0]);
                        $scope.parametro = $scope.ultimoparametro;

                    });


                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });

        };


        $scope.saveParametroFtta = function (parametro) {
          var novoParametro = {};
          novoParametro = angular.copy(parametro);

          novoParametro.host_id = Host.id;
          novoParametro.idevento = $rootScope.eventoSelecionado.id;
          novoParametro.username = $rootScope.operador.username;
          novoParametro.mac = $scope.patrimonioselecionado.mac;
          novoParametro.patrimonio = $scope.patrimonioselecionado.patrimonio;

          ParametrosFttaService.insertParametros(novoParametro, function (response) {
              if (response.status === 'OK') {
                  toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");

                  var Parametros = ParametrosFttaService.get({id: $route.current.params.id});
                  Parametros.$promise.then(function (data) {
                      $scope.parametros = angular.copy(data.dados);
                      $scope.ultimoparametro = angular.copy($scope.parametros[0]);
                      $scope.parametro = $scope.ultimoparametro;

                  });

              } else {
                  toaster.pop('error', response.mensagem, response.dados);
              }
          });

      };

        $scope.seleciona = function(patrimonio){
          $scope.patrimonioselecionado = patrimonio;
        }

        $scope.clone = function () {

            //angular.copy(Parametro, $scope.parametro);
            $scope.parametro = $scope.ultimoparametro;
            $scope.atualizaTela();

        };

        $scope.novo = function () {

            Parametro = {};
            $scope.parametro = {};

        };

        $scope.atualizaTela = function(){

            var result = $filter('filter')($scope.hardwares, {id: $scope.parametro.hardware}, true);
            if(result[0] !== undefined) {
              $scope.gerenciavel = result[0].gerenciavel;
            }
        };

        $scope.buscaPatrimonio = function(termos, tipo){
            $scope.patrimonioselecionado = {};
            if($scope.tipobusca=='patrimonio'){
              var s = '?patrimonio=' + termos;
            } else {
              if(tipo !== '')
                 var s = '?mac=|' + termos + '|&tipo=' + tipo;
              else
                var s = '?mac=|' + termos;

            }
            $http.get(API_CONFIG.url + '/materiais' + s, {}).then(function(response){
            if(response.data.rows.length > 0){
              $scope.patrimonios = response.data.rows;
            } else {
              $scope.patrimonios = [];
            }

          });
        }

        $scope.changeTipo = function(tipo){
            $scope.tipobusca = tipo;
            $scope.patrimonio = undefined;
            $scope.parametro.patrimonio = '';
            $scope.parametro.mac = '';
            focus(tipo);
        }

        $scope.changeProprietario = function(){
          $scope.patrimonioselecionado = {};
          $scope.patrimonios = [];
        }

        $scope.getTipos = function(){
          $http.get(API_CONFIG.url + '/tipos?categoria=1').then(function (response, status) {
             $scope.tipos = response.data;
          });
        }

        $scope.getTipos();

        $scope.getMarcas = function(){
          $http.get(API_CONFIG.url + '/marcas?tipo=' + $scope.material.tipo).then(function (response, status) {
             $scope.marcas = response.data;
             $scope.modelos = [];
             $scope.material.marca=null;
             $scope.material.modelo=null;
          });
        }

        $scope.getModelos = function(){
          $http.get(API_CONFIG.url + '/modelos?tipo=' + $scope.material.tipo+'&marca='+$scope.material.marca).then(function (response, status) {
             $scope.modelos = response.data;
             $scope.material.modelo=null;
          });
        }

        $scope.changeModelo = function(){
          console.log('changeModelo');
          var result = $filter('filter')($scope.modelos, {modelo: $scope.material.modelo}, true);

          if(result[0] !== undefined) {
            $scope.mac_enabled = result[0].mac;
            if(result[0].mac==1){
              $scope.serial_enabled=2;
            } else {
              $scope.serial_enabled = result[0].serial;
            }

          }
        }

    });
