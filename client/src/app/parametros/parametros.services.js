'use strict';

angular.module('app')

    .factory('ParametrosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:host/parametros', {},
            {
                getParametros: {method: 'GET', isArray: false},
                insertParametros: {
                    method: 'POST',
                    params: {host: '@host'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })
    .service('Parametro', function () {
        return {};
    });