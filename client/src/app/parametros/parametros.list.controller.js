(function () {
    'use strict';

    angular
        .module('app')
        .controller('ParametrosListController', ParametrosListController);

    /** @ngInject */
    function ParametrosListController($scope, $route, ParametrosService) {

        var vm = this;

        vm.parametros = [];
        vm.getParametros = getParametros;

        activate();

        function activate() {
            getParametros();
        }
        
        function getParametros(){
            var Parametros = ParametrosService.get({host: $route.current.params.host});
            Parametros.$promise.then(function (data) {
                vm.parametros = data.dados;
                $scope.HFC.ultimoparametro = angular.copy(vm.parametros[0]);
            });
        }
    }

})();