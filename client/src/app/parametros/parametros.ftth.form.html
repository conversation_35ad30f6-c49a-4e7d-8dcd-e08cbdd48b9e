        <div class="modal" id="frmparametros" tabindex="-1" role="dialog"
     aria-labelledby="frmparametroslabel" aria-hidden="true" modal="showModal" close="cancel()">

    <div class="modal-dialog">
        <div class="modal-content">
            <!-- <PERSON><PERSON> Header -->
            <div class="modal-header">
                <button type="button" class="close"
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmparametroslabel">
                    Parâmetros do equipamento
                </h4>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                <form role="form" name="frmparametros">
                  <div class="col-xs-12">
                        <div class="form-group" ng-if="tipobusca=='patrimonio'">
                            <label for="patrimonio">Patrimônio</label><span class="pull-right"><a href="" ng-click="changeTipo('mac')"><i class="glyphicon glyphicon-refresh"></i> Busca por MAC</a></span>
                             <div class="input-group ">
                            <input type="number" class="form-control" id="patrimonio" placeholder="Digite o patrimônio" ng-model="parametro.patrimonio" required autofocus ng-keydown="$event.which === 13 && buscaPatrimonio(parametro.patrimonio)">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" ng-click="buscaPatrimonio(parametro.patrimonio)" ng-disabled="parametro.patrimonio=='' || parametro.patrimonio == undefined"><i class="glyphicon glyphicon-search"></i> Buscar</button>
                            </span>
                            </div>
                        </div>

                      <div class="form-group" ng-if="tipobusca=='mac'">
                            <label for="mac">MAC</label><span class="pull-right"><a href="" ng-click="changeTipo('patrimonio')"><i class="glyphicon glyphicon-refresh"></i> Busca por Patrimônio</a></span>
                             <div class="input-group ">
                            <input type="text" class="form-control" id="mac" placeholder="Digite o MAC" ng-model="parametro.mac" required ng-keydown="$event.which === 13 && buscaPatrimonio(parametro.mac, 'ONU')">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" ng-click="buscaPatrimonio(parametro.mac, 'ONU')" ng-disabled="parametro.mac=='' || parametro.mac == undefined"><i class="glyphicon glyphicon-search"></i> Buscar</button>
                            </span>
                            </div>
                        </div>



                        <div class="form-group" ng-if="gerenciavel" ng-class="{ 'has-error': frmparametros.ip.$invalid && frmparametros.ip.$dirty }">
                            <label for="ip">IP</label>
                            <input type="text" class="form-control"
                          id="ip"
                          name="ip"
                          placeholder="IP de admin"
                          ng-model="parametro.ip"
                          ng-required="gerenciavel"
                          ng-pattern='/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/' ng-model-options="{ updateOn: 'blur' }">

                        </div>
                      <!--
                        <div class="form-group">
                            <label for="modo">Proprietário</label>
                            <select class="form-control" ng-model="parametro.modooperacao"
                              ng-options="o as o for o in modos" required></select>
                        </div>
-->
<div class="form-group">
    Resultado da busca
              <div class="pre-scrollable" style="height:150px;">
                              <table class="table table-bordered">
                                <thead>
                                    <th></th>
                                    <th class="vert-align text-center">Patrimônio</th>
                                    <th>Tipo</th>
                                    <th class="vert-align text-center">MAC</th>
                                    <th class="vert-align text-center">Marca</th>
                                    <th class="vert-align text-center">Modelo</th>
                                </thead>
                                <tbody>
                                   <tr ng-repeat="patrimonio in patrimonios">
                                    <td class="vert-align text-center"><input type="radio"
                                      ng-model="$parent.patrimonioselecionado"
                                      name="patrimonio"
                                      ng-value="{{patrimonio}}"
                                      ng-change="seleciona(patrimonio)"
                                      ></td>
                                    <td class="vert-align text-center">{{patrimonio.patrimonio}}</td>
                                    <td class="vert-align text-center">{{patrimonio.tipo}}</td>
                                    <td class="vert-align text-center">{{patrimonio.mac}}</td>
                                    <td class="vert-align text-center">{{patrimonio.marca}}</td>
                                    <td class="vert-align text-center">{{patrimonio.modelo}}</td>
                                  </tr>
                                </tbody>
                            </table>
                          </div>
<!--
                            <div class="alert alert-danger" ng-if="patrimonioselecionado.tipo !== 'ONU' && patrimonioselecionado.tipo !=='' && patrimonioselecionado.tipo !== undefined">
                                <strong>Tipo de equipamento incompatível</strong>: {{patrimonio.tipo}}
                            </div>
                            <div class="alert alert-danger" ng-if="patrimonioselecionado.patrimonio == ''">
                                <strong>Patrimônio não localizado</strong>
                            </div>

                            <div class="alert alert-success" ng-if="patrimonioselecionado.tipo === 'ONU'">
                                <strong>Equipamento OK</strong>
                            </div>
                          -->

                        </div>

                    </div>

                <div class="col-xs-12">
                  <div class="form-group">
                          <label for="olt">OLT</label>
                          <ol class="nya-bs-select form-control"
                                 id="olt"
                                 title="Selecione um serviço"
                                 ng-model="parametro.olt_id"
                                 data-live-search="true"
                                 data-size="8"
                                 required
                             >
                                 <li nya-bs-option="olt in olts" data-value="olt.id">
                                     <a>
                                         {{ olt.nomeservico }}
                                         <span class="glyphicon glyphicon-ok check-mark"></span>
                                     </a>
                                 </li>
                             </ol>
                      </div>
                    <div class="form-group">
                            <label for="local">Local</label>
                            <input type="text" class="form-control"
                          id="local" ng-model="parametro.local"/>
                        </div>
                        <div class="form-group">
                        <label for="observacao">Observação</label>
                            <textarea class="form-control"
                                id="observacao" ng-model="parametro.observacao" style="min-height: 150px;"/></textarea>
                        </div>

                </div>

                </form>
        </div>

            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>
                <button type="button" class="btn btn-primary" ng-click="saveParametroFtth(parametro);" ng-disabled="frmparametros.$invalid || patrimonioselecionado.tipo !== 'ONU'" data-dismiss="modal">
                    Salvar</button>
            </div>
        </div>
    </div>
</div>
