'use strict';

angular.module('app')

    .controller('ParametrosFtthListController', function ($scope, $rootScope, $route,
      ParametrosServicosFtthService, toaster) {

        var ParametrosFtth = ParametrosServicosFtthService.get({id: $route.current.params.servico_id});
        ParametrosFtth.$promise.then(function (data) {
            $scope.parametros_ftth = data.dados;
            $scope.ultimoparametro = angular.copy($scope.parametros_ftth[0]);
        });

        $scope.saveParametroFtth = function (parametro) {

            var host = $route.current.params.id_host;
            var servico = $route.current.params.servico_id;

            var novoParametro = {};
            novoParametro = angular.copy(parametro);
            novoParametro.servico_id = servico;

            novoParametro.idevento = $rootScope.eventoSelecionado.id;
            novoParametro.username = $rootScope.operador.username;

            ParametrosServicosFtthService.insertParametros(novoParametro, function (response) {
                if (response.status === 'OK') {
                    var ParametrosFtth = ParametrosServicosFtthService.get({id: $route.current.params.servico_id});
                    ParametrosFtth.$promise.then(function (data) {
                        $scope.parametros_ftth = data.dados;
                        $scope.ultimoparametro = angular.copy($scope.parametros_ftth[0]);

                    });
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

        $scope.clone = function () {

            $scope.parametro_ftth = $scope.ultimoparametro;

        };

        $scope.novo = function () {

            $scope.parametro_ftth = {};

        };

    });
