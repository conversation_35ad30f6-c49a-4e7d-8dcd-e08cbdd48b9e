<div class="barra">
        <div class="form-group">
            <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="novo()" authorize="['hosts.write']"><i class="glyphicon glyphicon-plus"></i><br>Incluir</button>
            <button type="button" class="btn btn-warning btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="clone()" authorize="['hosts.write']"><i class="glyphicon glyphicon-copy"></i><br>Clonar</button>
        </div>
</div>

<div ng-if="parametros.length > 0">

<h5>Parâmetro Atual</h5>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="vert-align text-center">Proprietário</th>
            <th class="vert-align text-center">Monitoramento</th>
            <th class="vert-align text-center">Patrimônio</th>
            <th class="vert-align text-center">Marca</th>
            <th class="vert-align text-center">Modelo</th>
            <th class="vert-align text-center">IP</th>
            <th class="vert-align text-center">MAC</th>
            <th class="vert-align text-center">Local</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="vert-align text-center">{{ ultimoparametro.modooperacao}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.monitoramento}}</td>
            <td class="vert-align text-center"><a href="/materiais/patrimonios/{{ ultimoparametro.patrimonio}}">{{ ultimoparametro.patrimonio}}</a></td>
            <td class="vert-align text-center">{{ ultimoparametro.marca}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.modelo}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.ip}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.mac}}</td>
            <td class="vert-align text-center">{{ ultimoparametro.local}}</td>
          </tr>
        </tbody>
      </table>
</div>

<!--
<div class="navbar">
    <form class="navbar-form navbar-left">
       <button type="button" class="btn btn-success" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-show="eventoSelecionado.id != ''" ng-click="novo()"><i class="glyphicon glyphicon-plus"></i> Adicionar Parâmetro</button>
       <button type="button" class="btn btn-warning" ng-show="eventoSelecionado.id != ''" ng-click="clone()" data-toggle="modal" data-target="#frmparametros"><i class="glyphicon glyphicon-copy"></i> Clonar Último Parâmetro</button>

    </form>
    <form class="navbar-form">
        <div class="form-group pull-right">
        <input type="text" class="search form-control" ng-model="filtro" placeholder="Pesquisar...">
        </div>
    </form>
  </div> -->



    <h5>Histórico de alterações <span class="badge">{{parametros.length}}</span></h5>

    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Proprietário</th>
          <th class="vert-align text-center">Monitoramento</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Marca</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">IP</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Evento</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">Local</th>
          <th class="vert-align text-center">Observação</th>
        </tr>
      </thead>
      <tbody>
               <tr ng-repeat="parametro in parametros | filter:filtro" ng-class="{'success': parametro.idevento == eventoSelecionado.id}">
            <td class="vert-align text-center">{{ parametro.modooperacao}}</td>
            <td class="vert-align text-center">{{ parametro.monitoramento}}</td>
            <td class="vert-align text-center"><a href="/materiais/patrimonios/{{ parametro.patrimonio}}">{{ parametro.patrimonio}}</a></td>
            <td class="vert-align text-center">{{ parametro.marca}}</td>
            <td class="vert-align text-center">{{ parametro.modelo}}</td>
            <td class="vert-align text-center">{{ parametro.ip}}</td>
            <td class="vert-align text-center">{{ parametro.mac}}</td>
            <td class="vert-align text-center"><a href="/eventos/{{:: parametro.idevento}}">#{{:: parametro.idevento}}</a></td>
            <td class="vert-align text-center">{{ parametro.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{ parametro.username}}</td>
            <td class="vert-align text-center">{{ parametro.local}}</td>
            <td class="vert-align text-center">{{ parametro.observacao}}</td>
      </tbody>
      </table>
