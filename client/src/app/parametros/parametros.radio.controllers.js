'use strict';

angular.module('app')

    .controller('ParametrosRadioListCtrl', function ($scope, $rootScope, $route, ParametrosRadioService, toaster) {

        var ParametrosRadio = ParametrosRadioService.get({servico: $route.current.params.servico});
        ParametrosRadio.$promise.then(function (data) {
            $scope.parametros_radio = data.dados;
            $scope.ultimoparametro = angular.copy($scope.parametros_radio[0]);
        });

        $scope.saveParametroRadio = function (parametro_radio) {

            var host = $route.current.params.id_host;
            var servico = $route.current.params.servico;

            var novoParametro = {};
            novoParametro = angular.copy(parametro_radio);
            novoParametro.host = host;
            novoParametro.servico = servico;

            novoParametro.idevento = $rootScope.eventoSelecionado.id;
            novoParametro.username = $rootScope.operador.username;


            ParametrosRadioService.insertParametrosRadio(novoParametro, function (response) {
                if (response.status === 'OK') {
                    //alert('Dados salvos com sucesso!');
                    var ParametrosRadio = ParametrosRadioService.get({servico: $route.current.params.servico});
                    ParametrosRadio.$promise.then(function (data) {
                        $scope.parametros_radio = data.dados;
                        $scope.ultimoparametro = angular.copy($scope.parametros_radio[0]);

                    });
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

        $scope.clone = function () {

            $scope.parametro_radio = $scope.ultimoparametro;
            console.log($scope.parametro_radio);

        };

        $scope.novo = function () {

            $scope.parametro_radio = {};

        };

    });
