        <div class="modal" id="frmparametros" tabindex="-1" role="dialog"
     aria-labelledby="frmparametroslabel" aria-hidden="true" modal="showModal" close="cancel()">

    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <button type="button" class="close"
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmparametroslabel">
                    Parâmetros gerais
                </h4>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <form role="form" name="frmparametros" class="form-horizontal">
                  <div class="row">
                  <div class="col-xs-12">
                      <div class="col-xs-6" style="padding-left: 0px">
                    <label for="modo">Proprietário</label>
                      <select class="form-control" ng-model="parametro.modooperacao"
                              ng-options="o as o for o in modos" required ng-change="changeProprietario()"></select>

                      </div>
                    <div class="col-xs-6" style="padding-right: 0px">
                    <div class="form-group">
                    <label for="modo">Monitoramento</label>
                      <select class="form-control" ng-model="parametro.monitoramento" required>
                          <option value="Desativado">Desativado</option>
                          <option value="Desativado+Ping">Desativado + Ping</option>
                          <option value="Simples" ng-if="parametro.modooperacao == 'Hardware do Provedor'">Simples</option>
                          <option value="Completo" ng-if="parametro.modooperacao == 'Hardware do Provedor'">Completo</option>


                      </select>
                  </div>
                      </div>
                        <div class="form-group">
                      <div class="btn-group"  ng-show="parametro.modooperacao == 'Hardware do Provedor'">
                        <label class="btn btn-primary" ng-model="patrimoniolegivel" uib-btn-radio="'1'">Patrimônio Legível</label>
                        <label class="btn btn-primary" ng-model="patrimoniolegivel" uib-btn-radio="'0'">Patrimônio Ilegível</label>
                      </div>
                    </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-xs-12" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '1'">

                        <div class="form-group" ng-if="tipobusca=='patrimonio'">
                            <label for="patrimonio">Patrimônio</label><span class="pull-right"><a href="" ng-click="changeTipo('mac')"><i class="glyphicon glyphicon-refresh"></i> Busca por MAC</a></span>
                             <div class="input-group ">
                            <input type="number" class="form-control" id="patrimonio" placeholder="Digite o patrimônio" ng-model="parametro.patrimonio" required autofocus ng-keydown="$event.which === 13 && buscaPatrimonio(parametro.patrimonio)">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" ng-click="buscaPatrimonio(parametro.patrimonio)" ng-disabled="parametro.patrimonio=='' || parametro.patrimonio == undefined"><i class="glyphicon glyphicon-search"></i> Buscar</button>
                            </span>
                            </div>
                        </div>

                      <div class="form-group" ng-if="tipobusca=='mac'">
                            <label for="mac">MAC</label><span class="pull-right"><a href="" ng-click="changeTipo('patrimonio')"><i class="glyphicon glyphicon-refresh"></i> Busca por Patrimônio</a></span>
                             <div class="input-group ">
                            <input type="text" class="form-control" id="mac" placeholder="Digite o MAC" ng-model="parametro.mac" required ng-keydown="$event.which === 13 && buscaPatrimonio(parametro.mac)">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" ng-click="buscaPatrimonio(parametro.mac, '')" ng-disabled="parametro.mac=='' || parametro.mac == undefined"><i class="glyphicon glyphicon-search"></i> Buscar</button>
                            </span>
                            </div>
                        </div>
                        <div class="form-group">
                          Resultado da busca
                          <div class="pre-scrollable" style="height:150px;">
                              <table class="table table-bordered table-hover">
                                <thead>
                                    <th></th>
                                    <th class="vert-align text-center">Patrimônio</th>
                                    <th>Tipo</th>
                                    <th class="vert-align text-center">MAC</th>
                                    <th class="vert-align text-center">Marca</th>
                                    <th class="vert-align text-center">Modelo</th>
                                </thead>
                                <tbody>

                                   <tr ng-repeat="patrimonio in patrimonios">
                                    <td class="vert-align text-center"><input type="radio"
                                      ng-model="$parent.patrimonioselecionado"
                                      name="patrimonio"
                                      ng-value="{{patrimonio}}"
                                      ng-change="seleciona(patrimonio)"
                                      ></td>
                                    <td class="vert-align text-center" >{{patrimonio.patrimonio}}</td>
                                    <td class="vert-align text-center">{{patrimonio.tipo}}</td>
                                    <td class="vert-align text-center">{{patrimonio.mac}}</td>
                                    <td class="vert-align text-center">{{patrimonio.marca}}</td>
                                    <td class="vert-align text-center">{{patrimonio.modelo}}</td>

                                  </tr>
                                </tbody>
                            </table>
                          </div>
                          <div class="alert alert-danger" ng-if="patrimonioselecionado.categoria_id !== undefined && patrimonioselecionado.categoria_id !== 1 && patrimonioselecionado.categoria_id !== 4">
                            Não é permitido o uso deste tipo de patrimônio nesta operação.
                          </div>
                        </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-xs-12" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '0'">
                      <div class="form-group">
                        <label for="motivo">Motivo</label>
                          <input type="text" class="form-control" id="motivo" placeholder="Digite o motivo" ng-model="material.motivo" required>
                      </div>
                      <div class="form-group" ng-class="{ 'has-error': frmparametros.material.tipo.$invalid && frmparametros.material.tipo.$dirty }">
                          <label class="control-label" for="tipopatrimonio"> Tipo</label>

                              <ol class="nya-bs-select form-control"
                                    title="Tipo"
                                    id="tipopatrimonio"
                                    name="tipopatrimonio"
                                    ng-model="material.tipo"
                                    ng-change="getMarcas()"
                                    data-live-search="true"
                                    data-size="8"
                                    required
                                >
                                <li nya-bs-option="tipo in tipos">
                                        <a>
                                            {{ tipo }}
                                            <span class="glyphicon glyphicon-ok check-mark"></span>
                                        </a>
                                    </li>
                                </ol>

                          <!--
                          <div class="col-md-3" authorize="['materiais.read']">
                            <input id="tipo" class="form-control input-md"
                              ng-model="PFC.material.tipo" type="text" disabled>
                          </div>
                          -->
                        </div>
                        <div class="form-group" ng-class="{ 'has-error': frmparametro.material.marca.$invalid && frmparametro.material.marca.$dirty }" ng-if="material.tipo != undefined">
                      <label class="control-label" for="marca"> Marca</label>
                          <ol class="nya-bs-select form-control"
                                title="Marca"
                                id="marca"
                                name="marca"
                                ng-model="material.marca"
                                ng-change="getModelos()"
                                data-live-search="true"
                                data-size="8"
                                required
                            >
                            <li nya-bs-option="marca in marcas">
                                    <a>
                                        {{ marca }}
                                        <span class="glyphicon glyphicon-ok check-mark"></span>
                                    </a>
                                </li>
                            </ol>

                      <!--
                      <div class="col-md-3" authorize="['materiais.read']">
                            <input id="marca" class="form-control input-md"
                          ng-model="PFC.material.marca" type="text" disabled>
                      </div>
                      -->
                    </div>
                    <div class="form-group" ng-class="{ 'has-error': frmparametro.material.modelo.$invalid && frmparametro.material.modelo.$dirty }" ng-if="material.marca != undefined">
                  <label class="control-label" for="modelo"> Modelo</label>
                      <ol class="nya-bs-select form-control"
                            title="Modelo"
                            id="modelo"
                            name="modelo"
                            ng-model="material.modelo"
                            ng-change="changeModelo()"
                            data-live-search="true"
                            data-size="8"
                            required
                        >
                        <li nya-bs-option="modelo in modelos" data-value="modelo.modelo">
                                <a>
                                    {{ modelo.modelo }}
                                    <span class="glyphicon glyphicon-ok check-mark"></span>
                                </a>
                            </li>
                        </ol>

                   <!--
                      <div class="col-md-3" authorize="['materiais.read']">
                        <input id="modelo" class="form-control input-md"
                          ng-model="PFC.material.modelo" type="text" disabled>
                      </div>
                   -->
                  </div>
                  <div class="form-group" ng-class="{ 'has-error': frmparametros.material.mac.$invalid && frmparametros.material.mac.$dirty }" ng-if="mac_enabled==1">

                    <label class="control-label">MAC</label>
                        <input style=""
                          class="form-control input-md" id="material.mac" name="material.mac"
                          placeholder=""
                          ng-required="mac_enabled==1"
                          ng-model="material.mac" mask="ww:ww:ww:ww:ww:ww"
                          ng-pattern="/^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/"
                          type="text">
                  </div>


                    </div>
                  </div>

                <div class="form-group" ng-if="(parametro.modooperacao == 'Hardware do Provedor' && patrimonioselecionado.gerenciavel) || (parametro.modooperacao !== 'Hardware do Provedor' && parametro.monitoramento !== 'Desativado') || (mac_enabled==1)" ng-class="{ 'has-error': frmparametros.ip.$invalid && frmparametros.ip.$dirty }">
                    <label for="ip">IP</label>
                        <input type="text" class="form-control"
                          id="ip"
                          name="ip"
                          ng-required="parametro.monitoramento=='Simples' || parametro.monitoramento=='Completo' || parametro.monitoramento=='Desativado+Ping'" 
                          placeholder="IP de admin"
                          ng-model="parametro.ip"
                          ng-pattern='/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/'
                          ng-model-options="{ updateOn: 'blur' }">

                </div>
                <!--
                <div class="form-group" ng-if="gerenciavel" ng-class="{ 'has-error': frmparametros.mac.$invalid && frmparametros.mac.$dirty }">
                    <label for="mac">MAC (LAN)</label>
                      <input type="text" class="form-control"
                          id="mac"
                          name="mac"
                          placeholder=""
                          ng-model="parametro.mac"
                          mask="ww:ww:ww:ww:ww:ww"
                          ng-pattern='/^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/' ng-model-options="{ updateOn: 'blur' }">
                  </div>
                -->

                    <div class="form-group">
                    <label for="local">Local</label>
                      <input type="text" class="form-control"
                          id="local" ng-model="parametro.local"/>
                  </div>
                    <div class="form-group">
                    <label for="observacao">Observação</label>
                      <textarea class="form-control"
                                id="observacao" ng-model="parametro.observacao" style="min-height: 150px;"/></textarea>
                  </div>
                </form>


            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>

                <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '0'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal"> Salvar</button>
                <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '1'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid || (patrimonioselecionado.categoria_id !== undefined && patrimonioselecionado.categoria_id !== 1 && patrimonioselecionado.categoria_id !== 4) || (patrimonioselecionado.categoria_id == undefined)" data-dismiss="modal"> Salvar</button>
                <!-- <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
                    Salvar</button> -->

                <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Cliente'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
                        Salvar</button>
            </div>
        </div>
    </div>
</div>
