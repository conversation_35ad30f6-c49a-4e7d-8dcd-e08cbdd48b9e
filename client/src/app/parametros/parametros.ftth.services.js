'use strict';

angular.module('app')

    .factory('ParametrosFtthService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:id/parametros/ftth', {},
            {
                getParametros: {method: 'GET', isArray: false},
                insertParametros: {
                    method: 'POST',
                    params: {id: '@host_id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('ParametrosFttaService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hosts/:id/parametros/ftta', {},
            {
                getParametros: {method: 'GET', isArray: false},
                insertParametros: {
                    method: 'POST',
                    params: {id: '@host_id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('ParametrosServicosFtthService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/:id/parametros/ftth', {},
            {
                getParametros: {method: 'GET', isArray: false},
                insertParametros: {
                    method: 'POST',
                    params: {id: '@servico_id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('OltListService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/ftth/olts', {},
            {
                getAll: {method: 'GET', isArray: true}
            }
        );
    })
