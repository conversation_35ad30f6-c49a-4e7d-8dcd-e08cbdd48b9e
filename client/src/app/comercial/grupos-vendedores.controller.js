(function () {
	'use strict';

	angular
		.module('app')
		.controller('GruposVendedoresController', GruposVendedoresController);

	/** @ngInject */
	function GruposVendedoresController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

		var vm = this;

		vm.editando_grupos = [];

        vm.grupos = [];

		vm.novo_grupo = {
			nome: ''
		};

		vm.grupo_selecionado = {};

		vm.vendedores_sem_grupo = [];

		vm.adicionar_grupo = adicionar_grupo;
		vm.excluir_grupo = excluir_grupo;
		vm.selecionar_grupo = selecionar_grupo;

		vm.adicionar_vendedor = adicionar_vendedor;
		vm.excluir_vendedor = excluir_vendedor;

        vm.habilitar_edicao = habilitar_edicao;
        vm.salvar_edicao = salvar_edicao;
        vm.cancelar_edicao = cancelar_edicao;

		vm.grupos_vendedores = [];

		activate();

		function activate() {
            listar_grupos_vendedores();
		}

		function listar_grupos_vendedores() {
			vm.carregando_dados_modal_grupos = true;
			vm.grupos = [];
			vm.vendedores_sem_grupo = [];

			$http({
				method: 'GET',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/grupos-vendedores'
			}).then(function (response) {
				console.log('response.data:', response.data);
				vm.carregando_dados_modal_grupos = false;
				vm.grupos = response.data.grupos;
				vm.vendedores_sem_grupo = response.data.vendedores_sem_grupo;

				window.setTimeout(function () {
					if (vm.grupo_selecionado.hasOwnProperty('id')) {
						var grupo_selecionado = vm.grupo_selecionado;
						vm.grupo_selecionado = {};
						selecionar_grupo(grupo_selecionado);
					}
				}, 500);
			}, function (error) {
				console.log(error);
			});
		}

		function selecionar_grupo(grupo) {
			$('.grupo-os-vendedores').collapse('hide');

			if (vm.grupo_selecionado.id == grupo.id) {
				vm.grupo_selecionado = {};
				return;
			}

			vm.grupo_selecionado = grupo;

			$('.grupo-os-vendedores.grupo-' + grupo.id).collapse('show');
		}

		function cancelar_edicao(id_grupo) {
			var index = vm.editando_grupos.indexOf(id_grupo);
			vm.editando_grupos.splice(index, 1);
		}

		function habilitar_edicao(id_grupo) {
			vm.editando_grupos.push(id_grupo);
		}

		function salvar_edicao(id_grupo, grupo) {
			var novoNome = $('#input-nome-grupo-' + id_grupo).val().trim();

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/editar-grupo',
				data: {
					grupo: grupo,
					novoNome: novoNome
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('As alterações foram salvas com sucesso.');
                    cancelar_edicao(id_grupo);
					listar_grupos_vendedores();
                    vm.grupo_selecionado = {};
				}
				else {
					alert('Ocorreu um erro ao alterar as informações do grupo.');
				}
			}, function (error) {
				console.log(error);
			});
			

			// $http();
			//		onSuccess: reloadRelatorio();

			cancelar_edicao(id_grupo);
		}

		function adicionar_vendedor(vendedor) {
			var grupo = vm.grupo_selecionado.grupo;

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/adicionar-vendedor',
				data: {
					grupo: grupo,
					vendedor: vendedor
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O vendedor foi adicionado ao grupo');
					listar_grupos_vendedores();
				}
				else {
					alert('Ocorreu um erro ao adicionar o vendedor ao grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_vendedor(id_grupo, vendedor) {
			var grupo = vm.grupo_selecionado.grupo;

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/excluir-vendedor',
				data: {
					grupo: grupo,
					vendedor: vendedor
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O vendedor foi excluído do grupo');
					listar_grupos_vendedores();
				}
				else {
					alert('Ocorreu um erro ao excluir o vendedor do grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function adicionar_grupo() {
			var nome = vm.novo_grupo.nome;

			if (nome.trim() == '') {
				alert('Erro no preenchimento. Verifique o campo "nome".');
				return false;
			}

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/adicionar-grupo',
				data: {
					nome: nome
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O grupo foi adicionado com sucesso');
					vm.novo_grupo = {
						nome: ''
					};
					listar_grupos_vendedores();
				}
				else {
					alert('Ocorreu um erro ao adicionar o grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_grupo(grupo) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/comercial/grupos-vendedores/excluir-grupo',
				data: {
					grupo: grupo
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O grupo foi excluído com sucesso');
					listar_grupos_vendedores();
				}
				else {
					alert('Ocorreu um erro ao excluir o grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

	}

})();
