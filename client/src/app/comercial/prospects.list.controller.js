(function () {
    'use strict';

    angular
        .module('app')
        .controller('ProspectListController', ProspectListController);

    /** @ngInject */
    function ProspectListController($http, API_CONFIG, cfpLoadingBar, $rootScope,
        toaster, $routeParams, $location,  NgMap) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo='nome';
        vm.termos='';
        vm.prospects = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        //vm.desativa = desativa;
        //vm.retira = retira;
        //vm.ativa = ativa;
        vm.sort = sort;
        vm.limpar = limpar;
        vm.seleciona = seleciona;
        vm.selecionado = {};

        vm.googleMapsUrl="https://maps.googleapis.com/maps/api/js?key=AIzaSyDIxOOZwZKo62jMl4KC_pDmh-MtPm1qFCU&libraries=places";

        activate();

        function activate() {

            if($routeParams.exito !== undefined){
              vm.exito = $routeParams.exito;
            } else {
              vm.exito = '';
            }

            if($routeParams.atendimento !== undefined){
              vm.atendimento = $routeParams.atendimento;
            } else {
              vm.atendimento = '';
            }

            if($routeParams.cliente !== undefined){
              vm.cliente = $routeParams.cliente;
            } else {
              vm.cliente = '';
            }

            if($routeParams.cdm !== undefined){
              vm.cdm = $routeParams.cdm;
            } else {
              vm.cdm = '';
            }


            if($routeParams.sortby !== undefined){
              vm.sortBy = $routeParams.sortby;
            } else {
              vm.sortBy = 'datacad';
            }

            if($routeParams.sortorder !== undefined){
              vm.sortOrder = $routeParams.sortorder;
            } else {
              vm.sortOrder = 'dsc';
            }

            if($routeParams.nome !== undefined && $routeParams.nome !== ''){
              vm.tipo='nome';
              vm.termos=$routeParams.nome;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.logradouro !== undefined){
              vm.tipo='logradouro';
              vm.termos=$routeParams.logradouro;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.bairro !== undefined){
              vm.tipo='bairro';
              vm.termos=$routeParams.bairro;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.usuario !== undefined){
              vm.tipo='usuario';
              vm.termos=$routeParams.usuario;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.id !== undefined){
              vm.tipo='id';
              vm.termos=$routeParams.id;
              vm.filtro = '&' + vm.tipo + '=' + vm.termos;
            }

            getData();
        }

        NgMap.getMap({id: 'mapa_prospect'}).then(function(map) {
          vm.map = map;

        //  google.maps.event.addListener(vm.map, 'center_changed', function() {
          //  vm.posicao = JSON.parse(JSON.stringify(vm.map.center));
          //});

          //vm.panorama = map.getStreetView();

          //google.maps.event.addListener(vm.panorama, 'pov_changed', function() {
          //  vm.prospect.pov_heading = vm.panorama.getPov().heading;
        //    vm.prospect.pov_pitch = vm.panorama.getPov().pitch;
      //    });
        });

        function getData() {

            var urlApi = API_CONFIG.url + '/comercial/prospect?page=' + $routeParams.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
               if(vm.exito !=='') urlApi += '&exito=' + vm.exito;
               if(vm.atendimento !=='') urlApi += '&atendimento=' + vm.atendimento;
               if(vm.cliente !=='') urlApi += '&cliente=' + vm.cliente;
               if(vm.cdm !=='') urlApi += '&cdm=' + vm.cdm;


            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.prospects);
                angular.copy(response.data.pagination, vm.pagination);
            })
            .catch(function (err) {
              console.log(err);

            });
            ;
        }

        function pageChanged() {
            var urlApi = '/comercial/prospects?page=' + vm.pagination.page +
                '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos;
            if(vm.exito !=='') urlApi += '&exito=' + vm.exito;
            if(vm.atendimento !=='') urlApi += '&atendimento=' + vm.atendimento;
            if(vm.cliente !=='') urlApi += '&cliente=' + vm.cliente;
            if(vm.cdm !=='') urlApi += '&cdm=' + vm.cdm;
            $location.url(urlApi);

        }

        function sort(field){
            if(field == vm.sortBy){
                if(vm.sortOrder == 'asc'){
                    vm.sortOrder = 'dsc';
                } else {
                    vm.sortOrder = 'asc';
                }
            }

            vm.sortBy = field;
            pageChanged();
        }

        function busca(termos) {

            vm.pagination.page = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            pageChanged();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.tipo = 'host';
          vm.termos = '';
          $location.url('/comercial/prospects');
        }

        function seleciona(prospect){
          vm.selecionado = prospect;
          vm.panorama = vm.map.getStreetView();
          if(prospect.latitude != undefined && prospect.latitude != '' &&
            prospect.longitude != undefined && prospect.longitude != ''){
              var posicao = {lat: parseFloat(prospect.latitude), lng: parseFloat(prospect.longitude)};
              vm.map.getStreetView().setPosition(posicao);
              vm.map.setCenter(posicao);
          }

          if(prospect.pov_heading != undefined && prospect.pov_heading != '' &&
            prospect.pov_pitch != undefined && prospect.pov_pitch != ''){
            var pov = {heading: parseFloat(prospect.pov_heading), pitch: parseFloat(prospect.pov_pitch)};
            vm.map.getStreetView().setPov(pov);
          }

          vm.map.setZoom(18);

        }



    }

})();
