<div class="modal" id="frmprospect" tabindex="-1" role="dialog" aria-labelledby="frmprospect" aria-hidden="true"
  modal="showModal" close="cancel()">

  <div class="modal-dialog">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmparametroslabel">
          Prospect
        </h4>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <div class="row">
          <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#dados" data-toggle="tab" style="cursor: pointer;">
                <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
            </li>
            <li>
              <a data-toggle="tab" data-target="#mapa" style="cursor: pointer;">
                <i class="glyphicon glyphicon-globe"></i> Mapa</a>
            </li>
          </ul>

          <div class="tab-content">
            <div id="dados" class="tab-pane fade in active">

              <table class="table table-bordered table-striped">
                <tbody>
                  <tr>
                    <th class="text-nowrap" scope="row">Venda</th>
                    <td><span class="label label-success ng-scope" style="cursor: default;"
                        ng-if="PLC.selecionado.exito==1">Efetuada</span><span class="label label-danger"
                        style="cursor: default;" ng-if="PLC.selecionado.exito==0">Não Efetuada</span><span
                        class="label label-warning" style="cursor: default;" ng-if="PLC.selecionado.exito==2">Não
                        Atendido</span></td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Motivo</th>
                    <td>{{PLC.selecionado.motivo}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Nome</th>
                    <td>{{PLC.selecionado.nome}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Telefone</th>
                    <td>{{PLC.selecionado.telefone}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">E-mail</th>
                    <td>{{PLC.selecionado.email}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Cobertura</th>
                    <td>{{PLC.selecionado.cobertura}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Plano</th>
                    <td>{{PLC.selecionado.plano}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Logradouro</th>
                    <td>{{PLC.selecionado.logradouro}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Número</th>
                    <td>{{PLC.selecionado.numero}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Complemento</th>
                    <td>{{PLC.selecionado.complemento}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Bairro</th>
                    <td>{{PLC.selecionado.bairro}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Cidade</th>
                    <td>{{PLC.selecionado.cidade}}</td>
                  </tr>

                  <tr>
                    <th class="text-nowrap" scope="row">CPF</th>
                    <td>{{PLC.selecionado.cpf}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">RG</th>
                    <td>{{PLC.selecionado.rg}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Profissão</th>
                    <td>{{PLC.selecionado.profissao}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Estado Civil</th>
                    <td>{{PLC.selecionado.estadocivil}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Dia Vencimento</th>
                    <td>{{PLC.selecionado.diavencimento}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Prazo Contratual</th>
                    <td>{{PLC.selecionado.prazocontratual}}</td>
                  </tr>
                  <tr>
                    <th class="text-nowrap" scope="row">Data Instalação</th>
                    <td>{{PLC.selecionado.prazocontratual | amDateFormat:'DD/MM/YYYY'}}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div id="mapa" class="tab-pane fade in">
              <div map-lazy-load="https://maps.google.com/maps/api/js" map-lazy-load-params="{{PLC.googleMapsUrl}}">

                <ng-map id="mapa_prospect" center="Rua Prefeito Chagas, 305 - Centro, Poços de Caldas - MG, Brasil"
                  default-style="false" disable-default-u-i="true" zoom-control="true"
                  style="display:block; height:600px">
                  <marker position="{{PLC.selecionado.latitude}}, {{PLC.selecionado.longitude}}"
                    icon="{path:'CIRCLE', scale: 6, strokeColor: 'red', zIndex:10}"></marker>
                  <!--

            <street-view-panorama
            click-to-go="false"
            disable-default-u-i="true"
            disable-double-click-zoom="false"
            position="-21.78856390811573 -46.56475945987847"
            pov="{heading: heading, pitch: pitch}"
            scrollwheel="false"
            enable-close-button="false"
            fullscreen-control="false"
            zoom-control="true"
            visible="true">

            </street-view-panorama>
            -->

                </ng-map>
              </div>
            </div>



          </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">
            Fechar
          </button>

          <button type="button" class="btn btn-primary"
            ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '0'"
            ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
            Salvar</button>
          <button type="button" class="btn btn-primary"
            ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '1'"
            ng-click="saveParametro(parametro);"
            ng-disabled="frmparametros.$invalid || (patrimonioselecionado.categoria_id !== undefined && patrimonioselecionado.categoria_id !== 1 && patrimonioselecionado.categoria_id !== 4) || (patrimonioselecionado.categoria_id == undefined)"
            data-dismiss="modal"> Salvar</button>
          <!-- <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
            Salvar</button> -->

          <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Cliente'"
            ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
            Salvar</button>
        </div>
      </div>
    </div>
  </div>