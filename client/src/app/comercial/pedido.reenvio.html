<div class="modal" id="frmreenvio" tabindex="-1" role="dialog" aria-labelledby="frmreenvio" aria-hidden="true"
    modal="showModal" close="cancel()">

    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmparametroslabel">
                    Reenvio de Documentos - {{PC.selecionado.nome}}
                </h4>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <ul class="nav nav-tabs">
                    <li class="active">
                        <a data-target="#solicitacao" data-toggle="tab" style="cursor: pointer;">
                            <i class="glyphicon glyphicon-list-alt"></i> Solicitação</a>
                    </li>
                    <li>
                        <a data-target="#reenvios" data-toggle="tab" style="cursor: pointer;">
                            <i class="glyphicon glyphicon-list-alt"></i> Reenvios pendentes</a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div id="solicitacao" class="tab-pane fade in active">
                        <p>Selecione os documentos que deseja solicitar o reenvio pelo cliente</p>

                        <form>
                            <div class="form-check" ng-if="PC.selecionado.pessoa=='F'">
                                <input type="checkbox" class="form-check-input" id="cpf"
                                    ng-disabled="PC.selecionado.cpf_reenvio==1" ng-model="PC.docs.cpf">

                                <label class="form-check-label" for="cpf">CPF <span class="label label-warning"
                                        ng-if="PC.selecionado.cpf_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>
                            <div class="form-check" ng-if="PC.selecionado.pessoa=='J'">
                                <input type="checkbox" class="form-check-input" id="cnpj"
                                    ng-disabled="PC.selecionado.cnpj_reenvio==1" ng-model="PC.docs.cnpj">
                                <label class="form-check-label" for="cnpj">CNPJ <span class="label label-warning"
                                        ng-if="PC.selecionado.cnpj_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>
                            <div class="form-check" ng-if="PC.selecionado.pessoa=='F'">
                                <input type="checkbox" class="form-check-input" id="identidade"
                                    ng-disabled="PC.selecionado.identidade_reenvio==1" ng-model="PC.docs.identidade">
                                <label class="form-check-label" for="identidade">Identidade <span class="label label-warning"
                                        ng-if="PC.selecionado.identidade_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>
                            <div class="form-check" ng-if="PC.selecionado.pessoa=='F'">
                                <input type="checkbox" class="form-check-input" id="selfie"
                                    ng-disabled="PC.selecionado.selfie_reenvio==1" ng-model="PC.docs.selfie">
                                <label class="form-check-label" for="selfie">Selfie com documento <span class="label label-warning"
                                        ng-if="PC.selecionado.selfie_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>
                            <div class="form-check" ng-if="PC.selecionado.pessoa=='J'">
                                <input type="checkbox" class="form-check-input" id="contratosocial"
                                    ng-disabled="PC.selecionado.contrato_social_reenvio==1"
                                    ng-model="PC.docs.contrato_social">
                                <label class="form-check-label" for="contratosocial">Contrato Social <span
                                        class="label label-warning"
                                        ng-if="PC.selecionado.contrato_social_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>

                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="endereco"
                                    ng-disabled="PC.selecionado.endereco_reenvio==1" ng-model="PC.docs.endereco">
                                <label class="form-check-label" for="endereco">Comprovante de Endereço <span
                                        class="label label-warning" ng-if="PC.selecionado.endereco_reenvio==1">Reenvio
                                        Pendente</span></label>
                            </div>

                        </form>
                    </div>
                    <div id="reenvios" class="tab-pane fade in">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th class="vert-align text-center">Data</th>
                                    <th class="vert-align text-center">Atendente</th>
                                    <th class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">CPF</th>
                                    <th class="vert-align text-center" ng-if="PC.selecionado.pessoa=='J'">CNPJ</th>
                                    <th class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">Identidade
                                    </th>
                                    <th class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">Selfie com documento
                                    </th>
                                    <th class="vert-align text-center" ng-if="PC.selecionado.pessoa=='J'">Contrato
                                        Social</th>
                                    <th class="vert-align text-center">Endereço</th>
                                    <th class="vert-align text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="reenvio in PC.selecionado.reenvios">
                                    <td class="vert-align text-center">
                                        {{reenvio.data | amDateFormat:'DD/MM/YYYY dddd HH:mm:ss'}}</td>
                                    <td class="vert-align text-center">{{reenvio.atendente}}</td>
                                    <td class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">
                                        <span class="label label-default"
                                            ng-class="[{'label-success': reenvio.cpf == 1},
                                            {'label-danger': reenvio.cpf == 0}]">{{reenvio.cpf == 1 ? "Sim":"Não"}}</span>
                                    </td>
                                    <td class="vert-align text-center" ng-if="PC.selecionado.pessoa=='J'"><span
                                            class="label label-default"
                                            ng-class="[{'label-success': reenvio.cnpj == 1},
                                            {'label-danger': reenvio.cnpj == 0}]">{{reenvio.cnpj == 1 ? "Sim":"Não"}}</span>
                                    </td>
                                    <td class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">
                                        <span class="label label-default"
                                            ng-class="[{'label-success': reenvio.identidade == 1},
                                            {'label-danger': reenvio.identidade == 0}]">{{reenvio.identidade == 1 ? "Sim":"Não"}}</span>
                                    </td>
                                    <td class="vert-align text-center" ng-if="PC.selecionado.pessoa=='F'">
                                        <span class="label label-default"
                                            ng-class="[{'label-success': reenvio.selfie == 1},
                                            {'label-danger': reenvio.selfie == 0}]">{{reenvio.selfie == 1 ? "Sim":"Não"}}</span>
                                    </td>
                                    <td class="vert-align text-center" ng-if="PC.selecionado.pessoa=='J'">
                                        <span class="label label-default"
                                            ng-class="[{'label-success': reenvio.contrato_social == 1},
                                            {'label-danger': reenvio.contrato_social == 0}]">{{reenvio.contrato_social == 1 ? "Sim":"Não"}}</span>
                                    </td>
                                    <td class="vert-align text-center"><span class="label label-default"
                                            ng-class="[{'label-success': reenvio.endereco == 1},
                                        {'label-danger': reenvio.endereco == 0}]">{{reenvio.endereco == 1 ? "Sim":"Não"}}</span></td>
                                    <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-success': reenvio.status == 'Concluído'},
                                        {'label-warning': reenvio.status == 'Aguardando'},
                                        {'label-danger': reenvio.status == 'Em Aberto'}]">{{reenvio.status}}</td>
                                </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="modal-footer">
                    <button class="btn btn-primary btn-default pull-left" ng-click="PC.reenvio(PC.selecionado, PC.docs)"
                        ng-disabled="PC.docs.cpf == 0 && PC.docs.cnpj == 0 && PC.docs.identidade == 0 && PC.docs.contrato_social == 0 && PC.docs.endereco == 0 && PC.docs.selfie == 0">Solicitar Reenvio</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        Fechar
                    </button>
                </div>
            </div>
        </div>
    </div>