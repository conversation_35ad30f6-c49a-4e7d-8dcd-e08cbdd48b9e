<style>
	.plano-servicos-container {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
		width: 100%;
		padding: 5px;
	}
	.plano-servicos-container h5 {
		width: 100%;
		text-align: center;
		padding: 5px;
	}

	.plano-servicos-container.ativos h5 {
		background: #5CB85C;
		color: #fff;
	}

	.plano-servicos-container.inativos h5 {
		background: #D9534F;
		color: #fff;
	}

   .plano-servicos-container label {
      text-align: left;
      border: 1px solid #CCC;
      border-radius: 4px;
      padding: 0px 5px;
      margin: 2px 5px;
      width: 200px;
   }

   .plano-servicos-container label:hover {
      cursor: pointer;
      background: rgba(0, 0, 0, 0.1)
   }
</style>

<div class="modal" id="frmplano" tabindex="-1" role="dialog" aria-labelledby="frmplano" aria-hidden="true"
   modal="showModal" close="cancel()">

   <div class="modal-dialog modal-lg" style="max-width: 700px;">
      <div class="modal-content">

         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" id="frmplano_fechar">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title">
               Plano {{PLN.selecionado.nome}} ({{PLN.selecionado.tecnologia_txt}}) - {{PLN.selecionado.cidade_txt}}
            </h4>
         </div>

         <!-- Modal Body -->
         <div class="modal-body" style="padding-bottom: 15px;">

            <ul class="nav nav-tabs">
               <li role="presentation" class="active" data-target="#detalhes_plano" data-toggle="tab">
                  <a href="#">
                     <i class="glyphicon glyphicon-globe margin-right-5"></i>
                     Detalhes do plano
                  </a>
               </li>
               <li role="presentation" data-target="#servicos_inclusos" data-toggle="tab">
                  <a href="#">
                     <i class="glyphicon glyphicon-th-list margin-right-5"></i>
                     Serviços inclusos
                  </a>
               </li>
            </ul>

            <div class="tab-content">
               <div id="detalhes_plano" class="tab-pane fade in active">
                  <table class="table bottom-spaced" style="width: 60%; margin: 0 auto; margin-top: 10px;">
                     <tbody>
                        <tr>
                           <td class="align-center" colspan="3" style="padding-bottom: 20px !important;">
                              <label for="plano_selecionado_ativo">Ativo:</label>
                              <select id="plano_selecionado_ativo" ng-model="PLN.selecionado.ativo" class="form-control"
                                 style="display: inline; width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                        </tr>
                        <tr>
                           <td style="width: 60%; padding-right: 5px;">
                              <label for="plano_selecionado_nome">Nome:</label>
                              <input type="text" class="form-control" ng-model="PLN.selecionado.nome"
                                 id="plano_selecionado_nome">
                           </td>
                           <td style="width: 40%; padding: 0px 5px;">
                              <label for="plano_tecnologia">Tecnologia:</label>
                              <select name="" id="plano_tecnologia" class="form-control"
                                 ng-model="PLN.selecionado.tecnologia">
                                 <option value="fibra">Fibra Óptica</option>
                                 <option value="radio">Via Rádio</option>
                                 <option value="cdmht">CDMHT</option>
                              </select>
                           </td>
                        </tr>
                        <tr>
                           <td style="width: 60%; padding-right: 5px;">
                              <label for="plano_caption">Legenda:</label>
                              <input type="text" class="form-control" ng-model="PLN.selecionado.caption"
                                 id="plano_caption">
                           </td>
                           <td style="width: 40%; padding-left: 5px;">
                              <label for="plano_velocidade">Velocidade:</label>
                              <div class="input-group">
                                 <input type="text" class="form-control align-center" id="plano_velocidade"
                                    ng-model="PLN.selecionado.velocidade" style="font-size: 11pt;"><span
                                    class="input-group-addon">Mbps</span>
                              </div>
                           </td>
                        </tr>
                     </tbody>
                  </table>
                  <div
                     style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                  </div>
                  <table class="table bottom-spaced" style="width: 70%; margin: 0 auto;">
                     <tbody>
                        <tr>
                           <td style="width: 50%; text-align: center;">
                              <label for="disponivel_contratacao">Disponível para novos contratos?</label>
                              <select ng-change="PLN.planChanged('frmplano')" id="disponivel_contratacao" ng-model="PLN.selecionado.disponivel_contratacao" class="form-control mx-auto"
                                 style="width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                           <td style="width: 50%; text-align: center;">
                              <label for="disponivel_migracao">Disponível para migração?</label>
                              <select ng-change="PLN.planChanged('frmplano')" id="disponivel_migracao" ng-model="PLN.selecionado.disponivel_migracao" class="form-control mx-auto"
                                 style="width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                        </tr>
                     </tbody>
                  </table>
                  <div
                     style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                  </div>
                  <table class="align-right bottom-spaced" style="width: 70%; margin: 0 auto;">
                     <tbody>
                        <tr>
                           <td>
                           </td>
                           <td class="align-center">
                              <label for="plano_tv">Possui TV?</label>
                              <select ng-change="PLN.planChanged('frmplano')" id="plano_tv"
                                 ng-model="PLN.selecionado.tv" class="form-control"
                                 style="display: inline; width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <label for="plano_roteador">Roteador disponível?</label>
                              <select id="plano_roteador" ng-change="PLN.planChanged('frmplano')"
                                 ng-model="PLN.selecionado.roteador" class="form-control"
                                 style="display: inline; width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                           <td>
                              <label for="plano_decoder">Decoder disponível?</label>
                              <select ng-change="PLN.planChanged('frmplano')" ng-disabled="PLN.selecionado.tv == 0"
                                 id="plano_decoder" ng-model="PLN.selecionado.decoder" class="form-control"
                                 style="display: inline; width: 80px;">
                                 <option value="1">Sim</option>
                                 <option value="0">Não</option>
                              </select>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <label for="plano_roteador_opcional">Roteador incluso?</label>
                              <select ng-change="PLN.planChanged('frmplano')"
                                 ng-disabled="PLN.selecionado.roteador == 0" id="plano_roteador_opcional"
                                 id="plano_roteador_opcional" ng-model="PLN.selecionado.roteador_opcional"
                                 class="form-control" style="display: inline; width: 80px;">
                                 <option value="0">Sim</option>
                                 <option value="1">Não</option>
                              </select>
                           </td>
                           <td>
                              <label for="plano_decoder_opcional">Decoder incluso?</label>
                              <select ng-change="PLN.planChanged('frmplano')"
                                 ng-disabled="PLN.selecionado.tv == 0 || PLN.selecionado.decoder == 0"
                                 id="plano_decoder_opcional" ng-model="PLN.selecionado.decoder_opcional"
                                 class="form-control" style="display: inline; width: 80px;">
                                 <option value="0">Sim</option>
                                 <option value="1">Não</option>
                              </select>
                           </td>
                        </tr>
                     </tbody>
                  </table>
                  <div
                     style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                  </div>
                  <table class="align-center bottom-spaced" style="width: 70%; margin: 0 auto;">
                     <tbody>
                        <tr>
                           <td style="padding-right: 5px;">
                              <label for="plano_valor_roteador">Valor do roteador:</label>
                              <div class="input-group">
                                 <span class="input-group-addon">R$</span>
                                 <input money-mask
                                    ng-disabled="PLN.selecionado.roteador == 0 || PLN.selecionado.roteador_opcional == 0"
                                    type="text" class="form-control align-center" id="plano_valor_roteador"
                                    ng-model="PLN.selecionado.valor_roteador" style="font-size: 11pt;">
                              </div>
                           </td>
                           <td style="padding-left: 5px;">
                              <label for="plano_valor_decoder">Valor do decoder:</label>
                              <div class="input-group">
                                 <span class="input-group-addon">R$</span>
                                 <input money-mask
                                    ng-disabled="PLN.selecionado.tv == 0 || PLN.selecionado.decoder == 0 || PLN.selecionado.decoder_opcional == 0"
                                    type="text" class="form-control align-center" id="plano_valor_decoder"
                                    ng-model="PLN.selecionado.valor_decoder" style="font-size: 11pt;">
                              </div>
                           </td>
                        </tr>
                        <tr>
                          <td class="align-center" colspan="2" style="width: 50%;">
                            <label for="plano_selecionado_valor">Valor do plano:</label>
                            <div class="input-group" style="width: 50%; margin: 0 auto !important;">
                                <span class="input-group-addon">R$</span>
                                <input money-mask type="text" class="form-control align-center" id="plano_selecionado_valor"
                                  ng-model="PLN.selecionado.valor" style="font-size: 11pt;">
                            </div>
                          </td>
                        </tr>
                        <tr ng-if="PLN.selecionado.tecnologia == 'fibra'">
                          <td colspan="2">
                            <div style="width: 100%; height: 1px; margin: 5px auto; background: #CCC;">
                            </div>
                          </td>
                        </tr>
                        <tr ng-if="PLN.selecionado.tecnologia == 'fibra'">
                          <td>
                            <label for="disponivel_backbone">Plano disponível para backbone?</label>
                            <select id="disponivel_backbone" ng-change="PLN.planChanged('frmplano')"
                                ng-model="PLN.selecionado.disponivel_backbone" class="form-control mx-auto"
                                style="width: 80px;">
                                <option value="1">Sim</option>
                                <option value="0">Não</option>
                            </select>
                          </td>
                          <td>
                            <label for="plano_valor_backbone">Valor para backbone:</label>
                            <div class="input-group">
                              <span class="input-group-addon">R$</span>
                                <input money-mask
                                  ng-disabled="PLN.selecionado.disponivel_backbone == 0"
                                  type="text" class="form-control align-center" id="plano_valor_backbone"
                                  ng-model="PLN.selecionado.valor_backbone" style="font-size: 11pt;">
                            </div>
                          </td>
                        </tr>
                     </tbody>
                  </table>
               </div>


               <div id="servicos_inclusos" class="tab-pane fade in">
                  <div class="plano-servicos-container ativos">
                     <h5>Serviços comercializados atualmente</h5>
                     <div style="margin-bottom: 10px; width: 100%; text-align: center;">
                        <span class="text-info" style="font-size: 10pt;">Estes serviços serão exibidos junto ao plano, sendo listados como já incluídos no pacote</span>
                     </div>
                     <label for="plano-item-{{servico.id}}" class="plano-servico-item" ng-repeat="servico in PLN.selecionado.lista_servicos" ng-if="servico.ativo">
                        <input type="checkbox" id="plano-item-{{servico.id}}" ng-model="servico.incluido">
                        {{ servico.servico }}
                     </label>
                  </div>

                  <div class="plano-servicos-container inativos" style="margin-top: 10px;">
                     <h5>Serviços não comercializados atualmente</h5>
                     <div style="margin-bottom: 10px; width: 100%; text-align: center;">
                        <span class="text-warning" style="font-size: 10pt;">Estes serviços não serão exibidos junto ao plano, até que o serviço seja ativado na aba "lista de serviços"</span>
                     </div>
                     <label for="plano-item-{{servico.id}}" class="plano-servico-item" ng-repeat="servico in PLN.selecionado.lista_servicos" ng-if="!servico.ativo">
                        <input type="checkbox" id="plano-item-{{servico.id}}" ng-model="servico.incluido">
                        {{ servico.servico }}
                     </label>
                  </div>
               </div>

            </div>


         </div>
         <!-- Modal Footer -->
         <div class="modal-footer">
            <button class="btn btn-primary" ng-click="PLN.checkSalvar()"><i class="fa fa-check btn-icon"></i>Salvar</button>
         </div>
      </div>
   </div>
