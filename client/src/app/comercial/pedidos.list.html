<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

  <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
  <li><a href="/comercial/atendimento"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
  <li></i> Pedidos</a></li>
</ol>

<h4 class="section-header no-margin">Pedidos assistidos:</h4>

<ul class="nav nav-tabs">
	<li role="presentation" class="active" data-target="#pedidos_assistidos_atuais" data-toggle="tab">
		<a href="#">
			<i class="glyphicon glyphicon-time margin-right-5"></i>
			Pendentes / em andamento
		</a>
	</li>
	<li role="presentation" data-target="#pedidos_assistidos_historico" data-toggle="tab">
		<a href="#">
			<i class="glyphicon glyphicon-list margin-right-5"></i>
			Histórico
		</a>
	</li>
</ul>

<div class="tab-content">
  <div id="pedidos_assistidos_atuais" role="tabpanel" class="tab-pane active" style="margin-bottom: 10px;">
    <table class="full-width">
      <tr>
        <td class="half-width align-center"
          style="border-right: 2px solid #EEE; border-left: 1px solid #CCC; vertical-align: top !important;">

          <h5>Pedidos assistidos <span class="text-success">pendentes</span>:</h5>
          <div class="align-center" style="max-height: 250px; overflow-y: auto; border: 1px solid #CCC;">
            <table class="table table-sm table-striped table-bordered full-width align-center no-margin no-left-border"
              style="border-left: none;">
              <thead ng-if="PC.pedidosAssistidos.pendentes.length > 0">
                <tr>
                  <th>ID</th>
                  <th>Data</th>
                  <th>Nome</th>
                  <th>Contactar via</th>
                  <th>Contato</th>
                  <th>Reservar</th>
                </tr>
              </thead>
              <tbody ng-if="PC.pedidosAssistidos.pendentes.length == 0">
                <tr>
                  <td class="text-warning">Não há pedidos pendentes.</td>
                </tr>
              </tbody>
              <tbody ng-if="PC.pedidosAssistidos.pendentes.length > 0">
                <tr ng-repeat="pedido in PC.pedidosAssistidos.pendentes">
                  <td>{{ pedido.id }}</td>

                  <td>{{ pedido.created_at | amDateFormat:'DD/MM/YYYY' }} <b>{{ pedido.created_at | abbreviateDay }}</b>
                    {{ pedido.created_at | amDateFormat:'HH:mm' }}h</td>

                  <td><b>{{ pedido.nome }}</b></td>

                  <td>{{ pedido.meioContatoStr }}</td>

                  <td>
                    <a ng-if="pedido.meio_contato === 'whatsapp'"
                      href="https://api.whatsapp.com/send/?phone={{ pedido.whatsappNumber }}&text={{PC.whatsappText}}&type=phone_number&app_absent=1"
                      target="_blank">{{ pedido.info_contato }}</a>

                    <a ng-if="pedido.meio_contato === 'email'"
                      href="mailto:{{pedido.info_contato}}">{{pedido.info_contato}}</a>

                    <span ng-if="pedido.meio_contato === 'telefone'">{{pedido.info_contato}}</span>
                  </td>
                  <td ng-if="pedido.status === 'PENDENTE'">
                    <button class="btn btn-vsm btn-success"
                      ng-really-message="Deseja realmente reservar este pedido assistido?"
                      ng-really-click="PC.reservarPedidoAssistido(pedido.id)">
                      <i class="glyphicon glyphicon-share-alt"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </td>
        <td class="half-width align-center" style="border-right: 1px solid #CCC; vertical-align: top !important;">

          <h5>Pedidos assistidos <span class="text-primary">em andamento</span>:</h5>
          <div class="align-center no-left-border" style="max-height: 250px; overflow-y: auto; border: 1px solid #CCC;">
            <table class="table table-sm table-striped table-bordered full-width align-center no-margin no-left-border">
              <thead ng-if="PC.pedidosAssistidos.em_andamento.length > 0">
                <tr>
                  <th>ID</th>
                  <th>Data</th>
                  <th>Nome</th>
                  <th>Contactar via</th>
                  <th>Contato</th>
                  <th>Atendente</th>
                  <th>Finalizar</th>
                </tr>
              </thead>
              <tbody ng-if="PC.pedidosAssistidos.em_andamento.length == 0">
                <tr>
                  <td class="text-warning">Não há pedidos em andamento.</td>
                </tr>
              </tbody>
              <tbody ng-if="PC.pedidosAssistidos.em_andamento.length > 0">
                <tr ng-repeat="pedido in PC.pedidosAssistidos.em_andamento">
                  <td>{{ pedido.id }}</td>

                  <td>{{ pedido.created_at | amDateFormat:'DD/MM/YYYY' }} <b>{{ pedido.created_at | abbreviateDay }}</b>
                    {{ pedido.created_at | amDateFormat:'HH:mm' }}h</td>

                  <td><b>{{ pedido.nome }}</b></td>
                  <td>{{ pedido.meioContatoStr }}</td>
                  <td>
                    <a ng-if="pedido.meio_contato === 'whatsapp'"
                      href="https://api.whatsapp.com/send/?phone={{ pedido.whatsappNumber }}&text={{PC.whatsappText}}&type=phone_number&app_absent=1"
                      target="_blank">{{ pedido.info_contato }}</a>

                    <a ng-if="pedido.meio_contato === 'email'"
                      href="mailto:{{pedido.info_contato}}">{{pedido.info_contato}}</a>

                    <span ng-if="pedido.meio_contato === 'telefone'">{{pedido.info_contato}}</span>
                  </td>
                  <td>{{ pedido.atendente }}</td>
                  <td>
                    <button class="btn btn-vsm btn-primary"
                      ng-really-message="Deseja realmente finalizar este pedido assistido?"
                      ng-really-click="PC.finalizarPedidoAssistido(pedido.id)">
                      <i class="glyphicon glyphicon-ok"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </td>
      </tr>
    </table>
  </div>

  <div id="pedidos_assistidos_historico" role="tabpanel" class="tab-pane">
    <table class="full-width">
      <tr>
        <td class="align-center" style="width: 60%; border-left: 1px solid #CCC; vertical-align: top !important;">

          <h5>Pedidos assistidos (histórico):</h5>
          <div class="align-center no-right-border" style="max-height: 250px; overflow-y: auto; border: 1px solid #CCC;">
            <table class="table table-sm table-striped table-bordered full-width align-center no-margin no-right-border">
              <thead ng-if="PC.pedidosAssistidos.historico.length > 0">
                <tr>
                  <th>ID</th>
                  <th>Data</th>
                  <th>Nome</th>
                  <th>Contactar via</th>
                  <th>Contato</th>
                  <th>Atendente</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody ng-if="PC.pedidosAssistidos.historico.length == 0">
                <tr>
                  <td class="text-warning">Não há pedidos na busca especificada.</td>
                </tr>
              </tbody>
              <tbody ng-if="PC.pedidosAssistidos.historico.length > 0">
                <tr ng-repeat="pedido in PC.pedidosAssistidos.historico">
                  <td>{{ pedido.id }}</td>

                  <td>{{ pedido.created_at | amDateFormat:'DD/MM/YYYY' }} <b>{{ pedido.created_at | abbreviateDay }}</b>
                    {{ pedido.created_at | amDateFormat:'HH:mm' }}h</td>

                  <td><b>{{ pedido.nome }}</b></td>
                  <td>{{ pedido.meioContatoStr }}</td>
                  <td> {{pedido.info_contato}} </span>
                  </td>
                  <td>{{ pedido.atendente }}</td>
                  <td>
                    <span class="label" ng-class="[
                    {'label-warning': pedido.status == 'PENDENTE'},
                      {'label-primary': pedido.status == 'EM ANDAMENTO'},
                      {'label-success': pedido.status == 'FINALIZADO'}
                      ]">{{pedido.status}}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </td>

        <td
          style="width: 40%; border-left: 2px solid #EEE; border-right: 1px solid #CCC; vertical-align: top !important; padding: 0px 10px;">

          <div class="full-width align-center">
            <h5>Filtros de busca:</h5>
          </div>

          <table class="full-width">
            <tr>
              <td colspan="2" class="align-center" style="padding-bottom: 10px;">
                <form class="form-inline" role="form">
                  <div class="form-group">
                    <b>Período:</b>
                  </div>
                  <div class="input-group">
                    <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="PC.filtroAssistidos.dtinicio"
                      is-open="PC.opened3" clear-text="Limpar" close-text="Fechar" current-text="Hoje" ng-change="PC.valida()"
                      size="8" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="PC.open3()"><i
                          class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                  </div>
                  a
                  <div class="input-group">
                    <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="PC.filtroAssistidos.dtfim"
                      is-open="PC.opened4" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="dtfim"
                      ng-change="PC.valida()" size="8" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="PC.open4()"><i
                          class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                  </div>
                </form>
              </td>
            </tr>
            <tr>
              <td style="padding-bottom: 10px;">
                <form class="form-inline full-width" role="form">
                  Nome:
                  <input type="text" class="form-control" ng-model="PC.filtroAssistidos.nome">
                </form>
              </td>
              <td style="padding-bottom: 10px;">
                <form class="form-inline full-width" role="form">
                  Atendente:
                  <select class="form-control" ng-model="PC.filtroAssistidos.atendente" ng-change="PC.assistidosPageChanged()"
                    ng-options="o for o in PC.atendentes">
                  </select>
                </form>
              </td>
            </tr>
            <tr>
              <td>
                <form class="form-inline full-width" role="form">
                  Status:
                  <select class="form-control" ng-model="PC.filtroAssistidos.status" ng-change="PC.assistidosPageChanged()">
                    <option value="">Todos</option>
                    <option value="PENDENTE">Pendente</option>
                    <option value="EM ANDAMENTO">Em andamento</option>
                    <option value="FINALIZADO">Finalizado</option>
                  </select>
                </form>
              </td>
              <td>
                <button class="btn btn-primary" style="margin-top: 10px;" ng-click="PC.getPedidosAssistidosHistorico()">
                  <i class="glyphicon glyphicon-search margin-right-5"></i>
                  Buscar
                </button>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</div>




<h4 class="section-header">Pedidos realizados:</h4>

<div style="display: flex; flex-direction: row; justify-content: space-between;">

  <div class="com-alerts-container">
    <div class="com-alert pointer margin-right-10" ng-class="PC.showingTab == 'visao_geral' ? 'bg-primary' : 'bg-white'" ng-click="PC.showVisaoGeral();">
      VISÃO GERAL
      </br>
      </br>
      <span class="hd-alert-data glyphicon glyphicon-eye-open">
      </span>
    </div>
    <div class="com-alert pointer margin-right-10" ng-class="PC.showingTab == 'agendamentos' && PC.cidade == 'grupo_pcs' ? 'bg-warning-dark' : 'bg-white'" ng-click="PC.showAgendamentos('grupo_pcs');">
      Aguardando agendamento
      </br>
      PCS / CPS
      </br>
      <span class="hd-alert-data">
        {{PC.agendamentos_pcs ? PC.agendamentos_pcs : '...'}}
      </span>
    </div>
    <div class="com-alert pointer margin-right-10" ng-class="PC.showingTab == 'agendamentos' && PC.cidade == 'grupo_and' ? 'bg-warning-dark' : 'bg-white'" ng-click="PC.showAgendamentos('grupo_and');">
      Aguardando agendamento
      </br>
      AND / ESP / JRD / SJB
      </br>
      <span class="hd-alert-data">
        {{PC.agendamentos_and ? PC.agendamentos_and : '...'}}
      </span>
    </div>

    <div class="com-alert pointer" ng-class="PC.showingTab == 'instalacoes' ? 'bg-warning-dark' : 'bg-white'" ng-click="PC.showInstalacoes();">
      Aguardando instalação
      </br>
      {{PC.cidade.trim() === '' ? 'TODAS AS CIDADES' : (PC.cidade !== 'grupo_and' && PC.cidade !== 'grupo_pcs' ? PC.cidade.toUpperCase() : (PC.cidade === 'grupo_and' ? 'AND / ESP / JRD / SJB' : 'PCS / CPS'))}}
      </br>
      <span class="hd-alert-data">
        {{PC.instalacoes ? PC.instalacoes : '...'}}
      </span>
    </div>
  </div>

  <button class="btn btn-primary mx-3" title="Exportar listagem de pedidos para planilha" ng-click="PC.getData(true);" style="height: 63px; max-height: 63px;">
    <i class="glyphicon glyphicon-export"></i>
    <br>
    Exportar
  </button>

</div>

<div class="barra">
  <div class="table">
    <div class="tr">
      <!-- Função desabilitada em 09/05/2025, a pedido da Rita -->
      <!-- <div class="td valign-middle align-center">
        <button type="button" class="btn btn-primary" ng-click="PC.reservarPedido()">
          <i class="fa fa-lock btn-icon"></i><b>Reservar pedido</b>
        </button>
      </div> -->
      <div class="td">
        <div style="display: table;width: 100%;">
          <div style="display: table-row;">
            <div style="display: table-cell;" class="pull-right">
              <form class="form-inline" role="form">
                <div class="form-group">
                  <b>Cidade:</b>
                  <select ng-disabled="PC.showingTab == 'agendamentos'" class="form-control" ng-model="PC.cidade" ng-change="PC.pageChanged()">
                    <option value="">Todas</option>
                    <optgroup label="Grupos">
                      <option value="grupo_pcs">PCS / CPS</option>
                      <option value="grupo_and">AND / ESP / JRD / SJB</option>
                    </optgroup>
                    <optgroup label="Individuais">
                      <option value="Poços de Caldas">Poços de Caldas</option>
                      <option value="Andradas">Andradas</option>
                      <option value="Campestre">Campestre</option>
                      <option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
                      <option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
                      <option value="São João da Boa Vista">São João da Boa Vista</option>
                    </optgroup>
                  </select>
                </div>
                <!-- <div class="form-group">
                  <label for="cb_show_finished" style="font-weight: 100; cursor: pointer;">
                    <input ng-disabled="PC.showingTab == 'agendamentos' || PC.showingTab == 'instalacoes'" type="checkbox" id="cb_show_finished" ng-model="PC.finalizados" ng-change="PC.pageChanged()">
                    Exibir finalizados
                  </label>
                </div> -->
                <div class="form-group">
                  <b>Status:</b>
                  <select ng-disabled="PC.showingTab == 'agendamentos' || PC.showingTab == 'instalacoes'" class="form-control" ng-model="PC.status" ng-change="PC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">Cadastro no Site</option>
                    <option value="2">Confirmação de disponibilidade</option>
                    <option value="3">Validação / Aprovação</option>
                    <option value="4">Agendamento Confirmado</option>
                    <option value="5">Instalação Realizada</option>
                  </select>
                </div>
                <div class="form-group conclusoes-select">
                  <label class="control-label" for="bairro">Conclusão:</label>
                  <ol class="nya-bs-select" title="Todas" id="conclusao" ng-model="PC.conclusoes_selecionadas"
                    data-live-search="false" data-size="3" multiple="true">
                    <li nya-bs-option="conclusao in PC.conclusoes">
                      <a>{{ conclusao }} <span class="glyphicon glyphicon-ok check-mark"></span></a>
                    </li>
                  </ol>
                </div>
                <div class="form-group">
                  <b>Atendente:</b>
                  <select class="form-control" ng-model="PC.atendente" ng-change="PC.pageChanged()"
                    ng-options="o for o in PC.atendentes">
                  </select>
                </div>
              </form>
            </div>
          </div>
          <div style="display: table-row;">
            <div style="display: table-cell;" class="pull-right">
              <form class="form-inline" role="form">

                <div class="form-group">
                  <b>Vendedor:</b>
                  <select class="form-control" ng-model="PC.vendedor" ng-change="PC.pageChanged()">
                    <option>Todos</option>
                    <option>Externos</option>
                    <option>Sem vendedor</option>
                    <optgroup label="Vendedores">
                      <option ng-repeat="vendedor in PC.vendedores">{{ vendedor }}</option>
                    </optgroup>
                  </select>
                </div>

                <div class="form-group">
                  <b>Período:</b>
                </div>
                <div class="input-group">
                  <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="PC.dtinicio"
                    is-open="PC.opened1" clear-text="Limpar" close-text="Fechar" current-text="Hoje"
                    ng-change="PC.valida()" size="8" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default" ng-click="PC.open1()"><i
                        class="glyphicon glyphicon-calendar"></i></button>
                  </span>
                </div>
                a
                <div class="input-group">
                  <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="PC.dtfim"
                    is-open="PC.opened2" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="dtfim"
                    ng-change="PC.valida()" size="8" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default" ng-click="PC.open2()"><i
                        class="glyphicon glyphicon-calendar"></i></button>
                  </span>
                </div>
                <div class="form-group">
                  <select class="form-control" ng-model="PC.tipo">
                    <option value="nome">Nome</option>
                    <option value="logradouro">Logradouro</option>
                    <option value="cpf">CPF/CNPJ</option>
                    <option value="rg">RG</option>
                    <option value="celular">Celular</option>
                    <option value="email">E-mail</option>
                    <option value="id">ID</option>
                  </select>
                </div>
                <div class="form-group">
                  <input size="30" maxlength="30" class="form-control" type="text" ng-model="PC.termos">
                  <button class="btn btn-default" title="Pesquisar" ng-click="PC.busca()">Pesquisar</button>
                  <button class="btn btn-default filter-col" ng-click="PC.limpar()">
                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="table-responsive">
  <span class="counter pull-right"></span>
  <table class="table table-striped table-bordered">
    <tbody ng-repeat="pedido in PC.pedidos">
      <tr>
        <th class="vert-align text-center">ID</th>
        <th class="vert-align text-center">Atendente</th>
        <th class="vert-align text-center">Data Pedido</th>
        <th class="vert-align text-center" ng-if="pedido.pessoa=='F'">Nome</th>
        <th class="vert-align text-center" ng-if="pedido.pessoa=='J'">Razão Social</th>
        <th class="vert-align text-center" ng-if="pedido.pessoa=='F'">CPF</th>
        <th class="vert-align text-center" ng-if="pedido.pessoa=='J'">CNPJ</th>
        <th class="vert-align text-center">Agendamento da visita</th>
        <th class="vert-align text-center">Logradouro</th>
        <th class="vert-align text-center">Num.</th>
        <th class="vert-align text-center">Compl.</th>
        <th class="vert-align text-center">Bairro</th>
        <th class="vert-align text-center">Cidade</th>
        <th class="vert-align text-center">Migração</th>
        <th class="vert-align text-center">Plano</th>
        <th class="vert-align text-center">Vendedor</th>
        <th class="vert-align text-center">Conclusão</th>
      </tr>

      <tr ng-class="pedido.downgrade && pedido.aprovacao_downgrade == 'PENDENTE' ? 'bg-warning' : ''">
        <td class="vert-align text-center">
          <a href="" data-toggle="modal" data-target="#frmpedido" ng-click="PC.seleciona(pedido)">{{pedido.id}}</a>
          <a class="btn btn-default btn-sm" title="Visualizar Pedido" data-toggle="modal" data-target="#frmpedido"
            ng-click="PC.seleciona(pedido)"><i class="glyphicon glyphicon-search"></i></a>
        </td>
        <td class="vert-align text-center">
          <i ng-if="PC.operador.toLowerCase().trim() == pedido.atendente.toLowerCase().trim()"
            class="glyphicon glyphicon-remove pointer text-danger" title="Sair deste atendimento"
            ng-really-message="Deseja realmente se desvincular deste pedido?"
            ng-really-click="PC.desvincularAtendente(pedido);"></i>
          <span ng-if="pedido.atendente != undefined && pedido.atendente != ''">
            {{pedido.atendente}}</span>
        </td>
        <td class="vert-align text-center">{{pedido.pedido_termino | amDateFormat:'DD/MM/YYYY dddd HH:mm:ss'}}</td>
        <td class="vert-align text-center" ng-if="pedido.pessoa=='F'" ng-click="PC.copyToClipboard(pedido.nome)"
          style="cursor: copy;"><b>{{pedido.nome}}</b></td>
        <td class="vert-align text-center" ng-if="pedido.pessoa=='J'" ng-click="PC.copyToClipboard(pedido.razao_social)"
          style="cursor: copy;"><b>{{pedido.razao_social}}</b></td>
        <td class="vert-align text-center" ng-if="pedido.pessoa=='F'" ng-click="PC.copyToClipboard(pedido.cpf)"
          style="cursor: copy;">{{pedido.cpf}}</td>
        <td class="vert-align text-center" ng-if="pedido.pessoa=='J'" ng-click="PC.copyToClipboard(pedido.cnpj)"
          style="cursor: copy;">{{pedido.cnpj}}</td>
        <td class="vert-align text-center">
          <b>
            <span ng-if="pedido.status < 4 && pedido.migracao == 0">(Abrir OS de agendamento)</span>
            <span ng-if="pedido.status >= 4 && pedido.visita_necessaria == 1">(OS de agendamento aberta)</span>
            <span ng-if="pedido.status >= 4 && pedido.visita_necessaria == 0">Visita não necessária</span>
          </b>
          <select ng-model="pedido.agendamento" ng-if="pedido.status == 3 && pedido.conclusao=='ABERTO' && pedido.migracao == 1">
            <option value="" default hidden>Selecione...</option>
            <option value="os_aberta">Já abri a OS de agendamento</option>
            <option value="nao_necessario">Visita não será necessária</option>
          </select>
        </td>
        <td class="vert-align text-center"
          ng-click="PC.copyToClipboard(pedido.endereco_instalacao__logradouro + ',' + pedido.endereco_instalacao__numero)"
          style="cursor: copy;">{{pedido.endereco_instalacao__logradouro}}</td>
        <td class="vert-align text-center">{{pedido.endereco_instalacao__numero}}</td>
        <td class="vert-align text-center">{{pedido.endereco_instalacao__complemento}}</td>
        <td class="vert-align text-center">{{pedido.endereco_instalacao__bairro}}</td>
        <td class="vert-align text-center">{{pedido.endereco_instalacao__cidade}}</td>
        <td class="vert-align text-center">
          <span class="label" ng-class="[{'label-success': pedido.migracao == 1},
                              {'label-primary': pedido.migracao == 0 || pedido.migracao == NULL}]">{{pedido.migracao ==
            1 ? 'SIM' : 'NÃO'}}</span>
        </td>

        <td class="vert-align text-center">
          {{pedido.plano_nome}}
          <i ng-if="pedido.downgrade" class="glyphicon glyphicon-exclamation-sign text-danger ms-1"></i>
        </td>

        <td class="vert-align text-center" style="color: darkorange; font-weight: 600;">{{pedido.vendedor}}</td>
        <td class="vert-align text-center">
          <span class="label"
            ng-class="[{'label-success': pedido.conclusao == 'SUCESSO'},
                      {'label-primary': pedido.conclusao == 'ABERTO'},
                      {'label-danger': pedido.conclusao == 'ERRO_CPF' || pedido.conclusao == 'INVALIDO' || pedido.conclusao == 'INVALIDO_MSG'}]">{{pedido.conclusao}}</span>
        </td>
      </tr>

      <tr>
        <td colspan="15">

          <button item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.atendente != undefined && pedido.atendente != ''},
            {'btn-danger': (pedido.conclusao!='ABERTO' && pedido.conclusao!='SUCESSO') && (pedido.atendente == undefined || pedido.atendente == '')}]"
            ng-really-message="Tem certeza de que deseja iniciar o atendimento para o(a) cliente {{pedido.nome}}?"
            ng-really-click="PC.changeStatus(pedido, 0)" authorize="['comercial.write']"

            ng-disabled="
              (pedido.atendente != undefined && pedido.atendente != '')
              || pedido.conclusao != 'ABERTO'
              || (pedido.downgrade && pedido.aprovacao_downgrade != 'APROVADO')
              "
            ><i
              class="fa fa-check-square" ng-if="pedido.atendente != undefined && pedido.atendente != ''"></i>
            <i class="fa fa-square" ng-if="pedido.atendente == undefined || pedido.atendente == ''"></i>
            Atendimento</button>

          <i class="glyphicon glyphicon-arrow-right"></i>
          <button item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.status >= 1 && pedido.pedido_confirmado == 1},
            {'btn-danger': (pedido.conclusao!=='ABERTO' && pedido.conclusao!=='SUCESSO') && pedido.pedido_confirmado == 0}]"
            ng-really-message="Tem certeza que deseja reenviar o e-mail de confirmação para o cliente {{pedido.nome}} ?"
            ng-really-click="PC.changeStatus(pedido, 1)" authorize="['comercial.write']"
            ng-disabled="pedido.status >= 2 || pedido.pedido_confirmado == 1 || (pedido.atendente == undefined || pedido.atendente == '') || pedido.conclusao!='ABERTO'"><i
              class="fa fa-check-square" ng-if="pedido.status >= 1 && pedido.pedido_confirmado == 1"></i>
            <i class="fa fa-square" ng-if="pedido.pedido_confirmado != 1"></i> <span
              ng-if="pedido.pedido_confirmado != 1">Confirmação de E-mail</span>
            <span ng-if="pedido.pedido_confirmado == 1">E-mail Confirmado</span>
          </button>
          <i class="glyphicon glyphicon-arrow-right"></i>
          <button
            ng-really-message="Tem certeza que deseja alterar o status do pedido de {{pedido.nome}} para 'Confirmação de disponibilidade' ?"
            ng-really-click="PC.changeStatus(pedido, 2)" item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.status >= 2},
                      {'btn-danger': (pedido.conclusao!='ABERTO' && pedido.conclusao!='SUCESSO') && pedido.status < 2}]" authorize="['comercial.write']"
            ng-disabled="pedido.status != 1 || pedido.pedido_confirmado != 1 || (pedido.atendente == undefined || pedido.atendente == '') || pedido.conclusao!='ABERTO'"><i
              class="fa fa-check-square" ng-if="pedido.status >= 2"></i>
            <i class="fa fa-square" ng-if="pedido.status < 2"></i> <span ng-if="pedido.status < 2">Confirmação
              de disponibilidade</span><span ng-if="pedido.status >= 2">Disponibilidade Confirmada</span></button>
          <i class="glyphicon glyphicon-arrow-right"></i>
          <button
            ng-really-message="Tem certeza que deseja alterar o status do pedido de {{pedido.nome}} para 'Validação / Aprovação' ?"
            ng-really-click="PC.changeStatus(pedido, 3)" item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.status >= 3},{'btn-danger': (pedido.conclusao!='ABERTO' && pedido.conclusao!='SUCESSO') && pedido.status < 3}]"
            authorize="['comercial.write']" ng-disabled="pedido.status != 2 || pedido.conclusao!='ABERTO'"><i
              class="fa fa-check-square" ng-if="pedido.status >= 3"></i>
            <i class="fa fa-square" ng-if="pedido.status < 3"></i> <span ng-if="pedido.status < 3">Validação /
              Aprovação</span>
            <span ng-if="pedido.status >= 3">Documentos Aprovados</span>
          </button>
          <i class="glyphicon glyphicon-arrow-right"></i>
          <button
            ng-really-message="<b>Certifique-se de que a OS de agendamento foi aberta, em caso de necessidade de visita técnica.</b><br>Tem certeza de que deseja marcar o Agendamento como concluído?"
            ng-really-click="PC.changeStatus(pedido, 4)" item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.status >= 4}, {'btn-danger': (pedido.conclusao!='ABERTO' && pedido.conclusao!='SUCESSO') && pedido.status < 4}]"
            authorize="['comercial.write']"
            ng-disabled="pedido.conclusao!='ABERTO' || pedido.status != 3 || (pedido.migracao == 1 && (pedido.agendamento == '' || pedido.agendamento == undefined))"><i
              class="fa fa-check-square" ng-if="pedido.status >= 4"></i>
            <i class="fa fa-square" ng-if="pedido.status < 4"></i> Agendamento</button>
          <i class="glyphicon glyphicon-arrow-right"></i>
          <button
            ng-really-message="Tem certeza que deseja alterar o status do pedido de {{pedido.nome}} para 'Instalação Realizada' ?"
            ng-really-click="PC.changeStatus(pedido, 5)" item="pedido" class="btn btn-primary btn-default"
            ng-class="[{'btn-success': pedido.status >= 5}, {'btn-danger': (pedido.conclusao!='ABERTO' && pedido.conclusao!='SUCESSO') && pedido.status < 5}]"
            authorize="['comercial.write']" ng-disabled="pedido.conclusao!='ABERTO'|| pedido.status != 4"><i
              class="fa fa-check-square" ng-if="pedido.status >= 5"></i>
            <i class="fa fa-square" ng-if="pedido.status < 5"></i> Instalação Realizada</button>
        </td>
      </tr>

      <tr style="background-color:darkorange;">
        <td colspan="15"></td>
      </tr>

    </tbody>
  </table>

  <div class="text-center">

    <div class="text-center">
      <uib-pagination total-items="PC.pagination.size" ng-model="PC.pagination.page" ng-change="PC.pageChanged()"
        items-per-page="PC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
        boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
      </uib-pagination>
    </div>
    <div class="text-center">
      Página <span class="badge">{{ PC.pagination.page}}</span> de <span class="badge">{{ PC.pagination.pages}}</span>
      de <span class="badge">{{ PC.pagination.size}}</span> registro(s)</span>
    </div>
  </div>

  <div ng-include="'app/comercial/pedido.form.html'"></div>
  <div ng-include="'app/comercial/pedido.reenvio.html'"></div>
