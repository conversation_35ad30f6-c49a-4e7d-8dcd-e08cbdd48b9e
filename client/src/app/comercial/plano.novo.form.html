<div class="modal" id="frmnovoplano" tabindex="-1" role="dialog" aria-labelledby="frmnovoplano" aria-hidden="true"
   modal="showModal" close="cancel()">

   <div class="modal-dialog modal-md">
      <div class="modal-content">

         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" id="frmnovoplano_fechar">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title">
               Novo plano
            </h4>
         </div>

         <!-- Modal Body -->
         <div class="modal-body" style="padding-bottom: 15px;">
            <div class="row">
               <div class="tab-content">
                  <div id="dados" class="tab-pane fade in active">
                     <table class="table bottom-spaced" style="width: 70%; margin: 0 auto; margin-bottom: 5px;">
                        <tbody>
                           <tr>
                              <td class="align-center" colspan="3" style="padding-bottom: 20px !important;">
                                 <label for="novoplano_ativo">Ativo:</label>
                                 <select id="novoplano_ativo" ng-model="PLN.novoplano.ativo" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                           </tr>
                           <tr>
                              <td style="width: 40%; padding-right: 5px;">
                                 <label for="novoplano_cidade">Cidade:</label>
                                 <select name="" id="novoplano_cidade" class="form-control"
                                    ng-model="PLN.novoplano.cidade">
                                    <option ng-repeat="cidade in PLN.cidadesAtendidas" value="{{ cidade.codigo_alt }}">{{ cidade.cidade }}</option>
                                 </select>
                              </td>
                              <td style="width: 30%; padding: 0px 5px;">
                                 <label for="novoplano_tecnologia">Tecnologia:</label>
                                 <select name="" id="novoplano_tecnologia" class="form-control"
                                    ng-model="PLN.novoplano.tecnologia">
                                    <option value="fibra">Fibra Óptica</option>
                                    <option value="radio">Via Rádio</option>
                                    <option value="cdmht">CDMHT</option>
                                 </select>
                              </td>
                              <td style="width: 30%; padding-left: 5px;">
                                 <label for="novoplano_velocidade">Velocidade:</label>
                                 <div class="input-group">
                                    <input type="text" class="form-control align-center" id="novoplano_velocidade"
                                       ng-model="PLN.novoplano.velocidade" style="font-size: 11pt;"><span
                                       class="input-group-addon">Mbps</span>
                                 </div>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                     <table class="table bottom-spaced" style="width: 70%; margin: 0 auto;">
                        <tbody>
                           <tr>
                              <td style="width: 55%; padding-right: 5px;">
                                 <label for="novoplano_nome">Nome:</label>
                                 <input type="text" class="form-control" ng-model="PLN.novoplano.nome"
                                    id="novoplano_nome">
                              </td>
                              <td style="width: 45%; padding-left: 5px;">
                                 <label for="novoplano_caption">Legenda:</label>
                                 <input type="text" class="form-control" ng-model="PLN.novoplano.caption"
                                    id="novoplano_caption">
                              </td>
                           </tr>
                        </tbody>
                     </table>
                     <div
                        style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                     </div>
                     <table class="table bottom-spaced" style="width: 70%; margin: 0 auto;">
                        <tbody>
                           <tr>
                              <td style="width: 50%; text-align: center;">
                                 <label for="disponivel_contratacao">Disponível para novos contratos?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')" id="disponivel_contratacao" ng-model="PLN.novoplano.disponivel_contratacao" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                              <td style="width: 50%; text-align: center;">
                                 <label for="disponivel_migracao">Disponível para migração?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')" id="disponivel_migracao" ng-model="PLN.novoplano.disponivel_migracao" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                     <div
                        style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                     </div>
                     <table class="align-right bottom-spaced" style="width: 70%; margin: 0 auto;">
                        <tbody>
                           <tr>
                              <td>
                              </td>
                              <td class="align-center">
                                 <label for="novoplano_tv">Possui TV?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')" id="novoplano_tv"
                                    ng-model="PLN.novoplano.tv" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                           </tr>
                           <tr>
                              <td>
                                 <label for="novoplano_roteador">Roteador disponível?</label>
                                 <select id="novoplano_roteador" ng-model="PLN.novoplano.roteador" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                              <td>
                                 <label for="novoplano_decoder">Decoder disponível?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')" ng-disabled="PLN.novoplano.tv == 0"
                                    id="novoplano_decoder" ng-model="PLN.novoplano.decoder" class="form-control"
                                    style="display: inline; width: 80px;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                           </tr>
                           <tr>
                              <td>
                                 <label for="novoplano_roteador_opcional">Roteador incluso?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')"
                                    ng-disabled="PLN.novoplano.roteador == 0" id="novoplano_roteador_opcional"
                                    id="novoplano_roteador_opcional" ng-model="PLN.novoplano.roteador_opcional"
                                    class="form-control" style="display: inline; width: 80px;">
                                    <option value="0">Sim</option>
                                    <option value="1">Não</option>
                                 </select>
                              </td>
                              <td>
                                 <label for="novoplano_decoder_opcional">Decoder incluso?</label>
                                 <select ng-change="PLN.planChanged('frmnovoplano')"
                                    ng-disabled="PLN.novoplano.tv == 0 || PLN.novoplano.decoder == 0"
                                    id="novoplano_decoder_opcional" ng-model="PLN.novoplano.decoder_opcional"
                                    class="form-control" style="display: inline; width: 80px;">
                                    <option value="0">Sim</option>
                                    <option value="1">Não</option>
                                 </select>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                     <div
                        style="width: 70%; height: 1px; margin: 0 auto; background: #CCC; margin-top: 15px; margin-bottom: 15px;">
                     </div>
                    <table class="align-center bottom-spaced" style="width: 70%; margin: 0 auto;">
                      <tbody>
                        <tr>
                          <td style="padding-right: 5px;">
                            <label for="novoplano_valor_roteador">Valor do roteador:</label>
                            <div class="input-group">
                              <span class="input-group-addon">R$</span>
                              <input money-mask ng-disabled="PLN.novoplano.roteador == 0 || PLN.novoplano.roteador_opcional == 0"
                                type="text" class="form-control align-center" id="novoplano_valor_roteador"
                                ng-model="PLN.novoplano.valor_roteador" style="font-size: 11pt;">
                            </div>
                          </td>
                          <td style="padding-left: 5px;">
                            <label for="novoplano_valor_decoder">Valor do decoder:</label>
                            <div class="input-group">
                              <span class="input-group-addon">R$</span>
                              <input money-mask
                                ng-disabled="PLN.novoplano.tv == 0 || PLN.novoplano.decoder == 0 || PLN.novoplano.decoder_opcional == 0"
                                type="text" class="form-control align-center" id="novoplano_valor_decoder"
                                ng-model="PLN.novoplano.valor_decoder" style="font-size: 11pt;">
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="align-center" colspan="2" style="width: 50%;">
                            <label for="novoplano_valor">Valor do plano:</label>
                            <div class="input-group" style="width: 50%; margin: 0 auto !important;">
                              <span class="input-group-addon">R$</span>
                              <input money-mask type="text" class="form-control align-center" id="novoplano_valor"
                                ng-model="PLN.novoplano.valor" style="font-size: 11pt;">
                            </div>
                          </td>
                        </tr>
                        <tr ng-if="PLN.novoplano.tecnologia == 'fibra'">
                          <td colspan="2">
                            <div style="width: 100%; height: 1px; margin: 5px auto; background: #CCC;">
                            </div>
                          </td>
                        </tr>
                        <tr ng-if="PLN.novoplano.tecnologia == 'fibra'">
                          <td>
                            <label for="disponivel_backbone">Plano disponível para backbone?</label>
                            <select id="disponivel_backbone" ng-change="PLN.planChanged('frmnovoplano')"
                              ng-model="PLN.novoplano.disponivel_backbone" class="form-control mx-auto" style="width: 80px;">
                              <option value="1">Sim</option>
                              <option value="0">Não</option>
                            </select>
                          </td>
                          <td>
                            <label for="novoplano_valor_backbone">Valor para backbone:</label>
                            <div class="input-group">
                              <span class="input-group-addon">R$</span>
                              <input money-mask ng-disabled="PLN.novoplano.disponivel_backbone == 0" type="text"
                                class="form-control align-center" id="novoplano_valor_backbone" ng-model="PLN.novoplano.valor_backbone"
                                style="font-size: 11pt;">
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
               </div>
            </div>
         </div>
         <!-- Modal Footer -->
         <div class="modal-footer">
            <button class="btn btn-primary" ng-click="PLN.checkAdicionar()"><i
                  class="fa fa-check btn-icon"></i>Adicionar</button>
         </div>
      </div>
   </div>
