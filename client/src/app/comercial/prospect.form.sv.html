<div class="modal" id="frmprospectsv" tabindex="-1" role="dialog"
aria-labelledby="frmprospectsv" aria-hidden="true" modal="showModal" close="cancel()">

<div class="modal-dialog">
<div class="modal-content">
    <!-- <PERSON><PERSON>er -->
    <div class="modal-header">
        <button type="button" class="close"
           data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmparametroslabel">
            Prospect
        </h4>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
          <div class="row">
<div map-lazy-load="https://maps.google.com/maps/api/js"
  map-lazy-load-params="{{PLC.googleMapsUrl}}">

  <ng-map
    center="Rua Prefeito Chagas, 305 - Centro, Poços de Caldas - MG, Brasil"
    zoom="12"
    default-style="false"
    style="display:block; height:600px">
    <marker position="{{PLC.selecionado.latitude}}, {{PLC.selecionado.longitude}}"
         icon="{path:'CIRCLE', scale: 10, strokeColor: 'red', zIndex:10}"></marker>
<!--

<street-view-panorama
click-to-go="false"
disable-default-u-i="true"
disable-double-click-zoom="false"
position="-21.78856390811573 -46.56475945987847"
pov="{heading: heading, pitch: pitch}"
scrollwheel="false"
enable-close-button="false"
fullscreen-control="false"
zoom-control="true"
visible="true">

</street-view-panorama>
-->

</ng-map>
</div>
</div>

<!-- Modal Footer -->
<div class="modal-footer">
    <button type="button" class="btn btn-default"
            data-dismiss="modal">
                Fechar
    </button>

    <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '0'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal"> Salvar</button>
    <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor' && patrimoniolegivel == '1'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid || (patrimonioselecionado.categoria_id !== undefined && patrimonioselecionado.categoria_id !== 1 && patrimonioselecionado.categoria_id !== 4) || (patrimonioselecionado.categoria_id == undefined)" data-dismiss="modal"> Salvar</button>
    <!-- <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Provedor'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
        Salvar</button> -->

    <button type="button" class="btn btn-primary" ng-if="parametro.modooperacao == 'Hardware do Cliente'" ng-click="saveParametro(parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
            Salvar</button>
</div>
</div>
</div>
</div>
