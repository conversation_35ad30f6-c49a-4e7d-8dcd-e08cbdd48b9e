<ol class="breadcrumb">

    <li><i class="glyphicon glyphicon-edit"></i> Cobertura</li>
    <li><a href="/comercial/cobertura"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
</ol>

<div map-lazy-load="https://maps.google.com/maps/api/js"
  map-lazy-load-params="{{CCC.googleMapsUrl}}">
 
  <form class="form-horizontal" name="frmConsulta">
<div class="panel" ng-class="{'panel-danger': frmConsulta.$invalid, 'panel-success': !frmConsulta.$invalid}" id="etapa1" ng-show="CCC.etapa==1">
    <div class="panel-heading">
       <h4 class="panel-title">Consulta</h4>
    </div>
    <div class="panel-body">
      
        <div class="form-group">
          <label class="col-md-2 control-label" for="atendimento">Tipo Atendimento</label>
          <div class="col-md-2">
            <select class="form-control" id="atendimento" ng-model="CCC.prospect.atendimento" required ng-disabled="CCC.terceiro">
              <option value="presencial">Presencial</option>
              <option value="telefone">Telefone</option>
              <option value="externo">Vendedor Externo</option>
            </select>  
          </div>
        </div>   
        <div class="form-group">
          <label class="col-md-2 control-label" for="cliente">Já é cliente?</label>
          <div class="col-md-1">
            <select class="form-control" id="cliente" ng-model="CCC.prospect.cliente" ng-change="CCC.cpfChange()" required>
              <option value="0">Não</option>
              <option value="1">Sim</option>
            </select>  
          </div>
        </div>
        <div class="form-group" ng-show="CCC.prospect.cliente=='1'">
          <label class="col-md-2 control-label" for="cpf">CPF</label>
          <div class="col-md-2">
            <input size="20" class="form-control" 
                       name="cpf"
                       id="cpf"
                       ng-change="CCC.cpfChange()"
                       ng-model="CCC.prospect.cpf"/>
            </div>
        </div>          
        
        <div class="form-group" ng-show="CCC.cpfvalido==true && (CCC.contratos.length > 1)">
            <label class="col-md-2 control-label" for="contrato">Contrato</label>
            <div class="col-md-2">
              <select class="form-control" ng-options="item as (item.logradouro + ', ' + item.numero) for item in CCC.contratos" ng-model="CCC.contratoselecionado"></select>
              </div> 
        </div>  
          
        <div class="form-group">
          <div class="col-md-2">
          </div>  
          <div class="col-md-1">
             <button ng-if="CCC.prospect.cliente=='1' && CCC.cpfvalido == false" id ="btnverificar" class="btn btn-warning" title="" ng-click="CCC.consultacpf(CCC.prospect.cpf)"><i class="glyphicon glyphicon-search"></i> Verificar CPF</button>
             <button ng-if="CCC.prospect.cliente=='0' || (CCC.prospect.cliente=='1' && CCC.contratoselecionado !== undefined && CCC.cpfvalido == true)" id ="btnprosseguir" class="btn btn-default" title="" ng-click="CCC.prosseguir()"><i class="glyphicon glyphicon-forward"></i> Prosseguir</button>
          </div> 
        </div>  
    </div>     
</div>     

<div class="panel" ng-class="{'panel-danger': frmConsulta.$invalid, 'panel-success': !frmConsulta.$invalid}" id="etapa2" ng-show="CCC.etapa==2">
   <div class="panel-heading clearfix">
      <div class="btn-group pull-right">
          <button id="btnvoltar" class="btn btn-default" title="" ng-click="CCC.voltar()"><i class="glyphicon glyphicon-backward"></i> Voltar</button>
          
      </div>
      <h4 class="panel-title">Informe os dados para pesquisa</h4>
    </div>
    <div class="panel-body">
        <div class="form-group" ng-if="CCC.prospect.cliente == '1'">
            <label class="col-md-2 control-label" for="cliente">Cliente</label>
            <div class="col-md-6">
              <h4>{{CCC.prospect.nome}}</h4>
            </div>
          </div>      
      <div class="form-group">
        <label class="col-md-2 control-label" for="cidade">Endereço</label>
        <div class="col-md-6">
          <input size="100" class="form-control" 
                      name="enderecogoogle"
                      id="enderecogoogle"
                      places-auto-complete 
                       types="['geocode']" 
                      on-place-changed="CCC.autocompleteCallback()" 
                      ng-change="CCC.enderecoChange()"
                      strict-bounds="true"
                      circle-bounds="{{CCC.limite}}"
                      ng-model="CCC.pesquisa.endereco" required/>
        </div>
      </div>          

      <div class="form-group">
        <label class="col-md-2 control-label" for="numero">Número</label>
        <div class="col-md-1">
          <input string-to-number size="5" type="number" class="form-control" id="numero" ng-model="CCC.pesquisa.numero" required/>
        </div>
      </div>         

      <div class="form-group">
        <label class="col-md-2 control-label" for="numero">Complemento</label>
        <div class="col-md-2">
          <input class="form-control" id="numero" ng-model="CCC.pesquisa.complemento"/>
        </div>
      </div>    
     
      
      <div class="form-group">
        <div class="col-md-2">
        </div>  
        <div class="col-md-1">
            <button id ="btnverificar" class="btn btn-warning" title="" ng-click="CCC.verificar()" ng-disabled="CCC.dropdown==false || CCC.pesquisa.numero == undefined"><i class="glyphicon glyphicon-search"></i> Verificar Endereço</button>
        </div> 
      </div>  
    </div>     
</div>     

 <div class="panel panel-success" id="etapa3" ng-show="CCC.etapa==3">
   <div class="panel-heading  clearfix">
    <div class="btn-group pull-right">
          <button id="btnvoltar" class="btn btn-default" title="" ng-click="CCC.voltar()"><i class="glyphicon glyphicon-backward"></i> Voltar</button>
          
      </div>
                  <h4 class="panel-title">Verifique e confirme a localização da residência</h4>
                </div>
                <div class="panel-body">
                  <h4>{{CCC.pesquisa.endereco}}</h4>

<br>
  <!--<button id="btnconfirmo" class="btn btn-suCCCess" title="" ng-click="CCC.cobertura()">Verificar cobertura</button>-->
  <button class="btn btn-success" ng-click="CCC.consultaCobertura()">Verificar cobertura</button>
</div>


</div>

<div class="panel" ng-class="{'panel-danger': ((CCC.prospect.cobertura == '') || (CCC.prospect.cobertura == undefined)), 'panel-success': (CCC.prospect.cobertura !== '') }" ng-show="CCC.etapa==4">
    
  <div class="panel-heading clearfix">
      <div class="btn-group pull-right">
          <button id="btnvoltar" class="btn btn-default" title="" ng-click="CCC.voltar()"><i class="glyphicon glyphicon-backward"></i> Voltar</button>
          
      </div>
                  <h4 class="panel-title">Selecione uma cobertura</h4>
            
                </div>
                
                <div class="panel-body">
                    <h4>{{CCC.pesquisa.endereco}}</h4>
                <div class="alert alert-danger" ng-show="CCC.cobertura.coberturas.length == 0">
                           <strong>Não existe cobertura de atendimento nessa área.</strong>
                 </div>
                 <div class="alert alert-success" ng-show="CCC.cobertura.coberturas.length > 0">
                      <strong>Esta área é atendida pelo provedor. Por favor selecione uma área de cobertura.</strong>
                  </div>
                  <div class="row" ng-show="CCC.cobertura.coberturas.length > 0">        
                <div class="col-md-6">

<p><strong>Tecnologias disponíveis:</strong></p>

<table class="table table-striped table-hover table-bordered">
  <thead>
  <tr>
  <th></th>
  <th class="vert-align text-center">Cobertura</th>
  <th class="vert-align text-center">Tecnologia</th>
  </tr>
  </thead>
  <tbody>
    <tr ng-repeat="cobertura in CCC.cobertura.coberturas">
    <td class="vert-align text-center"><input type="radio" ng-model="CCC.prospect.cobertura" ng-value="{{cobertura}}" ng-click="CCC.getPlanos()"></td>
    <td class="vert-align text-center">{{cobertura.cobertura}}</td>
    <td class="vert-align text-center">{{cobertura.tecnologia}} <span class="label label-success" ng-if="cobertura.cdm == 1">CDM</span></td>

  </tr>
  </tbody>
  </table>
  <div class="alert alert-warning" ng-show="CCC.cobertura.clientes > 5">
      <strong>Existe(m) mais {{CCC.cobertura.clientes}} cliente(s) neste endereço. A equipe técnica será alertada para verificar a possibilidade da criação de um novo CDM no local.</strong>
  </div>
  
  <div class="alert alert-warning" ng-show="CCC.cobertura.area_fibra == true && CCC.prospect.cobertura.ht == '0' && CCC.prospect.cobertura.cdm == '1'">
    <strong>Este local se encontra na área de cobertura Fibra. A equipe técnica será alertada para realizar a migração para Fibra.</strong>
  </div>

  
</div>



</div>

<div class="alert alert-warning" ng-show="CCC.cobertura.previsao !== '' && CCC.cobertura.previsao !== false">
  <strong>Previsao para atendimento FIBRA: {{CCC.cobertura.previsao | amDateFormat:'MMMM/YYYY'}}</strong>
</div>

                </div>
</div>    


<div class="panel panel-default" ng-show="CCC.etapa==4">
  <div class="panel-heading">
    <h4 class="panel-title">Planos disponíveis</h4>
  </div>
  <div class="panel-body">
    
      <table class="table table-bordered table-sm" style="margin-bottom: 0px;width:98.8%">
        <thead>
        <tr>
          <th class="vert-align text-center" style="width: 3%;"></th>
          <th class="vert-align text-center" style="width: 25%;">Plano</th>
          <th class="vert-align text-center" style="width: 12%;">Download</th>
          <th class="vert-align text-center" style="width: 12%;">Upload</th>
          <th class="vert-align text-center" style="width: 12%;">Franq. Download</th>
          <th class="vert-align text-center" style="width: 12%;">Franq. Upload</th>
          <th class="vert-align text-center" style="width: 12%;">Franq. Reduzida Download</th>
          <th class="vert-align text-center" style="width: 12%;">Franq. Reduzida Upload</th>
        </tr>
        </thead>
      </table>  
      <div class="pre-scrollable ng-scope" style="height:200px;margin-bottom:15px;">
      <table class="table table-bordered table-hover table-striped">
           <tbody>
            <tr ng-repeat="plano in CCC.planos">
            <td class="vert-align text-center" style="width: 3%;"><input type="radio" ng-model="CCC.prospect.plano" ng-value="{{plano}}"></td>
            <td class="vert-align text-center" style="width: 25%;">{{plano.nomeplano}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.down}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.up}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.quotadown}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.quotaup}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.downfranqreduz}}</td>
            <td class="vert-align text-center" style="width: 12%;">{{plano.upfranqreduz}}</td>
          </tr>
          </tbody>
          </table>
        </div>
        <p>
        <strong>Plano selecionado: </strong>{{CCC.prospect.plano.nomeplano}} 
        </p>
</div>    
</div>

<div class="panel panel-default" ng-show="CCC.etapa==4">
    <div class="panel-heading">
      <h4 class="panel-title">Finalização</h4>
    </div>
    <div class="panel-body">
        <div class="form-group">
            <label class="col-md-2 control-label">Status:</label>
                <div class="col-md-3">
        
          <select class="form-control" ng-model="CCC.prospect.exito" required>
            <option ng-if="CCC.cobertura.coberturas.length > 0" value="1">Venda Efetivada</option>
            <option value="0">Venda Não Efetivada</option>
            <option value="2">Não Atendido</option>
            <option value="3" ng-if="CCC.cobertura.area_fibra">Solicitar FTTA</option>
          </select>
        </div>  
    </div>

    <div class="form-group" ng-show="CCC.prospect.exito=='0'">
        <label class="col-md-2 control-label">Motivo:</label>
            <div class="col-md-4">
                <input type="text" class="form-control" id="motivo" ng-model="CCC.prospect.motivo" placeholder="Motivo"  ng-required="CCC.prospect.exito=='0'">
            </div>    
    </div>        
</div>    
</div>

  

  <div class="panel panel-default" ng-show="CCC.etapa==4">
      <div class="panel-heading">
        <h4 class="panel-title">Ficha de Cadastro</h4>
      </div>
      <div class="panel-body">
      <div class="form-group">
          <label class="col-md-2 control-label">Nome:</label>
              <div class="col-md-3">
               <input type="text" 
                class="form-control input-md" 
                ng-model="CCC.prospect.nome" 
                autofocus="autofocus"
                >
              </div>
       </div>       

       <div class="form-group">
       <label class="col-md-2 control-label">Telefone:</label>
           <div class="col-md-2">
            <input id="telefone" type="text" class="form-control input-md" 
                  ng-model="CCC.prospect.telefone"> 
            </div>
        </div>    

      <div class="form-group">
            <label class="col-md-2 control-label">E-mail:</label>
                <div class="col-md-3">
                 <input id="email" type="email" class="form-control input-md" ng-model="CCC.prospect.email"    
                  >
            </div>
      </div>      

      <div class="form-group">
          <label class="col-md-2 control-label">CPF:</label>
              <div class="col-md-2">
                <input id="cpf" class="form-control input-md" ng-model="CCC.prospect.cpf">
              </div>
      </div>        

      <div class="form-group">
          <label class="col-md-2 control-label">RG:</label>
            <div class="col-md-2">
              <input id="rg" type="number" class="form-control input-md" ng-model="CCC.prospect.rg">
            </div>
      </div>      

      <div class="form-group">
          <label class="col-md-2 control-label">Profissão:</label>
              <div class="col-md-2">
                <input id="profissao" type="text" class="form-control input-md" ng-model="CCC.prospect.profissao">
              </div>
      </div>        

      <div class="form-group">
          <label class="col-md-2 control-label">Estado Civil:</label>
              <div class="col-md-2">
                  <select id="estadocivil" class="form-control input-md" ng-model="CCC.prospect.estadocivil">
                    <option value="solteiro">Solteiro(a)</option>
                    <option value="casado">Casado(a)</option>
                    <option value="amasiado">Amasiado(a)</option>
                    <option value="divorciado">Divorciado(a)</option>
                    <option value="viuvo">Viuvo(a)</option>
                  </select>
              </div>
      </div>        
      <div class="form-group">
          <label class="col-md-2 control-label">Dia Vencimento:</label>
              <div class="col-md-1">
                <input id="diavencimento" type="number" class="form-control input-md" ng-model="CCC.prospect.diavencimento">
              </div>
      </div>        

      <div class="form-group">
          <label class="col-md-2 control-label">Prazo Contratual:</label>
              <div class="col-md-1">
                <input id="prazocontratual" type="number" class="form-control input-md" ng-model="CCC.prospect.prazocontratual">
              </div>
       </div>       

       <div class="form-group">
          <label class="col-md-2 control-label">Data Instalação</label>
            <div class="col-md-2">
              <div class="input-group">
                  <input class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="CCC.prospect.datainstalacao" is-open="CCC.opened" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="datainstalacao" type="text" ng-required="false">
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default" ng-click="CCC.open()"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
          </div>
        </div>  
      </div>  
    </div>
  </div>  
</form>

<button id="btnfinalizar" class="btn btn-success" title="" ng-click="CCC.finaliza()" authorize="['comercial.write']" ng-show="CCC.etapa==4" ng-disabled="frmConsulta.$invalid">FINALIZAR</button>

<ng-map 
id="mapa_cobertura"
center="R. Maj. Bonifácio, 112 - Centro, Andradas - MG, 37795-000" 
zoom="12" 
default-style="false" 
style="display:block; height:500px">

<street-view-panorama 
id="panorama_cobertura"
position="-22.0702781,-46.5910499"
pov="{heading: heading, pitch: pitch}"
visible="true">

</street-view-panorama>

</ng-map>
</div>