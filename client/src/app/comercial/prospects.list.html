<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

    <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
    <li><a href="/comercial/atendimento"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
    <li><i class="glyphicon glyphicon-usd"></i> Prospects</a></li>
</ol>
<div class="barra">
    <div class="form-group">

    <a href="/comercial/atendimento" type="button" class="btn btn-success btn-incluir text-center" ng-if="eventoSelecionado.id != ''" authorize="['hosts.write']"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>

    <div class="form-group pull-right">
        <form class="form-inline" role="form">
                <div class="form-group">
                    Venda:
                  <select class="form-control" ng-model="PLC.exito" ng-change="PLC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">Efetuada</option>
                    <option value="0">Não Efetuada</option>
                    <option value="2">Não Atendido</option>
                  </select>
                  Atendimento:
                  <select class="form-control" ng-model="PLC.atendimento" ng-change="PLC.pageChanged()">
                        <option value="">Todos</option>
                        <option value="presencial">Presencial</option>
                        <option value="telefone">Telefone</option>
                        <option value="externo">Externo</option>
                      </select>
                  Cliente?:
                  <select class="form-control" ng-model="PLC.cliente" ng-change="PLC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">Clientes</option>
                    <option value="0">Não Clientes</option>
                  </select>
                  CDM?
                  <select class="form-control" ng-model="PLC.cdm" ng-change="PLC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">CDM</option>
                    <option value="0">Não CDM</option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control" ng-model="PC.filtro.tipo">
                    <option value="nome">Nome</option>
                    <option value="logradouro">Logradouro</option>
                    <option value="cpf">CPF/CNPJ</option>
                    <option value="rg">RG</option>
                    <option value="celular">Celular</option>
                    <option value="id">ID</option>
                  </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="PLC.termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="PLC.busca(PLC.termos)">Pesquisar</button>
                <button class="btn btn-default filter-col" ng-click="PLC.limpar()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
        </div>
      </div>
</div>

<!--
<div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
                  <thead style="background-color: lightgray;">
                    <tr>
                      <th class="vert-align text-center" style="width: 10px;"></th>
                      <th class="vert-align text-center">ID</th>
                      <th class="vert-align text-center">Nome</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat-start="prospect in PLC.prospects">
                      <td class="vert-align text-center">
                        <button ng-if="prospect.expanded" ng-click="prospect.expanded = false">-</button>
                        <button ng-if="!prospect.expanded" ng-click="prospect.expanded = true">+</button>
                      </td>
                      <td class="vert-align text-center">{{prospect.id}}</td>
                      <td class="vert-align text-center">{{prospect.nome}}</td>
                    </tr>
                    <tr ng-if="prospect.expanded" ng-repeat-end="">
                      <td colspan="3">
                              <table class="table table-striped table-hover table-bordered">
                                 <thead>
                                    <tr>
                                      <th class="vert-align text-center">ID</th>
                                    </tr>
                                 </thead>
                                <tbody>
                                  <tr>
                                    <td class="vert-align text-center">{{prospect.id}}</a></td>
                                  </tr>
                                </tbody>
                              </table>



                  </td>
                    </tr>
                  </tbody>
                </table>
                </div>

              -->

<div class="table-responsive">
    <span class="counter pull-right"></span>

            <table class="table table-striped table-hover table-bordered">
              <thead>
                <tr>
                    <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('id')"> ID
                <span ng-show="PLC.sortBy == 'id' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'id' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                    <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('datacad')"> Data
                <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('atendimento')"> Atendimento
                <span ng-show="PLC.sortBy == 'atendimento' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'atendimento' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cliente')"> Cliente?
                <span ng-show="PLC.sortBy == 'cliente' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cliente' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('nome')"> Nome
                <span ng-show="PLC.sortBy == 'nome' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'nome' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                    <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('telefone')"> Telefone
                <span ng-show="PLC.sortBy == 'telefone' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'telefone' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('email')"> E-mail
                <span ng-show="PLC.sortBy == 'email' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'email' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('logradouro')"> Logradouro
                <span ng-show="PLC.sortBy == 'logradouro' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'logradouro' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('numero')"> Número
                <span ng-show="PLC.sortBy == 'numero' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'numero' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('complemento')"> Complemento
                <span ng-show="PLC.sortBy == 'complemento' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'complemento' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('bairro')"> Bairro
                <span ng-show="PLC.sortBy == 'bairro' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'bairro' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cidade')"> Cidade
                <span ng-show="PLC.sortBy == 'cidade' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cidade' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cobertura')"> Cobertura
                <span ng-show="PLC.sortBy == 'cobertura' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cobertura' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('plano')"> Plano
                <span ng-show="PLC.sortBy == 'plano' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'plano' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cdm')"> CDM
                <span ng-show="PLC.sortBy == 'cmd' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cdm' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('exito')"> Venda
                <span ng-show="PLC.sortBy == 'exito' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'exito' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('motivo')"> Motivo
                <span ng-show="PLC.sortBy == 'motivo' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'motivo' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('username')"> Operador
                <span ng-show="PLC.sortBy == 'username' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'username' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>

                </tr>
              </thead>
              <tbody>

                <tr ng-repeat="prospect in PLC.prospects">
                    <td class="vert-align text-center"><a href="" data-toggle="modal" data-target="#frmprospect" ng-click="PLC.seleciona(prospect)">{{prospect.id}}</a></td>
                    <td class="vert-align text-center">{{prospect.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{prospect.atendimento}}</a></td>
                    <td class="vert-align text-center"><span class="label label-success ng-scope" style="cursor: default;" ng-if="prospect.cliente==1">Sim</span><span class="label label-danger" style="cursor: default;" ng-if="prospect.cliente==0">Não</span></td>
                    <td class="vert-align text-center">{{prospect.nome}}</a></td>
                    <td class="vert-align text-center">{{prospect.telefone}}</td>
                    <td class="vert-align text-center">{{prospect.email}}</td>
                    <td class="vert-align text-center">{{prospect.logradouro}}</td>
                    <td class="vert-align text-center">{{prospect.numero}}</td>
                    <td class="vert-align text-center">{{prospect.complemento}}</td>
                    <td class="vert-align text-center">{{prospect.bairro}}</td>
                    <td class="vert-align text-center">{{prospect.cidade}}</td>
                    <td class="vert-align text-center">{{prospect.cobertura}}</td>
                    <td class="vert-align text-center">{{prospect.plano}}</td>
                    <td class="vert-align text-center"><span class="label label-success ng-scope" style="cursor: default;" ng-if="prospect.cdm==1">Sim</span><span class="label label-danger" style="cursor: default;" ng-if="prospect.cdm==0">Não</span></td>
                    <td class="vert-align text-center"><span class="label label-success ng-scope" style="cursor: default;" ng-if="prospect.exito==1">Efetuada</span><span class="label label-danger" style="cursor: default;" ng-if="prospect.exito==0">Não Efetuada</span><span class="label label-warning" style="cursor: default;" ng-if="prospect.exito==2">Não Atendido</span></td>
                    <td class="vert-align text-center">{{prospect.motivo}}</td>
                    <td class="vert-align text-center">{{prospect.usuario}}</td>

                </tr>

              </tbody>
            </table>
            <div class="text-center">
              <uib-pagination total-items="PLC.pagination.size" ng-model="PLC.pagination.page" ng-change="PLC.pageChanged()" items-per-page="PLC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
            </div>
            <div class="text-center">
              Página <span class="badge">{{ PLC.pagination.page}}</span> de  <span class="badge">{{ PLC.pagination.pages}}</span> de <span class="badge">{{ PLC.pagination.size}}</span> registro(s)</span>
            </div>
          </div>

        <div ng-include="'app/comercial/prospect.form.html'"></div>
