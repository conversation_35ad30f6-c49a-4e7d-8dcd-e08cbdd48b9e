(function () {
    'use strict';

    angular
        .module('app')
        .controller('CampanhasController', CampanhasController);

    /** @ngInject */
    function CampanhasController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

        var vm = this;
        vm.campanhas = [];
        vm.logs = [];
        vm.campanhasLoading = false;
        vm.logsLoading = false;
		vm.pagination = {page:1};
		vm.paginationLogs = {page:1};

        vm.getCampanhas = getCampanhas;
        vm.getLogs = getLogs;

        activate();

        function activate() {
            getCampanhas();
            getLogs();
        }

        function getCampanhas() {
            vm.campanhasLoading = true;

            $http({
                url: API_CONFIG.url + '/comercial/campanhas/email',
                method: "POST",
                data: {
                    'page': vm.pagination.page
                }
            })
                .then(function (response) {
                    vm.campanhasLoading = false;

                    if (response && response.data && response.data.status == 'success') {
                        angular.copy(response.data.campanhas, vm.campanhas);
                        angular.copy(response.data.pagination, vm.pagination);
                    }
                    else {
                        console.error('Ocorreu um erro ao listar as campanhas de marketing.');
                    }
                },
                    function (response) {
                    });
        }

        function getLogs() {
            vm.logsLoading = true;

            $http({
                url: API_CONFIG.url + '/comercial/campanhas/logs',
                method: "POST",
                data: {
                    'page': vm.paginationLogs.page
                }
            })
                .then(function (response) {
                    vm.logsLoading = false;

                    if (response && response.data && response.data.status == 'success') {
                        angular.copy(response.data.logs, vm.logs);
                        angular.copy(response.data.pagination, vm.paginationLogs);
                    }
                    else {
                        console.error('Ocorreu um erro ao listar os registros de atividades das campanhas de marketing.');
                    }
                },
                    function (response) {
                    });
        }
    }

})();
