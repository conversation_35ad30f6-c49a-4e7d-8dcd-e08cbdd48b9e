<a href="/comercial/cobertura" type="button" class="btn btn-success btn-incluir text-center" authorize="['terceiro.write']"><span class="glyphicon glyphicon-plus"></span><br>Incluir</a>
        
<div class="form-group">
<form class="form-inline" role="form">
                <div class="form-group">
                   <select class="form-control" ng-model="PLC.exito" ng-change="PLC.pageChanged()">
                    <option value="">Todos</option>
                    <option value="1">Efetuada</option>
                    <option value="0">Não Efetuada</option>
                    <option value="2">Não Atendido</option>
                  </select>

                </div>
                <div class="form-group">
                  <select class="form-control" ng-model="PLC.tipo">
                    <option value="nome">Nome</option>
                    <option value="logradouro">Logradouro</option>
                    <option value="bairro">Bairro</option>
                    <option value="id">ID</option>
                  </select>
                </div>
                <div class="form-group">
                <input size="30" maxlength="30" class="form-control" type="text" ng-model="PLC.termos">
                <button class="btn btn-default" title="Pesquisar" ng-click="PLC.busca(PLC.termos)">Pesquisar</button>
                <button class="btn btn-default filter-col" ng-click="PLC.limpar()">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>

            </div>


<div class="table-responsive">
    <span class="counter pull-right"></span>

            <table class="table table-striped table-hover table-bordered">
              <thead>
                <tr>
                    <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('id')"> ID
                <span ng-show="PLC.sortBy == 'id' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'id' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('datacad')"> Data
                <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'datacad' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('nome')"> Nome
                <span ng-show="PLC.sortBy == 'nome' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'nome' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                    <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('telefone')"> Telefone
                <span ng-show="PLC.sortBy == 'telefone' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'telefone' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('email')"> E-mail
                <span ng-show="PLC.sortBy == 'email' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'email' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('logradouro')"> Logradouro
                <span ng-show="PLC.sortBy == 'logradouro' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'logradouro' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
                  <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('numero')"> Número
                <span ng-show="PLC.sortBy == 'numero' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'numero' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('complemento')"> Complemento
                <span ng-show="PLC.sortBy == 'complemento' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'complemento' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('bairro')"> Bairro
                <span ng-show="PLC.sortBy == 'bairro' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'bairro' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cidade')"> Cidade
                <span ng-show="PLC.sortBy == 'cidade' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cidade' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('cobertura')"> Cobertura
                <span ng-show="PLC.sortBy == 'cobertura' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'cobertura' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('plano')"> Plano
                <span ng-show="PLC.sortBy == 'plano' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'plano' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('exito')"> Venda
                <span ng-show="PLC.sortBy == 'exito' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'exito' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
          <th class="vert-align text-center"><a href="#" ng-click="PLC.sort('motivo')"> Motivo
                <span ng-show="PLC.sortBy == 'motivo' && PLC.sortOrder == 'dsc'" class="fa fa-caret-down"></span>
                <span ng-show="PLC.sortBy == 'motivo' && PLC.sortOrder == 'asc'" class="fa fa-caret-up"></span>
          </a></th>
               
                </tr>
              </thead>
              <tbody>

                <tr ng-repeat="prospect in PLC.prospects">
                    <td class="vert-align text-center">{{prospect.id}}</a></td>
                    <td class="vert-align text-center">{{prospect.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{prospect.nome}}</a></td>
                    <td class="vert-align text-center">{{prospect.telefone}}</td>
                    <td class="vert-align text-center">{{prospect.email}}</td>
                    <td class="vert-align text-center">{{prospect.logradouro}}</td>
                    <td class="vert-align text-center">{{prospect.numero}}</td>
                    <td class="vert-align text-center">{{prospect.complemento}}</td>
                    <td class="vert-align text-center">{{prospect.bairro}}</td>
                    <td class="vert-align text-center">{{prospect.cidade}}</td>
                    <td class="vert-align text-center">{{prospect.cobertura}}</td>
                    <td class="vert-align text-center">{{prospect.plano}}</td>
                    <td class="vert-align text-center"><span class="label label-success ng-scope" style="cursor: default;" ng-if="prospect.exito==1">Efetuada</span><span class="label label-danger" style="cursor: default;" ng-if="prospect.exito==0">Não Efetuada</span><span class="label label-warning" style="cursor: default;" ng-if="prospect.exito==2">Não Atendido</span></td>
                    <td class="vert-align text-center">{{prospect.motivo}}</td>
                </tr>
              </tbody>
            </table>
            
          </div>

          <div class="text-center">
            <uib-pagination total-items="PLC.pagination.size" ng-model="PLC.pagination.page" ng-change="PLC.pageChanged()" items-per-page="PLC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo" boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm"></uib-pagination>
          </div>
          <div class="text-center">
            Página <span class="badge">{{ PLC.pagination.page}}</span> de  <span class="badge">{{ PLC.pagination.pages}}</span> de <span class="badge">{{ PLC.pagination.size}}</span> registro(s)</span>
          </div>
