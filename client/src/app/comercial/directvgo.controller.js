(function () {
	'use strict';

	angular
		.module('app')
		.controller('DGOController', DGOController);

	/** @ngInject */
	function DGOController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window, $route) {

		var vm = this;
		vm.openInNewTab = openInNewTab;
		vm.getDivergencias = getDivergencias;
		vm.numeroDivergencias = 0;
		vm.loadingDivergencias = false;
		vm.loadingRegistroAtividades = false;
		vm.divergencias = {};
		vm.registroAtividades = [];
		vm.getRegistroAtividades = getRegistroAtividades;
		vm.pagination = {};
		vm.pageChanged = pageChanged;
		vm.data_inicial = new Date();
		vm.data_inicial.setDate(vm.data_inicial.getDate() - 30);
		vm.data_final = new Date();
		vm.busca = '';

		activate();

		function activate() {
			resetDivergencias();
			getDivergencias();
			atualizarNumeroDivergencias();
			getRegistroAtividades();

			if($route.current.params.page) {
				$('#registros-tab').click();
			}

			angular.element('#dgo_busca').on('keyup', function(e){	
				if (e.keyCode == 13)
					getRegistroAtividades();
			});
		}

		function resetDivergencias() {
			angular.copy({
				usuariosTvSemIntegracaoPlayhub: [],
				contratosComProdutosTipoIncorreto: [],
				contratosDgoSemUsuarioTv: [],
				usuariosTvSemPacoteDgo: [],
				usuariosAbranetSemUsuarioIxc: [],
				usuariosIxcSemUsuarioAbranet: [],
				contratosComPacotesDivergentes: []
			}, vm.divergencias);
		}

		function getDivergencias() {
			resetDivergencias();

			vm.loadingDivergencias = true;

			$http({
				url: API_CONFIG.url + '/comercial/directvgo/divergencias',
				method: "GET",
				ignoreLoadingBar: false
			}).then(function (response) {
				vm.loadingDivergencias = false;

				if (response.data.status == 'success') {
					angular.copy(response.data.divergencias, vm.divergencias);
					atualizarNumeroDivergencias();
				}
				else {
					alert('Houve um erro ao listar as divergências.');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function atualizarNumeroDivergencias() {
			var totalLength = 0;

			var obj = vm.divergencias;

			for (var property in obj) {
				if (Array.isArray(obj[property])) {
					totalLength += obj[property].length;
				}
			}

			vm.numeroDivergencias = totalLength;
		}

		function getRegistroAtividades() {
			vm.loadingRegistroAtividades = true;
			vm.registroAtividades = [];
			vm.pagination = {};

			var data = {
				'data_inicial': $filter('date')(vm.data_inicial, "yyyy-MM-dd"),
				'data_final': $filter('date')(vm.data_final, "yyyy-MM-dd"),
				'page': $routeParams.page,
				'busca': vm.busca
			}

			$http({
				url: API_CONFIG.url + '/comercial/directvgo/registro-atividades',
				method: "POST",
				data: data,
				ignoreLoadingBar: false
			}).then(function (response) {
				vm.loadingRegistroAtividades = false;
				angular.copy(response.data.pagination, vm.pagination);
				angular.copy(response.data.rows, vm.registroAtividades);
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function pageChanged() {
			var url = '/comercial/sky?page=' + vm.pagination.page;
			$location.url(url);

			$window.setTimeout(function() {
				$('#registros-tab').click();
			}, 100);
		}

		function openInNewTab(url) {
			var win = window.open(url, '_blank');
			win.focus();
		}

	}

})();
