<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

    <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
    <li><a href="/comercial/atendimento"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
    <li><i class="glyphicon glyphicon-bullhorn"></i> Campanhas de Marketing</a></li>
</ol>

<ul class="nav nav-tabs">
    <li role="presentation" class="active" data-target="#campanhas_list" data-toggle="tab">
        <a href="#">
            <i class="glyphicon glyphicon-bullhorn margin-right-5"></i>
            Campanhas
        </a>
    </li>
    <li role="presentation" data-target="#campanhas_logs" data-toggle="tab">
        <a href="#">
            <i class="glyphicon glyphicon-list margin-right-5"></i>
            Registro de atividade
        </a>
    </li>
</ul>

<div class="tab-content">
    <div id="campanhas_list" role="tabpanel" class="tab-pane active" style="margin-bottom: 10px;">
        <table class="table table-sm table-striped table-bordered table-centered full-width">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Descrição</th>
                    <th>Total contratos</th>
                    <th>Total e-mails</th>
                    <th>E-mails enviados</th>
                    <th>Total de cliques</th>
                    <th>Cliques WhatsApp</th>
                    <th>Cliques Portal</th>
                    <th>Contratos renovados</th>
                    <th>Último disparo</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="campanha in CC.campanhas">
                    <td>
                        {{ campanha.id }}
                    </td>
                    <td>
                        {{ campanha.descricao }}
                    </td>
                    <td>
                        {{ campanha.total_contratos }}
                    </td>
                    <td>
                        {{ campanha.total_emails }}
                    </td>
                    <td>
                        {{ campanha.emails_enviados }}
                    </td>
                    <td>
                        {{ campanha.total_cliques }}
                    </td>
                    <td>
                        {{ campanha.cliques_whatsapp }}
                    </td>
                    <td>
                        {{ campanha.cliques_portal }}
                    </td>
                    <td>
                        {{ campanha.contratos_renovados }}
                    </td>
                    <td>
                        {{ campanha.ultimo_disparo | amDateFormat:'DD/MM/YYYY - HH:mm:ss' }}
                    </td>
                    <td>
                        <span class="label" ng-class="[
                        {'label-warning': campanha.status == 'DISPARANDO'},
                        {'label-primary': campanha.status == 'OCIOSO'},
                        {'label-success': campanha.status == 'FINALIZADA'}
                        ]">{{ campanha.status }}</span>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="text-center">
            <uib-pagination total-items="CC.pagination.size" ng-model="CC.pagination.page" ng-change="CC.getCampanhas()"
                items-per-page="CC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
                boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
            </uib-pagination>
        </div>
        <div class="text-center">
            Página <span class="badge">{{ CC.pagination.page}}</span> de <span
                class="badge">{{ CC.pagination.pages}}</span>
            de <span class="badge">{{ CC.pagination.size}}</span> registro(s)</span>
        </div>
    </div>

    <div id="campanhas_logs" role="tabpanel" class="tab-pane" style="margin-bottom: 10px;">
        <table class="table table-sm table-striped table-bordered table-centered full-width">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Horário</th>
                    <th>ID campanha</th>
                    <th>Cliente</th>
                    <th>Destinatário</th>
                    <th>ID cliente</th>
                    <th>ID contrato</th>
                    <th>Mensagem</th>
                    <th>Erro</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="log in CC.logs">
                    <td>
                        {{ log.id }}
                    </td>
                    <td>
                        {{ log.horario | amDateFormat:'DD/MM/YYYY - HH:mm:ss' }}
                    </td>
                    <td>
                        {{ log.id_campanha }}
                    </td>
                    <td>
                        {{ log.cliente }}
                    </td>
                    <td>
                        {{ log.destinatario }}
                    </td>
                    <td>
                        {{ log.id_cliente }}
                    </td>
                    <td>
                        {{ log.id_contrato }}
                    </td>
                    <td>
                        {{ log.mensagem }}
                    </td>
                    <td>
                        <span class="label" ng-class="log.erro ? 'label-danger' : 'label-success'">{{ log.erro ? 'SIM' : 'NÃO' }}</span>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="text-center">
            <uib-pagination total-items="CC.paginationLogs.size" ng-model="CC.paginationLogs.page" ng-change="CC.getLogs()"
                items-per-page="CC.paginationLogs.count" max-size="9" previous-text="Anterior" next-text="Próximo"
                boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
            </uib-pagination>
        </div>
        <div class="text-center">
            Página <span class="badge">{{ CC.paginationLogs.page}}</span> de <span
                class="badge">{{ CC.paginationLogs.pages}}</span>
            de <span class="badge">{{ CC.paginationLogs.size}}</span> registro(s)</span>
        </div>
    </div>
</div>