(function () {
    'use strict';

    angular
        .module('app')
        .controller('ComercialLogradourosController', ComercialLogradourosController);

    /** @ngInject */
    function ComercialLogradourosController($http, API_CONFIG, logradouros, LogradourosService, toaster, $rootScope) {

        var vm = this;

        vm.salvarLogradouro = salvarLogradouro;

        vm.logradouro = [];

        activate();

        function activate() {
           vm.logradouros = logradouros;
        }

        function salvarLogradouro(logradouro_interfocus, logradouro_google, cidade, index){
            var logradouro = {
                logradouro_interfocus : logradouro_interfocus,
                logradouro_google : logradouro_google,
                cidade: cidade,
                username: $rootScope.operador.username
            }
            
             vm.logradouro[index] = {visible : false};
            
            LogradourosService.insertLogradouro(logradouro, function (response) {

                console.log(response);
                
                if (response.status === 'OK') {
                    toaster.pop('success', "Logradouro adicionado", "Logradouro adicionado com sucesso!");
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }

            });

        }
  };
})();
