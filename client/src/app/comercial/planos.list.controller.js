(function () {
	'use strict';

	angular
		.module('app')
		.controller('PlanosListController', PlanosListController);

	/** @ngInject */
	function PlanosListController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

		$scope.onlyNumbers = /^\d+$/;

		var vm = this;

		vm.cidade = 'pocos';
		vm.cidade_mesh = 'pocos';
		vm.tecnologia = 'todas';
		vm.alterar_todas_cidades = false;
		vm.planos = [];
		vm.planos_mesh = [];
		vm.selecionar = selecionar;
		vm.selecionado = {};
    vm.plano_directvgo = {};
    vm.plano_globoplay = {};
		vm.getPlanosInternet = getPlanosInternet;
		vm.salvarValorDgo = salvarValorDgo;
		vm.salvarValorGloboplay = salvarValorGloboplay;
		vm.getPlanosMesh = getPlanosMesh;
		vm.checkAdicionar = checkAdicionar;
		vm.adicionar = adicionar;
		vm.selecionarPlano = selecionarPlano;
		vm.checkSalvar = checkSalvar;
		vm.checkSalvarMesh = checkSalvarMesh;
		vm.salvarMesh = salvarMesh;
		vm.salvar = salvar;
		vm.excluir = excluir;
		vm.planChanged = planChanged;
		vm.initFrmnovoplano = initFrmnovoplano;
		vm.novoplano_default = {
			'cidade': 'pocos',
			'tecnologia': 'fibra',
			'nome': '',
			'caption': '',
			'velocidade': '',
			'tv': '1',
			'roteador': '1',
			'roteador_opcional': '1',
			'decoder': '1',
			'decoder_opcional': '1',
			'valor_roteador': '10.00',
			'valor_decoder': '20.00',
			'valor': '0.00',
			'key': '',
			'ativo': '0',
			'disponivel_contratacao': '1',
			'disponivel_migracao': '1',
			'disponivel_backbone': '0',
      'valor_backbone': '0.00'
		};
		vm.novoplano = {};
		vm.cidadesAtendidas = [];

		vm.planosServicos = [];

		vm.salvarServicos = salvarServicos;

		vm.planosServicosOriginal = [];
		vm.servicosDiff = [];
		vm.servicosDiffIds = [];
		vm.checkServicosDiff = checkServicosDiff

		vm.novoServico = {
			servico: '',
			descricao: '',
			ordem: null,
			ativo: true,
			todosPlanosFibra: false,
			todosPlanosCdmht: false,
			todosPlanosRadio: false,
		}
		vm.adicionarServico = adicionarServico;

		vm.removerServico = removerServico

		activate();

		function activate() {
			resetNovoPlano();
			getPlanosInternet();
			getPlanosMesh();
			getValorDirectvGo();
			getValorGloboplay();
			getCidadesAtendidas();
			getPlanosServicos();
		}

		function resetNovoServico() {
			vm.novoServico = {
				servico: '',
				descricao: '',
				ordem: null,
				ativo: true,
				todosPlanosFibra: false,
				todosPlanosCdmht: false,
				todosPlanosRadio: false,
			}
		}

		function removerServico(id_servico) {
			$http({
				url: API_CONFIG.url + '/comercial/planos/remover-servico',
				method: "POST",
				data: { id_servico: id_servico },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O serviço foi removido com sucesso!');
					getPlanosServicos();
				}
				else {
					alert('Erro ao remover o serviço');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function adicionarServico() {
			var error = false;
			vm.planosServicos.forEach(function(servico) {
				if (servico.servico == vm.novoServico.servico) {
					alert('Já existe um serviço com o nome "' + vm.novoServico.servico + '".');
					error = true;
					return;
				}
			});

			if (error)
				return false;

			$http({
				url: API_CONFIG.url + '/comercial/planos/adicionar-servico',
				method: "POST",
				data: { servico: vm.novoServico },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O serviço foi adicionado com sucesso!');
					getPlanosServicos();
					resetNovoServico();
					angular.element('#frmnovoservico').modal('hide');
				}
				else {
					alert('Erro ao adicionar o serviço');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function salvarServicos() {
			var error = false;
			vm.servicosDiff.forEach(function(servico) {
				if (servico.ordem == '' || isNaN(servico.ordem) || servico.ordem < 0 || servico.ordem % 1 !== 0) {
					alert('Ordem inválida para o serviço: ' + servico.servico);
					error = true;
					return false;
				}
			});

			if (error)
				return false;

			$http({
				url: API_CONFIG.url + '/comercial/planos/servicos',
				method: "POST",
				data: {
					servicos: vm.servicosDiff
				},
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('A lista foi salva com sucesso!');
					getPlanosServicos();
				}
				else {
					alert('Erro ao salvar a lista!');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function getPlanosServicos() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/servicos',
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					vm.planosServicosOriginal = [];
					vm.planosServicos = [];
					vm.servicosDiff = [];
					vm.servicosDiffIds = [];
					angular.copy(response.data.servicos, vm.planosServicosOriginal);
					angular.copy(response.data.servicos, vm.planosServicos);
				}
				else {
					console.error('Erro ao buscar os serviços');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function checkServicosDiff() {
			vm.servicosDiff = [];
			vm.servicosDiffIds = [];
			for (var i = 0; i < vm.planosServicos.length; i++) {
				var servico = vm.planosServicos[i];
				var original = vm.planosServicosOriginal.find(function (s) {
					return s.id === servico.id;
				});
				if (original && (servico.servico != original.servico ||
								servico.descricao != original.descricao ||
								servico.ordem != original.ordem ||
								servico.ativo != original.ativo)) {
					vm.servicosDiffIds.push(servico.id);
					vm.servicosDiff.push(servico);
				}
			}
		}

		function getCidadesAtendidas() {
			$http({
				url: API_CONFIG.url + '/telemidia/cidades-atendidas',
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					angular.copy(response.data.cidades, vm.cidadesAtendidas);
				}
				else {
					console.error('Erro ao buscar cidades atendidas');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function initFrmnovoplano() {
			if (vm.cidade !== 'todas')
				vm.novoplano.cidade = vm.cidade;
			if (vm.tecnologia !== 'todas')
				vm.novoplano.tecnologia = vm.tecnologia;

			$window.setTimeout(function () {
				$('#novoplano_numero').focus();
			}, 100);
		}

		function resetNovoPlano() {
			angular.copy(vm.novoplano_default, vm.novoplano);
		}

		function checkAdicionar() {
			if (confirm('Deseja realmente adicionar este plano?'))
				adicionar();
		}

		function adicionar() {
			if (vm.novoplano.nome.trim() == '' ||
				vm.novoplano.velocidade.replace(/\D/g, '').trim() == '' ||
				vm.novoplano.valor.toString().replace(/\D/g, '').replace('0', '').trim() == '') {
				alert('Erro! Confira os campos obrigatorios: nome, velocidade e valor.');
				return false;
			}

			$http({
				url: API_CONFIG.url + '/comercial/planos/insert',
				method: "POST",
				data: { plano: vm.novoplano },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O plano ' + vm.novoplano.nome + ' foi adicionado.');
				}
				else {
					alert('Houve um erro ao adicionar o plano.');
				}
				getPlanosInternet();
				resetNovoPlano();
				$('#frmnovoplano_fechar').click();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function checkSalvarMesh() {
			if (confirm('Deseja realmente salvar o valor do plano?'))
				salvarMesh();
		}

		function salvarValorDgo() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/dgo',
				method: 'PUT',
				data: {
					valor: vm.plano_directvgo.valor
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O valor foi salvo com sucesso.');
				}
				else {
					alert('Ocorreu um erro ao salvar o valor do plano.');
				}
			});
		}

		function salvarValorGloboplay() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/globoplay',
				method: 'PUT',
				data: {
					valor: vm.plano_globoplay.valor
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O valor foi salvo com sucesso.');
				}
				else {
					alert('Ocorreu um erro ao salvar o valor do plano.');
				}
			});
		}

		function getValorDirectvGo() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/dgo',
				method: 'GET'
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					vm.plano_directvgo.valor = response.data.valor;
				}
				else {
					alert('Ocorreu um erro ao consultar o valor atual do plano SKY+.');
				}
			});
		}

		function getValorGloboplay() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/globoplay',
				method: 'GET'
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					vm.plano_globoplay.valor = response.data.valor;
				}
				else {
					alert('Ocorreu um erro ao consultar o valor atual do plano da Globoplay.');
				}
			});
		}

		function salvarMesh() {
			var plano_id = vm.selecionado.id;
			var plano_valor = vm.selecionado.valor;
			var plano_cidade = vm.selecionado.cidade;
			var plano_nome = vm.selecionado.nome;
			var alterar_todas_cidades = vm.alterar_todas_cidades;

			$http({
				url: API_CONFIG.url + '/comercial/planos/mesh',
				method: "PUT",
				data: {
					plano_id: plano_id,
					plano_valor: plano_valor,
					plano_cidade: plano_cidade,
					plano_nome: plano_nome,
					alterar_todas_cidades: alterar_todas_cidades
				},
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('As alterações no plano ' + vm.selecionado.nome + ' foram salvas.');
				}
				else {
					alert('Houve um erro ao salvar as alterações.');
				}
				getPlanosMesh();
				$('#frmplano_mesh_fechar').click();
			});
		}

		function checkSalvar() {
			if (confirm('Deseja realmente salvar as alterações?'))
				salvar();
		}

		function salvar() {
			if (vm.selecionado.nome.trim() == '' ||
				vm.selecionado.velocidade.toString().replace(/\D/g, '').trim() == '' ||
				vm.selecionado.valor.toString().replace(/\D/g, '').replace('0', '').trim() == '') {
				alert('Erro! Verifique os campos obrigatorios: nome, velocidade e valor.');
				return false;
			}

			$http({
				url: API_CONFIG.url + '/comercial/planos',
				method: "PUT",
				data: { plano: vm.selecionado },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('As alterações no plano ' + vm.selecionado.nome + ' foram salvas.');
				}
				else {
					alert('Houve um erro ao salvar as alterações.');
				}
				getPlanosInternet();
				$('#frmplano_fechar').click();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function excluir(plano_id) {
			$http({
				url: API_CONFIG.url + '/comercial/planos/' + plano_id,
				method: "DELETE",
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O plano foi excluído.');
				}
				else {
					alert('Houve um erro ao excluir o plano.');
				}
				getPlanosInternet();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function planChanged(currentForm) {
			if (currentForm == 'frmplano') {
				var prefix = 'plano';
				var changed = vm.selecionado;
			}
			else if (currentForm == 'frmnovoplano') {
				var prefix = 'novoplano';
				var changed = vm.novoplano;
			}

			const select_tv = angular.element('#' + prefix + '_tv');
			const select_decoder = angular.element('#' + prefix + '_decoder');
			const select_decoder_opcional = angular.element('#' + prefix + '_decoder_opcional');

			changed.tv = select_tv.val();
			changed.decoder = select_decoder.val();
			changed.decoder_opcional = select_decoder_opcional.val();

			if (changed.tv == 0) {
				changed.decoder = '0';
				changed.decoder_opcional = '1';
				changed.valor_decoder = '0,00';
			}
			else if (changed.decoder == 0) {
				changed.decoder_opcional = '1';
				changed.valor_decoder = '0,00';
			}
			else if (changed.decoder_opcional == 0) {
				changed.valor_decoder = '0,00';
			}

			const select_roteador = angular.element('#' + prefix + '_roteador');
			const select_roteador_opcional = angular.element('#' + prefix + '_roteador_opcional');

			changed.roteador = select_roteador.val();
			changed.roteador_opcional = select_roteador_opcional.val();

			if (changed.roteador == 0) {
				changed.roteador_opcional = '1';
				changed.valor_roteador = '0,00';
			}
			else if (changed.roteador_opcional == 0) {
				changed.valor_roteador = '0,00';
			}

      if (changed.disponivel_backbone == 0) {
        changed.valor_backbone = '0,00';
      }
		}

		function selecionarPlano(plano) {
			angular.copy(plano, vm.selecionado);

			var booleanFields = [
				'ativo',
				'roteador',
				'roteador_opcional',
				'tv',
				'decoder',
				'decoder_opcional',
				'disponivel_contratacao',
				'disponivel_migracao',
        'disponivel_backbone'
			];

			booleanFields.forEach(function (field) {
				vm.selecionado[field] = vm.selecionado[field].toString();
			});

			vm.alterar_todas_cidades = false;

			$http({
				url: API_CONFIG.url + '/comercial/planos/plano-servicos/' + plano.id,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					vm.selecionado.lista_servicos = response.data.listaServicos
					vm.selecionado.lista_servicos.sort(function(a, b) {
						return a.ordem - b.ordem;
					});
				}
				else {
					alert('Ocorreu um erro ao listar os serviços deste plano.')
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function getPlanosInternet() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/internet/' + vm.cidade + '/' + vm.tecnologia,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				angular.copy(response.data, vm.planos);
				vm.planos.sort(function(a, b) {
					if (a.ativo != b.ativo) {
						return b.ativo - a.ativo;
					} else if (parseInt(a.velocidade.replace(/\D/g, '')) != parseInt(b.velocidade.replace(/\D/g, ''))) {
						return parseInt(b.velocidade.replace(/\D/g, '')) - parseInt(a.velocidade.replace(/\D/g, ''));
					} else {
						return parseFloat(b.valor.replace(',', '.')) - parseFloat(a.valor.replace(',', '.'));
					}
				});
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function getPlanosMesh() {
			$http({
				url: API_CONFIG.url + '/comercial/planos/mesh/' + vm.cidade_mesh,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				angular.copy(response.data, vm.planos_mesh);
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function selecionar(plano) {

			vm.selecionado = {};

			vm.docs = {
				cpf: 0,
				cnpj: 0,
				identidade: 0,
				contrato_social: 0,
				endereco: 0
			};


			$http({
				url: API_CONFIG.url + '/comercial/plano/' + plano.id,
				method: "GET"
			})
				.then(function (response) {
					console.log('response;', response);
					angular.copy(response.data, vm.selecionado);
					vm.selecionado.ref = plano;
					vm.selecionado.historicos = response.data.historicos;
					vm.selecionado.historicos.push({
						'data': response.data.plano_termino,
						'status': 'Plano realizado pelo cliente',
						'atendente': 'sistema'
					});
					vm.selecionado.documentos = response.data.documentos;
					vm.selecionado.reenvios = response.data.reenvios;
					vm.selecionado.contrato_url = '';
					if (response.data.contrato) {
						vm.selecionado.contrato = response.data.contrato;
						vm.selecionado.contrato_url = 'https://www.pocos-net.com.br/scripts/contratar/utils/get_documents.php/?url=' + vm.selecionado.contrato.doc_final_url + '&t=' + plano.token;
					}
				},
					function (response) {
					});

		};

	}

})();
