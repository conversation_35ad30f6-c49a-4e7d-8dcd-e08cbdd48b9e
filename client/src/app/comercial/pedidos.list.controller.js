(function () {
    'use strict';

    angular
        .module('app')
        .controller('PedidosListController', PedidosListController);

    /** @ngInject */
    function PedidosListController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window, AlertService) {

        var vm = this;

        vm.whatsappText = 'Ol%C3%A1!%0ARecebemos%20seu%20pedido%20de%20contato.%0AFicarei%20feliz%20em%20ajud%C3%A1-lo%20a%20escolher%20o%20melhor%20plano%20para%20voc%C3%AA!';
        vm.showVisaoGeral = showVisaoGeral;
        vm.showAgendamentos = showAgendamentos;
        vm.showInstalacoes = showInstalacoes;
        vm.showingTab = 'visao_geral';
        vm.agendamentos_pcs = null;
        vm.agendamentos_and = null;
        vm.instalacoes = null;
        vm.operador = $rootScope.operador.username;
        vm.limit = 20;
        vm.tipo = 'nome';
        vm.termos = '';
        vm.pedidos = [];
        vm.pagination = {};
        vm.changeStatus = changeStatus;
        vm.seleciona = seleciona;
        vm.limpar = limpar;
        vm.pageChanged = pageChanged;
        vm.busca = busca;
        vm.getData = getData;
        vm.selecionado = {};
        vm.filtro = '';
        vm.open = open;
        vm.open1 = open1;
        vm.opened1 = false;
        vm.open2 = open2;
        vm.opened2 = false;
        vm.open3 = open3;
        vm.opened3 = false;
        vm.open4 = open4;
        vm.opened4 = false;
        vm.valida = valida;
        vm.download = download;
        vm.copyToClipboard = copyToClipboard;
        vm.atendentes = [];
        vm.atendente = 'Todos';
        vm.vendedores = [];
        vm.vendedor = 'Todos';
        vm.activeTab = '';
        vm.setActiveTab = setActiveTab;
        vm.reenvio = reenvio;
        vm.pedidosAssistidos = {
            pendentes: [],
            historico: []
        };
        vm.getPedidosAssistidosAtuais = getPedidosAssistidosAtuais;
        vm.getPedidosAssistidosHistorico = getPedidosAssistidosHistorico;
        vm.docs = {
            cpf: 0,
            cnpj: 0,
            identidade: 0,
            selfie: 0,
            contrato_social: 0,
            endereco: 0
        };
        vm.setAprovacaoDowngrade = setAprovacaoDowngrade;

        vm.conclusao = conclusao;
        vm.reservarPedido = reservarPedido;
        vm.reservarPedidoAssistido = reservarPedidoAssistido;
        vm.finalizarPedidoAssistido = finalizarPedidoAssistido;

        vm.getDocumentCaption = getDocumentCaption;

        vm.conclusoes = [
            'ABERTO',
            'INVALIDO_MSG',
            'INVALIDO',
            'SUCESSO',
            'ERRO_CPF',
            'DOWNGRADE_NEGADO'
        ];
        vm.conclusoes_selecionadas = ['ABERTO'];

        vm.filtroAssistidos = {
            dtinicio: '',
            dtfim: '',
            nome: '',
            atendente: 'Todos',
            status: ''
        };

        window.setInterval(function () { console.log(vm.conclusoes_selecionadas);}, 5000)

        vm.desvincularAtendente = desvincularAtendente;

        var inicio = new Date();
        inicio.setDate(inicio.getDate() - 30);
        var fim = new Date();

        vm.dtinicio = inicio;
        vm.dtfim = fim;

        vm.filtroAssistidos.dtinicio = inicio;
        vm.filtroAssistidos.dtfim = fim;

        var horainicio = new Date();
        horainicio.setHours(0, 0, 0);
        vm.horainicio = horainicio;

        var horafim = new Date();
        horafim.setHours(23, 59, 59, 999);
        vm.horafim = horafim;

        $scope.$watch('horainicio', function (value) {
            if (!value) return;
            vm.horainicio = new Date(value);
        }, true);

        $scope.$watch('horafim', function (value) {
            if (!value) return;
            vm.horafim = new Date(value);
        }, true);

        function getDocumentCaption(doc_type, doc_side) {
            var caption = '';

            switch(doc_type) {
                case 'cpf':
                    caption = 'CPF';
                    break;
                case 'identidade':
                    caption = 'Identidade';
                    break;
                case 'endereco':
                    caption = 'Comprovante de endereço';
                    break;
                case 'selfie':
                    caption = 'Selfie com dcumento';
                    break;
                case 'cnpj':
                    caption = 'CNPJ';
                    break;
                case 'contrato_social':
                    caption = 'Contrato social';
                    break;
                default:
                    caption = '[Documento desconhecido]'
            }

            if (doc_side)
                caption += ' (' + doc_side + ')';

            return caption;
        }

        function download(arquivo) {
            DownloadFileService.startDownload(arquivo);
        }


        function setActiveTab(tab) {
            vm.activeTab = tab;
        }

        function valida() {
            if (vm.dtinicio > vm.dtfim) {
                vm.dtfim = vm.dtinicio;
            }

            if (vm.filtroAssistidos.dtinicio > vm.filtroAssistidos.dtfim) {
                vm.filtroAssistidos.dtfim = vm.filtroAssistidos.dtinicio;
            }
        }

        function open(pedido) {
            pedido.opened = true;
        };

        function open1() {
            vm.opened1 = true;
        };

        function open2() {
            vm.opened2 = true;
        };

        function open3() {
            vm.opened3 = true;
        };

        function open4() {
            vm.opened4 = true;
        };

        function setAprovacaoDowngrade(id_pedido, aprovacao) {
            if (aprovacao != 'APROVADO' && aprovacao != 'NÃO APROVADO')
                return false;

            $http({
                url: API_CONFIG.url + '/comercial/pedidos/aprovacao-downgrade',
                method: "POST",
                data: {
                    id_pedido: id_pedido,
                    aprovacao: aprovacao,
                }
            })
                .then(function (response) {
                    if (response && response.data && response.data.status && response.data.status == 'success') {
                        alert('O status da aprovação foi alterado com sucesso.');
                        vm.selecionado.aprovacao_downgrade = aprovacao;
                        getData();
                    }
                    else {
                        alert('Ocorreu um erro ao alterar o status de aprovação de downgrade.');
                    }
                },
                    function (response) {
                    });
        }

        function getAtendentes() {
            $http({
                url: API_CONFIG.url + '/comercial/atendentes',
                method: "GET"
            })
                .then(function (response) {
                    vm.atendentes = response.data;
                    vm.atendentes.unshift('Todos');
                },
                    function (response) {
                    });
        }

        function getVendedores() {
            $http({
                url: API_CONFIG.url + '/comercial/vendedores',
                method: "GET"
            })
                .then(function (response) {
                    vm.vendedores = response.data;
                },
                    function (response) {
                    });
        }

        activate();

        function activate() {
            getPedidosAssistidosAtuais();
            getPedidosAssistidosHistorico();

            if ($routeParams.reservar) {
                reservarPedidoAssistido($routeParams.reservar);
            }

            else if ($routeParams.finalizar) {
                finalizarPedidoAssistido($routeParams.finalizar);
            }

            if ($routeParams.showingTab !== undefined) {
                vm.showingTab = $routeParams.showingTab;
            }
            else {
                vm.showingTab = 'visao_geral';
            }

            getAtendentes();
            getVendedores();
            if ($routeParams.cidade !== undefined) {
                vm.cidade = $routeParams.cidade;
            } else {
                vm.cidade = '';
            }

            if ($routeParams.status !== undefined) {
                vm.status = $routeParams.status;
            } else {
                vm.status = '';
            }

            if ($routeParams.finalizados !== undefined) {
                vm.finalizados = $routeParams.finalizados === 'true';
            }
            else {
                vm.finalizados = '';
            }

            if ($routeParams.atendente !== undefined) {
                vm.atendente = $routeParams.atendente;
            } else {
                vm.atendente = 'Todos';
            }

            if ($routeParams.vendedor !== undefined) {
                vm.vendedor = $routeParams.vendedor;
            } else {
                vm.vendedor = 'Todos';
            }

            if ($routeParams.dtinicio !== undefined) {
                vm.dtinicio = new Date($routeParams.dtinicio + ' 00:00:00');
            }

            if ($routeParams.dtfim !== undefined) {
                vm.dtfim = new Date($routeParams.dtfim + ' 23:59:59');
            }

            if ($routeParams.sortby !== undefined) {
                vm.sortBy = $routeParams.sortby;
            } else {
                vm.sortBy = 'id';
            }

            if ($routeParams.sortorder !== undefined) {
                vm.sortOrder = $routeParams.sortorder;
            } else {
                vm.sortOrder = 'dsc';
            }

            if ($routeParams.nome !== undefined && $routeParams.nome !== '') {
                vm.tipo = 'nome';
                vm.termos = $routeParams.nome;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.logradouro !== undefined) {
                vm.tipo = 'logradouro';
                vm.termos = $routeParams.logradouro;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.cpf !== undefined) {
                vm.tipo = 'cpf';
                vm.termos = $routeParams.cpf;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.rg !== undefined) {
                vm.tipo = 'rg';
                vm.termos = $routeParams.rg;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.celular !== undefined) {
                vm.tipo = 'celular';
                vm.termos = $routeParams.celular;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.email !== undefined) {
                vm.tipo = 'email';
                vm.termos = $routeParams.email;
                vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if ($routeParams.id !== undefined) {
                vm.tipo = 'id';
                vm.termos = $routeParams.id;
                vm.filtro = '&' + vm.tipo + '=' + vm.termos;
            }

            if ($routeParams.conclusoes !== undefined && $routeParams.conclusoes !== '') {
                vm.conclusoes_selecionadas = $routeParams.conclusoes.split(',');
            }
            else if ($routeParams.conclusoes === '') {
                vm.conclusoes_selecionadas = [];
            }

            getData();

        }

        function finalizarPedidoAssistido(id_pedido) {
            $location.search('finalizar', null);

            AlertService.loading('Finalizando o pedido...');

            $http({
                url: API_CONFIG.url + '/comercial/pedidos-assistidos/finalizar',
                method: "POST",
                data: {
                    id_pedido: id_pedido,
                    atendente: $rootScope.operador.username
                },
                ignoreLoadingBar: true
            }).then(function (response) {
                response = response.data;

                if (response.status && response.status == 'success') {
                    getPedidosAssistidosAtuais();getPedidosAssistidosHistorico();
                    AlertService.loaded('success', 'O pedido assistido foi finalizado.');
                }
                else {
                    AlertService.loaded('error', response.message);
                }
            })
                .catch(function (err) {
                    console.log(err);

                });
        }

        function reservarPedidoAssistido(id_pedido) {
            $location.search('reservar', null);

            AlertService.loading('Reservando o pedido...');

            $http({
                url: API_CONFIG.url + '/comercial/pedidos-assistidos/reservar',
                method: "POST",
                data: {
                    id_pedido: id_pedido,
                    atendente: $rootScope.operador.username
                },
                ignoreLoadingBar: true
            }).then(function (response) {
                response = response.data;

                if (response.status && response.status == 'success') {
                    getPedidosAssistidosAtuais();
                    getPedidosAssistidosHistorico();
                    AlertService.loaded('success', 'O pedido assistido foi reservado.');
                }
                else {
                    AlertService.loaded('error', response.message);
                }
            })
                .catch(function (err) {
                    console.log(err);

                });
        }

        function getData(downloadCsv) {
            downloadCsv = downloadCsv || false

            var data = {
                'cidade': vm.cidade,
                'status': vm.status,
                'atendente': vm.atendente,
                'vendedor': vm.vendedor,
                'tipo': vm.tipo,
                'termos': vm.termos,
                'dtinicio': $filter('date')(vm.dtinicio, "yyyy-MM-dd"),
                'dtfim': $filter('date')(vm.dtfim, "yyyy-MM-dd"),
                'horainicio': $filter('date')(vm.horainicio, "HH:mm"),
                'horafim': $filter('date')(vm.horafim, "HH:mm"),
                'page': $routeParams.page,
                'conclusoes': vm.conclusoes_selecionadas,
                downloadCsv: downloadCsv
            }

            $http({
                url: API_CONFIG.url + '/comercial/pedidos',
                method: "POST",
                data: data,
                ignoreLoadingBar: true
            }).then(function (response) {

                if (downloadCsv) {
					var hoje = new Date();

					var filename = 'relatorio_pedidos' + '_' + vm.dtinicio.toLocaleDateString('pt-BR') + '_a_' + vm.dtfim.toLocaleDateString('pt-BR') + '.csv';

					var headers = response.headers();
					var contentType = headers['content-type'];

					var blob = new Blob([response.data], {
						type: contentType
					});
					if (navigator.msSaveBlob)
						navigator.msSaveBlob(blob, filename);
					else {
						var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
						if (saveBlob === undefined) {
							var linkElement = document.createElement('a');
							try {
								var blob = new Blob([response.data], { type: contentType });
								var url = window.URL.createObjectURL(blob);
								linkElement.setAttribute('href', url);
								linkElement.setAttribute("download", filename);

								var clickEvent = new MouseEvent("click", {
									"view": window,
									"bubbles": true,
									"cancelable": false
								});
								linkElement.dispatchEvent(clickEvent);
							} catch (ex) {
								console.log(ex);
							}
						}

					}

					return;
				}

                angular.copy(response.data.rows, vm.pedidos);
                angular.copy(response.data.pagination, vm.pagination);

                vm.agendamentos_pcs = response.data.agendamentos_pcs;
                vm.agendamentos_and = response.data.agendamentos_and;
                vm.instalacoes = response.data.instalacoes;
            })
                .catch(function (err) {
                    console.log(err);

                });
        }

        function changeStatus(pedido, status) {
            // Atualizando a propriedade "visita_necessaria" apenas pro front-end, a informação não precisa ser enviada na requisição
            if (status == 4) {
                if (pedido.migracao == 0 || (pedido.migracao == 1 && pedido.agendamento == 'os_aberta'))
                    pedido.visita_necessaria = 1;
                else
                    pedido.visita_necessaria = 0;
            }

            $http({
                url: API_CONFIG.url + '/comercial/pedido/' + pedido.id,
                method: "PUT",
                data: {
                    "status": status,
                    "atendente": $rootScope.operador.username,
                    "agendamento": pedido.agendamento
                },
                ignoreLoadingBar: true
            })
                .then(function (response) {
                    if (response.data.status == "OK") {
                        if (status != 0) {
                            pedido.status = status;
                            toaster.pop('success', "Status", "Status do pedido atualizado!");
                        } else {
                            pedido.atendente = response.data.dados.atendente;
                            if (response.data.dados.alerta !== undefined && response.data.dados.alerta !== '') {
                                alert(response.data.dados.alerta);
                            }
                            else {
                                toaster.pop('success', "Atendente", "Atendente do pedido atualizado!");
                            }
                        }

                        if (status == 4) {
                            pedido.instalacao_dia_confirmado = data.agendamento.dia;
                            pedido.instalacao_periodo_confirmado = data.agendamento.periodo;
                            toaster.pop('success', "Status", "Agendamento confirmado!");
                        }
                    }
                },
                    function (response) {

                    });

        }

        function conclusao(pedido, status, status_msg) {

            var resposta = false;
            if (status == 'ERRO_CPF') {
                resposta = $window.confirm('Tem certeza que deseja finalizar o pedido por motivo de CPF com pendência?');
            }

            if (status == 'INVALIDO' || status == 'INVALIDO_MSG') {
                resposta = $window.prompt('Digite uma observação para invalidar o pedido (Opcional):', " ");
                if (resposta !== undefined) {
                    status_msg = resposta;
                } else {
                    resposta = false;
                }
            }


            if (resposta) {
                var data = {
                    "conclusao": status,
                    "conclusao_msg": status_msg,
                    "atendente": $rootScope.operador.username
                }

                $http({
                    url: API_CONFIG.url + '/comercial/pedido/' + pedido.id,
                    method: "PUT",
                    data: data,
                    ignoreLoadingBar: true
                })
                    .then(function (response) {
                        if (response.data.status == "OK") {
                            toaster.pop('success', "Pedido finalizado", "Pedido finalizado com o status: " + status);
                            pedido.conclusao = status;
                            pedido.conclusao_msg = status_msg;
                            pedido.ref.conclusao = status;
                            pedido.ref.conclusao_msg = status_msg;

                        }
                    });
            }

        }

        function showVisaoGeral() {
            vm.showingTab = 'visao_geral';

            var inicio = new Date();
            inicio.setDate(inicio.getDate() - 30);
            var fim = new Date();

            vm.dtinicio = inicio;
            vm.dtfim = fim;

            vm.atendente = 'Todos';
            vm.status = '';
            vm.cidade = '';
            vm.finalizados = '';
            vm.tipo = 'nome';
            vm.termos = '';
            vm.busca();
        }

        function showInstalacoes() {
            var inicio = new Date();
            inicio.setDate(inicio.getDate() - 365);
            var fim = new Date();

            vm.dtinicio = inicio;
            vm.dtfim = fim;

            vm.showingTab = 'instalacoes';
            vm.status = '4';
            // if (vm.cidade == 'grupo_pcs' || vm.cidade == 'grupo_and')
            //     vm.cidade = '';
            vm.busca();
        }

        function showAgendamentos(group) {
            vm.showingTab = 'agendamentos';
            var fim = new Date();

            vm.dtinicio = '2019-01-01';
            vm.dtfim = fim;

            vm.atendente = 'Todos';
            vm.status = '3';
            vm.cidade = group;
            vm.finalizados = '';
            vm.tipo = 'nome';
            vm.conclusoes_selecionadas = ['ABERTO'];
            vm.termos = '';
            vm.busca();
        }

        function reenvio(pedido, docs) {

            var data = {
                "reenvio": 1,
                "cpf": Number(vm.docs.cpf),
                "cnpj": Number(vm.docs.cnpj),
                "identidade": Number(vm.docs.identidade),
                "selfie": Number(vm.docs.selfie),
                "contrato_social": Number(vm.docs.contrato_social),
                "endereco": Number(vm.docs.endereco),
                "atendente": $rootScope.operador.username
            }

            $http({
                url: API_CONFIG.url + '/comercial/pedido/' + pedido.id,
                method: "PUT",
                data: data,
                ignoreLoadingBar: true
            })
                .then(function (response) {
                    if (response.data.status == "OK") {
                        toaster.pop('success', "Reenvio solicitado", "Reenvio solicitado com sucesso!");
                        seleciona(pedido);

                    }
                });

        }

        function copyToClipboard(text) {

            // create temp element
            var copyElement = document.createElement("span");
            copyElement.appendChild(document.createTextNode(text));
            copyElement.id = 'tempCopyToClipboard';
            angular.element(document.body.append(copyElement));

            // select the text
            var range = document.createRange();
            range.selectNode(copyElement);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);

            // copy & cleanup
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            copyElement.remove();
        }

        angular.element(document).ready(function () {
            jQuery(function () {
                var gallery = jQuery("[data-magnify=gallery]");
                gallery.magnify({ footToolbar: ['zoomIn', 'zoomOut', 'prev', 'fullscreen', 'next', 'actualSize', 'rotateLeft', 'rotateRight'] });
            });
        });

        function seleciona(pedido) {

            vm.selecionado = {};

            vm.docs = {
                cpf: 0,
                cnpj: 0,
                identidade: 0,
                selfie: 0,
                contrato_social: 0,
                endereco: 0
            };


            $http({
                url: API_CONFIG.url + '/comercial/pedido/' + pedido.id,
                method: "GET"
            })
                .then(function (response) {
                    for (var i = 0; i < response.data.documentos.length; i++) {
                        var documento = response.data.documentos[i];
                        var filename = documento.filename;
                        var extension = filename.split('.').pop().toLowerCase();
                        // Define se o documento é uma imagem (será apresentada no front-end usando visualizador de imagens)
                        documento.isImage = (extension === 'jpg' || extension === 'jpeg' || extension === 'png' || extension === 'gif' || extension === 'bmp');
                        documento.extension = extension;
                    }

                    angular.copy(response.data, vm.selecionado);

                    var currencyProperties = [
                        'valor_plano',
                        'valor_roteador',
                        'valor_decoder',
                        'valor_mesh',
                        'directvgo_valor',
                        'globoplay_valor',
                        'valor_total'
                    ];

                    for (var i = 0; i < currencyProperties.length; i++) {
                        var property = currencyProperties[i];
                        vm.selecionado[property] = parseFloat(vm.selecionado[property]).toFixed(2);
                    }

                    vm.selecionado.ref = pedido;
                    vm.selecionado.historicos = response.data.historicos;
                    vm.selecionado.historicos.push({
                        'data': response.data.pedido_termino,
                        'status': 'Pedido realizado pelo cliente',
                        'atendente': 'sistema'
                    });
                    vm.selecionado.documentos = response.data.documentos;
                    vm.selecionado.reenvios = response.data.reenvios;
                    vm.selecionado.contrato_url = 'https://contratar.telemidia.net.br/common/api/get_documents/?filename=contrato.pdf&t=' + pedido.token;
                },
                    function (response) {
                    });

        };

        function busca() {

            vm.pagination.page = 1;
            pageChanged();

        }

        function limpar() {
            vm.pagination = {
                page: 1
            };
            vm.filtro = '';
            vm.tipo = 'nome';
            vm.termos = '';
            $location.url('/comercial/pedidos');
        }

        function pageChanged() {
            var urlApi = '/comercial/pedidos?page=' + vm.pagination.page +
                '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos
                + '&dtinicio=' + $filter('date')(vm.dtinicio, "yyyy-MM-dd")
                + '&dtfim=' + $filter('date')(vm.dtfim, "yyyy-MM-dd") + '&showingTab='+vm.showingTab + '&conclusoes=' + vm.conclusoes_selecionadas;

            if (vm.status !== '') urlApi += '&status=' + vm.status;

            if (vm.cidade !== '') urlApi += '&cidade=' + vm.cidade;

            if (vm.atendente !== '') urlApi += '&atendente=' + vm.atendente;

            if (vm.vendedor !== '') urlApi += '&vendedor=' + vm.vendedor;

            if (vm.finalizados !== '') urlApi += '&finalizados=' + vm.finalizados;

            $location.url(urlApi);

        }

        function reservarPedido() {
            var documento;
            do {
                documento = $window.prompt('Digite o CPF ou CNPJ do cliente a reservar o pedido: ', "");

                if (documento === null) {
                    return;
                }
                else if (documento.trim() === '') {
                    $window.alert('É necessário especificar um documento.');
                }
            } while (documento === null || documento.trim() === '');

            postReserva(documento);
        }

        function desvincularAtendente(pedido) {
            AlertService.loading('Desvinculando atendimento...');

            if (pedido.atendente.trim().toLowerCase() !== $rootScope.operador.username.trim().toLowerCase())
                return false;

            $http({
                url: API_CONFIG.url + '/comercial/pedidos/desvincular-atendente',
                method: "POST",
                data: {
                    pedido_id: pedido.id
                }
            })
                .then(function (response) {
                    response = response.data;

                    AlertService.loaded(response.status, 'O atendimento do pedido <b>#' + pedido.id + '</b> foi desvinculado de você.')

                    if (response.status == 'success')
                        pedido.atendente = null;
                },
                    function (response) {
                    });
        }

        function postReserva(documento) {

            $http({
                url: API_CONFIG.url + '/comercial/reserva',
                method: "POST",
                data: {
                    atendente: $rootScope.operador.username,
                    documento: documento
                }
            })
                .then(function (response) {
                    response = response.data;

                    response.documento_tipo = response.documento_tipo.toUpperCase();

                    switch (response.status) {
                        case 'success':
                            $window.alert('Sucesso! O pedido com o ' + response.documento_tipo + ' ' + response.documento_formatado + ' será reservado para você.');
                            break;
                        case 'invalido':
                            $window.alert('Erro! O documento digitado é inválido.');
                            break;
                        case 'indisponivel':
                            $window.alert('Erro! O pedido deste ' + response.documento_tipo + ' já foi reservado para o(a) atendente ' + response.atendente_reservado + '.');
                            break;
                    }
                },
                    function (response) {
                    });
        }

        function getPedidosAssistidosAtuais() {
            $http({
                url: API_CONFIG.url + '/comercial/pedidos-assistidos/atuais',
                method: "GET"
            }).then(function (response) {
                if (response && response.data && response.data.status && response.data.status == 'success') {
                    vm.pedidosAssistidos.pendentes = response.data.pendentes;
                    vm.pedidosAssistidos.em_andamento = response.data.em_andamento;

                    formatPedidosAssistidos(vm.pedidosAssistidos.pendentes);
                    formatPedidosAssistidos(vm.pedidosAssistidos.em_andamento);
                }
                else {
                    console.log('Erro ao consultar os pedidos assistidos pendentes.');
                }
            },
                function (response) {
                });
        }

        function getPedidosAssistidosHistorico() {
            var data = {
                nome: vm.filtroAssistidos.nome,
                status: vm.filtroAssistidos.status,
                atendente: vm.filtroAssistidos.atendente,
                dtinicio: $filter('date')(vm.filtroAssistidos.dtinicio, "yyyy-MM-dd"),
                dtfim: $filter('date')(vm.filtroAssistidos.dtfim, "yyyy-MM-dd"),
                horainicio: $filter('date')(vm.horainicio, "HH:mm"),
                horafim: $filter('date')(vm.horafim, "HH:mm")
            };

            $http({
                url: API_CONFIG.url + '/comercial/pedidos-assistidos/historico',
                method: "POST",
                data: data
            }).then(function (response) {
                if (response && response.data && response.data.status && response.data.status == 'success') {
                    vm.pedidosAssistidos.historico = response.data.historico;

                    formatPedidosAssistidos(vm.pedidosAssistidos.historico);
                }
                else {
                    console.log('Erro ao consultar o histórico de pedidos assistidos.');
                }
            },
                function (response) {
                });
        }

        function formatPedidosAssistidos(pedidos) {
            for(var i = 0; i<pedidos.length; i++) {
                var pedido = pedidos[i];

                pedido.whatsappNumber = '55' + pedido.info_contato.replace(/\D/g, '');

                switch(pedido.meio_contato) {
                    case 'whatsapp':
                        pedido.meioContatoStr = 'WhatsApp';
                        break;
                    case 'telefone':
                        pedido.meioContatoStr = 'Telefone';
                        break;
                    case 'email':
                        pedido.meioContatoStr = 'E-mail';
                        break;
                    default:
                        pedido.$meioContatoStr = '[INDEFINIDO]';
                }
            }
        }
    }

})();
