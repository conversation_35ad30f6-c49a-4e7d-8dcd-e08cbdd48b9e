<style>
	#lista_servicos {
		text-align: center;
	}

	.servicos-container {
		margin: 0 auto;
		max-width: 700px;
	}

	.servicos-container h4 {
		width: 100%;
		text-align: center;
		padding: 5px;
	}

	.servicos-container.ativos h4 {
		background: #5CB85C;
		color: #fff;
	}

	.servicos-container.inativos h4 {
		background: #D9534F;
		color: #fff;
	}

	.servicos-container td, .servicos-container th {
		padding: 3px 5px !important;
	}

	.servicos-container tr td:nth-child(1) {
		width: 150px;
	}
	.servicos-container tr td:nth-child(2) {
		width: 200px;
	}
	.servicos-container tr td:nth-child(3) {
		width: 60px;
	}
	.servicos-container tr td:nth-child(4) {
		width: 60px;
	}
	.servicos-container tr td:nth-child(5) {
		width: 60px;
	}

	.servicos-container tr td:nth-child(1) input {
		font-weight: bold;
	}

	.servicos-container tr.changed {
		background: #FFB100;
	}
</style>

<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

	<li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
	<li><a href="/comercial/atendimento"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
	<li><i class="glyphicon glyphicon-shopping-cart"></i> Planos</li>
</ol>

<ul class="nav nav-tabs">
	<li role="presentation" class="active" data-target="#planos_internet" data-toggle="tab">
		<a href="#">
			<i class="glyphicon glyphicon-globe margin-right-5"></i>
			Planos de internet
		</a>
	</li>
	<li role="presentation" data-target="#planos_mesh" data-toggle="tab">
		<a href="#">
			<i class="glyphicon glyphicon-link margin-right-5"></i>
			Planos Mesh
		</a>
	</li>
	<li role="presentation" data-target="#planos_directvgo" data-toggle="tab">
		<a href="#">
			<i class="fa fa-television margin-right-5"></i>
			SKY+
		</a>
	</li>
	<li role="presentation" data-target="#planos_globoplay" data-toggle="tab">
		<a href="#">
			<i class="fa fa-television margin-right-5"></i>
			Globoplay
		</a>
	</li>
	<li role="presentation" data-target="#lista_servicos" data-toggle="tab">
		<a href="#">
			<i class="glyphicon glyphicon-th-list margin-right-5"></i>
			Lista de serviços
		</a>
	</li>


	<!-- <li role="presentation" data-target="#registro-atividades" data-toggle="tab"><a href="#" id="tab-registro-atividade"><i class="glyphicon glyphicon-list margin-right-5"></i>Registro de atividades</a></li> -->
</ul>


<div class="tab-content">


	<div role="tabpanel" class="tab-pane active" id="planos_internet">

		<div class="table barra align-center" style="padding-top: 7px;">
			<div class="tr">
				<div class="td">
					<form class="form-inline" role="form">
						<div class="form-group">
							Cidade:
							<select class="form-control" ng-model="PLN.cidade" ng-change="PLN.getPlanosInternet()">
								<!--option value="todas">Todas</option-->
								<option ng-repeat="cidade in PLN.cidadesAtendidas" value="{{ cidade.codigo_alt }}">{{ cidade.cidade }}</option>
							</select>
						</div>
						<div class="form-group">
							Tecnologia:
							<select class="form-control" ng-model="PLN.tecnologia" ng-change="PLN.getPlanosInternet()">
								<option value="todas">Todas</option>
								<option value="fibra">Fibra Óptica</option>
								<option value="radio">Rádio</option>
								<option value="cdmht">CDMHT</option>
							</select>
						</div>
					</form>
				</div>
				<div class="td">
					<button class="btn btn-primary" ng-click="PLN.initFrmnovoplano()" data-toggle="modal"
						data-target="#frmnovoplano"><i class="glyphicon glyphicon-plus btn-icon"></i>Novo plano</button>
				</div>
			</div>

		</div>

		<div class="table-responsive">
			<span class="counter pull-right"></span>
			<table class="table table-striped table-bordered valign-middle align-center">
				<thead>
					<tr>
						<th>Editar</th>
						<th>Cidade</th>
						<th>Tecnologia</th>
						<th>Nome</th>
						<th>Legenda</th>
						<th>Velocidade</th>
						<th>Valor</th>
						<th>Roteador</th>
						<th>Roteador incluso</th>
						<th>Valor Roteador</th>
						<th>TV</th>
						<th>Decoder</th>
						<th>Decoder incluso</th>
						<th>Valor Decoder</th>
						<th>URL</th>
						<th>Contratação</th>
						<th>Migração</th>
						<th>Excluir</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="plano in PLN.planos" ng-class="plano.ativo == 0 ? 'desativado' : ''">
						<td>
							<a class="btn btn-primary btn-sm" title="Editar plano" data-toggle="modal" data-target="#frmplano"
								ng-click="PLN.selecionarPlano(plano)"><i class="glyphicon glyphicon-pencil"></i></a>
						</td>
						<td>{{plano.cidade_txt}}</td>
						<td><span class="label" ng-class="[{'label-info': plano.tecnologia === 'radio'},
                              {'label-primary': plano.tecnologia === 'fibra'},
                              {'label-warning': plano.tecnologia === 'cdmht'}]">{{plano.tecnologia_txt}}</span></td>
						<td>{{plano.nome}}</td>
						<td>{{plano.caption}}</td>
						<td>{{plano.velocidade_txt}}</td>
						<td>{{plano.valor_txt}}</td>
						<td>
							<span class="label" ng-class="[{'label-success': plano.roteador == 1},
                              {'label-danger': plano.roteador == 0}]">{{plano.roteador == 1 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>
							<span class="label"
								ng-class="(plano.roteador == 1 && plano.roteador_opcional == 0) ? 'label-success' : 'label-danger'">{{plano.roteador == 1 && plano.roteador_opcional == 0 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>{{plano.valor_roteador_txt}}</td>
						<td>
							<span class="label" ng-class="[{'label-success': plano.tv == 1},
				                              {'label-danger': plano.tv == 0}]">{{plano.tv == 1 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>
							<span class="label" ng-class="[{'label-success': plano.decoder == 1},
				                              {'label-danger': plano.decoder == 0}]">{{plano.decoder == 1 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>
							<span class="label"
								ng-class="(plano.decoder == 1 && plano.decoder_opcional == 0) ? 'label-success' : 'label-danger'">{{plano.decoder == 1 && plano.decoder_opcional == 0 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>{{plano.valor_decoder_txt}}</td>
						<td>
							<span ng-if="plano.ativo == 1" class="copier" copy-to-clipboard="{{plano.url}}">Copiar</span>
						</td>
						<td>
							<span class="label" ng-class="[{'label-success': plano.disponivel_contratacao == 1},
				                              {'label-danger': plano.disponivel_contratacao == 0}]">{{plano.disponivel_contratacao == 1 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>
							<span class="label" ng-class="[{'label-success': plano.disponivel_migracao == 1},
				                              {'label-danger': plano.disponivel_migracao == 0}]">{{plano.disponivel_migracao == 1 ? 'SIM' : 'NÃO'}}</span>
						</td>
						<td>
							<a class="btn btn-danger btn-sm" title="Excluir plano"
								ng-really-message="Tem certeza de que deseja excluir o plano <b>{{plano.nome}}</b> da cidade de <b>{{plano.cidade_txt}}</b>?"
								ng-really-click="PLN.excluir(plano.id)"><i class="glyphicon glyphicon-trash"></i></a>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>


	<!-- Planos Mesh - INÍCIO -->
	<div role="tabpanel" class="tab-pane" id="planos_mesh" style="padding-top: 20px;">

		<div class="table barra align-center" style="padding-top: 7px;">
			<div class="tr">
				<div class="td">
					<form class="form-inline" role="form">
						<div class="form-group">
							Cidade:
							<select class="form-control" ng-model="PLN.cidade_mesh" ng-change="PLN.getPlanosMesh()">
								<!--option value="todas">Todas</option-->
								<option ng-repeat="cidade in PLN.cidadesAtendidas" value="{{ cidade.codigo_alt }}">{{ cidade.cidade }}</option>
							</select>
						</div>
					</form>
				</div>
			</div>
		</div>

		<div class="table-responsive">
			<span class="counter pull-right"></span>
			<table class="table table-striped table-bordered valign-middle align-center">
				<thead>
					<tr>
						<th>Editar</th>
						<th>Cidade</th>
						<th>Tecnologia</th>
						<th>Nome</th>
						<th>Roteadores Mesh</th>
						<th>Valor</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="plano in PLN.planos_mesh" ng-class="plano.ativo == 0 ? 'desativado' : ''">
						<td>
							<a class="btn btn-primary btn-sm" title="Editar plano" data-toggle="modal"
								data-target="#frmplano_mesh" ng-click="PLN.selecionarPlano(plano)"><i
									class="glyphicon glyphicon-pencil"></i></a>
						</td>
						<td>{{plano.cidade_txt}}</td>
						<td><span class="label" ng-class="[{'label-success': plano.tecnologia === 'mesh'}]">{{plano.tecnologia_txt}}</span></td>
						<td>{{plano.nome}}</td>
						<td>{{plano.roteadores_mesh}}</td>
						<td>{{plano.valor_txt}}</td>
					</tr>
				</tbody>
			</table>
		</div>

	</div>
	<!-- Planos Mesh - FIM -->

	<div role="tabpanel" class="tab-pane" id="planos_directvgo" style="padding-top: 20px;">
		<div style="width: 200px; margin: 0 auto !important; text-align: center;">
			<label for=""><b>Valor SKY+:</b></label>
			<div class="input-group">
				<div class="input-group-addon">
					R$
				</div>
				<input money-mask type="text" class="form-control align-center" id="plano_directvgo_valor" ng-model="PLN.plano_directvgo.valor" style="font-size: 11pt;">
			</div>
			<br>
			<button class="btn btn-primary" ng-really-message="Deseja realmente salvar o novo valor do plano da SKY+?" ng-really-click="PLN.salvarValorDgo()"><i class="fa fa-check btn-icon"></i>Salvar</button>
		</div>
	</div>

	<div role="tabpanel" class="tab-pane" id="planos_globoplay" style="padding-top: 20px;">
		<div style="width: 200px; margin: 0 auto !important; text-align: center;">
			<label for=""><b>Valor Globoplay:</b></label>
			<div class="input-group">
				<div class="input-group-addon">
					R$
				</div>
				<input money-mask type="text" class="form-control align-center" id="plano_globoplay_valor" ng-model="PLN.plano_globoplay.valor" style="font-size: 11pt;">
			</div>
			<br>
			<button class="btn btn-primary" ng-really-message="Deseja realmente salvar o novo valor do plano da Globoplay?" ng-really-click="PLN.salvarValorGloboplay()"><i class="fa fa-check btn-icon"></i>Salvar</button>
		</div>
	</div>

	<div role="tabpanel" class="tab-pane" id="lista_servicos">
		<button class="btn btn-primary" data-toggle="modal" data-target="#frmnovoservico" style="margin-top: 10px;">
			<i class="fa fa-plus" style="margin-right: 5px;"></i> Novo serviço
		</button>

		<div class="horizontal-divider"></div>

		<div style="margin-top: 10px;">
			<button class="btn btn-primary" ng-really-message="Deseja realmente salvar as alterações?" ng-really-click="PLN.salvarServicos()" ng-disabled="PLN.servicosDiffIds.length == 0">
				<i class="fa fa-check" style="margin-right: 5px;"></i> Salvar alterações
			</button>
		</div>

		<div class="servicos-container ativos">
			<h4>Serviços ativos</h4>
			<table class="table">
				<thead>
					<tr>
						<th>Serviço</th>
						<th>Descrição</th>
						<th class="text-center">Ordem</th>
						<th class="text-center">Ativo</th>
						<th class="text-center">Excluir</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="servico in PLN.planosServicos" ng-if="servico.ativo" ng-class="{changed: PLN.servicosDiffIds.includes(servico.id)}">
						<td>
							<input type="text" class="form-control" ng-model="servico.servico" ng-change="PLN.checkServicosDiff()" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="servico.descricao" ng-change="PLN.checkServicosDiff()" />
						</td>
						<td>
							<input type="number" class="form-control text-center" ng-model="servico.ordem" ng-change="PLN.checkServicosDiff()">
						</td>
						<td>
							<select class="form-control text-center" ng-model="servico.ativo" ng-change="PLN.checkServicosDiff()">
								<option ng-value="true">SIM</option>
								<option ng-value="false">NÃO</option>
							</select>
						</td>
						<td>
							<button class="btn btn-sm btn-danger" ng-really-message="Deseja realmente remover este serviço? Ele será desvinculado de todos os planos onde ele esteja ativo." ng-really-click="PLN.removerServico(servico.id)">
								<i class="fa fa-trash"></i>
							</button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

		<div class="servicos-container inativos">
			<h4>Serviços inativos</h4>
			<table class="table">
				<thead>
					<tr>
						<th>Serviço</th>
						<th>Descrição</th>
						<th class="text-center">Ordem</th>
						<th class="text-center">Ativo</th>
						<th class="text-center">Excluir</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="servico in PLN.planosServicos" ng-if="!servico.ativo" ng-class="{changed: PLN.servicosDiffIds.includes(servico.id)}">
						<td>
							<input type="text" class="form-control" ng-model="servico.servico" ng-change="PLN.checkServicosDiff()" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="servico.descricao" ng-change="PLN.checkServicosDiff()" />
						</td>
						<td>
							<input type="number" class="form-control text-center" ng-model="servico.ordem" ng-change="PLN.checkServicosDiff()">
						</td>
						<td>
							<select class="form-control text-center" ng-model="servico.ativo" ng-change="PLN.checkServicosDiff()">
								<option ng-value="true">SIM</option>
								<option ng-value="false">NÃO</option>
							</select>
						</td>
						<td>
							<button class="btn btn-sm btn-danger" ng-really-message="Deseja realmente remover este serviço? Ele será desvinculado de todos os planos onde ele esteja ativo." ng-really-click="PLN.removerServico(servico.id)">
								<i class="fa fa-trash"></i>
							</button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<div ng-include="'app/comercial/plano.form.mesh.html'"></div>
<div ng-include="'app/comercial/plano.form.html'"></div>
<div ng-include="'app/comercial/plano.novo.form.html'"></div>
<div ng-include="'app/comercial/servico.novo.form.html'"></div>
