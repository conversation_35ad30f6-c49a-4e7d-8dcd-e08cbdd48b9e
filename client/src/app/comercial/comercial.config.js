'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider
            .when('/comercial/cobertura', {
                templateUrl: 'app/comercial/comercial.cobertura.html',
                controller: 'ComercialCoberturaController',
                controllerAs: 'CCC',
                title: 'Atendimento',
                authorize: ['comercial.read', 'comercial.write'],
                resolve: {

                    dadosAPI: function ($q, CidadesService) {
                        var Cidades = CidadesService.getCidades();
                        return $q.all([Cidades.$promise]);
                    }
                }
            })

            .when('/comercial/prospects', {
                templateUrl: 'app/comercial/prospects.list.html',
                controller: 'ProspectListController',
                controllerAs: 'PLC',
                title: 'Prospects',
                authorize: ['comercial.read', 'comercial.write'],

            })

            .when('/comercial/pedidos', {
                templateUrl: 'app/comercial/pedidos.list.html',
                controller: 'PedidosListController',
                controllerAs: 'PC',
                title: 'Pedidos',
                authorize: ['comercial.read', 'comercial.write'],

            })

            .when('/comercial/planos', {
                templateUrl: 'app/comercial/planos.list.html',
                controller: 'PlanosListController',
                controllerAs: 'PLN',
                title: 'Planos',
                authorize: ['comercial.read', 'comercial.write'],

            })

            .when('/comercial/grupos-vendedores', {
                templateUrl: 'app/comercial/grupos-vendedores.html',
                controller: 'GruposVendedoresController',
                controllerAs: 'GVC',
                title: 'Grupos de vendedores',
                authorize: ['comercial.write'],

            })

            .when('/comercial/logradouros', {
                templateUrl: 'app/comercial/comercial.logradouros.html',
                controller: 'ComercialLogradourosController',
                controllerAs: 'CLC',
                title: 'Logradouros',
                resolve: {
                    logradouros: function (LogradourosService) {
                        return LogradourosService.getLogradouros().$promise;
                    }
                },
                authorize: ['comercial.read', 'comercial.write']
            })

            .when('/comercial/sky', {
                templateUrl: 'app/comercial/sky.html',
                controller: 'DGOController',
                controllerAs: 'DC',
                title: 'SKY+',
                authorize: ['comercial.read', 'comercial.write'],

            })

            .when('/comercial/campanhas', {
                templateUrl: 'app/comercial/campanhas.html',
                controller: 'CampanhasController',
                controllerAs: 'CC',
                title: 'Campanhas de Marketing',
                authorize: ['comercial.read', 'comercial.write'],

            })

    });
