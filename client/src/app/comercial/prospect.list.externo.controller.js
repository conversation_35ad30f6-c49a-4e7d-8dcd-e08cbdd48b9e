(function () {
    'use strict';

    angular
        .module('app')
        .controller('ProspectListExternoController', ProspectListExternoController);

    /** @ngInject */
    function ProspectListExternoController($http, API_CONFIG, cfpLoadingBar, $rootScope, 
        toaster, $routeParams, $location) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.tipo='nome';
        vm.termos='';
        vm.prospects = [];
        vm.pagination = {};
        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.sort = sort;
        vm.limpar = limpar;
        vm.usuario = $rootScope.operador.username;

        activate();

        function activate() {

            

            if($routeParams.exito !== undefined){
              vm.exito = $routeParams.exito;
            } else {
              vm.exito = '';
            }

            if($routeParams.sortby !== undefined){
              vm.sortBy = $routeParams.sortby;
            } else {
              vm.sortBy = 'datacad';
            }

            if($routeParams.sortorder !== undefined){
              vm.sortOrder = $routeParams.sortorder;
            } else {
              vm.sortOrder = 'dsc';
            }

            if($routeParams.nome !== undefined){
              vm.tipo='nome';
              vm.termos=$routeParams.nome;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.logradouro !== undefined){
              vm.tipo='logradouro';
              vm.termos=$routeParams.logradouro;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.bairro !== undefined){
              vm.tipo='bairro';
              vm.termos=$routeParams.bairro;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.usuario !== undefined){
              vm.tipo='usuario';
              vm.termos=$routeParams.usuario;
              vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
            }

            if($routeParams.id !== undefined){
              vm.tipo='id';
              vm.termos=$routeParams.id;
              vm.filtro = '&' + vm.tipo + '=' + vm.termos;
            }
            
            getData();
        }

        function getData() {

            var urlApi = API_CONFIG.url + '/comercial/prospect?page=' + $routeParams.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
               if(vm.exito !=='') urlApi += '&exito=' + vm.exito;
               urlApi += '&usuario='+vm.usuario;

            $http.get(urlApi).then(function (response) {
                 angular.copy(response.data.rows, vm.prospects);
                angular.copy(response.data.pagination, vm.pagination);
            });
        }

        function pageChanged() {
            var urlApi = '/comercial/prospects?page=' + vm.pagination.page +
                '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos;
            if(vm.exito !=='') urlApi += '&exito=' + vm.exito;
            urlApi += '&usuario='+vm.usuario;;
            $location.url(urlApi);

        }

        function sort(field){
            if(field == vm.sortBy){
                if(vm.sortOrder == 'asc'){
                    vm.sortOrder = 'dsc';
                } else {
                    vm.sortOrder = 'asc';
                }
            }

            vm.sortBy = field;
            pageChanged();
        }

        function busca(termos) {

            vm.pagination.page = 1;
            vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

            pageChanged();

        }

        function limpar(){
          vm.pagination = {
              page: 1
          };
          vm.filtro = '';
          vm.tipo = 'host';
          vm.termos = '';
          $location.url('/comercial/prospects');
        }

        

    }

})();
