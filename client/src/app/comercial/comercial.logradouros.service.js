'use strict';

angular.module('app')

    .factory('LogradourosService', function ($resource, API_CONFIG) {

        return $resource(API_CONFIG.url + '/comercial/logradouros/:logradouro', {},
            {
                getLogradouros: {method: 'GET', isArray: true},
                insertLogradouro: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateLogradouro: {
                    method: 'PUT',
                    params: {logradouro_interfocus: '@logradouro'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    });

   