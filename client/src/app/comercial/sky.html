<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

	<li><a href="/comercial/atendimento"><i class="glyphicon glyphicon-usd"></i> Comercial</a></li>
	<li><i class="glyphicon glyphicon-hd-video"></i> SKY+</li>
</ol>

<ul class="nav nav-tabs">
	<li role="presentation" class="active" data-target="#divergencias" data-toggle="tab" id="divergencias-tab">
		<a href="#">
			<i class="glyphicon glyphicon-random margin-right-5"></i>
			Divergências IXC - SKY+ <b><span ng-if="!DC.loadingDivergencias"
					ng-class="DC.numeroDivergencias == 0 ? 'text-success-dark' : 'text-danger-dark'">({{DC.numeroDivergencias}})</span><span
					ng-if="DC.loadingDivergencias">(...)</span></b>
		</a>
	</li>

	<li role="presentation" data-target="#registro-atividades" data-toggle="tab" id="registros-tab"><a href="#"
			id="tab-registro-atividade"><i class="glyphicon glyphicon-list margin-right-5"></i>Registro de
			atividades</a></li>
</ul>

<div class="tab-content">


	<div role="tabpanel" class="tab-pane active" id="divergencias" style="padding-top: 20px;">

		<div style="width: 100%; text-align: center; margin-bottom: 20px">
			<button class="btn btn-default" title="Atualizar" ng-click="DC.getDivergencias();">
				<i class="glyphicon glyphicon-refresh"></i> Atualizar
			</button>
		</div>

		<div style="width: 100%; text-align: center; margin-bottom: 10px">
			<h3>Erros de processo:</h3>
		</div>

		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Contrato-com-produtos-DGO-do-tipo-incorreto')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.contratosComProdutosTipoIncorreto.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.contratosComProdutosTipoIncorreto.length}})</span>
						Contratos com produtos SKY+ do tipo incorreto (deveria ser "TV/Streaming")</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.contratosComProdutosTipoIncorreto.length > 0">
						<tr>
							<th>ID do contrato</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr
							ng-if="!DC.loadingDivergencias && DC.divergencias.contratosComProdutosTipoIncorreto.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="contrato in DC.divergencias.contratosComProdutosTipoIncorreto">
							<td>
								{{ contrato }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Contrato-DGO-sem-usuario-de-TV')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.contratosDgoSemUsuarioTv.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.contratosDgoSemUsuarioTv.length}})</span>
						Contratos com produto SKY+, sem usuário de TV ativo</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.contratosDgoSemUsuarioTv.length > 0">
						<tr>
							<th>ID do contrato</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr ng-if="!DC.loadingDivergencias && DC.divergencias.contratosDgoSemUsuarioTv.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="contrato in DC.divergencias.contratosDgoSemUsuarioTv">
							<td>
								{{ contrato }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Contrato-com-usuario-de-TV-mas-sem-pacotes-DGO')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.usuariosTvSemPacoteDgo.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.usuariosTvSemPacoteDgo.length}})</span>
						Contratos com usuário de TV ativo, mas sem pacotes SKY+</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.usuariosTvSemPacoteDgo.length > 0">
						<tr>
							<th>ID do contrato</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr ng-if="!DC.loadingDivergencias && DC.divergencias.usuariosTvSemPacoteDgo.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="contrato in DC.divergencias.usuariosTvSemPacoteDgo">
							<td>
								{{ contrato }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Usuario-de-TV-no-IXC-sem-a-integracao-a-Playhub')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.usuariosTvSemIntegracaoPlayhub.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.usuariosTvSemIntegracaoPlayhub.length}})</span>
						Usuários de TV no IXC, sem a opção de integração com a Playhub</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.usuariosTvSemIntegracaoPlayhub.length > 0">
						<tr>
							<th>ID contrato</th>
							<th>Usuário TV</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr
							ng-if="!DC.loadingDivergencias && DC.divergencias.usuariosTvSemIntegracaoPlayhub.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="usuario in DC.divergencias.usuariosTvSemIntegracaoPlayhub">
							<td>
								{{ usuario.id_contrato }}
							</td>
							<td>
								{{ usuario.usuario }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div style="width: 100%; text-align: center; margin-bottom: 10px;">
			<h3>Erros de integração:</h3>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Usuario-cadastrado-na-Abranet-sem-usuario-de-TV-no-IXC')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.usuariosAbranetSemUsuarioIxc.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.usuariosAbranetSemUsuarioIxc.length}})</span>
						Usuários cadastrados na Abranet, sem usuário de TV no IXC</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.usuariosAbranetSemUsuarioIxc.length > 0">
						<tr>
							<th>E-mail</th>
							<th>Nome</th>
							<th>Documento</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr ng-if="!DC.loadingDivergencias && DC.divergencias.usuariosAbranetSemUsuarioIxc.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="usuario in DC.divergencias.usuariosAbranetSemUsuarioIxc">
							<td>
								{{ usuario.email }}
							</td>
							<td>
								{{ usuario.name }}
							</td>
							<td>
								{{ usuario.document }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Usuario-de-TV-no-IXC-sem-cadastro-ativo-na-Abranet')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.usuariosIxcSemUsuarioAbranet.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.usuariosIxcSemUsuarioAbranet.length}})</span>
						Usuários de TV no IXC, sem cadastro ativo na Abranet</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.usuariosIxcSemUsuarioAbranet.length > 0">
						<tr>
							<th>ID contrato</th>
							<th>Usuário TV</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr ng-if="!DC.loadingDivergencias && DC.divergencias.usuariosIxcSemUsuarioAbranet.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="usuario in DC.divergencias.usuariosIxcSemUsuarioAbranet">
							<td>
								{{ usuario.id_contrato }}
							</td>
							<td>
								{{ usuario.usuario }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>


		<div class="panel panel-primary">
			<div class="panel-heading clearfix">
				<span class="pull-right">
					<button class="btn btn-default btn-sm"
						ng-click="DC.openInNewTab('https://wiki.telemidia.net.br/pt-br/Comercial/DirecTV-GO/Divergencias/Contrato-possui-pacotes-DGO-divergentes-entre-IXC-e-Abranet')"><i
							class="glyphicon glyphicon-info-sign"></i> Como corrigir
					</button>
				</span>

				<h3 class="panel-title" style="margin-top: 5px;"><strong><span
							ng-class="DC.divergencias.contratosComPacotesDivergentes.length > 0 ? 'text-danger-dark' : ''">({{DC.divergencias.contratosComPacotesDivergentes.length}})</span>
						Contratos que possuem pacotes SKY+ divergentes entre IXC e Abranet</strong></h3>
			</div>
			<div class="panel-body no-padding">

				<table class="table table-striped table-bordered valign-middle align-center no-margin">
					<thead ng-if="DC.divergencias.contratosComPacotesDivergentes.length > 0">
						<tr>
							<th>ID contrato</th>
							<th>Pacotes IXC</th>
							<th>Pacotes Abranet</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="DC.loadingDivergencias">
							<td class="text-warning">
								Carregando...
							</td>
						</tr>
						<tr
							ng-if="!DC.loadingDivergencias && DC.divergencias.contratosComPacotesDivergentes.length == 0">
							<td class="text-success">
								Não há divergências
							</td>
						</tr>
						<tr ng-repeat="contrato in DC.divergencias.contratosComPacotesDivergentes">
							<td>
								{{ contrato.id_contrato }}
							</td>
							<td>
								{{ contrato.pacotes_ixc }}
							</td>
							<td>
								{{ contrato.pacotes_abranet }}
							</td>
						</tr>
					</tbody>
				</table>

			</div>
		</div>
	</div>

	<div role="tabpanel" class="tab-pane" id="registro-atividades">
		<div class="table barra align-center" style="padding-top: 7px;">

			<table style="margin: 0 auto;">
				<tr>
					<td style="padding-bottom: 10px;">
						<input type="text" id="dgo_busca" class="form-control" placeholder="Buscar por e-mail, ID do contrato ou ID do cliente" ng-model="DC.busca" style="margin-bottom: 10px;">
						<div class="input-group" style="width: 400px;">
							<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
							<input type="date" class="form-control input-date-md" id="data_inicial"
								ng-model="DC.data_inicial">
							<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
							<input type="date" class="form-control input-date-md" id="data_final"
								ng-model="DC.data_final">
						</div>
					</td>
					<td style="padding-left: 20px;">
						<button class="btn btn-primary" ng-click="DC.getRegistroAtividades()">
							<i class="glyphicon glyphicon-search btn-icon"></i>
							Buscar
						</button>
					</td>
				</tr>
			</table>
		</div>

		<table class="table table-bordered spaced-td" ng-if="DC.loadingRegistroAtividades">
			<tr>
				<td class="align-center text-warning">Carregando...</td>
			</tr>
		</table>

		<table class="table table-striped table-hover table-bordered spaced-td" ng-if="!DC.loadingRegistroAtividades">
			<thead>
				<tr>
					<th class="vert-align text-center">
						#
					</th>
					<th class="vert-align text-center">
						Data
					</th>
					<th class="vert-align text-center">
						Status
					</th>
					<th class="vert-align text-center">
						ID cliente
					</th>
					<th class="vert-align text-center">
						ID contrato
					</th>
					<th class="vert-align text-center">
						Conta SKY+
					</th>
					<th class="vert-align text-center">
						Ação
					</th>
					<th class="vert-align text-center">
						Mensagem
					</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="registro in DC.registroAtividades" ng-class="{'danger': registro.error}">
					<td class="vert-align text-center">
						{{registro.id}}
					</td>

					<td class="vert-align text-center nowrap">
						{{registro.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
					</td>

					<td class="vert-align text-center">
						<span ng-if="registro.error" class="label label-danger">ERRO</span>
						<span ng-if="!registro.error" class="label label-success">OK</span>
					</td>

					<td class="vert-align text-center">
						{{registro.id_cliente}}
					</td>

					<td class="vert-align text-center">
						{{registro.id_contrato}}
					</td>

					<td class="vert-align text-center">
						{{registro.conta}}
					</td>
					<td class="vert-align text-center">
						{{registro.acao}}
					</td>
					<td class="vert-align text-center">
						{{registro.mensagem}}
					</td>
				</tr>
			</tbody>
		</table>
		<div class="text-center">
			<uib-pagination total-items="DC.pagination.size" ng-model="DC.pagination.page" ng-change="DC.pageChanged()"
				items-per-page="DC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
				boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
			</uib-pagination>
		</div>
		<div class="text-center">
			Página <span class="badge">{{ DC.pagination.page}}</span> de <span class="badge">{{
				DC.pagination.pages}}</span>
			de <span class="badge">{{ DC.pagination.size}}</span> registro(s)</span>
		</div>
	</div>
</div>