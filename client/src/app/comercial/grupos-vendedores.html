<div style="width: 100%; padding: 20px; text-align: center;">
    <div class="table" style="max-width: 900px; margin: 0 auto;">
        <div class="tr">
            <div class="td" style="width: 60%; padding-right: 15px; max-width: 300px;">
    
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">Grupos de vendedores</h3>
                    </div>
                    <div class="panel-body" style="padding: 0px; max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
                            <tbody>
                                <tr ng-if="GVC.carregando_dados_modal_grupos">
                                    <td class="align-center valign-middle" colspan="3" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
                                </tr>
                                <tr ng-if="!GVC.carregando_dados_modal_grupos" ng-repeat-start="grupo in GVC.grupos" class="pointer hoverable"
                                    ng-class="{'selected': grupo.id == GVC.grupo_selecionado.id}" ng-click="GVC.selecionar_grupo(grupo)">
                                    <td class="align-center valign-middle" style="border-right: none;">
                                        <i class="glyphicon"
                                            ng-class="grupo.id == GVC.grupo_selecionado.id ? 'glyphicon-chevron-up' : 'glyphicon-chevron-down'"></i>
                                    </td>
                                    <td class="valign-middle" style="border-left: none;">
                                        <span ng-if="!GVC.editando_grupos.includes(grupo.id)">{{ grupo.grupo }} ({{ grupo.vendedores.length }})</span>
                                        <input ng-if="GVC.editando_grupos.includes(grupo.id)" type="text"
                                            class="form-control input-sm align-center" id="input-nome-grupo-{{ grupo.id }}"
                                            value="{{ grupo.grupo }}">
                                    </td>
                                    <td class="align-center valign-middle">
                                        <div ng-if="!GVC.editando_grupos.includes(grupo.id)">
                                            <button title="Editar grupo" class="btn btn-vsm btn-primary"
                                                ng-click="$event.stopPropagation(); GVC.habilitar_edicao(grupo.id);"><i
                                                    class="glyphicon glyphicon-pencil"></i></button>
                                            <button title="Excluir grupo" class="btn btn-vsm btn-danger"
                                                ng-click="$event.stopPropagation();"
                                                ng-really-message="Deseja realmente excluir o grupo?"
                                                ng-really-click="$event.stopPropagation(); GVC.excluir_grupo(grupo.grupo);"><i
                                                    class="glyphicon glyphicon-trash"></i></button>
                                        </div>
    
                                        <div ng-if="GVC.editando_grupos.includes(grupo.id)">
                                            <button title="Salvar grupo" class="btn btn-vsm btn-success"
                                                ng-click="$event.stopPropagation(); GVC.salvar_edicao(grupo.id, grupo.grupo);"><i
                                                    class="glyphicon glyphicon-ok"></i></button>
                                            <button title="Cancelar edição" class="btn btn-vsm btn-danger"
                                                ng-click="$event.stopPropagation(); GVC.cancelar_edicao(grupo.id);"><i
                                                    class="glyphicon glyphicon-ban-circle"></i></button>
                                        </div>
                                    </td>
                                </tr>
                                <tr ng-repeat-end>
                                    <td colspan="4" style="padding: 0 !important;">
                                        <div id="grupo-os-vendedores-{{ grupo.id }}"
                                            class="grupo-os-vendedores grupo-{{ grupo.id }} collapse">
                                            <div class="vert-align">
                                                <table class="table table-warning table-bordered" style="margin-bottom: 0px;">
                                                    <thead ng-if="grupo.vendedores.length > 0">
                                                        <th style="padding-left: 40px !important;">Vendedor</th>
                                                        <th></th>
                                                    </thead>
                                                    <tbody>
                                                        <tr class="bg-warning" ng-if="grupo.vendedores.length < 1">
                                                            <td class="valign-middle" colspan="4" style="padding-left: 40px !important;">
                                                                Não há vendedores vinculados a este grupo.
                                                            </td>
                                                        </tr>
                                                        <tr ng-repeat="vendedor in grupo.vendedores">
                                                            <td style="padding-left: 40px !important;">{{ vendedor }}</td>
                                                            <td class="align-center" style="width: 80px;">
                                                                <button class="btn btn-danger btn-vsm"
                                                                    ng-really-message="Deseja realmente excluir o vendedor deste grupo?"
                                                                    ng-really-click="GVC.excluir_vendedor(grupo.id, vendedor)">
                                                                    <i class="glyphicon glyphicon-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
    
                <div class="input-group input-group-sm">
                    <span class="input-group-addon">Novo grupo:</span>
                    <input class="form-control" type="text" ng-model="GVC.novo_grupo.nome">
                    <span class="input-group-btn">
                        <button class="btn btn-primary btn-sm" ng-click="GVC.adicionar_grupo()">Adicionar</button>
                    </span>
                </div>
            </div>
            <div class="td" style="padding-left: 5px;">
    
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">Vendedores ainda não agrupados</h3>
                    </div>
                    <div class="panel-body" style="padding: 0px; max-height: 400px; overflow-y: auto;">
                        <input type="text" class="form-control" ng-model="search" placeholder="Pesquisar...">
                        <table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
                            <tbody>
                                <tr ng-if="GVC.carregando_dados_modal_grupos">
                                    <td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
                                </tr>
                                <tr ng-if="!GVC.carregando_dados_modal_grupos" ng-repeat="vendedor in GVC.vendedores_sem_grupo | filter:search">
                                    <td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ vendedor }}</td>
                                    <td class="align-center valign-middle">
                                        <button title="Adicionar vendedor ao grupo selecionado" class="btn btn-vsm btn-success" ng-disabled="(GVC.grupo_selecionado | json) == '{}'" ng-really-message="Deseja realmente adicionar este vendedor ao grupo <b>{{ GVC.grupo_selecionado.grupo }}</b>?" ng-really-click="GVC.adicionar_vendedor(vendedor);"><i
                                                class="glyphicon glyphicon-plus"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
    
            </div>
        </div>
    </div>
    * É necessário primeiro selecionar o grupo, para adicionar um vendedor ao grupo.
</div>