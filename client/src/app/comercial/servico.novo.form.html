<div class="modal" id="frmnovoservico" tabindex="-1" role="dialog" aria-labelledby="frmnovoservico" aria-hidden="true"
   modal="showModal" close="cancel()" style=" z-index: 1045;">

   <div class="modal-dialog" style="width: 800px;">
      <div class="modal-content">

         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" id="frmnovoservico_fechar">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title text-center">
               Novo serviço
            </h4>
         </div>

         <!-- Modal Body -->
         <div class="modal-body" style="padding-bottom: 15px;">
            <div class="servicos-container novo text-center">
                <table class="table" style="margin-bottom: 10px;">
                    <thead>
                        <tr>
                            <th>Serviço *</th>
                            <th>Descrição</th>
                            <th class="text-center">Ordem *</th>
                            <th class="text-center">Ativo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" ng-model="PLN.novoServico.servico" />
                            </td>
                            <td>
                                <input type="text" class="form-control" ng-model="PLN.novoServico.descricao" />
                            </td>
                            <td>
                                <input type="number" class="form-control text-center" ng-model="PLN.novoServico.ordem">
                            </td>
                            <td>
                                <select class="form-control text-center" ng-model="PLN.novoServico.ativo">
                                    <option ng-value="true">SIM</option>
                                    <option ng-value="false">NÃO</option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <span class="text-info">
                    Se nenhuma das opções abaixo for marcada, o serviço será adicionado na lista de serviços, mas ainda não será vinculado a nenhum plano.
                    <br>
                    Dessa forma, será necessário vinculá-lo aos planos manualmente, posteriormente.
                </span>
            
                <br>
                <br>
                <label for="novoServicoTodosPlanosFibra">
                    <input type="checkbox" id="novoServicoTodosPlanosFibra" ng-model="PLN.novoServico.todosPlanosFibra">
                    Vincular este serviço a todos os planos FIBRA
                </label>
                <br>
                <label for="novoServicoTodosPlanosCdmht">
                    <input type="checkbox" id="novoServicoTodosPlanosCdmht" ng-model="PLN.novoServico.todosPlanosCdmht">
                    Vincular este serviço a todos os planos CDMHT
                </label>
                <br>
                <label for="novoServicoTodosPlanosRadio">
                    <input type="checkbox" id="novoServicoTodosPlanosRadio" ng-model="PLN.novoServico.todosPlanosRadio">
                    Vincular este serviço a todos os planos RÁDIO
                </label>
            </div>
         </div>
         <!-- Modal Footer -->
         <div class="modal-footer">
            <button class="btn btn-primary" ng-disabled="PLN.novoServico.servico == '' || PLN.novoServico.ordem == '' || PLN.novoServico.ordem == null" ng-really-message="Deseja realmente adicionar este serviço?" ng-really-click="PLN.adicionarServico()">
                <i class="fa fa-plus" style="margin-right: 5px;"></i> Adicionar
            </button>
         </div>
      </div>
   </div>
</div>