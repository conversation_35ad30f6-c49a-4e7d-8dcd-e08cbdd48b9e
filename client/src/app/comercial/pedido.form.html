<div class="modal" id="frmpedido" tabindex="-1" role="dialog" aria-labelledby="frmpedido" aria-hidden="true"
   modal="showModal" close="cancel()" style="z-index: 1049;">

   <div class="modal-dialog modal-lg">
      <div class="modal-content">

         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title" id="frmparametroslabel" ng-if="PC.selecionado.pessoa=='F'">
               Pedido #{{PC.selecionado.id}} - {{PC.selecionado.nome}}
            </h4>
            <h4 class="modal-title" id="frmparametroslabel" ng-if="PC.selecionado.pessoa=='J'">
               Pedido #{{PC.selecionado.id}} - {{PC.selecionado.razao_social}}
            </h4>

         </div>

         <!-- Modal Body -->
         <div class="modal-body">

            <div class="row">
               <ul class="nav nav-tabs">
                  <li class="active">
                     <a data-target="#dados" data-toggle="tab" style="cursor: pointer;"
                        ng-click="PC.setActiveTab('dados')">
                        <i class="glyphicon glyphicon-list-alt"></i> Dados</a>
                  </li>
                  <li ng-class="{'highlight': PC.selecionado.vendedor.length > 0}">
                     <a data-target="#documentos" data-toggle="tab" style="cursor: pointer;"
                        ng-click="PC.setActiveTab('documentos')">
                        <i class="glyphicon glyphicon-list-alt"></i>
                        Documentos</a>
                  </li>

                  <li>
                     <a data-target="#contrato" data-toggle="tab" style="cursor: pointer;"
                        ng-click="PC.setActiveTab('contrato')">
                        <i class="glyphicon glyphicon-list-alt"></i> Contrato</a>
                  </li>
                  <li>
                     <a data-target="#historico" data-toggle="tab" style="cursor: pointer;"
                        ng-click="PC.setActiveTab('historico')">
                        <i class="glyphicon glyphicon-list-alt"></i>
                        Histórico</a>
                  </li>
               </ul>
               <div class="tab-content">

                  <div id="dados" class="tab-pane fade in active">

                     <div class="row">

                        <div class="col col-sm-6 col-md-3 text-center d-flex flex-column">
                           <label>Data do pedido</label>
                           <span>{{PC.selecionado.pedido_termino | amDateFormat:'DD/MM/YYYY dddd HH:mm:ss'}}</span>
                        </div>

                        <div class="col col-sm-6 col-md-3 text-center d-flex flex-column">
                           <label>Atendente</label>
                           <span class="label label-primary"
                              ng-if="PC.selecionado.atendente == undefined || PC.selecionado.atendente == ''">Nenhum
                              Atendente</span>
                           <span
                              ng-if="PC.selecionado.atendente != undefined && PC.selecionado.atendente != ''"><b>{{PC.selecionado.atendente}}</b></span>
                        </div>

                        <div class="col col-sm-6 col-md-3 text-center d-flex flex-column">
                           <label>Status</label>
                           <span class="label label-primary"
                              ng-if="PC.selecionado.status == 1 && PC.selecionado.pedido_confirmado == 0">Confirmação
                              do
                              E-mail</span>
                           <span class="label label-success"
                              ng-if="PC.selecionado.status == 1 && PC.selecionado.pedido_confirmado == 1">E-mail
                              Confirmado</span>
                           <span class="label label-primary" ng-if="PC.selecionado.status == 2">Confirmação de
                              disponibilidade</span>
                           <span class="label label-primary" ng-if="PC.selecionado.status == 3">Validação /
                              Aprovação</span>
                           <span class="label label-success" ng-if="PC.selecionado.status == 4">Agendamento
                              Confirmado</span>
                           <span class="label label-success" ng-if="PC.selecionado.status == 5">Instalação
                              Realizada</span>
                        </div>

                        <div class="col col-sm-6 col-md-3 text-center d-flex flex-column">
                           <label for="">Conclusão</label>
                           <span class="label label-primary"
                              ng-if="PC.selecionado.conclusao == 'ABERTO' && (PC.selecionado.atendente == undefined || PC.selecionado.atendente == '')">Aberto</span>
                           <span ng-if="PC.selecionado.conclusao!=='ABERTO'" class="label"
                              ng-class="[{'label-success': PC.selecionado.conclusao == 'SUCESSO'},
                                                        {'label-primary': PC.selecionado.conclusao == 'ABERTO'},
                                                        {'label-danger': PC.selecionado.conclusao == 'ERRO_CPF' || PC.selecionado.conclusao == 'INVALIDO' || PC.selecionado.conclusao == 'INVALIDO_MSG'}]">{{PC.selecionado.conclusao}}</span>
                           <span
                              ng-if="PC.selecionado.conclusao=='INVALIDO' || PC.selecionado.conclusao=='INVALIDO_MSG'">{{PC.selecionado.conclusao_msg}}</span>

                           <div
                              ng-if="(PC.selecionado.conclusao=='ABERTO') && (PC.selecionado.atendente != undefined && PC.selecionado.atendente != '')"
                              class="btn-group" uib-dropdown keyboard-nav dropdown-append-to="appendToEl"
                              style="margin: 0 auto;">
                              <button id="btn-append-to" type="button" class="btn btn-primary" uib-dropdown-toggle>
                                 Invalidar pedido <span class="caret"></span>
                              </button>
                              <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to">
                                 <li role="menuitem"><a href="#"
                                       ng-if="(PC.selecionado.pedido_confirmado != 0 && PC.selecionado.status < 3)"
                                       ng-click="PC.conclusao(PC.selecionado, 'ERRO_CPF', undefined)">CPF com
                                       pendência</a></li>
                                 <li role="menuitem"><a href="#"
                                       ng-click="PC.conclusao(PC.selecionado, 'INVALIDO', undefined)">Invalidar
                                       sem aviso ao cliente</a></li>
                                 <li role="menuitem" ng-if="PC.selecionado.pedido_confirmado == 1"><a href="#"
                                       ng-click="PC.conclusao(PC.selecionado, 'INVALIDO_MSG', undefined)">Invalidar
                                       com aviso ao cliente</a></li>
                              </ul>
                           </div>
                        </div>
                     </div>

                     <div class="row">
                        <div class="col col-sm-6 text-center d-flex flex-column">
                           <label for="">Vendedor</label>
                           <span ng-if="PC.selecionado.vendedor" style="color: darkorange; font-weight: 600;">{{
                              PC.selecionado.vendedor }}</span>
                           <span ng-if="!PC.selecionado.vendedor"><b>Nenhum</b> (o pedido foi realizado pelo
                              próprio cliente)</span>
                        </div>

                        <div class="col col-sm-6 text-center d-flex flex-column">
                           <label for="">Número do termo</label>
                           <span>{{PC.selecionado.termo_numero}}</span>
                        </div>
                     </div>

                     <div class="row">
                        <div ng-if="PC.selecionado.pessoa=='F'"
                           class="col col-sm-5 col-md-5 text-center d-flex flex-column">
                           <label for="">Nome (pessoa física)</label>
                           <span>{{PC.selecionado.nome}}</span>
                        </div>
                        <div ng-if="PC.selecionado.pessoa=='J'"
                           class="col col-sm-5 col-md-5 text-center d-flex flex-column">
                           <label for="">Razão social (pessoa jurídica)</label>
                           <span>{{PC.selecionado.razao_social}}</span>
                        </div>

                        <div ng-if="PC.selecionado.pessoa=='F'"
                           class="col col-sm-4 col-md-4 text-center d-flex flex-column">
                           <label for="">Data de nascimento</label>
                           <span>{{PC.selecionado.data_nascimento | amDateFormat:'DD/MM/YYYY'}}</span>
                        </div>
                        <div ng-if="PC.selecionado.pessoa=='J'"
                           class="col col-sm-4 col-md-4 text-center d-flex flex-column">
                           <label for="">Nome fantasia</label>
                           <span>{{PC.selecionado.nome_fantasia}}</span>
                        </div>

                        <div ng-if="PC.selecionado.pessoa=='F'"
                           class="col col-sm-3 col-md-3 text-center d-flex flex-column">
                           <label for="">CPF</label>
                           {{PC.selecionado.cpf}}
                        </div>

                        <div ng-if="PC.selecionado.pessoa=='J'"
                           class="col col-sm-3 col-md-3 text-center d-flex flex-column">
                           <label for="">CNPJ</label>
                           {{PC.selecionado.cnpj}}
                        </div>
                     </div>

                     <div class="row">
                        <div class="col text-center d-flex flex-column"
                           ng-class="{'col-sm-6': PC.selecionado.pessoa == 'F', 'col-sm-5': PC.selecionado.pessoa == 'J'}">
                           <label for="">E-mail</label>
                           <a href="mailto:{{PC.selecionado.email}}">{{PC.selecionado.email}}</a>
                        </div>

                        <div class="col text-center d-flex flex-column"
                           ng-class="{'col-sm-6': PC.selecionado.pessoa == 'F', 'col-sm-4': PC.selecionado.pessoa == 'J'}">
                           <label for="">Celular</label>
                           <div>
                              {{PC.selecionado.celular}} <span ng-if="PC.selecionado.whatsapp == 1"
                                 style="font-size: 12px; font-weight: bold; color: green;"><i
                                    class="fa fa-whatsapp"></i></span>
                           </div>
                        </div>

                        <div ng-if="PC.selecionado.pessoa=='J'" class="col col-sm-3 text-center d-flex flex-column">
                           <label for="">Inscrição Estadual</label>
                           <span>{{PC.selecionado.inscricao_estadual}}</span>
                        </div>
                     </div>

                     <div class="row">
                        <div class="col col-sm-6 text-center d-flex flex-column">
                           <label for="">Endereço de instalação</label>
                           <span>{{PC.selecionado.endereco_instalacao__logradouro}}
                              {{PC.selecionado.endereco_instalacao__numero}}
                              {{PC.selecionado.endereco_instalacao__complemento}}</span>
                           <span><b>Bairro:</b> {{PC.selecionado.endereco_instalacao__bairro}}</span>
                           <span><b>Cidade:</b> {{PC.selecionado.endereco_instalacao__cidade}}</span>

                           <label class="text-warning">Agendamento da instalação/visita:</label>
                           <span>
                              <b>
                                 {{ PC.selecionado.status < 4 ? '-' : (PC.selecionado.visita_necessaria==1
                                    ? 'A OS de agendamento foi aberta' : 'Visita técnica não necessária' ) }} </b>
                           </span>

                           <div ng-if="PC.selecionado.total_cdms >= 1">
                              <label for="">CDM</label>
                              {{PC.selecionado.cdm_nome}} ({{PC.selecionado.cdm_tecnologia}})
                           </div>
                        </div>

                        <div class="col col-sm-6 text-center d-flex flex-column">
                           <label for="">Endereço de correspondência</label>
                           <span>{{PC.selecionado.endereco_cobranca__logradouro}}
                              {{PC.selecionado.endereco_cobranca__numero}}
                              {{PC.selecionado.endereco_cobranca__complemento}}</span>
                           <span><b>Bairro:</b> {{PC.selecionado.endereco_cobranca__bairro}}</span>
                           <span><b>Cidade:</b> {{PC.selecionado.endereco_cobranca__cidade}}</span>
                        </div>
                     </div>

                     <div class="row">
                        <div class="col col-sm-6 text-center d-flex flex-column text-success">
                           <label for="">Pacote contratado</label>
                           {{PC.selecionado.plano_nome}} ({{PC.selecionado.plano_velocidade}})
                           <span ng-bind-html="PC.selecionado.plano_legenda"></span>
                           <span>- Valor do plano: R$ <b>{{ PC.selecionado.valor_plano }}</b></span>
                           <span ng-if="PC.selecionado.roteador_incluso == 1">
                              - Valor roteador: R$ <b>{{PC.selecionado.valor_roteador}}</b>
                              <br>
                           </span>
                           <span ng-if="PC.selecionado.decoder_incluso == 1">
                              - Valor decoder: R$ <b>{{PC.selecionado.valor_decoder}}</b>
                              <br>
                           </span>
                           <span>
                              Plano Mesh:
                              <span ng-if="!PC.selecionado.plano_mesh">não contratado</span>
                              <span ng-if="PC.selecionado.plano_mesh"><b>{{ PC.selecionado.plano_mesh }}</b></span>
                           </span>
                           <span ng-if="PC.selecionado.plano_mesh">
                              - Valor Mesh: R$ <b>{{ PC.selecionado.valor_mesh }}</b>
                              <br>
                           </span>

                           <span>
                              SKY+:
                              <span ng-if="!PC.selecionado.directvgo">não contratado</span>
                              <span ng-if="PC.selecionado.directvgo"><b>contratado</b></span>
                           </span>

                           <span ng-if="PC.selecionado.directvgo">
                              - Valor SKY+: R$ <b>{{ PC.selecionado.directvgo_valor }}</b>
                              <br>
                           </span>

                           <span>
                              Globoplay:
                              <span ng-if="!PC.selecionado.globoplay">não contratado</span>
                              <span ng-if="PC.selecionado.globoplay"><b>contratado</b></span>
                           </span>

                           <span ng-if="PC.selecionado.globoplay">
                              - Valor Globoplay: R$ <b>{{ PC.selecionado.globoplay_valor }}</b>
                              <br>
                           </span>

                           <span style="border-top: 1px solid #BBB; margin: 5px 0px; padding-top: 5px;"><b>Valor Total: R$ {{PC.selecionado.valor_total}}</b></span>
                        </div>

                        <div class="col col-sm-6 text-center d-flex flex-column">
                           <label for="">Contrato atual</label>
                           <div ng-if="PC.selecionado.migracao == 1" class="text-center d-flex flex-column">
                              <span><b>Código do assinante:</b> {{PC.selecionado.contrato_atual.codassinante}}</span>
                              <span><b>Número do contrato:</b> {{PC.selecionado.contrato_atual.numcontrato}}</span>
                              <span><b>Username:</b> {{PC.selecionado.contrato_atual.username}}</span>
                              <span><b>Nome do plano:</b> {{PC.selecionado.contrato_atual.nomeplano}}</span>
                           </div>
                        </div>
                     </div>

                     <div class="row">
                        <div class="col col-sm-2 text-center d-flex flex-column">
                           <label for="">Migração de plano</label>
                           <span>{{PC.selecionado.migracao == 1 ? 'SIM' : 'NÃO'}}</span>
                        </div>

                        <div class="col col-sm-2 text-center d-flex flex-column">
                           <label for="">Downgrade de velocidade</label>
                           <span>{{PC.selecionado.downgrade == 1 ? 'SIM' : 'NÃO'}}</span>
                        </div>

                        <div class="col col-sm-4 text-center d-flex flex-column">
                           <label for="">Motivo do downgrade</label>
                           <span>{{ PC.selecionado.motivo_downgrade }}</span>
                        </div>

                        <div class="col col-sm-4 text-center d-flex flex-column">
                           <label for="">Aprovação do downgrade</label>
                           <b>{{PC.selecionado.aprovacao_downgrade}}</b>
                           <div ng-if="PC.selecionado.aprovacao_downgrade == 'PENDENTE'">
                              <button authorize="['ger_pedidos.read', 'ger_pedidos.write']"
                                 class="btn btn-sm btn-primary"
                                 ng-really-message="Deseja realmente aprovar o pedido de downgrade?"
                                 ng-really-click="PC.setAprovacaoDowngrade(PC.selecionado.id, 'APROVADO')">
                                 Aprovar
                              </button>
                              <button authorize="['ger_pedidos.read', 'ger_pedidos.write']"
                                 class="btn btn-sm btn-danger"
                                 ng-really-message="Deseja realmente negar o pedido de downgrade?"
                                 ng-really-click="PC.setAprovacaoDowngrade(PC.selecionado.id, 'NÃO APROVADO')">
                                 Negar
                              </button>
                           </div>
                        </div>
                     </div>

                     <div ng-if="PC.selecionado.vendedor" class="row">
                        <div class="col col-sm-4 text-center d-flex flex-column">
                           <label for="">Dia de vencimento desejado</label>
                           <span>{{PC.selecionado.dia_vencimento}}</span>
                        </div>

                        <div class="col col-sm-4 text-center d-flex flex-column">
                           <label for="">Tipo de envio do boleto</label>
                           <span>{{PC.selecionado.tipo_envio_boleto}}</span>
                        </div>

                        <div class="col col-sm-4 text-center d-flex flex-column">
                           <label for="">Período preferencial para instalação</label>
                           <span>{{PC.selecionado.periodo_preferencial}}</span>
                        </div>
                     </div>

                     <div ng-if="PC.selecionado.vendedor" class="row">
                        <div class="col col-sm-12 text-center d-flex flex-column">
                           <label for="">Observações do vendedor</label>
                           <span>{{PC.selecionado.observacao_vendedor}}</span>
                        </div>
                     </div>

                  </div>

                  <div id="documentos" class="tab-pane fade in">

                     <div class="pre-scrollable" style="max-height: 450px;">
                        <div class="image-set m-t-20 d-flex" style="gap: 2px; flex-wrap: wrap;">

                           <a ng-if="documento.isImage" ng-repeat="documento in PC.selecionado.documentos" data-magnify="gallery" data-src=""
                              data-caption="{{ PC.getDocumentCaption(documento.doc_type, documento.doc_side) }}"
                              data-group="a"
                              href="https://contratar.telemidia.net.br/common/api/get_documents/?filename={{documento.filename}}&t={{PC.selecionado.token}}">
                              <figure class="fig">
                                 <img
                                    src="https://contratar.telemidia.net.br/common/api/get_documents/?filename={{documento.filename}}&t={{PC.selecionado.token}}"
                                    width="200" height="200">
                                 <figcaption class="image-caption-text">{{ PC.getDocumentCaption(documento.doc_type,
                                    documento.doc_side) }}
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'identidade' && PC.selecionado.identidade_reenvio==1">Reenvio
                                       Pendente</span>
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'selfie' && PC.selecionado.selfie_reenvio==1">Reenvio
                                       Pendente</span>
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'cpf' && PC.selecionado.cpf_reenvio==1">Reenvio
                                       Pendente</span>
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'cnpj' && PC.selecionado.cnpj_reenvio==1">Reenvio
                                       Pendente</span>
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'contrato_social' && PC.selecionado.contrato_socialreenvio==1">Reenvio
                                       Pendente</span>
                                    <span class="label label-warning"
                                       ng-if="documento.doc_type == 'endereco' && PC.selecionado.endereco_reenvio==1">Reenvio
                                       Pendente</span>
                                 </figcaption>
                              </figure>
                           </a>

                          <a ng-if="!documento.isImage" ng-repeat="documento in PC.selecionado.documentos"
                            href="https://contratar.telemidia.net.br/common/api/get_documents/?filename={{documento.filename}}&t={{PC.selecionado.token}}" target="_blank"
                            class="pedido-documento-item">
                            <div class="file-extension d-inline-block">
                              {{ documento.extension.toUpperCase() }}
                            </div>
                            <div class="file-caption image-caption-text d-inline-block">
                              {{ PC.getDocumentCaption(documento.doc_type, documento.doc_side) }}
                            </div>
                          </a>

                        </div>
                     </div>

                  </div>
                  <div id="contrato" class="tab-pane fade in" style="min-height: 500px;">

                     <h4 ng-if="PC.selecionado.contrato_url == undefined || PC.selecionado.contrato_url == ''">
                        Este pedido ainda não possui o contrato
                     </h4>
                     <object data="{{PC.selecionado.contrato_url || trustAsResourceUrl }}" type="application/pdf"
                        width="100%" style="min-height: 500px;" height="100% !important"
                        ng-if="PC.selecionado.contrato_url !== undefined && PC.selecionado.contrato_url !== ''">
                        <embed src="{{PC.selecionado.contrato_url || trustAsResourceUrl }}" type="application/pdf" />
                     </object>
                  </div>
                  <div id="historico" class="tab-pane fade in">
                     <table class="table table-striped table-bordered">
                        <thead>
                           <th class="vert-align text-center">Data</th>
                           <th class="vert-align text-center">Status</th>
                           <th class="vert-align text-center">Atendente</th>
                        </thead>
                        <tbody>
                           <tr ng-repeat="historico in PC.selecionado.historicos">
                              <td class="vert-align text-center">
                                 {{historico.data | amDateFormat:'DD/MM/YYYY dddd HH:mm:ss'}}</td>
                              <td class="vert-align text-center">
                                 {{historico.status}}</td>
                              <td class="vert-align text-center">
                                 {{historico.atendente}}</td>

                           </tr>
                        </tbody>
                     </table>
                  </div>
               </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
               <button class="btn btn-primary btn-default" data-toggle="modal" data-target="#frmreenvio"
                  class="btn btn-primary btn-default pull-left" ng-if="PC.activeTab=='documentos'"
                  ng-disabled="(PC.selecionado.conclusao!='ABERTO'  || PC.selecionado.status >= 3) || (PC.selecionado.atendente == undefined || PC.selecionado.atendente == '')"><i
                     class="fa fa-share-square"></i> Reenvio de
                  Documentos</button>
               <button class="btn btn-primary btn-danger" ng-click="PC.conclusao(PC.selecionado, 'ERRO_CPF', undefined)"
                  class="btn btn-danger btn-default pull-left" ng-if="PC.activeTab=='documentos'"
                  ng-disabled="(PC.selecionado.conclusao != 'ABERTO' || PC.selecionado.pedido_confirmado == 0 || PC.selecionado.status >= 3) || (PC.selecionado.atendente == undefined || PC.selecionado.atendente == '')"><i
                     class="fa fa-user-times"></i> CPF com
                  pendência</button>
               <button type="button" class="btn btn-default" data-dismiss="modal">
                  Fechar
               </button>
            </div>
         </div>
      </div>
   </div>
