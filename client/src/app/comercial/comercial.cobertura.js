(function () {
    'use strict';

    angular
        .module('app')
        .controller('ComercialCoberturaController', ComercialCoberturaController);

    /** @ngInject */
    function ComercialCoberturaController($http, API_CONFIG, NgMap, toaster, $rootScope, dadosAPI, limitToFilter, focus, AuthorizationService) {

        var vm = this;
        vm.autocompleteCallback = autocompleteCallback;
        vm.posicao = {};
        vm.googleMapsUrl="https://maps.googleapis.com/maps/api/js?key=AIzaSyCQKwsTMK2VoL5toABFONpKe7ORJrMrLOA&libraries=places";
        vm.GoogleMapsGeocodeUrl = "https://maps.googleapis.com/maps/api/geocode/json?key=AIzaSyCQKwsTMK2VoL5toABFONpKe7ORJrMrLOA";
       
        vm.geocoder = null;

        vm.pesquisa = {};
        vm.coberturas = [];
        vm.contratos = [];
        vm.cpfvalido = false;
        vm.prospect = {cobertura: null, exito: null, logradouro: null, numero: null, complemento: null, cliente: '0', plano: null};
        if(AuthorizationService.Authorize('["terceiro.write"]')){
          vm.prospect.atendimento = 'externo';
          vm.terceiro = true;
        } else {
          vm.prospect.atendimento = 'presencial'; 
          vm.terceiro = false;
        }
        
        vm.enderecoChange = enderecoChange;
        vm.cpfChange = cpfChange;
        vm.verificar = verificar;
        vm.consultaCobertura = consultaCobertura;
        vm.getPlanos = getPlanos;
        vm.voltar = voltar;
        vm.finaliza = finaliza;
        vm.prosseguir = prosseguir;
        vm.consultacpf = consultacpf;
        vm.etapa = 1;
        vm.dropdown = false;
        vm.opened = false;
        vm.open = open;

        activate();

        function activate(){
          vm.cidades = dadosAPI[0];
        }

        function open() {
            vm.opened = true;
          };

        function autocompleteCallback(){
          vm.place = this.getPlace();

         angular.forEach(vm.place.address_components, function(value, key) {
            if(value.types[0] == 'street_number'){
                vm.pesquisa.numero = parseInt(value.long_name);
            }
          });

          vm.dropdown = true;
        }

        NgMap.getMap({id:'mapa_cobertura'}).then(function(map) {
          vm.map = map;

          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
              var geolocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
              };
              vm.limite = {center: geolocation, radius: 80000};
            });
          } else {
            var geolocation = {
              lat: -21.8533921,
              lng: -46.5688472
            };
            vm.limite = {center: geolocation, radius: 80000};
          }

          vm.geocoder = new google.maps.Geocoder();
          vm.sv = new google.maps.StreetViewService();

          google.maps.event.addListener(vm.map, 'center_changed', function() {
            vm.posicao = JSON.parse(JSON.stringify(vm.map.center));
          });

          vm.panorama = vm.map.getStreetView({id:'panorama_cobertura'});

          google.maps.event.addListener(vm.panorama, 'pov_changed', function() {
            vm.prospect.pov_heading = vm.panorama.getPov().heading;
            vm.prospect.pov_pitch = vm.panorama.getPov().pitch;
          });
        });

        function enderecoChange(){
          if(vm.pesquisa.endereco == undefined){
              vm.pesquisa.numero = '';
          }
        };


        function cpfChange(){
            vm.cpfvalido = false;
            vm.contratos = [];      
            vm.contratoselecionado = undefined;      
        };

        function verificar(){

            vm.dropdown = false;

            var bNumero = false;

            angular.forEach(vm.place.address_components, function(value, key) {

                if(value.types[0] == 'route'){
                    vm.pesquisa.logradouro = value.long_name;
                }

                if(value.types[0] == 'sublocality_level_1'){
                    vm.pesquisa.bairro = value.long_name;
                }

                if(value.types[0] == 'administrative_area_level_2'){
                    vm.pesquisa.cidade = value.long_name;
                }

                if(value.types[0] == 'street_number'){
                    bNumero = true;
                    vm.pesquisa.place_id_completo = vm.place.place_id;
                }
            });

            vm.pesquisa.endereco = vm.pesquisa.logradouro + ', ' + vm.pesquisa.numero + ', '+vm.pesquisa.cidade;

            if(!bNumero){

                vm.pesquisa.place_id_logradouro = vm.place.place_id;

                vm.geocoder.geocode({'address': vm.pesquisa.endereco}, function(results, status) {
                  if (status == google.maps.GeocoderStatus.OK) {
                    var place = results[0];
                    
                    vm.pesquisa.place_id_completo = place.place_id;

                    //vm.map.getStreetView().setPosition(place.geometry.location);
                   // console.log(vm.panorama);
                    
                    //vm.panorama.setPosition(place.geometry.location);
                    
                    vm.posicao = JSON.parse(JSON.stringify(place.geometry.location));
                   // vm.panorama.setPosition(vm.posicao, 200);

                    vm.sv.getPanoramaByLocation(vm.posicao, 200, function(data, status){
                        if (status == google.maps.StreetViewStatus.OK) {
                          vm.panorama.setPano(data.location.pano);
                          vm.panorama.visible = true;
                          google.maps.event.trigger(vm.panorama, 'resize');
                        } else {
                          toaster.pop('error', "Sem Panorama", "Não existe panorama próximo ao local!"); 
                        }
                        
                    });
                  
                    
                    //vm.map.setZoom(18);
                  }
                  else {
                    //console.log('something went wrong'); // never displayed
                  }
                });

            } else {
                //vm.map.getStreetView().setPosition(vm.place.geometry.location);

                // Verifica o place_id apenas da rua
                vm.geocoder.geocode({'address': vm.pesquisa.logradouro + ', '+vm.pesquisa.cidade}, function(results, status) {
                    if (status == google.maps.GeocoderStatus.OK) {
                      var place = results[0];
                      
                      vm.pesquisa.place_id_logradouro = place.place_id;
                    }
                    else {
                      //console.log('something went wrong'); // never displayed
                    }
                });
                
                vm.posicao = JSON.parse(JSON.stringify(vm.place.geometry.location));

                vm.sv.getPanoramaByLocation(vm.posicao, 200, function(data, status){
                    if (status == google.maps.StreetViewStatus.OK) {
                      vm.panorama.setPano(data.location.pano);
                      vm.panorama.visible = true;
                      google.maps.event.trigger(vm.panorama, 'resize');
                    } else {
                      toaster.pop('error', "Sem Panorama", "Não existe panorama próximo ao local!"); 
                    }
                    
                });

                //vm.panorama.setPosition(vm.place.geometry.location);
               

                //vm.map.setCenter(vm.place.geometry.location);
                //vm.map.setZoom(18);
            }
            vm.etapa = 3;
            focus('btnconfirmo');

        }

        function consultaCobertura(){
           vm.planos = [];
           vm.prospect.plano = null; 
           $http({
                url: API_CONFIG.url + '/comercial/coberturav2',
                method: "POST",
                data: {'logradouro': vm.pesquisa.logradouro,
                        'numero': vm.pesquisa.numero, 
                        'complemento': vm.pesquisa.complemento,
                        'cidade': vm.pesquisa.cidade,
                        'bairro' : vm.pesquisa.bairro,
                        'lat' : vm.posicao.lat, 'lng': vm.posicao.lng,
                        'place_id_completo' : vm.pesquisa.place_id_completo,
                        'place_id_logradouro' : vm.pesquisa.place_id_logradouro,
                        'usuario' : $rootScope.operador.username
                    }
           })
           .then(function(response) {
              vm.cobertura = response.data;
              vm.etapa = 4;
          });
        }

        function getPlanos(){

                var cdm = 0;
                //if(vm.cobertura.clientes > 5) cdm = 1; else cdm=0;
                var ht = vm.prospect.cobertura.ht;
                var cdm = vm.prospect.cobertura.cdm;

                if(ht == 0 && cdm == 1 && vm.cobertura.area_fibra == true){
                    ht = 1;
                }
                $http({
                     url: API_CONFIG.url + '/comercial/planos?cobertura='+ vm.prospect.cobertura.cobertura +'&tecnologia='+vm.prospect.cobertura.tecnologia+'&cdm='+cdm+'&ht='+ht,
                     method: "GET"
                })
                .then(function(response) {
                vm.planos = response.data;
                },
                function(response) {
                });

        }

        function voltar(){
            vm.etapa = vm.etapa - 1;
            vm.prospect.cobertura = null;
        }

        function prosseguir(){

            
            vm.etapa = vm.etapa + 1;
            vm.pesquisa.endereco = vm.contratoselecionado.logradouro;
            vm.pesquisa.numero = vm.contratoselecionado.numero;
            vm.prospect.nome = vm.contratoselecionado.nome;
            vm.prospect.cpf = vm.contratoselecionado.cpf;
            vm.dropdown = false;
            focus('enderecogoogle');
        }


        function finaliza(){
            vm.prospect.endereco = vm.pesquisa.endereco;
            vm.prospect.logradouro = vm.pesquisa.logradouro;
            vm.prospect.numero = vm.pesquisa.numero;
            vm.prospect.complemento = vm.pesquisa.complemento;
            vm.prospect.bairro = vm.pesquisa.bairro;
            vm.prospect.cidade = vm.pesquisa.cidade;
            vm.prospect.latitude = vm.posicao.lat;
            vm.prospect.longitude = vm.posicao.lng;
            vm.prospect.cobertura = vm.prospect.cobertura.tecnologia;
            //vm.prospect.atendimento = 'externo';
            if(vm.cobertura.clientes > 5){
              vm.prospect.cdm = 1;
            } else {
              vm.prospect.cdm = 0;
            }

            if(vm.prospect.cobertura.ht == 0 && vm.cobertura.area_fibra == true){
              vm.prospect.migracao = 1;
            } else {
              vm.prospect.migracao = 0;
            }

            vm.prospect.usuario = $rootScope.operador.username;
            $http({
               url: API_CONFIG.url + '/comercial/prospectv2',
               method: "POST",
               data: vm.prospect
            })
            .then(function(response) {
                var resposta = response.data;
                if(resposta.status == 'OK'){
                    vm.posicao = {};
                    vm.prospect = {cobertura: null, exito: null, logradouro: null, numero: null, complemento: null, cliente: '0', plano: null};
                    if(AuthorizationService.Authorize('["terceiro.write"]')){
                        vm.prospect.atendimento = 'externo';
                    } else {
                        vm.prospect.atendimento = 'presencial'; 
                    }
                    toaster.pop('success', "Registro concluído", "Prospect salvo com sucesso!");
                }
            });

            vm.etapa = 1;
            vm.pesquisa = {};
            
        }

        function consultacpf(cpf){
           return $http({
                 url: API_CONFIG.url + '/comercial/consultacpf?cpf='+cpf,
                 method: "GET"
           }).then(function(response) {
             if(response.data.length == 0){
                vm.cpfvalido = false; 
                toaster.pop('error', "CPF Inválido", "CPF Não Encontrado");
             } else {
               // vm.etapa = vm.etapa + 1;
                vm.contratos = response.data;
                vm.cpfvalido = true; 

                if(vm.contratos.length == 1){
                    vm.contratoselecionado = vm.contratos[0];
                    vm.etapa = vm.etapa + 1;
                    console.log(vm.contratoselecionado);
                    vm.pesquisa.endereco = vm.contratoselecionado.logradouro;
                    vm.pesquisa.numero = vm.contratoselecionado.numero;
                    vm.prospect.nome = vm.contratoselecionado.nome;
                    vm.prospect.cpf = vm.contratoselecionado.cpf;
                    vm.dropdown = false;
                    focus('enderecogoogle');
                }
                

                //vm.pesquisa.endereco = response.data.logradouro;
                //vm.pesquisa.numero = response.data.numero;
                //vm.prospect.nome = response.data.nome;
                //vm.prospect.cpf = response.data.cpf;
                //vm.dropdown = false;
                //focus('enderecogoogle');
             }

               
           });
        }

    }
})();
