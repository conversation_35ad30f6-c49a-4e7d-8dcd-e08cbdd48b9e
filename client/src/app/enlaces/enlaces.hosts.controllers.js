'use strict';

angular.module('app')

    .controller('EnlacesHostCtrl', function ($scope,
                                             $rootScope,
                                             $route,
                                             EnlaceHostService,
                                             HostsServiceJSON,
                                             PopsJSONService,
                                             PopsHostsJSONService,
                                             ServicosJSONService,
                                             LogExclusaoService,
                                             toaster) {

        var enlace = $route.current.params.enlace;
        var Pops = PopsJSONService.getPops();
        Pops.$promise.then(function (data) {
            $scope.pops = data;
        });

        $scope.loadHosts = function (pop) {

            var Hosts = PopsHostsJSONService.getHosts({pop: pop});
            Hosts.$promise.then(function (data) {
                $scope.hosts = data;
            });
            $scope.servicos = {};

        };

        $scope.loadServicos = function (host) {

            var Servicos = ServicosJSONService.getServicos({host: host});
            Servicos.$promise.then(function (data) {
                $scope.servicos = data;
            });

        };

        $scope.saveEnlaceHost = function (enlacehost) {

            var novoEnlaceHost = {};
            novoEnlaceHost = angular.copy(enlacehost);
            novoEnlaceHost.enlace = enlace;
            novoEnlaceHost.idevento = $rootScope.eventoSelecionado.id;
            novoEnlaceHost.username = $rootScope.operador.username;

            EnlaceHostService.insertEnlaceHost(novoEnlaceHost, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Host salvo", "Host adicionado com sucesso!");
                    EnlaceHostService.get({enlace: enlace}, function (data) {
                        $scope.enlacehosts = data.dados;
                    });
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });
        };

        $scope.deleteHost = function (host) {

            host.idevento = $rootScope.eventoSelecionado.id;
            host.username = $rootScope.operador.username;

            EnlaceHostService.deleteEnlaceHost(host, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'enlace_hosts',
                        id: enlace.id,
                        nomeregistro: host.host_servicos,
                        idevento: host.idevento,
                        username: host.username
                    };


                    LogExclusaoService.insertLog(log, function () {

                      

                    });

                    toaster.pop('success', "Host desativado", "Host desativado com sucesso!");
                    host.ativo = 0;

                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };
    });
