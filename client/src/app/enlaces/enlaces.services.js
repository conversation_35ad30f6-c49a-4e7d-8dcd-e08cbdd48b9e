'use strict';

angular.module('app')

    .factory('EnlaceService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/enlaces/:enlace', {},
            {
                getEnlaces: {method: 'GET', isArray: false},
                insertEnlace: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                updateEnlace: {
                    method: 'PUT',
                    params: {enlace: '@enlace'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteEnlace: {
                    method: 'DELETE',
                    params: {
                        enlace: '@enlace'
                    },
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    });