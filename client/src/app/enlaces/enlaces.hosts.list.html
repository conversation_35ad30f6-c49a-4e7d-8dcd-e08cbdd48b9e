<div class="barra">
        <div class="form-group">
            <button class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmhost" data-placement="top" rel="tooltip" type="submit" ng-if="eventoSelecionado.id != ''" title="Incluir Host" authorize="['enlaces.write']">
                <span class="glyphicon glyphicon-plus"></span><br>Incluir
            </button>      
        </div>
    </div>  

    <table class="table table-striped table-hover">
                  <thead>
                <tr>
                  <th>Host</th>
                  <th>Serviço</th>
                  <th>Hardware</th>
                  <th>IP</th>
                  <th>Operador</th>
                  <th>Data</th>
                  <th>Ação</th>    
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="enlacehost in enlacehosts | filter: filtro">
                    <td>{{::enlacehost.host}}</td>
                    <td>{{::enlacehost.hostservicos}}</td>
                    <td>{{::enlacehost.hardware}}</td>
                    <td>{{::enlacehost.ip}}</td>
                    <td>{{::enlacehost.username}}</td>
                    <td>{{::enlacehost.datacad | amDateFormat:'DD/MM/YYYY hh:mm:ss'}}</td>
                    <td><button type="button" class="btn btn-primary btn-danger btn-sm" ng-click="deleteHost(enlacehost);" authorize="['enlaces.write']"><i class="glyphicon glyphicon-remove"></i></button></td>
                  </tr>                  
              </tbody>
      </table> 
      