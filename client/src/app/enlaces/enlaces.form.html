<ol class="breadcrumb">
    <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><a href="/enlaces"><i class="glyphicon glyphicon-link"></i> Enlaces</a></li>
    <li class="active">{{enlace.enlace}}</li>
</ol>

<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#dados"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-list-alt"></i> Dados </a>
    </li>
    <li>
      <a data-target="#hosts"  data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-folder-close"></i> Hosts </a>
    </li>
    <li>
        <a data-target="#calculosptp" data-toggle="tab" style="cursor: pointer;"><i class="glyphicon glyphicon-random"></i> Cálculos PTP</a>
    </li>
    <li>
      <a data-target="#anexos" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-tasks"></i> Anexos </a>
    </li>
</ul>

<div class="tab-content">
<div id="dados" class="tab-pane fade in active">
    <div class="barra">
    <div class="form-group">
    <button ng-click="saveEnlace(enlace)" class="btn btn-primary btn-info btn-incluir text-center" type="submit" ng-disabled="frmEnlace.$invalid" ng-if="eventoSelecionado.id != ''" authorize="['enlaces.write']">
          <span class="glyphicon glyphicon-check"></span><br>Salvar
      </button>    
    </div>
</div>  
  <div class="row top-buffer">
    <form class="form-horizontal" name="frmEnlace">
        <div class="form-group">
        <label class="col-md-2 control-label" for="enlace">Nome <i
            class="glyphicon glyphicon-asterisk text-danger"
            style="font-size: 11px;"></i></label>
        <div class="col-md-4">
        <input id="enlace" name="enlace" type="text" placeholder="Nome do enlace" class="form-control input-md" required="required" ng-model="enlace.enlace" ng-disabled="eventoSelecionado.id == ''">
        </div>
        </div>
        <div class="form-group">
        <label class="col-md-2 control-label" for="distancia">Distância <i
            class="glyphicon glyphicon-asterisk text-danger"
            style="font-size: 11px;"></i></label>
        <div class="col-md-1">
        <input id="distancia" name="distancia" type="number" class="form-control input-md" required="required" ng-model="enlace.distancia_m" ng-disabled="eventoSelecionado.id == ''">
        </div>
        </div>
        <div class="form-group">
        <label class="col-md-2 control-label" for="dataativacao">Data Ativação </label>
        <div class="col-md-2">
        <input id="dataativacao" type="date" class="form-control input-md" ng-model="enlace.dataativacao" ng-disabled="eventoSelecionado.id == ''">
        </div>
        </div>
        <div class="form-group">
    <label class="col-md-2 control-label" for="observacao">Observação</label>
    <div class="col-md-6">
      <textarea id="observacao" class="form-control input-md" ng-model="enlace.observacao"
                ng-disabled="eventoSelecionado.id == ''" style="min-height: 200px;"></textarea>
    </div>
  </div>
     </form>    
    </div>  
  </div> 
    
  <div id="hosts" class="tab-pane fade in" ng-controller="EnlacesHostCtrl">
        <div ng-include="'app/enlaces/enlaces.hosts.list.html'"></div>
        <div ng-include="'app/enlaces/enlaces.hosts.form.html'"></div>
    </div> 
    <div id="calculosptp" class="tab-pane fade" >
        <div ng-include="'app/ptp/ptp.list.html'"></div>
    </div>
    <div id="anexos" class="tab-pane fade in">
        <div ng-include="'app/enlaces/anexos/anexosViewList.html'" ng-controller="EnlaceAnexoController"></div>
    </div>
    
</div>    



