'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/enlaces', {
                templateUrl: 'app/enlaces/enlaces.list.html',
                controller: 'EnlacesListController',
                controllerAs: 'ELC',
                title: 'Enlaces',
                authorize: ['enlaces.read', 'enlaces.write']

            })

            .when('/enlaces/novo', {
                templateUrl: 'app/enlaces/enlaces.form.html',
                controller: 'EnlacesFormCtrl',
                title: 'Enlaces',
                resolve: {

                    dadosAPI: function () {
                        return 0;
                    },
                    formType: function () {

                        return 'create';

                    }
                },
                authorize: ['enlaces.write']
            })

            .when('/enlaces/:id/:enlace', {
                templateUrl: 'app/enlaces/enlaces.form.html',
                controller: 'EnlacesFormCtrl',
                title: 'Enlaces',
                resolve: {
                    dadosAPI: function ($q, EnlaceService, EnlaceHostService, CalculoPtpService, $route) {

                        var Enlace = EnlaceService.get({enlace: $route.current.params.enlace});
                        var EnlaceHosts = EnlaceHostService.get({enlace: $route.current.params.enlace});
                        var CalculosPtp = CalculoPtpService.getEnlaceCalculosPtp({enlace: $route.current.params.enlace});

                        return $q.all([Enlace.$promise, EnlaceHosts.$promise, CalculosPtp.$promise]);

                    },

                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['enlaces.read', 'enlaces.write']
            })

            .when('/enlaces/:id/:enlace/calculos/ptp/novo', {

                templateUrl: 'app/ptp/ptp.form.html',
                controller: 'CalculosPtpCtrl',
                title: 'Cálculos PTP',
                resolve: {
                    dadosAPI: function ($q, EnlaceService, EnlaceHostService, $route) {

                        var Enlace = EnlaceService.get({enlace: $route.current.params.enlace});
                        var EnlaceHosts = EnlaceHostService.get({enlace: $route.current.params.enlace});

                        return $q.all([Enlace.$promise, EnlaceHosts.$promise]);

                    },

                    formType: function () {

                        return 'create';

                    }
                },
                authorize: ['enlaces.write']


            })

            .when('/enlaces/:id/:enlace/calculos/ptp/:calculo', {

                templateUrl: 'app/ptp/ptp.form.html',
                controller: 'CalculosPtpCtrl',
                title: 'Cálculos PTP',
                resolve: {
                    dadosAPI: function ($q, EnlaceHostService, CalculoPtpService, $route) {

                        var CalculoPtp = CalculoPtpService.getEnlaceCalculosPtp({
                            enlace: $route.current.params.enlace,
                            calculo: $route.current.params.calculo
                        });
                        var EnlaceHosts = EnlaceHostService.get({enlace: $route.current.params.enlace});

                        return $q.all([CalculoPtp.$promise, EnlaceHosts.$promise]);

                    },

                    formType: function () {

                        return 'edit';

                    }
                },
                authorize: ['enlaces.read', 'enlaces.write']


            });

    });
