'use strict';

angular.module('app')

    .factory('EnlaceHostService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/enlaces/:enlace/hosts/:id', {},
            {
                getEnlaceHosts: {method: 'GET', isArray: false},
                insertEnlaceHost: {
                    method: 'POST',
                    params: {enlace: '@enlace'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteEnlaceHost: {
                    method: 'DELETE',
                    params: {enlace: '@enlace', id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('EnlaceHostServiceJSON', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/enlaces/:enlace/hosts.json', {},
            {getEnlaceHosts: {method: 'GET', isArray: true}}
        );
    });
