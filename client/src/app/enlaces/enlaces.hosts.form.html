<div modal-show modal-visible="showDialog" class="modal fade" id="frmhost" tabindex="-1" role="dialog" aria-labelledby="frmhostlabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- <PERSON><PERSON>er -->
            <div class="modal-header">
                <button type="button" class="close" 
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Fechar</span>
                </button>
                <h4 class="modal-title" id="frmhostlabel">
                    Incluir Host
                </h4>
            </div>
            
            <!-- Modal Body -->
            <div class="modal-body">
                
                <form role="form" name="frmHost">
                 <div class="form-group">
                    <label for="pop">Pop</label>
                      <!-- <select class="form-control" ng-model="pop"
                     ng-options="o as o for o in pops" ng-change="loadHosts(pop)"></select>
                     -->
                     <ol class="nya-bs-select form-control"
                         title="Selecione um POP"
                         id="pop"
                         ng-model="pop"
                         ng-change="loadHosts(pop)"
                         data-live-search="true"
                         data-size="8"
                     >
                         <li nya-bs-option="option in pops">
                             <!-- the text content of anchor element will be used for search -->
                             <a>
                                 {{ ::option }}
                                 <span class="glyphicon glyphicon-ok check-mark"></span>
                             </a>
                         </li>
                     </ol>

                  </div>
                  <div class="form-group">
                    <label for="host">Host</label>
                      <ol class="nya-bs-select form-control"
                          title="Selecione um Host"
                          id="host"
                          ng-model="host"
                          ng-change="loadServicos(host)"
                          data-live-search="true"
                          data-size="8"
                      >
                          <li nya-bs-option="option in hosts">
                              <!-- the text content of anchor element will be used for search -->
                              <a>
                                  {{ ::option }}
                                  <span class="glyphicon glyphicon-ok check-mark"></span>
                              </a>
                          </li>
                      </ol>
                      <!-- <select class="form-control" ng-model="host"
                     ng-options="o as o for o in hosts" ng-change="loadServicos(host)"></select>
                     -->
                  </div>
                   <div class="form-group">
                    <label for="servico">Serviço</label>
                       <ol class="nya-bs-select form-control"
                           title="Selecione um Serviço"
                           id="servico"
                           ng-model="enlacehost.hostservico"
                           data-live-search="true"
                           data-size="8"
                       >
                           <li nya-bs-option="option in servicos">
                               <!-- the text content of anchor element will be used for search -->
                               <a>
                                   {{ ::option }}
                                   <span class="glyphicon glyphicon-ok check-mark"></span>
                               </a>
                           </li>
                       </ol>
                      <!--<select class="form-control" ng-model="enlacehost.hostservico"
                     ng-options="o as o for o in servicos"></select>
                     -->
                  </div>
                    <div class="form-group">
                    <label for="observacao">Observação</label>
                      <textarea class="form-control"
                                id="observacao" ng-model="enlacehost.observacao"/></textarea>
                  </div>    
                    
                </form>

            </div>
            
            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Fechar
                </button>
                <button type="button" class="btn btn-primary" ng-click="saveEnlaceHost(enlacehost);" ng-disabled="frmHost.$invalid" data-dismiss="modal">
                    Salvar</button>
            </div>
        </div>
    </div>
</div>