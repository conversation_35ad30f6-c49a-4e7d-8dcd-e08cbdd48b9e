'use strict';

angular.module('app')

    .controller('EnlacesListCtrl', ['$scope', 'dadosAPI', 'EnlaceService', 'LogExclusaoService', '$rootScope', 'toaster', function ($scope, dadosAPI, EnlaceService, LogExclusaoService, $rootScope, toaster) {

        $scope.enlaces = dadosAPI[0].dados;


        $scope.delete = function (enlace) {

            enlace.idevento = $rootScope.eventoSelecionado.id;
            enlace.username = $rootScope.operador.username;

            EnlaceService.deleteEnlace(enlace, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'enlaces',
                        id: enlace.id,
                        nomeregistro: enlace.enlace,
                        idevento: enlace.idevento,
                        username: enlace.username
                    };


                    LogExclusaoService.insertLog(log, function () {

                        

                    });

                    toaster.pop('success', "Enlace desativado", "Enlace desativado com sucesso!");
                    enlace.ativo = 0;

                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        };

    }])


    .controller('EnlacesFormCtrl', ['$scope', '$rootScope', '$location', 'dadosAPI', 'formType', 'EnlaceService', 'LogAlteracaoService', 'toaster',
        function ($scope, $rootScope, $location, dadosAPI, formType, EnlaceService, LogAlteracaoService, toaster) {


            if (formType === 'create') {

                $scope.enlace = {};

                $scope.saveEnlace = function (enlace) {

                    enlace.idevento = $rootScope.eventoSelecionado.id;
                    enlace.username = $rootScope.operador.username;

                    EnlaceService.save(enlace, function (response) {
                        if (response.status === 'OK') {
                            toaster.pop('success', "Enlace adicionado", "Enlace adicionado com sucesso!");
                            $location.path('/enlaces/' + response.id + '/' + enlace.enlace);

                        } else {
                            toaster.pop('error', "Ocorreu um erro", response.dados);
                        }
                    });

                };


            } else {
                $scope.enlace = dadosAPI[0].dados[0];
                $scope.enlacehosts = dadosAPI[1].dados;
                $scope.calculosptp = dadosAPI[2].dados;

                $scope.valores_anteriores = angular.copy(dadosAPI[0].dados[0]);

                $scope.saveEnlace = function (enlace) {

                    EnlaceService.updateEnlace(enlace, function (response) {
                        if (response.status === 'OK') {
                            toaster.pop('success', "Enlace atualizado", "Enlace atualizado com sucesso!");

                            //Insere log da alteração
                            var log = {
                                idevento: $rootScope.eventoSelecionado.id,
                                username: $rootScope.operador.username,
                                tabela: 'enlaces',
                                nomeregistro: $scope.valores_anteriores.enlace,
                                campos: $scope.findDiff(enlace, $scope.valores_anteriores)
                            };

                            LogAlteracaoService.insertLog(log, function () {

                                //console.log(response);

                            });

                            


                        } else {
                            toaster.pop('error', "Ocorreu um erro", response.dados);
                        }


                    });
                };
            }

            //Monta o array com os campos modificados
            $scope .findDiff = function(original, edited) {
                var campos = [];
                var diff = {};
                var registro = {};
                for (var key in original) {
                    if (original[key] !== edited[key]) {
                        registro = {};
                        diff[key] = edited[key];
                        registro.campo = key;
                        registro.valor_anterior = diff[key];
                        registro.valor_atual = original[key];
                        campos.push(registro);
                    }
                }
                return campos;
            }


        }]);
