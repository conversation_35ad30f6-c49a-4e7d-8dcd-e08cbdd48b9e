<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li class="active"><i class="glyphicon glyphicon-link"></i> Enlaces</li>
</ol>

<div class="barra">
  <div class="form-group">

    <a href="/enlaces/novo" type="button" class="btn btn-success btn-incluir text-center"
      ng-if="eventoSelecionado.id != ''" authorize="['enlaces.write']"><span
        class="glyphicon glyphicon-plus"></span><br>Incluir</a>


    <div class="form-group pull-right">
      <form class="form-inline" role="form">
        <div class="form-group">
          <select class="form-control" ng-model="ELC.tipo" ng-init="ELC.tipo = 'nome'">
            <option value="nome">Nome Enlace</option>
          </select>
        </div>
        <div class="form-group">
          <input size="30" maxlength="30" class="form-control" type="text" ng-model="termos">
          <button class="btn btn-default" title="Pesquisar" ng-click="ELC.busca(termos)">Pesquisar</button>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="table-responsive">
  <span class="counter pull-right"></span>
  <table class="table table-striped table-hover table-bordered">
    <thead>
      <tr>
        <th class="vert-align text-center">Evento</th>
        <th class="vert-align text-center">Enlace</th>
        <th class="vert-align text-center">Distância (m)</th>
        <th class="vert-align text-center">Data Cadastro</th>
        <th class="vert-align text-center">Operador</th>
        <th class="vert-align text-center">Ação</th>
      </tr>
    </thead>

    <tbody>
      <tr ng-repeat="enlace in ELC.enlaces | filter:{ativo : 1}"
        ng-class="{'success': enlace.idevento == eventoSelecionado.id}">
        <td class="vert-align text-center"><a href="/eventos/{{::enlace.idevento}}">#{{::enlace.idevento}}</a></td>
        <td class="vert-align text-center"><a
            href="/enlaces/{{::enlace.id}}/{{::enlace.enlace}}">{{::enlace.enlace}}</a></td>
        <td class="vert-align text-center">{{::enlace.distancia_m}}</td>
        <td class="vert-align text-center">{{::enlace.datacad | amDateFormat:'DD/MM/YYYY hh:mm:ss'}}</td>
        <td class="vert-align text-center">{{::enlace.username}}</td>
        <td class="vert-align text-center" ng-show="eventoSelecionado.id == ''"><a
            href="/enlaces/{{::enlace.id}}/{{::enlace.enlace}}" class="btn btn-default btn-sm"
            title="Visualizar Enlace"><i class="glyphicon glyphicon-search"></i></a></td>
        <td class="vert-align text-center" ng-show="eventoSelecionado.id != ''"><a
            href="/enlaces/{{::enlace.id}}/{{::enlace.enlace}}" class="btn btn-warning btn-sm" title="Editar Enlace"><i
              class="glyphicon glyphicon-edit"></i></a> <a href="" class="btn btn-danger btn-sm"
            title="Desativar Enlace" ng-click="ELC.deleteEnlace(enlace)"><i class="glyphicon glyphicon-trash"></i></a>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="text-center">

    <uib-pagination total-items="ELC.pagination.size" ng-model="ELC.pagination.page" ng-change="ELC.pageChanged()"
      items-per-page="ELC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
      boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
    </uib-pagination>
  </div>
  <div class="text-center">
    Página <span class="badge">{{ELC.pagination.page}}</span> de <span class="badge">{{ELC.pagination.pages}}</span> de
    <span class="badge">{{ELC.pagination.size}}</span> registro(s)</span>
  </div>
</div>