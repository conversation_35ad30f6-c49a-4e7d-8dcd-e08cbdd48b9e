<div class="barra">
        <div class="form-group">
            <button class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmanexo" data-placement="top" rel="tooltip" type="submit" ng-if="eventoSelecionado.id != ''" title="Anexar arquivo" authorize="['enlaces.write']">
                <span class="glyphicon glyphicon-plus"></span><br>Incluir
            </button>      
        </div>
    </div>  

<table class="table table-striped table-hover table-bordered">
  <thead>
    <tr>
      <th class="vert-align text-center">Arquivo</th>
      <th class="vert-align text-center">Tipo</th>
      <th class="vert-align text-center">Evento</th>
      <th class="vert-align text-center">Data</th>
      <th class="vert-align text-center">Operador</th>
      <th class="vert-align text-center">Observação</th>
    </tr>
  </thead>
  <tbody>
   <tr ng-repeat="anexo in anexos" ng-class="{'success': anexo.idevento == eventoSelecionado.id}">
   <td class="vert-align text-center">
      <a href="" ng-click="download('anexo', 'enlaces', '', anexo.arquivo)">{{anexo.arquivo}}</a>  
   </td> 
        <td class="vert-align text-center"><span class="badge"><i class="glyphicon glyphicon-globe" ng-show="anexo.tipo=='Cobertura'"></i> <i class="glyphicon glyphicon-equalizer" ng-show="anexo.tipo == 'Airview'"></i> {{anexo.tipo}}</span></td>
        <td class="vert-align text-center"><a href="/eventos/{{anexo.idevento}}">#{{anexo.idevento}}</a></td>
        <td class="vert-align text-center">{{anexo.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
        <td class="vert-align text-center">{{anexo.username}}</td>

        <td class="vert-align text-center">{{anexo.observacao}}</td>
  </tbody>
  </table>
</div>

</div>

<div class="modal fade" id="frmanexo" tabindex="-1" role="dialog"
aria-labelledby="frmanexoslabel" aria-hidden="true" modal="showModal" close="cancel()">

<div class="modal-dialog">
<div class="modal-content">
    <!-- Modal Header -->
    <div class="modal-header">
        <button type="button" class="close"
           data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmanexoslabel">
            Anexar arquivo
        </h4>
    </div>
      <div class="modal-body">
<form name="frmAnexo">
<div class="row">
  <div class="col-md-3">
    <div class = "form-group">
      <label for = "tipo">Tipo</label>
      <select class="form-control" ng-model="anexo.tipo"
  ng-options="o as o for o in tipos">
      </select>
    </div>
  </div>
  <div class="col-md-9">
    <div class = "form-group">
      <label for = "observacao">Observação</label>
      <textarea class = "form-control" rows = "3" ng-model="anexo.observacao" name="observacao"></textarea>
   </div>
 </div>
</div>
</form>
<div class="row" ng-show="anexo.tipo=='Airview'">
  <div class="text-center">
  <p><i>Preview</i></p>
  <img ng-src="{{image_source}}" class="img-rounded" width="300" heigth="300">
  </div>
</div>

<div class="row" ng-show="anexo.tipo=='Cobertura'">
  <div class="text-center">
  <img ng-src="assets/kmz.png" class="img-rounded">
  </div>
</div>

    <!-- Modal Footer -->
    <div class="modal-footer">
      <span class="btn btn-default btn-file" ng-show="anexo.tipo=='Airview'">
    Selecionar arquivo ... <input type="file" id="trigger" onchange="angular.element(this).scope().setFile(this)" ngf-select ng-model="picFile" name="file" accept="image/*" ngf-max-size="20MB" required class="form-control">
</span>
<span class="btn btn-default btn-file" ng-show="anexo.tipo=='Cobertura'">
Selecionar arquivo ... <input type="file" id="trigger" onchange="angular.element(this).scope().setFile(this)" ngf-select ng-model="picFile" name="file" accept=".kmz" ngf-max-size="20MB" required class="form-control">
</span>
        <button type="button" class="btn btn-default"
                data-dismiss="modal">
                    Fechar
        </button>
        <button type="button" class="btn btn-primary" ng-click="uploadPic(picFile)" data-dismiss="modal" ng-disabled="frmAnexo.$invalid">
            Anexar</button>
    </div>
</div>
</div>
</div>
