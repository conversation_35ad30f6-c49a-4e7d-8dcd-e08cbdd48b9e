(function () {
    'use strict';

    angular
        .module('app')
        .controller('EnlacesListController', EnlacesListController);

    /** @ngInject */
    function EnlacesListController($http, API_CONFIG, cfpLoadingBar,
                                   $rootScope, toaster, $cookies, EnlaceService, LogExclusaoService) {

        var vm = this;

        vm.limit = 20;
        vm.filtro = '';
        vm.enlaces = [];
        vm.sortBy = 'enlace';
        vm.sortOrder = 'asc';
        vm.pagination = {
            page: 1
        };

        vm.busca = busca;
        vm.pageChanged = pageChanged;
        vm.deleteEnlace = deleteEnlace;

        activate();

        function activate() {
            getData();
        }

        function getData() {
            cfpLoadingBar.start();
            var urlApi = API_CONFIG.url + '/enlaces?page=' + vm.pagination.page + "&count=" +
              vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder + '&ativo=1';
            $http.get(urlApi).then(function (response) {
                angular.copy(response.data.rows, vm.enlaces);
                angular.copy(response.data.pagination, vm.pagination);
               
                cfpLoadingBar.complete();
            });
        }

        function pageChanged() {
            getData();
        }

        function busca(termos) {

            vm.currentPage = 1;

            vm.filtro = '&enlace=|' + termos + '|';

            getData();

        }

        function deleteEnlace(enlace) {

            enlace.idevento = $rootScope.eventoSelecionado.id;
            enlace.username = $rootScope.operador.username;

            EnlaceService.deleteEnlace(enlace, function (response) {
                if (response.status === 'OK') {

                    var log = {
                        tabela: 'enlaces',
                        id: enlace.id,
                        nomeregistro: enlace.enlace,
                        idevento: enlace.idevento,
                        username: enlace.username
                    };


                    LogExclusaoService.insertLog(log, function () {

                        

                    });

                    toaster.pop('success', "Enlace desativado", "Enlace desativado com sucesso!");
                    enlace.ativo = 0;

                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
            });


        }

    }

})();
