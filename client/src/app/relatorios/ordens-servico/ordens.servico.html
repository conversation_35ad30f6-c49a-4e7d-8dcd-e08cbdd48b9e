<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
	<li class="active"><i class="glyphicon glyphicon-list"></i> Ordens de serviço</li>

</ol>
<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%;">
	<div class="table filter-section-container">
		<div class="tr">
			<div class="td centered" style="border-right: 1px solid #CCC;">
				<label for="agrupar_por_ticket">
					<input type="checkbox" id="agrupar_por_ticket" ng-model="OSC.filtros.agrupar_por_ticket" ng-change="OSC.pageChanged();"> Agrupar por ticket
				</label>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="id_os">ID da O.S.</label>
							<input class="form-control" type="text" id="id_os" ng-model="OSC.filtros.id_os">
						</td>
						<td>
							<label for="id_ticket">ID do ticket</label>
							<input class="form-control" type="text" id="id_ticket"
								ng-model="OSC.filtros.id_ticket">
						</td>
					</tr>
					<tr>
						<td>
							<label for="os_auditoria">Auditoria</label>
							<select class="form-control" id="os_auditoria" ng-model="OSC.filtros.os_auditoria">
								<option value="T">Todas</option>
								<option value="S">Sim</option>
								<option value="N">Não</option>
							</select>
						</td>
						<td>
							<label for="status_os">Status da O.S.</label>
							<select class="form-control" id="status_os" ng-model="OSC.filtros.status_os">
								<option value="todos">Todos</option>
								<option value="A">Aberta</option>
								<option value="F">Finalizada</option>
								<option value="EN">Encaminhada</option>
								<option value="AS">Assumida</option>
								<option value="AG">Agendada</option>
								<option value="RAG">Reagendar</option>
							</select>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td colspan="2">
							<label for="cliente">Cliente</label>
							<input class="form-control" type="text" id="cliente" ng-model="OSC.filtros.cliente"
								placeholder="ID, CPF/CNPJ ou nome">
						</td>
					</tr>
					<tr>
						<td>
							<label for="login">Login</label>
							<input class="form-control" type="text" id="login" ng-model="OSC.filtros.login">
						</td>
						<td>
							<label for="cidade">Cidade</label>
							<select class="form-control" id="cidade" ng-model="OSC.filtros.cidade">
								<option value="todas">Todas</option>
								<option value="Andradas">Andradas</option>
								<option value="Poços de Caldas">Poços de Caldas</option>
								<option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
								<option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
								<option value="Campestre">Campestre</option>
								<option value="São João da Boa Vista">São João da Boa Vista</option>
							</select>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="aberta_em__inicio">Aberta em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="aberta_em__inicio" ng-model="OSC.filtros.aberta_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="aberta_em__final" ng-model="OSC.filtros.aberta_em__final">
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label for="finalizada_em__inicio">Finalizada em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="finalizada_em__inicio" ng-model="OSC.filtros.finalizada_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="finalizada_em__final" ng-model="OSC.filtros.finalizada_em__final">
							</div>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="padding-left: 15px;">
				<button class="btn btn-default" ng-click="OSC.resetFiltros();" style="margin-top: -10px;">
					<i class="glyphicon glyphicon-trash btn-icon"></i>Limpar
				</button>
				<br>
				<button class="btn btn-primary" ng-click="OSC.atualizarBusca();" style="margin-top: 10px;">
					<i class="glyphicon glyphicon-search btn-icon"></i>Buscar
				</button>
			</div>
		</div>
	</div>
</div>
<table class="table table-striped">
	<thead>
		<tr>
			<th class="centered">Status</th>
			<th>ID</th>
			<th class="centered">Auditoria</th>
			<th class="centered">Iniciada por</th>
			<th class="centered">Aberta em</th>
			<th>ID ticket</th>
			<th>Cliente</th>
			<th>Login</th>
			<th>Mensagem</th>
			<th class="centered">Finalizada em</th>
			<th class="centered">Cidade</th>
		</tr>
	</thead>
	<tbody>
		<tr ng-repeat="os in OSC.ordensServico" class="pointer hoverable" ng-class="$index == OSC.selectedOsIndex ? 'selected' : ''" ng-click="OSC.abrirOs(os.id, this);">
			<td class="centered">
				<span class="label white-bordered" ng-class="{'label-info': os.status === 'Agendada', 'label-success': os.status === 'Finalizada', 'label-warning': os.status === 'Reagendar', 'label-primary' : !['Agendada', 'Finalizada', 'Reagendar'].includes(os.status)}" style="display: block; width: 100%;">{{os.status | uppercase}}</span>
			</td>
			<td class="valign-middle">
				<b>{{os.id}}</b>
			</td>
			<td class="centered">
				<span class="label white-bordered" ng-class="os.auditoria == 'S' ? 'label-success' : 'label-primary'" style="display: block; width: 70%; margin: 0 auto;">{{(os.auditoria == 'S' ? 'Sim' : 'Não') | uppercase}}</span>
			</td>
			<td class="centered">
				{{os.aberta_por}}
			</td>
			<td class="centered" title="{{os.aberta_em | amDateFormat:'dddd'}}">
				{{os.aberta_em | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
			</td>
			<td>
				<button class="btn btn-vvsm" ng-class="OSC.filtros.id_ticket == os.id_ticket ? 'btn-success' : 'btn-primary'" title="Adicionar este ticket ao filtro" ng-click="OSC.adicionarFiltro('id_ticket', os.id_ticket); $event.stopPropagation();">
					<span class="glyphicon glyphicon-filter"></span></button> {{os.id_ticket}}
			</td>
			<td>
				<button class="btn btn-vvsm" ng-class="OSC.filtros.cliente == os.id_cliente ? 'btn-success' : 'btn-primary'" title="Adicionar este cliente ao filtro" ng-click="OSC.adicionarFiltro('id_cliente', os.id_cliente); $event.stopPropagation();">
					<span class="glyphicon glyphicon-filter"></span></button> (<b>{{os.id_cliente}}</b>) {{os.cliente}}
			</td>
			<td>
				<button class="btn btn-vvsm" ng-class="OSC.filtros.login == os.login ? 'btn-success' : 'btn-primary'" title="Adicionar este login ao filtro" ng-click="OSC.adicionarFiltro('login', os.login); $event.stopPropagation();">
					<span class="glyphicon glyphicon-filter"></span></button> 
				{{os.login}}
			</td>
			<td title="{{os.mensagem}}">
				{{os.mensagem.length > 40 ? os.mensagem.substr(0, 40) + '...' : os.mensagem}}
			</td>
			<td class="centered" title="{{os.finalizada_em | amDateFormat:'dddd'}}">
				{{os.status === 'Finalizada' ? (os.finalizada_em | amDateFormat:'DD/MM/YYYY HH:mm:ss') : '-'}}
			</td>
			<td class="centered" title="{{os.cidade}}">
				<span class="label label-primary" style="display: block; width: 100%;">
					{{os.cidade_cod ? os.cidade_cod : '-'}}
				</span>
			</td>
		</tr>
	</tbody>
</table>

<div class="text-center">

	<div class="text-center">
		<uib-pagination total-items="OSC.pagination.size" ng-model="OSC.pagination.page" ng-change="OSC.pageChanged()"
			items-per-page="OSC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
			boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
		</uib-pagination>
	</div>
	<div class="text-center">
		Página <span class="badge">{{ OSC.pagination.page}}</span> de <span class="badge">{{ OSC.pagination.pages}}</span>
		de <span class="badge">{{ OSC.pagination.size}}</span> registro(s)</span>
	</div>
</div>

<div ng-include="'app/relatorios/ordens-servico/ordem.servico.modal.html'"></div>