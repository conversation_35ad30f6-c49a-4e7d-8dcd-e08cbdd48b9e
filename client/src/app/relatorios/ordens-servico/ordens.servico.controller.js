(function () {
	'use strict';

	angular
		.module('app')
		.controller('OrdensServicoController', OrdensServicoController);

	/** @ngInject */
	function OrdensServicoController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window, $localStorage, AlertService) {

		var vm = this;
		vm.atualizarBusca = atualizarBusca;
		vm.adicionarFiltro = adicionarFiltro;
		vm.resetFiltros = resetFiltros;
		vm.abrirOs = abrirOs;
		vm.pageChanged = pageChanged;
		vm.selectedOsIndex = null;
		vm.selectedOs = {};
		vm.pagination = {
			page: 1,
			size: 0,
			count: 0
		};


		var dateFields = [
			'aberta_em__inicio',
			'aberta_em__final',
			'finalizada_em__inicio',
			'finalizada_em__final'
		];

		vm.filtro = '';
		vm.filtros = {};
		vm.resultados = [];

		var aberta_em__inicio = new Date();
		aberta_em__inicio.setDate(aberta_em__inicio.getDate() - 30);
		var aberta_em__final = new Date();

		vm.defaultFiltros = {
			agrupar_por_ticket: true,
			id_os: '',
			id_ticket: '',
			os_auditoria: 'T',
			status_os: 'todos',
			id_cliente: '',
			cliente: '',
			login: '',
			cidade: 'todas',
			aberta_em__inicio: aberta_em__inicio,
			aberta_em__final: aberta_em__final,
			finalizada_em__inicio: '',
			finalizada_em__final: ''
		};

		angular.element(document).ready(function () {
			getFiltros();
			buscarOs();
			$('input').on('keydown', function (e) {
				if (e.keyCode === 13) {
					atualizarBusca();
				}
			});
		});

		function atualizarBusca() {
			vm.pagination.page = 1;
			pageChanged();
		}

		function pageChanged() {
			var urlApi = '/relatorios/ordens-servico?page=' + vm.pagination.page;

			Object.keys(vm.filtros).forEach(function (key) {
				urlApi += '&' + key + '=';

				if (dateFields.includes(key))
					urlApi += $filter('date')(vm.filtros[key], "yyyy-MM-dd");
				else
					urlApi += vm.filtros[key];
			});

            $location.url(urlApi);
		}

		function buscarOs() {
			var page = $routeParams['page'] && $routeParams['page'] > 0 ? $routeParams['page'] : 1;

			$http({
				url: API_CONFIG.url + '/relatorios/ordens-servico/buscar',
				method: 'POST',
				data: {filtros:vm.filtros, page: page}
			}).then(function (response) {
				vm.ordensServico = response.data.list;
				vm.pagination = response.data.pagination;
			}, function (response) {
				console.log('error');
			});
		}

		function getFiltros() {
			Object.keys(vm.defaultFiltros).forEach(function (key) {
				vm.filtros[key] = $routeParams[key] !== undefined ? $routeParams[key] : vm.defaultFiltros[key];

				var timeZoneHoursOffset = 3;

				if (dateFields.includes(key)) {
					var utc = new Date(vm.filtros[key]);
					vm.filtros[key] = new Date(utc.getTime() + 3600 * 1000 * timeZoneHoursOffset);
					vm.filtros[key + 'Str'] = $filter('date')(vm.filtros[key], 'yyyy-MM-dd');
				}
				else if (key === 'agrupar_por_ticket' && $routeParams[key] !== undefined) {
					vm.filtros[key] = $routeParams[key] === 'true';
				}
			});
		}

		function abrirOs(index) {
			vm.selectedOsIndex = index;
			$('#osModal').modal('show');
			getOsDetails(index);
		}

		function getOsDetails(id) {
			vm.selectedOs = {};

			$http({
				url: API_CONFIG.url + '/relatorios/ordens-servico/detalhes/' + id,
				method: 'GET'
			}).then(function (response) {
				vm.selectedOs = response.data.os;
			}, function (response) {
			});
		}

		function resetFiltros() {
			angular.copy(vm.defaultFiltros, vm.filtros);
		}

		function adicionarFiltro(campo, valor) {
			var campoTxt;
			switch (campo) {
				case 'id_ticket':
					campoTxt = 'ticket';
					break;
				case 'id_cliente':
					campoTxt = 'cliente';
					campo = 'cliente';
					break;
				default:
					campoTxt = campo;
			}

			if (vm.filtros[campo] === valor) {
				AlertService.error('Este ' + campoTxt + ' já está sendo filtrado.');
				return false;
			}

			AlertService.alert({
				html: 'Deseja filtrar apenas por este <b>' + campoTxt + '</b> ou adicioná-lo aos filtros atuais?',
				buttons: [
					{
						text: 'Filtrar apenas <b>' + campoTxt + '</b>',
						action: function () {
							resetFiltros();
							vm.filtros[campo] = valor;
							atualizarBusca();
						}
					},
					{
						text: 'Adicioná-lo aos filtros',
						action: function () {
							vm.filtros[campo] = valor;
							atualizarBusca();
						}
					}
				],
				cancelBtnText: 'Cancelar',
				cancelBtnColor: 'default'
			});
		}
	}

})();
