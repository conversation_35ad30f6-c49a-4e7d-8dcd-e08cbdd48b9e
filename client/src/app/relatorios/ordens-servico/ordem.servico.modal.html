<div class="modal" id="osModal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()">

	<div class="modal-dialog" style="min-width: 800px; max-width: 1000px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Ordem de serviço #{{OSC.selectedOs.id}} - <span class="label white-bordered"
						ng-class="{'label-info': OSC.selectedOs.status === 'Agendada', 'label-success': OSC.selectedOs.status === 'Finalizada', 'label-warning': OSC.selectedOs.status === 'Reagendar', 'label-primary' : !['Agendada', 'Finalizada', 'Reagendar'].includes(OSC.selectedOs.status)}"
						style="font-size: 9pt;">{{OSC.selectedOs.status | uppercase}}</span>
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 15px;">


				<table class="full-width">
					<tr>
						<td class="nowrap valign-top" style="width: 33%;">
							<label>Iniciada por:</label>
							<span>{{OSC.selectedOs.aberta_por}}</span>

							<br>

							<label>Auditoria:</label>
							<span class="label white-bordered" ng-class="OSC.selectedOs.auditoria == 'S' ? 'label-success' : 'label-primary'">{{(OSC.selectedOs.auditoria == 'S' ? 'Sim' : 'Não') | uppercase}}</span>
						</td>
						<td class="nowrap" style="padding-right: 30px;">
							<label>ID do ticket: </label>
							<span>{{OSC.selectedOs.id_ticket}}</span>

							<br>

							<label>Protocolo da O.S.: </label>
							<span>{{OSC.selectedOs.protocolo}}</span>
						</td>
						<td class="nowrap align-right">
							<label>Aberta em:</label>
							<span class="label label-primary">{{OSC.selectedOs.aberta_em| amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</span>

							<br>

							<label>Finalizada em:</label>
							<span class="label label-success">{{OSC.selectedOs.finalizada_em | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</span>
						</td>
					</tr>
				</table>

				<div class="horizontal-divider spacing-sm"></div>

				<p style="margin: 10px 0px 0px;">
					<label>Assunto:</label>
					{{OSC.selectedOs.assunto}}
				</p>

				<div class="horizontal-divider spacing-sm"></div>

				<div class="panel panel-primary data-panel" style="margin: 10px 0px;">
					<div class="panel-heading">
						<h4 class="panel-title">Dados do cliente</h4>
					</div>
					<div class="panel-body">
						<table class="full-width">
							<tr>
								<td>
									<label>ID:</label> {{OSC.selectedOs.id_cliente}}
								</td>
								<td>
									<label>Login:</label> {{OSC.selectedOs.login}}
								</td>
							</tr>
							<tr>
								<td>
									<label>Nome:</label> {{OSC.selectedOs.cliente}}
								</td>
								<td>
									<label>CPF/CNPJ:</label> {{OSC.selectedOs.cnpj_cpf}}
								</td>
							</tr>
							<tr>
								<td>
									<label>Contrato:</label> {{OSC.selectedOs.id_contrato}}
								</td>
								<td>
									<label>Plano:</label> {{OSC.selectedOs.plano}}
								</td>
							</tr>
							<tr>
								<td colspan="2">
									<div class="horizontal-divider spacing-sm"></div>
									<label>Endereço:</label> {{OSC.selectedOs.endereco}}
								</td>
							</tr>
							<tr>
								<td>
									<label>Bairro:</label> {{OSC.selectedOs.bairro}}
								</td>
								<td>
									<label>Cidade:</label> {{OSC.selectedOs.cidade}}
								</td>
							</tr>
						</table>
					</div>
				</div>

				<div class="horizontal-divider spacing-sm"></div>

				<div class="panel panel-primary" style="margin: 10px 0px;">
					<div class="panel-heading">
						<h4 class="panel-title">Mensagens</h4>
					</div>
					<div class="panel-body" style="padding: 0px;">
						<table class="table table-striped spaced-td" style="margin: 0px; border-collapse: collapse;">
							<thead>
								<tr>
									<th class="centered">Data</th>
									<th class="centered">Operador</th>
									<th class="centered">Técnico</th>
									<th>Mensagem</th>
									<th class="centered">Evento</th>
									<th class="centered">Próx. tarefa</th>
								</tr>
							</thead>
							<tbody>
								<tr ng-repeat="mensagem in OSC.selectedOs.mensagens" style="border-bottom: 1px solid #CCC;">
									<td class="centered">
										{{mensagem.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
									</td>
									<td class="centered">
										{{mensagem.operador ? mensagem.operador : '-'}}
									</td>
									<td class="centered">
										{{mensagem.tecnico ? mensagem.tecnico : '-'}}
									</td>
									<td>
										{{mensagem.mensagem}}
									</td>
									<td class="centered">
										<span class="label label-primary" style="display: block; width: 100%;">
											{{mensagem.evento}}
										</span>
									</td>
									<td class="centered">
										{{mensagem.proxima_tarefa}}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

			</div>
		</div>
	</div>
</div>