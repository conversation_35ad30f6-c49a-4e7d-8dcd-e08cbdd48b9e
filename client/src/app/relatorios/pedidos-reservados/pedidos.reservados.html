<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
	<li class="active"><i class="glyphicon glyphicon-lock"></i> Pedidos reservados</li>
</ol>
<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%; min-height: 90px;">
	<div class="table filter-section-container">
		<div class="tr">
			<div class="td centered" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="reservado_em__inicio">Reservado em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="reservado_em__inicio"
									ng-model="PRC.filtros.reservado_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="reservado_em__final" ng-model="PRC.filtros.reservado_em__final">
							</div>
						</td>
						<td>
							<label for="atendente">Atendente</label>
							<input type="text" class="form-control" id="atendente" ng-model="PRC.filtros.atendente">
						</td>
						<td style="vertical-align: bottom;">
							<button type="button" class="btn btn-primary" ng-click="PRC.atualizarBusca();" style="margin-top: 10px;">
								<i class="glyphicon glyphicon-search btn-icon"></i>Buscar
							</button>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>


<div class="table-responsive">
	<table ng-if="PRC.pedidosReservados.length == 0" class="table table-striped table-bordered valign-middle align-center">
		<tbody>
			<tr>
				<td style="font-size: 10pt;">
					<b>Não foram encontrados pedidos reservados, com base no filtro especificado.</b>
				</td>
			</tr>
		</tbody>
	</table>
	<table ng-if="PRC.pedidosReservados.length > 0" class="table table-striped table-bordered valign-middle align-center">
		<thead>
			<tr>
				<th>Data da reserva</th>
				<th>Atendente</th>
				<th>Documento reservado</th>
				<th>ID pedido</th>
				<th>Data do pedido</th>
				<th>Cliente</th>
				<th>Plano</th>
				<th>Valor</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-repeat="pedido in PRC.pedidosReservados">
				<td>
					<b>{{pedido.data_reserva | amDateFormat:'DD/MM/YYYY'}}</b>
				</td>
				<td>
					<b>{{pedido.atendente}}</b>
				</td>
				<td>
					{{pedido.documento_reservado}}
				</td>
				<td>
					{{pedido.id_pedido}}
				</td>
				<td>
					{{pedido.data_pedido| amDateFormat:'DD/MM/YYYY'}}
				</td>
				<td>
					{{pedido.cliente}}
				</td>
				<td>
					{{pedido.plano}}
				</td>
				<td>
					<b>{{pedido.valorTxt == 'NaN' ? '-' : 'R$ ' + pedido.valorTxt}}</b>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="text-center">

	<div class="text-center">
		<uib-pagination total-items="PRC.pagination.size" ng-model="PRC.pagination.page" ng-change="PRC.pageChanged()"
			items-per-page="PRC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
			boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
		</uib-pagination>
	</div>
	<div class="text-center">
		Página <span class="badge">{{ PRC.pagination.page}}</span> de <span class="badge">{{
			PRC.pagination.pages}}</span>
		de <span class="badge">{{ PRC.pagination.size}}</span> registro(s)</span>
	</div>
</div>