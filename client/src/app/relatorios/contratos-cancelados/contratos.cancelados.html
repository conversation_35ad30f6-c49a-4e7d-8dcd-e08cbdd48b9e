<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
	<li class="active"><i class="glyphicon glyphicon-ban-circle"></i> Contratos cancelados</li>
</ol>
<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%; min-height: 90px;">
	<div class="table filter-section-container">
		<div class="tr">
			<div class="td centered" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="">Motivo/obs. cancelamento</label>
							<input type="text" class="form-control" ng-model="CCC.filtros.motivo">
						</td>
					</tr>
					<tr>
						<td>
							<label for="cancelado_em__inicio">Cancelado em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="cancelado_em__inicio"
									ng-model="CCC.filtros.cancelado_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="cancelado_em__final" ng-model="CCC.filtros.cancelado_em__final">
							</div>
							<span style="display: inline-block; margin-top: 5px;"><b>* Estão disponíveis apenas dados a partir de 20/09/2021</b></span>
						</td>
					</tr>
				</table>
			</div>

			<div class="td centered" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center" style="width: 90%;">
					<tr>
						<td style="width: 30%;">
							<label for="">ID contrato</label>
							<input type="text" class="form-control" ng-model="CCC.filtros.id_contrato">
						</td>
						<td style="width: 70%;">
							<label for="">Cliente</label>
							<input type="text" class="form-control" placeholder="Nome do cliente" ng-model="CCC.filtros.cliente">
						</td>
					</tr>
				</table>
				<table class="filter-spaced-table self-to-center" style="width: 90%;">
					<tr>
						<td style="width: 60%;">
							<label for="">Atendente</label>
							<input type="text" class="form-control" ng-model="CCC.filtros.atendente">
						</td>
						<td style="width: 40%;">
							<label for="cidade">Cidade</label>
							<select id="cidade" class="form-control" ng-model="CCC.filtros.cidade">
								<option value="todas" selected="selected">Todas</option>
								<option value="Andradas">Andradas</option>
								<option value="Poços de Caldas">Poços de Caldas</option>
								<option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
								<option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
								<option value="Campestre">Campestre</option>
								<option value="São João da Boa Vista">São João da Boa Vista</option>
							</select>
						</td>
					</tr>
				</table>
			</div>

			<div class="td centered" style="padding-left: 15px;">
				<button type="button" class="btn btn-default" ng-click="CCC.resetFiltros();" style="margin-top: -10px;">
					<i class="glyphicon glyphicon-trash btn-icon"></i>Limpar
				</button>
				<br>
				<button type="button" class="btn btn-primary" ng-click="CCC.atualizarBusca();" style="margin-top: 10px;">
					<i class="glyphicon glyphicon-search btn-icon"></i>Buscar
				</button>
			</div>
		</div>
	</div>
</div>


<div class="table-responsive">
	<div class="full-width align-right" style="font-size: 10pt; border: 1px solid #CCC; border-radius: 5px; padding: 5px 10px; margin: 10px 0px;">
		<b>Valor total (considerando todas as páginas do resultado da busca):</b>
		<span class="label label-danger" style="font-size: 10pt;">R$ {{CCC.valorTotal ? CCC.valorTotal : '...'}}</span>
	</div>
	<table ng-if="CCC.contratosCancelados.length == 0" class="table table-striped table-bordered valign-middle align-center">
		<tbody>
			<tr>
				<td style="font-size: 10pt;">
					<b>Não foram encontrados contratos cancelados, com base no filtro especificado.</b>
				</td>
			</tr>
		</tbody>
	</table>
	<table ng-if="CCC.contratosCancelados.length > 0" class="table table-striped table-bordered valign-middle align-center">
		<thead>
			<tr>
				<th>Data do cancelamento</th>
				<th>ID contrato</th>
				<th>Cliente</th>
				<th>Cidade</th>
				<th>Motivo do cancelamento</th>
				<!-- <th>Observação do cancelamento</th> -->
				<th>Valor</th>
				<th>Atendente</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-repeat="contrato in CCC.contratosCancelados">
				<td>
					<b>{{contrato.data_cancelamento | amDateFormat:'DD/MM/YYYY'}}</b>
				</td>
				<td>
					<b>{{contrato.id_contrato}}</b>
				</td>
				<td>
					{{contrato.cliente}}
				</td>
				<td>
					{{contrato.cidade}}
				</td>
				<td>
					{{contrato.motivo_cancelamento}}
				</td>
				<!-- <td>
					{{contrato.obs_cancelamento}}
				</td> -->
				<td>
					<b>R$ {{contrato.valorTxt}}</b>
				</td>
				<td>
					{{contrato.atendente}}
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="text-center">

	<div class="text-center">
		<uib-pagination total-items="CCC.pagination.size" ng-model="CCC.pagination.page" ng-change="CCC.pageChanged()"
			items-per-page="CCC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
			boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
		</uib-pagination>
	</div>
	<div class="text-center">
		Página <span class="badge">{{ CCC.pagination.page}}</span> de <span class="badge">{{
			CCC.pagination.pages}}</span>
		de <span class="badge">{{ CCC.pagination.size}}</span> registro(s)</span>
	</div>
</div>