(function () {
	'use strict';

	angular
		.module('app')
		.controller('ContratosCanceladosController', ContratosCanceladosController);

	/** @ngInject */
	function ContratosCanceladosController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

		var vm = this;

		vm.contratosCancelados = [];

		vm.filtro = '';
		vm.filtros = {};

		vm.pagination = {
			page: 1,
			size: 0,
			count: 0
		};

		var dateFields = [
			'cancelado_em__inicio',
			'cancelado_em__final'
		];

		var hoje = new Date();
		var mesAtual = hoje.getMonth()+1;
		var anoAtual = hoje.getFullYear();
		var diasDoMes = daysInMonth(mesAtual, anoAtual);

		var cancelado_em__inicio = new Date(anoAtual + '-' + mesAtual + '-01');
		var cancelado_em__final = new Date(anoAtual + '-' + mesAtual + '-' + diasDoMes);

		vm.defaultFiltros = {
			id_contrato: '',
			cliente: '',
			atendente: '',
			cidade: 'todas',
			motivo: '',
			cancelado_em__inicio: cancelado_em__inicio,
			cancelado_em__final: cancelado_em__final,
		};

		vm.atualizarBusca = atualizarBusca;
		vm.pageChanged = pageChanged;
		vm.resetFiltros = resetFiltros;
		vm.valorTotal = null;

		vm.timeZoneHoursOffset = 3;

		activate();

		function activate() {
			getFiltros();
			getContratosCancelados();
		}

		function resetFiltros() {
			angular.copy(vm.defaultFiltros, vm.filtros);

			Object.keys(vm.filtros).forEach(function (key) {

				if (dateFields.includes(key)) {
					var utc = new Date(vm.filtros[key]);
					vm.filtros[key] = new Date(utc.getTime() + 3600 * 1000 * vm.timeZoneHoursOffset);
					vm.filtros[key + 'Str'] = $filter('date')(vm.filtros[key], 'yyyy-MM-dd');
				}
			});
		}

		function atualizarBusca() {
			vm.pagination.page = 1;
			pageChanged();
		}

		function pageChanged() {
			var urlApi = '/relatorios/contratos-cancelados?page=' + vm.pagination.page;

			Object.keys(vm.filtros).forEach(function (key) {
				urlApi += '&' + key + '=';

				if (dateFields.includes(key))
					urlApi += $filter('date')(vm.filtros[key], "yyyy-MM-dd");
				else
					urlApi += vm.filtros[key];
			});

			$location.url(urlApi);
		}

		function getFiltros() {
			Object.keys(vm.defaultFiltros).forEach(function (key) {
				vm.filtros[key] = $routeParams[key] !== undefined ? $routeParams[key] : vm.defaultFiltros[key];

				if (dateFields.includes(key)) {
					var utc = new Date(vm.filtros[key]);
					vm.filtros[key] = new Date(utc.getTime() + 3600 * 1000 * vm.timeZoneHoursOffset);
					vm.filtros[key + 'Str'] = $filter('date')(vm.filtros[key], 'yyyy-MM-dd');
				}
			});
		}

		function setContratosValoresTxt() {
			vm.contratosCancelados.forEach(function (obj, index) {
				vm.contratosCancelados[index].valorTxt = parseFloat(obj.valor).toFixed(2);
			});
		}

		function getContratosCancelados() {
			var page = $routeParams['page'] && $routeParams['page'] > 0 ? $routeParams['page'] : 1;

			$http({
				url: API_CONFIG.url + '/relatorios/contratos-cancelados',
				method: "POST",
				data: { filtros: vm.filtros, page: page },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status && response.data.status === 'success') {
					angular.copy(response.data.contratosCancelados, vm.contratosCancelados);

					setContratosValoresTxt();

					vm.pagination = response.data.pagination;
					vm.valorTotal = parseFloat(response.data.valorTotal).toFixed(2);
				}
				else {

				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}
	}

	function daysInMonth(month, year) {
		return new Date(year, month, 0).getDate();
	}

})();
