(function () {
    'use strict';

    angular
        .module('app')
        .controller('IpFixoController', IpFixoController);

    /** @ngInject */
    function IpFixoController($http, API_CONFIG) {

        var vm = this;
        vm.busca = busca;

        vm.filtro = '';
        vm.resultados = [];

       function busca(filtro, concentrador){
            vm.resultados = [];
            $http.get(API_CONFIG.url+'/relatorios/ipfixo?ip=' + vm.ip)
                .then(function(response) {
                    angular.copy(response.data, vm.resultados);
                });
      }
    }

})();
