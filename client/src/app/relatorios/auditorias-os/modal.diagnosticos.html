<div class="modal" id="diagnosticosModal" tabindex="-1" role="dialog" aria-labelledby="osModal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="min-width: 700px; max-width: 700px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="osModal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Gerenciar diagnósticos
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 15px;">

				<table class="full-width">
					<tr>
						<td class="valign-top" style="width: 47.5%">
							<table class="table table-striped table-success" style="margin-bottom: 0px;">
								<thead>
									<tr>
										<th class="centered bg-success text-light" colspan="2">Positivos</th>
									</tr>
								</thead>
							</table>

							<div style="overflow-y: auto; max-height: 400px;">
								<table class="table table-striped table-success">
									<tbody>
										<tr ng-repeat="positivo in AOC.diagnosticos.positivos">
											<td>{{ positivo.diagnostico }}</td>
											<td style="width: 5%;">
												<button class="btn btn-vsm btn-danger" ng-really-message="Deseja realmente excluir este diagnóstico?" ng-really-click="AOC.deleteDiagnostico(positivo.id)">
													<i class="glyphicon glyphicon-trash"></i>
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</td>

						<td class="centered" style="width: 5%;">
							<div style="width: 1px; height: 100%; background: #CCC; margin: 0 auto;">
								&nbsp;<br>&nbsp;<br>&nbsp;<br>&nbsp;<br>&nbsp;
							</div>
						</td>
							
						<td class="valign-top" style="width: 47.5%">
							<table class="table table-striped table-danger" style="margin-bottom: 0px;">
								<thead>
									<tr>
										<th class="centered bg-danger text-light" colspan="2">Negativos</th>
									</tr>
								</thead>
							</table>

							<div style="overflow-y: auto; max-height: 400px;">
								<table class="table table-striped table-danger">
									<tbody>
										<tr ng-repeat="negativo in AOC.diagnosticos.negativos">
											<td>{{ negativo.diagnostico }}</td>
											<td style="width: 5%;">
												<button class="btn btn-vsm btn-danger"
													ng-really-message="Deseja realmente excluir este diagnóstico?"
													ng-really-click="AOC.deleteDiagnostico(negativo.id)">
													<i class="glyphicon glyphicon-trash"></i>
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</td>
					</tr>
				</table>

				

				<div class="horizontal-divider spacing-sm"></div>

				<table class="full-width">
					<tr>
						<td>
							<input type="text" class="form-control" placeholder="Novo diagnóstico" ng-model="AOC.novoDiagnostico.diagnostico">
						</td>
						<td style="padding-left: 15px; width: 30%;">
							<select name="" id="" class="form-control" ng-model="AOC.novoDiagnostico.resultado">
								<option value="positivo">Positivo</option>
								<option value="negativo">Negativo</option>
							</select>
						</td>
						<td class="align-right" style="width: 15%;">
							<button class="btn btn-primary" ng-really-message="Deseja realmente adicionar este diagnóstico?" ng-really-click="AOC.addDiagnostico();">Adicionar</button>
						</td>
					</tr>
				</table>

			</div>
		</div>
	</div>
</div>