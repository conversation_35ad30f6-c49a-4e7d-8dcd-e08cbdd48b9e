<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
	<li class="active"><i class="glyphicon glyphicon-list"></i> Auditorias de OS</li>

</ol>
<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%;">
	<div class="table filter-section-container">
		<div class="tr">
			<div class="td centered" style="border-right: 1px solid #CCC; padding-right: 10px;">
				<button type="button" class="btn btn-info" data-toggle="modal" data-target="#diagnosticosModal" style="margin-top: 10px;">
					<i class="glyphicon glyphicon-cog btn-icon"></i>Gerenciar diagnósticos
				</button>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="id_os">ID da O.S.</label>
							<input class="form-control" type="text" id="id_os" ng-model="AOC.filtros.id_os">
						</td>
						<td>
							<label for="id_ticket">ID do ticket</label>
							<input class="form-control" type="text" id="id_ticket"
								ng-model="AOC.filtros.id_ticket">
						</td>
					</tr>
					<tr>
						<td>
							<label for="diagnostico">Diagnóstico</label>
							<select class="form-control" id="diagnostico" ng-model="AOC.filtros.diagnostico">
								<option value="todos">Todos</option>
								<option value="positivos">Positivos</option>
								<option value="negativos">Negativos</option>
							</select>
						</td>
						<td>
							<label for="status_os">Status da O.S.</label>
							<select class="form-control" id="status_os" ng-model="AOC.filtros.status_os">
								<option value="todos">Todos</option>
								<option value="Aberta">Aberta</option>
								<option value="Finalizada">Finalizada</option>
								<option value="Encaminhada">Encaminhada</option>
								<option value="Assumida">Assumida</option>
								<option value="Agendada">Agendada</option>
								<option value="Reagendar">Reagendar</option>
							</select>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center" style="width: 200px;">
					<tr>
						<td>
							<label for="tecnico">Técnico</label>
							<input class="form-control" type="text" id="tecnico" ng-model="AOC.filtros.tecnico">
						</td>
					</tr>
					<tr>
						<td>
							<label for="cidade">Cidade</label>
							<select class="form-control" id="cidade" ng-model="AOC.filtros.cidade">
								<option value="todas">Todas</option>
								<option value="Andradas">Andradas</option>
								<option value="Poços de Caldas">Poços de Caldas</option>
								<option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
								<option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
								<option value="Campestre">Campestre</option>
								<option value="São João da Boa Vista">São João da Boa Vista</option>
							</select>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="border-right: 1px solid #CCC;">
				<table class="filter-spaced-table self-to-center">
					<tr>
						<td>
							<label for="aberta_em__inicio">Aberta em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="aberta_em__inicio" ng-model="AOC.filtros.aberta_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="aberta_em__final" ng-model="AOC.filtros.aberta_em__final">
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label for="finalizada_em__inicio">Finalizada em</label>
							<div class="input-group">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">De</span>
								<input type="date" class="form-control input-date-md" id="finalizada_em__inicio" ng-model="AOC.filtros.finalizada_em__inicio">
								<span class="input-group-addon" style="font-weight: bold; font-size: 9pt;">a</span>
								<input type="date" class="form-control input-date-md" id="finalizada_em__final" ng-model="AOC.filtros.finalizada_em__final">
							</div>
						</td>
					</tr>
				</table>
			</div>
			<div class="td" style="padding-left: 15px;">
				<button class="btn btn-default" ng-click="AOC.resetFiltros();">
					<i class="glyphicon glyphicon-trash btn-icon"></i>Limpar
				</button>
				<button class="btn btn-primary" ng-click="AOC.atualizarBusca();">
					<i class="glyphicon glyphicon-search btn-icon"></i>Buscar
				</button>
				<br>
				<button class="btn btn-primary" ng-click="AOC.downloadRelatorio();" style="margin-top: 10px;">
					<i class="glyphicon glyphicon-download-alt btn-icon"></i>Baixar resultados
				</button>
			</div>
		</div>
	</div>
</div>
<table class="table table-striped">
	<thead>
		<tr>
			<th class="centered">Status</th>
			<th>ID</th>
			<th class="centered">Aberta em</th>
			<th>Técnico</th>
			<th>ID ticket</th>
			<th>Cliente</th>
			<th class="centered">Cidade</th>
			<th>Mensagem de resposta</th>
			<th class="centered">Finalizada em</th>
			<th>Diagnóstico da auditoria</th>
		</tr>
	</thead>
	<tbody>
		<tr ng-repeat="os in AOC.ordensServico" ng-class="$index == AOC.selectedOsIndex ? 'selected' : ''" ng-click="AOC.abrirOs(os.id, this);">
			<td class="centered">
				<span class="label white-bordered" ng-class="{'label-info': os.status === 'Agendada', 'label-success': os.status === 'Finalizada', 'label-warning': os.status === 'Reagendar', 'label-primary' : !['Agendada', 'Finalizada', 'Reagendar'].includes(os.status)}" style="display: block; width: 100%;">{{os.status | uppercase}}</span>
			</td>
			<td class="valign-middle">
				<b>{{os.id_os}}</b>
			</td>
			<td class="centered" title="{{os.data_abertura | amDateFormat:'dddd'}}">
				{{os.data_abertura | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
			</td>
			<td>
				{{os.tecnico}}
			</td>
			<td>
				{{os.id_ticket}}
			</td>
			<td>
				{{os.cliente}}
			</td>
			<td class="centered" title="{{os.cidade}}">
				<span class="label label-primary" style="display: block; width: 100%;">
					{{os.cidade_cod ? os.cidade_cod : '-'}}
				</span>
			</td>
			<td title="{{os.mensagem_resposta}}">
				{{os.mensagem_resposta.length > 40 ? os.mensagem_resposta.substr(0, 40) + '...' : os.mensagem_resposta}}
			</td>
			<td class="centered" title="{{os.data_fechamento | amDateFormat:'dddd'}}">
				{{os.status === 'Finalizada' ? (os.data_fechamento | amDateFormat:'DD/MM/YYYY HH:mm:ss') : '-'}}
			</td>
			<td ng-class="{'bg-success': os.resultado == 'positivo', 'bg-danger': os.resultado == 'negativo'}">
				{{os.diagnostico}}
			</td>
		</tr>
	</tbody>
</table>

<div class="text-center">

	<div class="text-center">
		<uib-pagination total-items="AOC.pagination.size" ng-model="AOC.pagination.page" ng-change="AOC.pageChanged()"
			items-per-page="AOC.pagination.count" max-size="9" previous-text="Anterior" next-text="Próximo"
			boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm">
		</uib-pagination>
	</div>
	<div class="text-center">
		Página <span class="badge">{{ AOC.pagination.page}}</span> de <span class="badge">{{ AOC.pagination.pages}}</span>
		de <span class="badge">{{ AOC.pagination.size}}</span> registro(s)</span>
	</div>
</div>

<div ng-include="'app/relatorios/auditorias-os/modal.diagnosticos.html'"></div>