(function () {
	'use strict';

	angular
		.module('app')
		.controller('AuditoriasOsController', AuditoriasOsController);

	/** @ngInject */
	function AuditoriasOsController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window, $localStorage, AlertService) {

		var vm = this;
		vm.atualizarBusca = atualizarBusca;
		vm.resetFiltros = resetFiltros;
		vm.pageChanged = pageChanged;
		vm.downloadRelatorio = downloadRelatorio;
		vm.selectedOsIndex = null;
		vm.selectedOs = {};
		vm.pagination = {
			page: 1,
			size: 0,
			count: 0
		};
		vm.diagnosticos = {
			positivos: [],
			negativos: []
		};


		var dateFields = [
			'aberta_em__inicio',
			'aberta_em__final',
			'finalizada_em__inicio',
			'finalizada_em__final'
		];

		vm.filtro = '';
		vm.filtros = {};
		vm.resultados = [];

		var aberta_em__inicio = new Date();
		aberta_em__inicio.setDate(aberta_em__inicio.getDate() - 30);
		var aberta_em__final = new Date();

		vm.defaultFiltros = {
			id_os: '',
			id_ticket: '',
			diagnostico: 'todos',
			status_os: 'todos',
			tecnico: '',
			login: '',
			cidade: 'todas',
			aberta_em__inicio: aberta_em__inicio,
			aberta_em__final: aberta_em__final,
			finalizada_em__inicio: '',
			finalizada_em__final: ''
		};

		vm.novoDiagnostico = {
			diagnostico: '',
			resultado: 'positivo'
		};

		vm.addDiagnostico = addDiagnostico;
		vm.deleteDiagnostico = deleteDiagnostico;

		angular.element(document).ready(function () {
			getFiltros();
			buscarOs();
			$('input').on('keydown', function (e) {
				if (e.keyCode === 13) {
					atualizarBusca();
				}
			});
			
			$(document).on('shown.bs.modal', '#diagnosticosModal', function (e) {
				getDiagnosticos();
			});
		});

		function atualizarBusca() {
			vm.pagination.page = 1;
			pageChanged();
		}

		function pageChanged() {
			var urlApi = '/relatorios/auditorias-os?page=' + vm.pagination.page;

			Object.keys(vm.filtros).forEach(function (key) {
				urlApi += '&' + key + '=';

				if (dateFields.includes(key))
					urlApi += $filter('date')(vm.filtros[key], "yyyy-MM-dd");
				else
					urlApi += vm.filtros[key];
			});

            $location.url(urlApi);
		}

		function buscarOs() {
			var page = $routeParams['page'] && $routeParams['page'] > 0 ? $routeParams['page'] : 1;

			$http({
				url: API_CONFIG.url + '/relatorios/auditorias-os/buscar',
				method: 'POST',
				data: {filtros:vm.filtros, page: page, download: false}
			}).then(function (response) {
				vm.ordensServico = response.data.list;
				vm.pagination = response.data.pagination;
			}, function (response) {
				console.log('error');
			});
		}

		function downloadRelatorio() {
			var page = $routeParams['page'] && $routeParams['page'] > 0 ? $routeParams['page'] : 1;

			var hoje = new Date();

			var filename = 'auditorias_os_' + hoje.getDate()+ '-' + (hoje.getMonth()+1) +'-'+hoje.getFullYear()+ '.csv';

			$http({
				url: API_CONFIG.url + '/relatorios/auditorias-os/buscar',
				method: 'POST',
				data: { filtros: vm.filtros, page: page, download: true },
				responseType: 'arraybuffer'
			}).then(function (response) {

				var headers = response.headers();
				var contentType = headers['content-type'];

				var blob = new Blob([response.data], {
					type: contentType
				});
				if (navigator.msSaveBlob)
					navigator.msSaveBlob(blob, filename);
				else {
					var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
					if (saveBlob === undefined) {
						var linkElement = document.createElement('a');
						try {
							var blob = new Blob([response.data], { type: contentType });
							var url = window.URL.createObjectURL(blob);
							linkElement.setAttribute('href', url);
							linkElement.setAttribute("download", filename);

							var clickEvent = new MouseEvent("click", {
								"view": window,
								"bubbles": true,
								"cancelable": false
							});
							linkElement.dispatchEvent(clickEvent);
						} catch (ex) {
							console.log(ex);
						}
					}

				}

			}, function (error) {
				console.log(error);
			});
		};   

		function getFiltros() {
			Object.keys(vm.defaultFiltros).forEach(function (key) {
				vm.filtros[key] = $routeParams[key] !== undefined ? $routeParams[key] : vm.defaultFiltros[key];

				var timeZoneHoursOffset = 3;

				if (dateFields.includes(key)) {
					var utc = new Date(vm.filtros[key]);
					vm.filtros[key] = new Date(utc.getTime() + 3600 * 1000 * timeZoneHoursOffset);
					vm.filtros[key + 'Str'] = $filter('date')(vm.filtros[key], 'yyyy-MM-dd');
				}
			});
		}

		function getDiagnosticos() {
			vm.diagnosticos.positivos = [];
			vm.diagnosticos.negativos = [];

			$http({
				url: API_CONFIG.url + '/relatorios/diagnosticos-auditoria',
				method: 'GET'
			}).then(function (response) {
				if (response.data.status == 'success') {
					vm.diagnosticos.positivos = response.data.positivos;
					vm.diagnosticos.negativos = response.data.negativos;
				}
			});
		}

		function addDiagnostico() {
			$http({
				url: API_CONFIG.url + '/relatorios/diagnosticos-auditoria',
				method: 'POST',
				data: {
					diagnostico: vm.novoDiagnostico.diagnostico,
					resultado: vm.novoDiagnostico.resultado
				},
			}).then(function (response) {
				if (response.data.status == 'success') {
					vm.novoDiagnostico.diagnostico = '';
					alert('O diagnóstico foi adicionado.');
					getDiagnosticos();
				}
			});
		}

		function deleteDiagnostico(id_diagnostico) {
			$http({
				url: API_CONFIG.url + '/relatorios/diagnosticos-auditoria/' + id_diagnostico,
				method: 'DELETE'
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O diagnóstico foi excluído.');
					getDiagnosticos();
				}
			});
		}

		function resetFiltros() {
			angular.copy(vm.defaultFiltros, vm.filtros);
		}
	}

})();
