'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/relatorios/ipfixo', {
                templateUrl: 'app/relatorios/ipfixo/ipfixo.html',
                controller: 'IpFixoController',
                controllerAs: 'IFC',
                title: 'IPs Fixos',
                authorize: ['relatorios.read', 'relatorios.write']
            })

            .when('/relatorios/personalizados', {
                templateUrl: 'app/relatorios/personalizados/personalizados.html',
                controller: 'RelPersController',
                controllerAs: 'RPC',
                title: 'Relatórios Personalizados',
                authorize: ['relatorios.read', 'relatorios.write', 'marketing.read', 'marketing.write']
            })

            .when('/relatorios/ordens-servico', {
                templateUrl: 'app/relatorios/ordens-servico/ordens.servico.html',
                controller: 'OrdensServicoController',
                controllerAs: 'OSC',
                title: 'Ordens de serviço',
                authorize: ['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']
            })

            .when('/relatorios/auditorias-os', {
                templateUrl: 'app/relatorios/auditorias-os/auditorias.os.html',
                controller: 'AuditoriasOsController',
                controllerAs: 'AOC',
                title: 'Auditorias de OS',
                authorize: ['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write']
            })

            .when('/relatorios/contratos-cancelados', {
                templateUrl: 'app/relatorios/contratos-cancelados/contratos.cancelados.html',
                controller: 'ContratosCanceladosController',
                controllerAs: 'CCC',
                title: 'Contratos cancelados',
                authorize: ['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write'],

            })

            .when('/relatorios/pedidos-reservados', {
                templateUrl: 'app/relatorios/pedidos-reservados/pedidos.reservados.html',
                controller: 'PedidosReservadosController',
                controllerAs: 'PRC',
                title: 'Pedidos reservados',
                authorize: ['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write'],

            })

            .when('/relatorios/capacidade-olts', {
                templateUrl: 'app/relatorios/capacidade-olts/capacidade.olts.html',
                controller: 'CapacidadeOltsController',
                controllerAs: 'COC',
                title: 'Capacidade dos OLTs',
                authorize: ['relatorios.read', 'relatorios.write', 'auxiliares.read', 'auxiliares.write'],

            })

    });
