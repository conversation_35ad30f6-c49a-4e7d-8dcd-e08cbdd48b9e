(function () {
    'use strict';

    angular
        .module('app')
        .controller('RelPersController', RelPersController);

    /** @ngInject */
    function RelPersController($http, API_CONFIG) {

        var vm = this;
        vm.download_relatorio = download_relatorio;

        vm.resultados = [];

        var url = API_CONFIG.url + '/relatorios/personalizados';
        $http({
            method: 'GET',
            url: url
        }).then(function (response) {
            vm.resultados = response.data;
        });    

      function download_relatorio(id, nome){
      
        var url = API_CONFIG.url + '/relatorios/personalizados/' + id;
        $http({
            method: 'GET',
            url: url,
            responseType: 'arraybuffer'
        }).then(function (response) {
  
            var headers = response.headers();
            var filename = nome + '.csv';
            var contentType = headers['content-type'];
 
            var blob = new Blob([response.data], {
                type: contentType
            });
            if (navigator.msSaveBlob)
             navigator.msSaveBlob(blob, filename);
            else {
                var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
                if (saveBlob === undefined) {
                    var linkElement = document.createElement('a');
                    try {
                        var blob = new Blob([response.data], { type: contentType });
                        var url = window.URL.createObjectURL(blob);
                        linkElement.setAttribute('href', url);
                        linkElement.setAttribute("download", filename);
  
                        var clickEvent = new MouseEvent("click", {
                            "view": window,
                            "bubbles": true,
                            "cancelable": false
                        });
                        linkElement.dispatchEvent(clickEvent);
                    } catch (ex) {
                        console.log(ex);
                    }
                }
                
            }
            
        }, function(error) {
            console.log(error);
        });
    };   

}

})();
