<ol class="breadcrumb">

	<li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
	<li><i class="glyphicon glyphicon-ban-circle"></i> Capacidade dos OLTs</li>
</ol>
<div class="barra align-center"
	style="padding: 10px; display: flex; text-align: center; width: 100%; min-height: 90px;">
	<div class="table filter-section-container" style="width: auto; margin: 0 auto;">
		<div class="tr">
			<div class="td centered" style="border-right: 1px solid #CCC; padding: 0px 20px;">
				<label for="">OLT</label>
				<select class="form-control" ng-model="COC.selectedOlt" ng-change="COC.selectedPlaca = '';" style="min-width: 100px;">
					<option value="">Todos</option>
					<option value="{{olt}}" ng-repeat="(olt, placas) in COC.olts_placas">{{olt}}</option>
				</select>
			</div>
			<div class="td centered" style="border-right: 1px solid #CCC; padding: 0px 20px;">
				<label for="">Placa</label>
				<select ng-disabled="COC.selectedOlt === null" class="form-control" ng-model="COC.selectedPlaca" style="min-width: 100px;">
					<option disabled ng-if="COC.selectedOlt === null">Selecione um OLT...</option>
					<option value="" ng-if="COC.selectedOlt !== null">Todas</option>
					<option value="{{placa}}" ng-repeat="placa in COC.olts_placas[COC.selectedOlt]">{{placa}}</option>
				</select>
			</div>
			<div class="td centered" style="padding-left: 20px; padding-top: 19px;">
				<img src="assets/images/ajax-loader.gif" ng-show="COC.atualizandoLista"> <button class="btn btn-default btn-sm"
					ng-click="COC.getCapacidadeOlts();"><i class="glyphicon glyphicon-refresh"></i> Atualizar</button>
			</div>
		</div>
	</div>
</div>
<table class="table table-striped table-bordered valign-middle align-center">
	<thead>
		<tr>
			<td>OLT</td>
			<td>Placa</td>
			<td>Porta</td>
			<td>Capacidade <button type="button" class="btn btn-vsm" ng-class="{'btn-primary': !COC.sort.capacidade, 'btn-success': COC.sort.capacidade === 'asc' || COC.sort.capacidade === 'desc'}" ng-click="COC.sortCapacidade()"><i class="glyphicon" ng-class="{'glyphicon glyphicon-sort-by-attributes': COC.sort.capacidade !== 'desc', 'glyphicon glyphicon-sort-by-attributes-alt': COC.sort.capacidade === 'desc'}"></i></button></td>
		</tr>
	</thead>
	<tbody>
		<tr ng-if="(porta.olt === COC.selectedOlt || COC.selectedOlt == '') && (COC.selectedPlaca == '' || COC.selectedPlaca == porta.placa)" ng-repeat="porta in COC.capacidadeOlts">
			<td>{{porta.olt}}</td>
			<td>{{porta.placa}}</td>
			<td>{{porta.porta}}</td>
			<td ng-class="{'text-success-dark': porta.total <= 100, 'text-warning-dark': porta.total > 100 && porta.total <= 120, 'text-danger-dark': porta.total > 120}">{{porta.total}} / 128 - ({{(porta.total / (128/100)).toFixed(2)}}%)</td>
		</tr>
	</tbody>
</table>