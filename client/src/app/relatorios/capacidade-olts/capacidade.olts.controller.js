(function () {
	'use strict';

	angular
		.module('app')
		.controller('CapacidadeOltsController', CapacidadeOltsController);

	/** @ngInject */
	function CapacidadeOltsController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

		var vm = this;

		vm.capacidadeOlts = [];
		vm.getCapacidadeOlts = getCapacidadeOlts;
		vm.olts_placas = {};
		vm.selectedOlt = '';
		vm.selectedPlaca = '';
		vm.atualizandoLista = false;
		vm.sortCapacidade = sortCapacidade;
		vm.sort = {};

		activate();

		function activate() {
			getCapacidadeOlts();
		}

		function sortCapacidade() {
			if (!vm.sort.capacidade || vm.sort.capacidade === 'asc')
				vm.sort.capacidade = 'desc';
			else
				vm.sort.capacidade = 'asc';
			
			vm.capacidadeOlts.sort(function (a, b) {
				if (vm.sort.capacidade === 'asc')
					return a.total - b.total;
				if (vm.sort.capacidade === 'desc')
					return b.total - a.total;
			});
		}

		function getCapacidadeOlts() {
			vm.atualizandoLista = true;

			$http({
				url: API_CONFIG.url + '/relatorios/capacidade-olts',
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				vm.atualizandoLista = false;
				if (response.data.status && response.data.status === 'success') {
					angular.copy(response.data.list, vm.capacidadeOlts);
					handleResultData();
					refreshSorting();
				}
				else {
					alert('Ocorreu um erro ao carregar a listagem.');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function refreshSorting() {
			if (vm.sort.capacidade) {
				if (vm.sort.capacidade === 'asc')
					vm.sort.capacidade = 'desc';
				else if (vm.sort.capacidade === 'desc')
					vm.sort.capacidade = 'asc';
				
				sortCapacidade();
			}
		}

		function handleResultData() {
			vm.capacidadeOlts.forEach(function (port, index) {
				if (!vm.olts_placas.hasOwnProperty(port.olt))
					vm.olts_placas[port.olt] = [];

				if (!vm.olts_placas[port.olt].includes(port.placa))
					vm.olts_placas[port.olt].push(port.placa);
			});

			// if(vm.selectedOlt === null)
			// 	vm.selectedOlt = vm.capacidadeOlts[0].olt;
		}

	}

})();
