<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-briefcase"></i> Gerencia</li>
    <li class="active"><i class="glyphicon glyphicon-check"></i> Mailing</li>
</ol>

<ul class="nav nav-tabs">
    <li class="active">
        <a data-target="#filtro" data-toggle="tab" style="cursor: pointer;">
            <i class="glyphicon glyphicon-filter"></i> Filtro </a>
    </li>

    <li>
        <a data-target="#lista" data-toggle="tab" style="cursor: pointer;">
            <i class="glyphicon glyphicon-list-alt"></i> Lista </a>
    </li>
</ul>

<div class="tab-content">
    <div id="filtro" class="tab-pane fade in active">
        <div class="barra">
            <div class="form-group">
                <button ng-click="MC.filtrar()" class="btn btn-primary btn-info btn-incluir text-center" type="submit"
                    ng-disabled="frmFiltro.$invalid" title="Salvar Lista" authorize="['comercial.write']">
                    <span class="glyphicon glyphicon-check"></span><br>Filtrar
                </button>

            </div>
        </div>

        <div class="row top-buffer">
            <form class="form-horizontal" name="frmFiltro">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="observacao"></label>
                    <div class="col-md-6">
                        <div class="table-responsive" ng-show="MC.alias.status=='OK'">
                            <span class="counter pull-right"></span>

                            <table class="table table-striped table-hover table-bordered">
                                <thead>
                                    <tr>
                                        <th class="vert-align text-center">Grupo</th>
                                        <th class="vert-align text-center">Validade</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="vert-align text-center"><a
                                                href="mailto:{{MC.alias.alias}}">{{MC.alias.alias}}</a> <button
                                                class="btn btn-primary" ng-click-copy="{{MC.alias.alias}}"><i
                                                    class="glyphicon glyphicon-copy"></i> Copiar</button></td>
                                        <td class="vert-align text-center">{{MC.alias.validade |
                                            amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="form-group">

                    <label class="col-md-2 control-label" for="observacao"></label>
                    <div class="col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h3 class="panel-title">Localização</h3>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">

                                    <label class="col-md-2 control-label" for="bairro">Cidade(s):</label>
                                    <ol class="nya-bs-select" title="Todas as Cidades" id="cidade"
                                        ng-model="MC.cidades_selecionadas" data-live-search="true" data-size="8"
                                        multiple="true" ng-change="MC.postCidade(MC.cidades_selecionadas)">

                                        <li nya-bs-option="cidade in MC.cidades track by $index">
                                            <a>{{ cidade }} <span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>

                                    </ol>
                                    <button class="btn btn-primary" ng-click="MC.limpar_cidade()">Limpar</button>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="observacao"></label>
                    <div class="col-md-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">Infraestrutura</h3>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="transmissor">Transmissor(es):</label>
                                    <ol class="nya-bs-select" title="Todos os Transmissores" id="transmissor"
                                        ng-model="MC.transmissores_selecionados" data-live-search="true" data-size="8"
                                        multiple="true">

                                        <li nya-bs-option="transmissor in MC.transmissores track by $index">
                                            <a>{{ transmissor }} <span
                                                    class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>

                                    </ol>
                                    <button class="btn btn-primary" ng-click="MC.limpar_transmissor()">Limpar</button>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="olt">OLTs:</label>
                                    <ol class="nya-bs-select" title="Todas as OLTs" id="olt"
                                        ng-model="MC.olts_selecionadas" data-live-search="true" data-size="8"
                                        multiple="true">

                                        <li nya-bs-option="olt in MC.olts track by $index">
                                            <a>{{ olt }} <span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>

                                    </ol>
                                    <button class="btn btn-primary" ng-click="MC.limpar_olt()">Limpar</button>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="olt">Placas:</label>
                                    <ol class="nya-bs-select" title="Todas as placas" id="placa"
                                        ng-model="MC.placas_selecionadas" data-live-search="true" data-size="8"
                                        multiple="true">

                                        <li class="nya-bs-option" data-value="1">
                                            <a>1<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="2">
                                            <a>2<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="3">
                                            <a>3<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="4">
                                            <a>4<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="5">
                                            <a>5<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="6">
                                            <a>6<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="7">
                                            <a>7<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="8">
                                            <a>8<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>

                                        <li class="nya-bs-option" data-value="11">
                                            <a>11<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="12">
                                            <a>12<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="13">
                                            <a>13<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="14">
                                            <a>14<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="15">
                                            <a>15<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="16">
                                            <a>16<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="17">
                                            <a>17<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="18">
                                            <a>18<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                    </ol>
                                    <button class="btn btn-primary" ng-click="MC.limpar_placa()">Limpar</button>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="porta">Portas:</label>
                                    <ol class="nya-bs-select" title="Todas as portas" id="porta"
                                        ng-model="MC.portas_selecionadas" data-live-search="true" data-size="8"
                                        multiple="true">

                                        <li class="nya-bs-option" data-value="1">
                                            <a>1<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="2">
                                            <a>2<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="3">
                                            <a>3<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="4">
                                            <a>4<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="5">
                                            <a>5<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="6">
                                            <a>6<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="7">
                                            <a>7<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="8">
                                            <a>8<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="9">
                                            <a>9<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="10">
                                            <a>10<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="11">
                                            <a>11<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="12">
                                            <a>12<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="13">
                                            <a>13<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="14">
                                            <a>14<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="15">
                                            <a>15<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                        <li class="nya-bs-option" data-value="16">
                                            <a>16<span class="glyphicon glyphicon-ok check-mark"></span></a>
                                        </li>
                                    </ol>
                                    <button class="btn btn-primary" ng-click="MC.limpar_porta()">Limpar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </form>


        </div>
    </div>

    <div id="lista" class="tab-pane fade in">
        <div class="table-responsive">
            <span class="counter pull-right"></span>

            <table class="table table-striped table-hover table-bordered">
                <thead>
                    <tr>
                        <th class="vert-align text-center">Grupo</th>
                        <th class="vert-align text-center">Filtro</th>
                        <th class="vert-align text-center">Validade</th>

                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="grupo in MC.grupos">
                        <td class="vert-align text-center"><a href="mailto:{{grupo.alias}}">{{grupo.alias}}</a> <button
                                class="btn btn-primary" ng-click-copy="{{grupo.alias}}"><i
                                    class="glyphicon glyphicon-copy"></i> Copiar</button></td>
                        <td class="vert-align text-center">{{grupo.filtro}}</td>
                        <td class="vert-align text-center">{{grupo.validade | amDateFormat:'DD/MM/YYYY HH:mm:ss'}} <a
                                class="btn btn-primary" ng-click="MC.updateGrupo(grupo)"><i
                                    class="glyphicon glyphicon-repeat"></i> Atualizar</a></td>

                    </tr>
                </tbody>
            </table>

        </div>
    </div>
</div>