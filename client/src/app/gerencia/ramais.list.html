<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

	<li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
	<li><i class="glyphicon glyphicon-usd"></i> Gerência</li>
	<li><i class="glyphicon glyphicon-earphone"></i> Ramais</li>
</ol>
<div class="table barra align-center" style="padding-top: 7px;">
	<div class="tr">
		<div class="td" style="width: 30%;"></div>
		<div class="td" style="width: 40%;">
			<form class="form-inline" role="form">
				<div class="form-group">
					<label for="ramais_cidade">Cidade:</label>
					<select class="form-control" ng-model="RC.cidade" ng-change="RC.getRamaisList()" id="ramais_cidade">
						<!--option value="todas">Todas</option-->
						<option value="pocos">Poços de Caldas</option>
						<option value="andradas">Andradas</option>
						<option value="pinhal">Espírito Santo do Pinhal</option>
						<option value="jardim">Santo Antônio do Jardim</option>
						<option value="campestre">Campestre</option>
						<option value="saojoao">São João da Boa Vista</option>
					</select>
				</div>
				<div class="form-group">
					<label for="ramais_setor">Setor:</label>
					<select class="form-control" ng-model="RC.setor" ng-change="RC.getRamaisList()" id="ramais_setor">
						<option value="todos">Todos</option>
						<option ng-repeat="setor in RC.setores" value="{{setor.id}}">{{setor.setor}}</option>
					</select>
				</div>
			</form>
		</div>
		<div class="td">
			<button class="btn btn-primary" ng-click="RC.initFrmnovoramal()" data-toggle="modal"
				data-target="#frmnovoramal" style="margin-right: 10px;"><i
					class="glyphicon glyphicon-plus btn-icon"></i>Novo ramal</button>
			<button class="btn btn-success" ng-click="RC.gerarWallpaper()" style="margin-left: 10px;"><i
					class="glyphicon glyphicon-picture btn-icon"></i>Atualizar
				wallpaper</button>
		</div>
	</div>

</div>
<div class="table-responsive">
	<span class="counter pull-right"></span>
	<table class="table table-striped table-bordered valign-middle align-center"
		style="width: 50%; margin: 0 auto; margin-bottom: 50px;">
		<thead>
			<tr ng-if="RC.ramais.length > 0">
				<th>Editar</th>
				<th>Ramal</th>
				<th>Usuário</th>
				<th>Setor</th>
				<th>Cidade</th>
				<th>Ativo</th>
				<th>Excluir</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-if="RC.ramais.length == 0">
				<td>
					Não foram encontrados ramais, de acordo com o filtro especificado.
				</td>
			</tr>
			<tr ng-if="RC.ramais.length > 0" ng-repeat="ramal in RC.ramais"
				ng-class="ramal.ativo == 0 ? 'desativado' : ''">
				<td>
					<a class="btn btn-primary btn-sm" title="Editar ramal" data-toggle="modal" data-target="#frmramal"
						ng-click="RC.selecionar(ramal); RC.initFrmramal()"><i class=" glyphicon
						glyphicon-pencil"></i></a>
				</td>
				<td>{{ramal.ramal}}</td>
				<td>{{ramal.usuario}}</td>
				<td>{{ramal.setor}}</td>
				<td>{{ramal.cidade_txt}}</td>
				<td>
					<span class="label"
						ng-class="ramal.ativo == 1 ? 'label-success' : 'label-default'">{{ramal.ativo == 1 ? 'SIM' : 'NÃO'}}</span>
				</td>
				<td>
					<a class="btn btn-danger btn-sm" title="Excluir ramal"
						ng-really-message="Tem certeza de que deseja excluir o ramal <b>{{ramal.ramal}}</b> do usuário <b>{{ramal.usuario}}</b>?"
						ng-really-click="RC.excluir(ramal.id)"><i class="glyphicon glyphicon-trash"></i></a>
				</td>
			</tr>
		</tbody>
	</table>
</div>
<div ng-include="'app/gerencia/ramal.form.html'"></div>
<div ng-include="'app/gerencia/ramal.novo.form.html'"></div>