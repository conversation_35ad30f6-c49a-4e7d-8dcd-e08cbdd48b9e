<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-briefcase"></i> Gerencia</li>
    <li class="active"><i class="glyphicon glyphicon-check"></i> <PERSON><PERSON><PERSON><PERSON></li>

</ol>

<div class="barra">
    <div class="form-group">
     <div class="form-group pull-right">
        <form class="form-inline" role="form">
         <div class="form-group">
         <p class="input-group">Período:</p>
         <p class="input-group">
          <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="DC.dtinicio" is-open="DC.opened1" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="dtinicio" ng-change="DC.getEquipes(DC.cidade)"/>
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="DC.open1()"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>

          <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="DC.dtfim" is-open="DC.opened2" clear-text="Limpar" close-text="Fechar" current-text="Hoje" ng-change="DC.getEquipes(DC.cidade)"/>
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="DC.open2()"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>

        </p>
        <p class="input-group">
              <select class="form-control" ng-model="DC.cidade" ng-init="DC.cidade = 'POÇOS DE CALDAS'" ng-change="DC.getEquipes(DC.cidade)">
                    <option value="TODAS AS CIDADES"> Todas as Cidades</option>
                    <option value="POÇOS DE CALDAS">Poços de Caldas</option>
                    <option value="ANDRADAS">Andradas</option>
                    <option value="CAMPESTRE">Campestre</option>
                    <option value="SANTO ANTONIO DO JARDIM">Santo Antônio do Jardim</option>
                    <option value="ESPIRITO SANTO DO PINHAL">Espírito Santo do Pinhal</option>
              </select>
        </p>
        <p class="input-group">
              <select class="form-control" ng-model="DC.tipo"
              ng-init="DC.tipo = 'VISITAS'" ng-change="DC.getEquipes(DC.cidade)">
                <option value="VISITAS">Visitas Técnicas</option>
                <option value="INSTALACAO">Instalações</option>
              </select>
        </p>
        <p class="input-group">
              <select class="form-control" ng-model="DC.equipe" ng-options="o for o in DC.equipes">
              </select>
        <!--
          <ol class="nya-bs-select form-control"
                     title="Selecione uma Equipe"
                          id="equipe"
                          ng-model="DC.equipe"
                          data-live-search="true"
                          data-size="8"
                      >
                      <li nya-bs-option="equipe in DC.equipes">
                              <a>
                                  {{ equipe }}
                                  <span class="glyphicon glyphicon-ok check-mark"></span>
                              </a>
                          </li>
               </ol>
               -->
        </p>

        <p class="input-group">

                      <button class="btn btn-default" title="Atualizar" ng-click="DC.atualizaGrafico()"><i class="glyphicon glyphicon-repeat"></i> Atualizar</button>
          </p>
        </div>
      <div class="form-group">


      </div>
        </form>



</div>
</div>
</div>




<ul class="nav nav-tabs">
    <li class="active">
      <a data-target="#grafico_desempenho" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-equalizer"></i> Gráfico </a>
    </li>
    <li>
      <a data-target="#total" data-toggle="tab" style="cursor: pointer;">
        <i class="glyphicon glyphicon-ok-sign"></i> Total por Equipe </a>
    </li>
    <li><a data-target="#detalhes" data-toggle="tab" style="cursor: pointer;" ><i class="glyphicon glyphicon-list-alt" ></i> Detalhes</a></li>
</ul>

<div class="tab-content">
  <div id="grafico_desempenho" class="tab-pane fade in active">
      <highchart id="desempenho" config="DC.graficoDesempenho"></highchart>
      <hr />
      <highchart id="desempenhotempo" config="DC.graficoDesempenhoTempo"></highchart>
  </div>
  <div id="total" class="tab-pane fade in">
      <h5 class="text-center">Atendidas / Executadas</h5>
     <div class="pre-scrollable" style="height:300px;">
    <table class="table table-striped table-hover table-bordered">
  <thead>
    <tr>
      <th class="vert-align text-center">#</th>
      <th class="vert-align text-center">Equipe</th>
      <th class="vert-align text-center">Atendimento</th>
      <th class="vert-align text-center">Execução</th>
    </tr>
  </thead>
  <tbody>
    <tr ng-repeat="total in DC.totais">
      <td class="vert-align text-center">{{ $index + 1}}</td>
      <td class="vert-align text-center">{{total.equipe}}</td>
      <td class="vert-align text-center">{{total.atendimento}}</td>
      <td class="vert-align text-center">{{total.execucao}}</td>
    </tr>
  </tbody>
</table>
 </div>
      <hr />
      <h5 class="text-center">Executadas - Tempo para execução</h5>
      <div class="pre-scrollable" style="height:300px;">
    <table class="table table-striped table-hover table-bordered">
  <thead>
    <tr>
      <th class="vert-align text-center">Equipe</th>
      <th class="vert-align text-center">12</th>
      <th class="vert-align text-center">24</th>
      <th class="vert-align text-center">48</th>
    </tr>
  </thead>
  <tbody>
    <tr ng-repeat="total in DC.totais_tempo">
      <td class="vert-align text-center">{{total.equipe}}</td>
      <td class="vert-align text-center">{{total.doze}}</td>
      <td class="vert-align text-center">{{total.vintequatro}}</td>
      <td class="vert-align text-center">{{total.quarentaoito}}</td>
    </tr>
  </tbody>
</table>
 </div>
</div>

 <div id="detalhes" class="tab-pane fade in">
<div class="pre-scrollable" style="height:300px;">
<table class="table table-striped table-hover table-bordered">
  <thead>
    <tr>
      <th class="vert-align text-center">Data</th>
      <th class="vert-align text-center">Equipe</th>
      <th class="vert-align text-center">Atendimento</th>
      <th class="vert-align text-center">Execução</th>
    </tr>
  </thead>

  <tbody>
    <tr ng-repeat="detalhe in DC.detalhes">
      <td class="vert-align text-center">{{detalhe.data | amDateFormat:'DD/MM/YYYY (dddd)'}}</td>
      <td class="vert-align text-center">{{detalhe.equipe}}</td>
      <td class="vert-align text-center">{{detalhe.atendimento}}</td>
      <td class="vert-align text-center">{{detalhe.execucao}}</td>
    </tr>
  </tbody>
</table>
     </div>
</div>
</div>
