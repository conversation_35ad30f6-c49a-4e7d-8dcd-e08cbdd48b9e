(function () {
	'use strict';

	angular
		.module('app')
		.controller('DesempenhoTecnicosController', DesempenhoTecnicosController);

	/** @ngInject */
	function DesempenhoTecnicosController($http, API_CONFIG, $filter, $timeout, $scope) {

		var vm = this;

		// 'simples' ou 'ponderada' (alternável pelo botão do gráfico)
		vm.tipoPontuacaoGrafico = 'ponderada';

		vm.os_tecnicos_arr = [];
		vm.sortOrder = '';
		vm.sortColumn = '';
		vm.sortOsTecnicos = sortOsTecnicos;

		vm.filter = {};

		var inicio = new Date();
		var fim = new Date();
		inicio.setDate(1);

		inicio.setHours(0, 0, 0, 0);
		fim.setHours(0, 0, 0, 0);

		vm.filter.data_fechamento_inicio = inicio;
		vm.filter.data_fechamento_fim = fim;

		vm.grupos_os = [];

		vm.graficoDesempenho = null;
		vm.graficoDesempenhoSubtitle = null;
		vm.changeTipoPontuacaoGrafico = changeTipoPontuacaoGrafico;
		vm.getGraficoDesempenhoData = getGraficoDesempenhoData;

		vm.novo_grupo = {
			nome: '',
			peso: 1
		};

		vm.tecnicos = {
			exibidos: [
			],
			nao_exibidos: [
			]
		};

		vm.editando_grupos = [];
		vm.editando_tecnicos = [];

		vm.grupo_selecionado = {};

		vm.diagnosticos_sem_grupo = [];

		vm.get_os_list = get_os_list;

		vm.adicionar_grupo = adicionar_grupo;
		vm.excluir_grupo = excluir_grupo;
		vm.selecionar_grupo = selecionar_grupo;
		vm.habilitar_edicao = habilitar_edicao;
		vm.habilitar_edicao_tecnico = habilitar_edicao_tecnico;
		vm.cancelar_edicao = cancelar_edicao;
		vm.cancelar_edicao_tecnico = cancelar_edicao_tecnico;
		vm.salvar_edicao = salvar_edicao;
		vm.salvar_edicao_tecnico = salvar_edicao_tecnico;

		vm.adicionar_diagnostico = adicionar_diagnostico;
		vm.excluir_diagnostico = excluir_diagnostico;

		vm.listar_tecnicos = listar_tecnicos;
		vm.adicionar_tecnico = adicionar_tecnico;
		vm.excluir_tecnico = excluir_tecnico;

		vm.pageChanged = pageChanged;

		vm.carregando_dados_modal_grupos = false;
		vm.carregando_dados_modal_tecnicos = false;
		vm.carregando_os_list = false;

		vm.os_tecnicos = {};
		vm.os_list = [];
		vm.os_list_paged = [];

		vm.pagination = {
			itemsPerPage: 20
		};

		vm.grupos_diagnosticos = [];

		activate();

		function activate() {
			listar_tecnicos();
			initializeDesempenhoChart();
			get_os_list();

			$(document).on('shown.bs.modal', '#modal-grupos-os', function (e) {
				listar_grupos_diagnosticos();
			});

			$(document).on('shown.bs.modal', '#modal-tecnicos-os', function (e) {
				vm.editouTecnicos = false;
			});

			$(document).on('hidden.bs.modal', '#modal-tecnicos-os', function (e) {
				if(vm.editouTecnicos) {
					get_os_list();
					getGraficoDesempenhoData();
				}
			});
		}

		function pageChanged() {
			vm.pagination.pages = Math.ceil(vm.os_list.length / vm.pagination.itemsPerPage);

			vm.os_list_paged = vm.os_list.slice(
				(vm.pagination.page - 1) * vm.pagination.itemsPerPage,
				vm.pagination.page * vm.pagination.itemsPerPage
			);
		}

		function get_os_list(options) {
			var downloadCsv = false;
			if (options && options.download && options.download === true) {
				downloadCsv = true;
			}

			var downloadTarget = '';
			if (options && options.downloadTarget) {
				downloadTarget = options.downloadTarget;
			}

			if (!downloadCsv) {
				vm.carregando_os_list = true;
				vm.grupos_os = [];
				vm.diagnosticos_sem_grupo = [];
				vm.os_tecnicos = {};
				vm.pagination.page = 1;
			}

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/os-list',
				data: { filter: vm.filter, downloadCsv: downloadCsv, downloadTarget: downloadTarget },
				responseType: downloadCsv ? 'arraybuffer' : 'text',
				ignoreLoadingBar: true
			}).then(function (response) {

				if (downloadCsv) {
					var hoje = new Date();

					var filename = '';
					if (downloadTarget == 'os_list')
						filename = 'relatorio_lista_os_tecnicos_'

					else if (downloadTarget == 'os_tecnicos')
						filename = 'relatorio_resumo_os_tecnicos_'

					filename += '_' + vm.filter.data_fechamento_inicio.toLocaleDateString('pt-BR') + '_a_' + vm.filter.data_fechamento_fim.toLocaleDateString('pt-BR') + '.csv';

					var headers = response.headers();
					var contentType = headers['content-type'];

					var blob = new Blob([response.data], {
						type: contentType
					});
					if (navigator.msSaveBlob)
						navigator.msSaveBlob(blob, filename);
					else {
						var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
						if (saveBlob === undefined) {
							var linkElement = document.createElement('a');
							try {
								var blob = new Blob([response.data], { type: contentType });
								var url = window.URL.createObjectURL(blob);
								linkElement.setAttribute('href', url);
								linkElement.setAttribute("download", filename);

								var clickEvent = new MouseEvent("click", {
									"view": window,
									"bubbles": true,
									"cancelable": false
								});
								linkElement.dispatchEvent(clickEvent);
							} catch (ex) {
								console.log(ex);
							}
						}

					}

					return;
				}

				vm.carregando_os_list = false;
				angular.copy(response.data.os_tecnicos, vm.os_tecnicos);
				angular.copy(response.data.grupos_diagnosticos, vm.grupos_diagnosticos);
				angular.copy(response.data.os_list, vm.os_list);

				// Converte objeto para array
				vm.os_tecnicos_arr = Object.keys(vm.os_tecnicos).map(function (key) {
					return vm.os_tecnicos[key];
				});

				sortOsTecnicos('Total');

				vm.pagination.size = vm.os_list.length;
				pageChanged();

			}, function (error) {
				console.log(error);
			});
		}


		function sortOsTecnicos(sortColumn) {
			if (sortColumn == vm.sortColumn) {
				vm.sortOrder = vm.sortOrder == 'asc' ? 'desc' : 'asc';
			}
			else {
				vm.sortOrder = 'desc';
			}

			vm.sortColumn = sortColumn;

			vm.os_tecnicos_arr.sort(customSort);
		}

		function customSort(a, b) {
			if (vm.sortColumn !== 'Total') {
				var valorA = a.os[vm.sortColumn].quantidade;
				var valorB = b.os[vm.sortColumn].quantidade;
			}
			else {
				var valorA = a.total_ponderado;
				var valorB = b.total_ponderado;
			}

			if (valorA == valorB)
				return 0
			else if (valorA > valorB)
				return vm.sortOrder == 'asc' ? 1 : -1;
			else
				return vm.sortOrder == 'asc' ? -1 : 1;
		}

		function getGraficoDesempenhoData() {
			setGraficoDesempenhoLoading();

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/grafico',
				data: { filter: vm.filter },
				ignoreLoadingBar: true
			}).then(function (response) {	
				console.log('response:', response.data);
				vm.graficoDesempenho.setSubtitle({text: null});
				var desempenhoSeries = getDesempenhoSeries(response.data);
				setDesempenhoChart(desempenhoSeries);
				
			}, function (error) {
				console.log(error);
			});
		}

		function listar_tecnicos() {
			vm.carregando_dados_modal_tecnico = true;

			$http({
				method: 'GET',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/listar-tecnicos'
			}).then(function (response) {
				vm.carregando_dados_modal_tecnico = false;

				vm.tecnicos = response.data
			}, function (error) {
				console.log(error);
			});
		}

		function adicionar_tecnico(id_tecnico) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/adicionar-tecnico',
				data: { id_tecnico: id_tecnico }
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O técnico foi adicionado com sucesso.');
					listar_tecnicos();
					get_os_list();
				}
				else {
					alert('Ocorreu um erro ao adicionar o técnico');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_tecnico(id_tecnico) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/excluir-tecnico',
				data: { id_tecnico: id_tecnico }
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O técnico foi excluído com sucesso.');
					listar_tecnicos();
					get_os_list();
				}
				else {
					alert('Ocorreu um erro ao excluir o técnico');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function listar_grupos_diagnosticos() {
			vm.carregando_dados_modal_grupos = true;
			vm.grupos_os = [];
			vm.diagnosticos_sem_grupo = [];

			$http({
				method: 'GET',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/grupos-diagnosticos'
			}).then(function (response) {
				console.log('response.data:', response.data);
				vm.carregando_dados_modal_grupos = false;
				vm.grupos_os = response.data.grupos;
				vm.diagnosticos_sem_grupo = response.data.diagnosticos_sem_grupo;

				window.setTimeout(function () {
					if (vm.grupo_selecionado.hasOwnProperty('id')) {
						var grupo_selecionado = vm.grupo_selecionado;
						vm.grupo_selecionado = {};
						selecionar_grupo(grupo_selecionado);
					}
				}, 500);
			}, function (error) {
				console.log(error);
			});
		}

		function selecionar_grupo(grupo) {
			$('.grupo-os-diagnosticos').collapse('hide');

			if (vm.grupo_selecionado.id == grupo.id) {
				vm.grupo_selecionado = {};
				return;
			}

			vm.grupo_selecionado = grupo;

			$('.grupo-os-diagnosticos.grupo-' + grupo.id).collapse('show');
		}

		function cancelar_edicao(id_grupo) {
			var index = vm.editando_grupos.indexOf(id_grupo);
			vm.editando_grupos.splice(index, 1);
		}

		function cancelar_edicao_tecnico(id_tecnico) {
			var index = vm.editando_tecnicos.indexOf(id_tecnico);
			vm.editando_tecnicos.splice(index, 1);
		}

		function habilitar_edicao(id_grupo) {
			vm.editando_grupos.push(id_grupo);
			angular.element('#input-peso-grupo-' + id_grupo).focus();
		}

		function habilitar_edicao_tecnico(id_tecnico) {
			vm.editando_tecnicos.push(id_tecnico);
			angular.element('#select-tecnico-terceirizado-' + id_tecnico).focus();
		}

		function salvar_edicao(id_grupo) {
			var nome = $('#input-nome-grupo-' + id_grupo).val().trim();
			var peso = parseInt($('#input-peso-grupo-' + id_grupo).val().trim());

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/editar-grupo',
				data: {
					id_grupo: id_grupo,
					nome: nome,
					peso: peso
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('As alterações foram salvas com sucesso.');
					listar_grupos_diagnosticos();
					get_os_list();
				}
				else {
					alert('Ocorreu um erro ao alterar as informações do grupo.');
				}
			}, function (error) {
				console.log(error);
			});
			

			// $http();
			//		onSuccess: reloadRelatorio();

			cancelar_edicao(id_grupo);
		}

		function salvar_edicao_tecnico(id_tecnico) {
			var terceirizado = $('#select-tecnico-terceirizado-' + id_tecnico).val().trim();

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/editar-tecnico',
				data: {
					id_tecnico: id_tecnico,
					terceirizado: terceirizado
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('A alteração foi salva com sucesso.');
					listar_tecnicos();
					get_os_list();
					vm.editouTecnicos = true;
				}
				else {
					alert('Ocorreu um erro ao alterar as informações do técnico.');
				}
			}, function (error) {
				console.log(error);
			});
			

			// $http();
			//		onSuccess: reloadRelatorio();

			cancelar_edicao_tecnico(id_tecnico);
		}

		function adicionar_diagnostico(diagnostico) {
			var id_grupo = vm.grupo_selecionado.id;

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/adicionar-diagnostico',
				data: {
					id_grupo: id_grupo,
					diagnostico: diagnostico
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O diagnóstico foi adicionado ao grupo');
					listar_grupos_diagnosticos();
				}
				else {
					alert('Ocorreu um erro ao adicionar o diagnóstico ao grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_diagnostico(id_grupo, diagnostico) {
			var id_grupo = vm.grupo_selecionado.id;

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/excluir-diagnostico',
				data: {
					id_grupo: id_grupo,
					diagnostico: diagnostico
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O diagnóstico foi excluído do grupo');
					listar_grupos_diagnosticos();
				}
				else {
					alert('Ocorreu um erro ao excluir o diagnóstico do grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function adicionar_grupo() {
			var nome = vm.novo_grupo.nome;
			var peso = vm.novo_grupo.peso;

			if (nome.trim() == '' || peso.toString().trim() == '' || peso < 1) {
				alert('Erro no preenchimento. Verifique os campos "nome" e "peso".');
				return false;
			}

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/adicionar-grupo',
				data: {
					nome: nome,
					peso: peso
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O grupo foi adicionado com sucesso');
					vm.novo_grupo = {
						nome: '',
						peso: 1
					};
					listar_grupos_diagnosticos();
					get_os_list();
				}
				else {
					alert('Ocorreu um erro ao adicionar o grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_grupo(id_grupo) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-tecnicos/excluir-grupo',
				data: {
					id: id_grupo
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O grupo foi excluído com sucesso');
					listar_grupos_diagnosticos();
					get_os_list();
				}
				else {
					alert('Ocorreu um erro ao excluir o grupo.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function getDesempenhoSeries(dadosGrafico) {
			var tipoPontuacao = vm.tipoPontuacaoGrafico;

			var indexPontuacao = '';
			if(tipoPontuacao == 'simples')
				indexPontuacao = 'pontos_simples';
			else if (tipoPontuacao == 'ponderada')
				indexPontuacao = 'pontos_ponderados'

			var desempenhoSeries = [];
			for(var tecnicoId in dadosGrafico) {
				var tecnico = dadosGrafico[tecnicoId];
				var tecnicoObj = {
					name: tecnico.tecnico,
					data: []
				};
				
				if(tecnicoObj.name == 'Média') {
					tecnicoObj.color = '#0000FF';
				}
				
				else if(tecnicoObj.name == '25% acima da média') {
					tecnicoObj.color = '#00FF00';
				}
				
				if(tecnicoObj.name == '25% abaixo da média') {
					tecnicoObj.color = '#FF0000';
				}
				for(var i = 0; i < tecnico[indexPontuacao].length; i++) {
					var pontuacao = tecnico[indexPontuacao][i];
					var pontuacaoObj = {
						x: Date.UTC(pontuacao.ano, pontuacao.mes-1, 1),
						y: pontuacao.pontos
					}
					tecnicoObj.data.push(pontuacaoObj);
				}

				desempenhoSeries.push(tecnicoObj);
			}

			return desempenhoSeries;
		}

		function setDesempenhoChart(series) {
			clearDesempenhoSeries();
			vm.graficoDesempenhoSubtitle = 'Pontuação ' + vm.tipoPontuacaoGrafico;

			series.forEach(function(serie){
				vm.graficoDesempenho.addSeries(serie);
			});
		}

		function setGraficoDesempenhoLoading() {
			if(!isDesempenhoChartInitialized())
				return false;

			clearDesempenhoSeries();

			vm.graficoDesempenho.setSubtitle({text: 'Carregando...'});
		}

		function clearDesempenhoSeries() {
			if(!isDesempenhoChartInitialized())
				return false;

			while (vm.graficoDesempenho.series.length) {
				vm.graficoDesempenho.series[0].remove();
			}
		}

		function isDesempenhoChartInitialized() {
			return (vm.graficoDesempenho
				&& typeof vm.graficoDesempenho === 'object');
		}

		function initializeDesempenhoChart() {
			var today = new Date();
			var currentMonth = today.getMonth();
			var currentYear = today.getFullYear();

			// Subtract 6 months from current date, to get initial chart date (years rollback automatically if needed)
			today.setMonth(currentMonth - 6);
			var initialMonth = today.getMonth();
			var initialYear = today.getFullYear();

			vm.filter.graficoDesempenho = {
				periodoInicial: new Date(initialYear, initialMonth),
				periodoFinal: new Date(currentYear, currentMonth)
			}

			vm.graficoDesempenho = Highcharts.chart('graficoDesempenho', {

				// Desabilitados título e subtítulo (adicionados direto no HTML, para melhorar o layout)
				title: { text: null },
				subtitle: { text: null },
			
				yAxis: {
					title: {
						text: 'Pontuação'
					}
				},
			
				legend: {
					layout: 'vertical',
					align: 'right',
					verticalAlign: 'middle'
				},
			
				xAxis: {
					tickWidth: 0,
					type: 'datetime',
					labels: {
						dateTimeLabelFormats: {
							month: '%b'
						}
					}
				},
			
				series: [],
			
				responsive: {
					rules: [{
						condition: {
							maxWidth: 800
						},
						chartOptions: {
							legend: {
								layout: 'horizontal',
								align: 'center',
								verticalAlign: 'bottom'
							}
						}
					}]
				}
			
			});

			getGraficoDesempenhoData();
		}

		function changeTipoPontuacaoGrafico(tipo) {
			var tipoAtual = vm.tipoPontuacaoGrafico;

			vm.tipoPontuacaoGrafico = tipo;

			if(vm.tipoPontuacaoGrafico != tipoAtual) {
				getGraficoDesempenhoData();
			}
		}

	}

})();