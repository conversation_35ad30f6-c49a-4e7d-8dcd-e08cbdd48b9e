<div class="modal" id="modal-tecnicos-os" tabindex="-1" role="dialog" aria-labelledby="frmramal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="width: 900px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmramal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Gerenciar técnicos a serem exibidos
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<div class="table">
					<div class="tr">
						<div class="td" style="width: 55%; padding-right: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Técnicos exibidos</h3>
								</div>
								<div class="panel-body" style="padding: 0px;">
									<input type="text" class="form-control" ng-model="search_tecnicos_exibidos.nome" placeholder="Pesquisar...">
									<div style="max-height: 300px; overflow-y: auto;">
										<table class="table full-width" ng-if="!DTC.carregando_dados_modal_tecnicos && DTC.tecnicos.exibidos.length == 0" style="margin-bottom: 0px;">
											<tr class="bg-warning">
												<td style="padding: 0px 10px;">Não há técnicos a serem exibidos.</td>
											</tr>
										</table>
										<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;" ng-if="DTC.tecnicos.exibidos.length > 0">
											<thead ng-if="!DTC.carregando_dados_modal_tecnicos">
												<tr>
													<th class="valign-middle" style="padding-left: 10px;">Técnico</th>
													<th class="valign-middle" style="padding-left: 10px;">Terceirizado</th>
													<th class="align-center valign-middle"></th>
												</tr>
											</thead>
											<tbody>
												<tr ng-if="DTC.carregando_dados_modal_tecnicos">
													<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30"
															src="assets/images/spinner-blue.gif" /></td>
												</tr>
												<tr ng-if="!DTC.carregando_dados_modal_tecnicos"
													ng-repeat="tecnico in DTC.tecnicos.exibidos | filter:search_tecnicos_exibidos">
													<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ tecnico.nome
													}}</td>
													<td class="align-center valign-middle" style="border-left: none; padding-left: 10px !important;">
														<span ng-if="!DTC.editando_tecnicos.includes(tecnico.id)">{{ tecnico.terceirizado ? 'Sim' : 'Não' }}</span>
														<select ng-if="DTC.editando_tecnicos.includes(tecnico.id)" class="form-control" style="height: 18px;" id="select-tecnico-terceirizado-{{ tecnico.id }}">
															<option value="true" ng-selected="tecnico.terceirizado">Sim</option>
															<option vaule="false" ng-selected="!tecnico.terceirizado">Não</option>
														</select>
													</td>
													<td class="align-center valign-middle">
														<div ng-if="!DTC.editando_tecnicos.includes(tecnico.id)">
															<button title="Editar técnico" class="btn btn-vsm btn-primary"
																ng-click="$event.stopPropagation(); DTC.habilitar_edicao_tecnico(tecnico.id);"><i
																	class="glyphicon glyphicon-pencil"></i></button>
															<button title="Excluir técnico da lista" class="btn btn-vsm btn-danger"
																ng-really-message="Deseja realmente excluir este técnico da lista?"
																ng-really-click="DTC.excluir_tecnico(tecnico.id);"><i class="glyphicon glyphicon-trash"></i></button>
														</div>

														<div ng-if="DTC.editando_tecnicos.includes(tecnico.id)">
															<button title="Salvar alterações" class="btn btn-vsm btn-success"
																ng-click="$event.stopPropagation(); DTC.salvar_edicao_tecnico(tecnico.id);"><i
																	class="glyphicon glyphicon-ok"></i></button>
															<button title="Cancelar edição" class="btn btn-vsm btn-danger"
																ng-click="$event.stopPropagation(); DTC.cancelar_edicao_tecnico(tecnico.id);"><i
																	class="glyphicon glyphicon-ban-circle"></i></button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="td" style="padding-left: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Técnicos não exibidos</h3>
								</div>
								<div class="panel-body" style="padding: 0px;">
									<input type="text" class="form-control" ng-model="search_tecnicos_nao_exibidos.nome" placeholder="Pesquisar...">
									<div style="max-height: 300px; overflow-y: auto;">
										<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
											<thead ng-if="!DTC.carregando_dados_modal_tecnicos">
												<tr>
													<th class="valign-middle" style="padding-left: 10px;">Técnico</th>
													<th class="align-center valign-middle" style="width: 60px;">Adicionar</th>
												</tr>
											</thead>
											<tbody>
												<tr ng-if="DTC.carregando_dados_modal_tecnicos">
													<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
												</tr>
												<tr ng-if="!DTC.carregando_dados_modal_tecnicos" ng-repeat="tecnico in DTC.tecnicos.nao_exibidos | filter:search_tecnicos_nao_exibidos">
													<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ tecnico.nome }}</td>
													<td class="align-center valign-middle">
														<button title="Adicionar técnico à lista" class="btn btn-vsm btn-success" ng-really-message="Deseja realmente adicionar este técnico à lista?" ng-really-click="DTC.adicionar_tecnico(tecnico.id);"><i
																class="glyphicon glyphicon-plus"></i></button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>
				
			</div>
			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#modal-tecnicos-os">Fechar</button>
			</div>
		</div>
	</div>
</div>