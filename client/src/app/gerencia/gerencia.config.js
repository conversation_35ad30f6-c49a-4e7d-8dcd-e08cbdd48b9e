'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider

            .when('/gerencia/desempenho-tecnicos', {
                templateUrl: 'app/gerencia/desempenho.tecnicos.html',
                controller: 'DesempenhoTecnicosController',
                controllerAs: 'DTC',
                title: 'An<PERSON><PERSON><PERSON> de desempenho: Técnicos de Campo',
                authorize: ['gerencia.read', 'gerencia.write']
            })

            .when('/gerencia/desempenho-comercial', {
                templateUrl: 'app/gerencia/desempenho.comercial.html',
                controller: 'DesempenhoComercialController',
                controllerAs: 'DCC',
                title: 'Análise de desempenho: Comercial',
                authorize: ['gerencia.read', 'gerencia.write']
            })

            .when('/gerencia/mailing', {
                templateUrl: 'app/gerencia/mailing.html',
                controller: 'MailingController',
                controllerAs: 'MC',
                title: 'Mailing',
                authorize: ['gerencia.read', 'gerencia.write'],
            })

            .when('/gerencia/ramais', {
                templateUrl: 'app/gerencia/ramais.list.html',
                controller: 'RamaisListController',
                controllerAs: 'RC',
                title: 'Ramais',
                authorize: ['wallpaper.read', 'wallpaper.write'],
            })
    });
