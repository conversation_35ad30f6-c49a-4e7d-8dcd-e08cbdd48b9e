<div class="modal" id="frmnovoramal" tabindex="-1" role="dialog" aria-labelledby="frmnovoramal" aria-hidden="true"
   modal="showModal" close="cancel()">

   <div class="modal-dialog" style="width: 400px;">
      <div class="modal-content">

         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" id="frmnovoramal_fechar">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
            </button>
            <h4 class="modal-title">
               Novo ramal
            </h4>
         </div>

         <!-- Modal Body -->
         <div class="modal-body" style="padding-bottom: 15px;">
            <div class="row">
               <div class="tab-content">
                  <div id="dados" class="tab-pane fade in active">
                     <table class="table bottom-spaced" style="width: 80%; margin: 0 auto;">
                        <tbody>
                           <tr>
                              <td class="align-center" colspan="2" style="padding-bottom: 20px !important;">
                                 <label for="novoramal_ativo">Ativo:</label>
                                 <select id="novoramal_ativo" ng-model="RC.novoramal.ativo" class="form-control"
                                    style="width: 80px; margin: 0 auto;">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                 </select>
                              </td>
                           </tr>
                           <tr>
                              <td style="width: 50%; padding-right: 5px;">
                                 <label for="novoramal_numero">Ramal:</label>
                                 <input type="text" class="form-control" ng-model="RC.novoramal.ramal"
                                    id="novoramal_numero">
                              </td>
                              <td style="width: 50%; padding-left: 5px;">
                                 <label for="novoramal_usuario">Usuário:</label>
                                 <input type="text" class="form-control" ng-model="RC.novoramal.usuario"
                                    id="novoramal_usuario">
                              </td>
                           </tr>
                           <tr>
                              <td style="padding-right: 5px;">
                                 <label for="novoramal_cidade">Cidade:</label>
                                 <select class="form-control" ng-model="RC.novoramal.cidade" id="novoramal_cidade">
                                    <option value="pocos">Poços de Caldas</option>
                                    <option value="andradas">Andradas</option>
                                    <option value="pinhal">Espírito Santo do Pinhal</option>
                                    <option value="jardim">Santo Antônio do Jardim</option>
                                    <option value="campestre">Campestre</option>
                                    <option value="saojoao">São João da Boa Vista</option>
                                 </select>
                              </td>
                              <td style="padding-left: 5px;">
                                 <label for="novoramal_setor">Setor:</label>
                                 <select class="form-control" ng-model="RC.novoramal.setor_id" id="novoramal_setor">
                                    <option ng-repeat="setor in RC.setores" value="{{setor.id}}">{{setor.setor}}
                                    </option>
                                 </select>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                  </div>
               </div>
            </div>
         </div>
         <!-- Modal Footer -->
         <div class="modal-footer">
            <button class="btn btn-primary" ng-click="RC.adicionar()"><i
                  class="fa fa-check btn-icon"></i>Adicionar</button>
         </div>
      </div>
   </div>