<div class="modal" id="modal-grupos-os" tabindex="-1" role="dialog" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="width: 800px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmramal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Gerenciar grupos/pesos de OS
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<div class="table">
					<div class="tr">
						<div class="td" style="width: 60%; padding-right: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Grupos de OS</h3>
								</div>
								<div class="panel-body" style="padding: 0px; max-height: 300px; overflow-y: auto;">
									<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
										<thead ng-if="!DTC.carregando_dados_modal_grupos">
											<tr>
												<th style="width: 1%; border-right: none;"></th>
												<th style="border-left: none;">Grupo</th>
												<th class="align-center valign-middle" style="width: 15%;">Peso</th>
												<th class="align-center valign-middle" style="width: 80px;"></th>
											</tr>
										</thead>
										<tbody>
											<tr ng-if="DTC.carregando_dados_modal_grupos">
												<td class="align-center valign-middle" colspan="3" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
											</tr>
											<tr ng-if="!DTC.carregando_dados_modal_grupos" ng-repeat-start="grupo in DTC.grupos_os" class="pointer hoverable"
												ng-class="{'selected': grupo.id == DTC.grupo_selecionado.id}" ng-click="DTC.selecionar_grupo(grupo)">
												<td class="align-center valign-middle" style="border-right: none;">
													<i class="glyphicon"
														ng-class="grupo.id == DTC.grupo_selecionado.id ? 'glyphicon-chevron-up' : 'glyphicon-chevron-down'"></i>
												</td>
												<td class="valign-middle" style="border-left: none;">
													<span ng-if="!DTC.editando_grupos.includes(grupo.id)">{{ grupo.grupo }} ({{ grupo.diagnosticos.length }})</span>
													<input ng-if="DTC.editando_grupos.includes(grupo.id)" type="text"
														class="form-control input-sm align-center" id="input-nome-grupo-{{ grupo.id }}"
														value="{{ grupo.grupo }}">
												</td>
												<td class="align-center valign-middle">
													<span ng-if="!DTC.editando_grupos.includes(grupo.id)">{{ grupo.peso }}</span>
													<input ng-if="DTC.editando_grupos.includes(grupo.id)" type="number"
														class="form-control input-sm align-center" id="input-peso-grupo-{{ grupo.id }}"
														value="{{ grupo.peso }}">
												</td>
												<td class="align-center valign-middle">
													<div ng-if="!DTC.editando_grupos.includes(grupo.id)">
														<button title="Editar peso" class="btn btn-vsm btn-primary"
															ng-click="$event.stopPropagation(); DTC.habilitar_edicao(grupo.id);"><i
																class="glyphicon glyphicon-pencil"></i></button>
														<button title="Excluir grupo" class="btn btn-vsm btn-danger"
															ng-click="$event.stopPropagation();"
															ng-really-message="Deseja realmente excluir o grupo?"
															ng-really-click="$event.stopPropagation(); DTC.excluir_grupo(grupo.id);"><i
																class="glyphicon glyphicon-trash"></i></button>
													</div>
							
													<div ng-if="DTC.editando_grupos.includes(grupo.id)">
														<button title="Salvar peso" class="btn btn-vsm btn-success"
															ng-click="$event.stopPropagation(); DTC.salvar_edicao(grupo.id);"><i
																class="glyphicon glyphicon-ok"></i></button>
														<button title="Cancelar edição" class="btn btn-vsm btn-danger"
															ng-click="$event.stopPropagation(); DTC.cancelar_edicao(grupo.id);"><i
																class="glyphicon glyphicon-ban-circle"></i></button>
													</div>
												</td>
											</tr>
											<tr ng-repeat-end>
												<td colspan="4" style="padding: 0 !important;">
													<div id="grupo-os-diagnosticos-{{ grupo.id }}"
														class="grupo-os-diagnosticos grupo-{{ grupo.id }} collapse">
														<div class="vert-align">
															<table class="table table-warning table-bordered" style="margin-bottom: 0px;">
																<thead ng-if="grupo.diagnosticos.length > 0">
																	<th style="padding-left: 40px !important;">Diagnóstico</th>
																	<th></th>
																</thead>
																<tbody>
																	<tr class="bg-warning" ng-if="grupo.diagnosticos.length < 1">
																		<td class="valign-middle" colspan="4" style="padding-left: 40px !important;">
																			Não há diagnósticos vinculados a este grupo.
																		</td>
																	</tr>
																	<tr ng-repeat="diagnostico in grupo.diagnosticos">
																		<td style="padding-left: 40px !important;">{{ diagnostico }}</td>
																		<td class="align-center" style="width: 80px;">
																			<button class="btn btn-danger btn-vsm"
																				ng-really-message="Deseja realmente excluir o diagnóstico deste grupo?"
																				ng-really-click="DTC.excluir_diagnostico(grupo.id, diagnostico)">
																				<i class="glyphicon glyphicon-trash"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>

							<div class="input-group input-group-sm">
								<span class="input-group-addon">Novo grupo:</span>
								<input class="form-control" type="text" ng-model="DTC.novo_grupo.nome">
								<span class="input-group-addon">Peso:</span>
								<input class="form-control" type="number" ng-model="DTC.novo_grupo.peso" style="width: 70px;">
								<span class="input-group-btn">
									<button class="btn btn-primary btn-sm" ng-click="DTC.adicionar_grupo()">Adicionar</button>
								</span>
							</div>
						</div>
						<div class="td" style="padding-left: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Diagnósticos ainda não agrupados</h3>
								</div>
								<div class="panel-body" style="padding: 0px; max-height: 300px; overflow-y: auto;">
									<input type="text" class="form-control" ng-model="search" placeholder="Pesquisar...">
									<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
										<thead ng-if="!DTC.carregando_dados_modal_grupos">
											<tr>
												<th class="valign-middle" style="padding-left: 10px;">Diagnóstico</th>
												<th class="align-center valign-middle" style="width: 60px;"></th>
											</tr>
										</thead>
										<tbody>
											<tr ng-if="DTC.carregando_dados_modal_grupos">
												<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
											</tr>
											<tr ng-if="!DTC.carregando_dados_modal_grupos" ng-repeat="diagnostico in DTC.diagnosticos_sem_grupo | filter:search">
												<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ diagnostico }}</td>
												<td class="align-center valign-middle">
													<button title="Adicionar diagnóstico ao grupo selecionado" class="btn btn-vsm btn-success" ng-disabled="(DTC.grupo_selecionado | json) == '{}'" ng-really-message="Deseja realmente adicionar este diagnóstico ao grupo <b>{{ DTC.grupo_selecionado.grupo }}</b>?" ng-really-click="DTC.adicionar_diagnostico(diagnostico);"><i
															class="glyphicon glyphicon-plus"></i></button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>

						</div>
					</div>
				</div>
				
			</div>
			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#modal-grupos-os">Fechar</button>
			</div>
		</div>
	</div>
</div>