(function () {
  'use strict';

  angular
    .module('app')
    .controller('MailingController', MailingController);

  /** @ngInject */
  function MailingController($http, API_CONFIG, cfpLoadingBar, $rootScope,
    toaster, $routeParams, $location) {

    var vm = this;
    vm.limit = 20;
    vm.filtro = '';
    vm.tipo = 'nome';
    vm.termos = '';
    vm.cidades = [];
    vm.bairros = [];
    vm.escopos = [];
    vm.transmissores = [];
    vm.olts = [];
    vm.planos = [];
    vm.clientes = [];
    vm.grupos = [];
    vm.pagination = {};
    vm.busca = busca;
    vm.pageChanged = pageChanged;
    vm.postCidade = postCidade;
    vm.updateGrupo = updateGrupo;

    vm.filtrar = filtrar;
    //vm.desativa = desativa;
    //vm.retira = retira;
    //vm.ativa = ativa;
    vm.sort = sort;
    vm.limpar_cidade = limpar_cidade;
    vm.limpar_bairro = limpar_bairro;
    vm.limpar_transmissor = limpar_transmissor;
    vm.limpar_olt = limpar_olt;
    vm.limpar_placa = limpar_placa;
    vm.limpar_porta = limpar_porta;
    vm.cidades_selecionadas = [];
    vm.bairros_selecionados = [];
    vm.transmissores_selecionados = [];
    vm.olts_selecionadas = [];
    vm.alias = { 'alias': '' };

    activate();

    function activate() {

      getCidades();
      getTransmissores([]);
      getOlts([]);
      getGrupos();

      if ($routeParams.exito !== undefined) {
        vm.exito = $routeParams.exito;
      } else {
        vm.exito = '';
      }

      if ($routeParams.cliente !== undefined) {
        vm.cliente = $routeParams.cliente;
      } else {
        vm.cliente = '';
      }

      if ($routeParams.cdm !== undefined) {
        vm.cdm = $routeParams.cdm;
      } else {
        vm.cdm = '';
      }


      if ($routeParams.sortby !== undefined) {
        vm.sortBy = $routeParams.sortby;
      } else {
        vm.sortBy = 'datacad';
      }

      if ($routeParams.sortorder !== undefined) {
        vm.sortOrder = $routeParams.sortorder;
      } else {
        vm.sortOrder = 'dsc';
      }

      if ($routeParams.nome !== undefined) {
        vm.tipo = 'nome';
        vm.termos = $routeParams.nome;
        vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
      }

      if ($routeParams.logradouro !== undefined) {
        vm.tipo = 'logradouro';
        vm.termos = $routeParams.logradouro;
        vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
      }

      if ($routeParams.bairro !== undefined) {
        vm.tipo = 'bairro';
        vm.termos = $routeParams.bairro;
        vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
      }

      if ($routeParams.usuario !== undefined) {
        vm.tipo = 'usuario';
        vm.termos = $routeParams.usuario;
        vm.filtro = '&' + vm.tipo + '=|' + vm.termos + '|';
      }

      if ($routeParams.id !== undefined) {
        vm.tipo = 'id';
        vm.termos = $routeParams.id;
        vm.filtro = '&' + vm.tipo + '=' + vm.termos;
      }

      getData();

    }

    function getPlanos() {
      $http({
        url: API_CONFIG.url + '/comercial/planos',
        method: "POST"
      })
        .then(function (response) {
          vm.planos = response.data;
        },
          function (response) {

          });
    }

    function getGrupos() {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/grupos',
        method: "GET"
      })
        .then(function (response) {
          vm.grupos = response.data;
        },
          function (response) {

          });
    }

    function updateGrupo(grupo) {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/grupos/' + grupo.id,
        method: "PUT"
      })
        .then(function (response) {
          grupo.validade = response.data.validade;
          console.log(response.data);
        },
          function (response) {

          });
    }

    function getCidades() {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/cidades',
        method: "GET",
      }).then(function (response) {
        vm.cidades = response.data.dados;
      });
    };

    function getTransmissores(cidades) {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/transmissores',
        method: "POST",
        data: { cidades: cidades }
      })
        .then(function (response) {
          vm.transmissores = response.data.dados;
        });

    }

    function getOlts(cidades) {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/olts',
        method: "POST",
        data: { cidades: cidades }
      })
        .then(function (response) {
          vm.olts = response.data.dados;
        });
    }

    function postCidade(cidades) {
      /*
      $http({
        url: API_CONFIG.url + '/gerencia/mailing/bairros',
        method: "POST",
        data: { cidades: cidades }
      })
        .then(function (response) {
          vm.bairros = response.data.dados;
        });
      */
      //getTransmissores(cidades);
      //getOlts(cidades);

    }


    function filtrar() {
      $http({
        url: API_CONFIG.url + '/gerencia/mailing',
        method: "POST",
        data: {
          cidades: vm.cidades_selecionadas,
          transmissores: vm.transmissores_selecionados,
          olts: vm.olts_selecionadas,
          placas: vm.placas_selecionadas,
          portas: vm.portas_selecionadas
        }
      })
        .then(function (response) {
          vm.alias = response.data;
        },
          function (response) {

          });

    }

    function getData() {

      var urlApi = API_CONFIG.url + '/comercial/prospect?page=' + $routeParams.page + "&count=" +
        vm.limit + vm.filtro + '&sort-by=' + vm.sortBy + '&sort-order=' + vm.sortOrder;
      if (vm.exito !== '') urlApi += '&exito=' + vm.exito;
      if (vm.cliente !== '') urlApi += '&cliente=' + vm.cliente;
      if (vm.cdm !== '') urlApi += '&cdm=' + vm.cdm;


      $http.get(urlApi).then(function (response) {
        angular.copy(response.data.rows, vm.prospects);
        angular.copy(response.data.pagination, vm.pagination);
      });
    }

    function pageChanged() {
      var urlApi = '/comercial/prospects?page=' + vm.pagination.page +
        '&sortby=' + vm.sortBy + '&sortorder=' + vm.sortOrder + '&' + vm.tipo + '=' + vm.termos;
      if (vm.exito !== '') urlApi += '&exito=' + vm.exito;
      if (vm.cliente !== '') urlApi += '&cliente=' + vm.cliente;
      if (vm.cdm !== '') urlApi += '&cdm=' + vm.cdm;
      $location.url(urlApi);

    }

    function sort(field) {
      if (field == vm.sortBy) {
        if (vm.sortOrder == 'asc') {
          vm.sortOrder = 'dsc';
        } else {
          vm.sortOrder = 'asc';
        }
      }

      vm.sortBy = field;
      pageChanged();
    }

    function busca(termos) {

      vm.pagination.page = 1;
      vm.filtro = '&' + vm.tipo + '=|' + termos + '|';

      pageChanged();

    }

    function limpar_cidade() {
      vm.cidades_selecionadas = [];
      vm.bairros_selecionados = [];
      vm.transmissores_selecionados = [];
      vm.olts_selecionadas = [];
      vm.bairros = [];
      vm.pops = [];
      vm.escopos = [];
    }

    function limpar_bairro() {
      vm.bairros_selecionados = [];
    }

    function limpar_transmissor() {
      vm.transmissores_selecionados = [];
    }

    function limpar_olt() {
      vm.olts_selecionadas = [];
    }

    function limpar_placa() {
      vm.placas_selecionadas = [];
    }

    function limpar_porta() {
      vm.portas_selecionadas = [];
    }


  }

})();