(function () {
	'use strict';

	angular
		.module('app')
		.controller('RamaisListController', RamaisListController);

	/** @ngInject */
	function RamaisListController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window) {

		var vm = this;

		vm.cidade = 'pocos';
		vm.setor = 'todos';
		vm.ramais = [];
		vm.setores = [];
		vm.selecionar = selecionar;
		vm.selecionado = {};
		vm.getRamaisList = getRamaisList;
		vm.adicionar = adicionar;
		vm.salvar = salvar;
		vm.excluir = excluir;
		vm.novoramal_default = {
			'cidade': '',
			'setor': '',
			'ramal': '',
			'usuario': '',
			'ativo': '1'
		};
		vm.novoramal = {},
			vm.gerarWallpaper = gerarWallpaper,
			vm.initFrmnovoramal = initFrmnovoramal;

		activate();

		function activate() {
			resetNovoRamal();
			getRamaisList();
			getSetores();
		}

		function initFrmnovoramal() {
			if (vm.cidade !== 'todas')
				vm.novoramal.cidade = vm.cidade;
			if (vm.setor !== 'todos')
				vm.novoramal.setor_id = vm.setor;

			$window.setTimeout(function () {
				$('#novoramal_numero').focus();
			}, 100);
		}

		function gerarWallpaper() {
			$http({
				url: API_CONFIG.url + '/gerencia/wallpaper',
				method: "POST",
				ignoreLoadingBar: false
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O wallpaper foi gerado com sucesso! Ele será atualizado nos computadores do domínio no próximo login do usuário.');
				}
				else {
					alert('Houve um erro ao gerar o wallpaper.');
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function resetNovoRamal() {
			angular.copy(vm.novoramal_default, vm.novoramal);
		}

		function adicionar() {
			if (vm.novoramal.ramal.trim() == '' ||
				vm.novoramal.usuario.trim() == '' ||
				vm.novoramal.cidade.trim() == '' ||
				typeof (vm.novoramal.setor_id) === 'undefined') {
				alert('Todos os dados devem ser preenchidos.');
				return false;
			}

			$http({
				url: API_CONFIG.url + '/gerencia/ramais',
				method: "POST",
				data: { ramal: vm.novoramal },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O ramal ' + vm.novoramal.ramal + ' (' + vm.novoramal.usuario + ') foi adicionado.');
				}
				else {
					alert('Houve um erro ao adicionar o ramal.');
				}
				getRamaisList();
				resetNovoRamal();
				$('#frmnovoramal_fechar').click();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function salvar() {
			if (vm.selecionado.ramal.trim() == '' ||
				vm.selecionado.usuario.trim() == '' ||
				vm.selecionado.cidade.trim() == '' ||
				typeof (vm.selecionado.setor_id) === 'undefined') {
				alert('Todos os dados devem ser preenchidos.');
				return false;
			}

			$http({
				url: API_CONFIG.url + '/gerencia/ramais',
				method: "PUT",
				data: { ramal: vm.selecionado },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('As alterações no ramal ' + vm.selecionado.ramal + ' - ' + vm.selecionado.usuario + ' foram salvas.');
				}
				else {
					alert('Houve um erro ao salvar as alterações.');
				}
				getRamaisList();
				$('#frmramal_fechar').click();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function excluir(ramal_id) {
			$http({
				url: API_CONFIG.url + '/gerencia/ramais/' + ramal_id,
				method: "DELETE",
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O ramal foi excluído.');
				}
				else {
					alert('Houve um erro ao excluir o ramal.');
				}
				getRamaisList();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function selecionar(ramal) {
			angular.copy(ramal, vm.selecionado);
		}

		function getRamaisList() {
			$http({
				url: API_CONFIG.url + '/gerencia/ramais/' + vm.cidade + '/' + vm.setor,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				angular.copy(response.data, vm.ramais);
				// Transforma variavel ativo em string, pois o ng-model do select só funciona com string
				vm.ramais.forEach(function (element, index, array) {
					element.ativo = element.ativo.toString();
					element.setor_id = element.setor_id.toString();
				});
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function getSetores() {
			$http({
				url: API_CONFIG.url + '/gerencia/setores',
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				angular.copy(response.data, vm.setores);
				// Transforma variavel ativo em string, pois o ng-model do select só funciona com string
				vm.setores.forEach(function (element, index, array) {
					element.id = element.id.toString();
				});
			})
				.catch(function (err) {
					console.log(err);

				});
		}
	}

})();
