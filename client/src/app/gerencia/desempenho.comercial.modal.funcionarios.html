<div class="modal" id="modal-funcionarios-os" tabindex="-1" role="dialog" aria-labelledby="frmramal" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="width: 900px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmramal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Gerenciar funcionários a serem exibidos
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<div class="table">
					<div class="tr">
						<div class="td" style="width: 55%; padding-right: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Funcionários exibidos</h3>
								</div>
								<div class="panel-body" style="padding: 0px;">
									<input type="text" class="form-control" ng-model="search_funcionarios_exibidos.nome" placeholder="Pesquisar...">
									<div style="max-height: 300px; overflow-y: auto;">
										<table class="table full-width" ng-if="!DCC.carregando_dados_modal_funcionarios && DCC.funcionarios.exibidos.length == 0" style="margin-bottom: 0px;">
											<tr class="bg-warning">
												<td style="padding: 0px 10px;">Não há funcionários a serem exibidos.</td>
											</tr>
										</table>
										<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;" ng-if="DCC.funcionarios.exibidos.length > 0">
											<thead ng-if="!DCC.carregando_dados_modal_funcionarios">
												<tr>
													<th class="valign-middle" style="padding-left: 10px;">Funcionário</th>
													<th class="align-center valign-middle"></th>
												</tr>
											</thead>
											<tbody>
												<tr ng-if="DCC.carregando_dados_modal_funcionarios">
													<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30"
															src="assets/images/spinner-blue.gif" /></td>
												</tr>
												<tr ng-if="!DCC.carregando_dados_modal_funcionarios"
													ng-repeat="funcionario in DCC.funcionarios.exibidos | filter:search_funcionarios_exibidos">
													<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ funcionario.nome
													}}</td>
													<td class="align-center valign-middle">
														<div ng-if="!DCC.editando_funcionarios.includes(funcionario.id)">
															<button title="Excluir funcionário da lista" class="btn btn-vsm btn-danger"
																ng-really-message="Deseja realmente excluir este funcionário da lista?"
																ng-really-click="DCC.excluir_funcionario(funcionario.id);"><i class="glyphicon glyphicon-trash"></i></button>
														</div>

														<div ng-if="DCC.editando_funcionarios.includes(funcionario.id)">
															<button title="Salvar alterações" class="btn btn-vsm btn-success"
																ng-click="$event.stopPropagation(); DCC.salvar_edicao_funcionario(funcionario.id);"><i
																	class="glyphicon glyphicon-ok"></i></button>
															<button title="Cancelar edição" class="btn btn-vsm btn-danger"
																ng-click="$event.stopPropagation(); DCC.cancelar_edicao_funcionario(funcionario.id);"><i
																	class="glyphicon glyphicon-ban-circle"></i></button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="td" style="padding-left: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Funcionários não exibidos</h3>
								</div>
								<div class="panel-body" style="padding: 0px;">
									<input type="text" class="form-control" ng-model="search_funcionarios_nao_exibidos.nome" placeholder="Pesquisar...">
									<div style="max-height: 300px; overflow-y: auto;">
										<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
											<thead ng-if="!DCC.carregando_dados_modal_funcionarios">
												<tr>
													<th class="valign-middle" style="padding-left: 10px;">Funcionário</th>
													<th class="align-center valign-middle" style="width: 60px;">Adicionar</th>
												</tr>
											</thead>
											<tbody>
												<tr ng-if="DCC.carregando_dados_modal_funcionarios">
													<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
												</tr>
												<tr ng-if="!DCC.carregando_dados_modal_funcionarios" ng-repeat="funcionario in DCC.funcionarios.nao_exibidos | filter:search_funcionarios_nao_exibidos">
													<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ funcionario.nome }}</td>
													<td class="align-center valign-middle">
														<button title="Adicionar funcionário à lista" class="btn btn-vsm btn-success" ng-really-message="Deseja realmente adicionar este funcionário à lista?" ng-really-click="DCC.adicionar_funcionario(funcionario.id, funcionario.nome);"><i
																class="glyphicon glyphicon-plus"></i></button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>
				
			</div>
			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#modal-funcionarios-os">Fechar</button>
			</div>
		</div>
	</div>
</div>