(function () {
    'use strict';

    angular
        .module('app')
        .controller('DesempenhoController', DesempenhoController);

    /** @ngInject */
    function DesempenhoController($http, API_CONFIG, $filter) {

        var vm = this;

        vm.atualizaGrafico = atualizaGrafico;

        vm.filtro = '';
        vm.open1 = open1;
        vm.opened1 = false;
        vm.open2 = open2;
        vm.opened2 = false;
        vm.equipes = [];
        vm.detalhes = [];
        vm.totais = [];
        vm.cidade = 'POÇOS DE CALDAS';
        vm.equipe = 'TODAS AS EQUIPES';
        vm.tipo='VISITAS';

        var inicio = new Date();
        var fim = new Date();
        inicio.setDate(inicio.getDate() - 7);

        vm.dtinicio = inicio;
        vm.dtfim = fim;
        vm.getEquipes = getEquipes;

        activate();

        function activate(){
            getEquipes('POÇOS DE CALDAS');
            atualizaGrafico();
        }

        function atualizaGrafico(){
            var filtro = '?dtinicio=' + $filter('date')(vm.dtinicio, "yyyy-MM-dd") +
                    '&dtfim=' + $filter('date')(vm.dtfim, "yyyy-MM-dd")+
                    '&cidade='+vm.cidade+'&equipe='+vm.equipe+'&tipo='+vm.tipo;
            $http.get(API_CONFIG.url+'/gerencia/desempenho' + filtro)
                .then(function(response) {
                    var resultado = angular.fromJson(response.data);
                    vm.graficoDesempenho.title.text = resultado.title;
                    vm.graficoDesempenho.subtitle.text = resultado.subtitle;
                    vm.graficoDesempenho.xAxis.categories = resultado.categories;
                    vm.graficoDesempenho.series = resultado.series;
                    vm.detalhes = resultado.tabela;
                    vm.totais = resultado.total;
                    switch(vm.tipo){
                        case 'VISITAS':
                          vm.graficoDesempenho.options.tooltip.valueSuffix = ' visita(s)';
                          vm.graficoDesempenho.yAxis.title.text = 'Visitas Técnicas';
                          break;
                        case 'PLANO':
                          vm.graficoDesempenho.options.tooltip.valueSuffix = ' mudança(s)';
                          vm.graficoDesempenho.yAxis.title.text = 'Mudanças de Plano';
                          break;
                        case 'INSTALACAO':
                          vm.graficoDesempenho.options.tooltip.valueSuffix = ' instalação(ões)';
                          vm.graficoDesempenho.yAxis.title.text = 'Instalações';
                          break;  
                        case 'ENDERECO':
                          vm.graficoDesempenho.options.tooltip.valueSuffix = ' mudança(s)';
                          vm.graficoDesempenho.yAxis.title.text = 'Mudanças de Endereço';
                          break;  
                      }
                });


            $http.get(API_CONFIG.url+'/gerencia/desempenho/porcentagem' + filtro)
                .then(function(response) {
                    var resultado = angular.fromJson(response.data);
                    vm.graficoDesempenhoTempo.title.text = resultado.title;
                    vm.graficoDesempenhoTempo.subtitle.text = resultado.subtitle;
                    vm.graficoDesempenhoTempo.xAxis.categories = resultado.categories;
                    vm.graficoDesempenhoTempo.series = resultado.series;
                    vm.totais_tempo = resultado.total;
                /*
                if(vm.tipo=='VISITAS'){
                        vm.graficoDesempenhoTempo.options.tooltip.valueSuffix = ' visita(s)';
                        vm.graficoDesempenhoTempo.yAxis.title.text = 'Visitas Técnicas';
                    } else {
                        vm.graficoDesempenhoTempo.options.tooltip.valueSuffix = ' instalação(ões)';
                        vm.graficoDesempenhoTempo.yAxis.title.text = 'Instalações';

                    }
                */

                });

        }

        function open1() {
          vm.opened1 = true;
        };

        function open2() {
          vm.opened2 = true;
        };

        function getEquipes(cidade){
          if(vm.dtinicio > vm.dtfim){
            vm.dtfim = vm.dtinicio;
          }
            $http.get(API_CONFIG.url+'/gerencia/equipes.json?tipo='+ vm.tipo + '&cidade=' + cidade + '&dtinicio=' + $filter('date')(vm.dtinicio, "yyyy-MM-dd") +
                    '&dtfim=' + $filter('date')(vm.dtfim, "yyyy-MM-dd"))
                .then(function(response) {
                  vm.equipes = response.data;
                  vm.equipe = 'TODAS AS EQUIPES';
                });
        }

        vm.graficoDesempenho = {
                options: {
                    chart: {
                        type: 'areaspline',
                        zoomType: 'x',
                        options3d: {
                           enabled: true,
                            alpha: 15,
                            beta: 15,
                            viewDistance: 25,
                            depth: 40
                        }

                    },
                    tooltip: {
                        shared: true,
                        valueSuffix: ' visita(s)'
                    },
                    /*
                    plotOptions: {
                    spline: {
                        dataLabels: {
                            enabled: true
                        }
                    },
                    areaspline: {
                        dataLabels: {
                            enabled: true
                        }
                    }
                    */
                },
                title: {
                    text: '',
                    style: {
                        fontWeight: 'bold',
                        fontSize: '14px'
                    }
                },
                subtitle: {
                    text: '',
                    style: {
                        fontSize: '11px'
                    }
                },
                xAxis: {
                    title: {text: 'Data'},
                    tickAmount: 10,
                    categories: [],
                },
                yAxis: {
                    title: {text: 'Visitas Técnicas'},
                    tickAmount: 11
                },
                plotOptions: {
                    areaspline: {
                        fillOpacity: 0.5,
                        dataLabels: {
                            enabled: true,
                            color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                            style: {
                                textShadow: '0 0 3px black'
                            }
                        }
                    },

                },
                credits: {
                    enabled: false
                },

                series: [],
                loading: false
            };

        vm.graficoDesempenhoTempo = {

          title: {
                    text: '',
                    style: {
                        fontWeight: 'bold',
                        fontSize: '14px'
                    }
                },
                subtitle: {
                    text: '',
                    style: {
                        fontSize: '11px'
                    }
                },
          xAxis: {
              categories: []
          },
          yAxis: {
              min: 0,
              title: {
                  text: 'Porcentagem (%)'
              },
              stackLabels: {
                  enabled: true,
                  style: {
                      fontWeight: 'bold',
                      color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
                  }
              }
          },
          options: {
            chart: {
              type: 'spline',
            options3d: {
                enabled: true,
                alpha: 10,
                beta: 25,
                depth: 70
            }
          },
          /*
          legend: {
              align: 'right',
              x: -70,
              verticalAlign: 'top',
              y: 20,
              floating: true,
              backgroundColor: (Highcharts.theme && Highcharts.theme.background2) || 'white',
              borderColor: '#CCC',
              borderWidth: 1,
              shadow: false
          },
          */
          tooltip: {
               headerFormat: '<b>{point.x}</b><br/>',
               pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.1f}%</b> ({point.y:,.0f})<br/>',
               formatter: function() {
                var s = '<b>'+ this.x +'</b>',
                    sum = 0;

                $.each(this.points, function(i, point) {
                    s += '<br/><span style="color:' + point.series.color + '">'+ point.series.name +'</span>: <b>'+
                        Math.round(point.percentage) + '%</b> (' + point.y + ')';
                    sum += point.y;
                });

                s += '<br/><b>Total:</b> '+sum

                return s;
               },
            shared: true
          },
          plotOptions: {
            spline: {
                dataLabels: {
                    enabled: true
                },
                enableMouseTracking: false,

            },
            line: {
                dataLabels: {
                    enabled: true
                },
                enableMouseTracking: false,

            },
              column: {
                  stacking: 'normal',
                  dataLabels: {
                      enabled: true,
                      color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                      style: {
                          textShadow: '0 0 3px black, 0 0 3px black'
                      }
                  }
              },
               area: {
                stacking: 'percent',
                lineColor: '#ffffff',
                lineWidth: 1,
                marker: {
                    lineWidth: 1,
                    lineColor: '#ffffff'
                },
                 dataLabels: {
                    enabled: false,
                    color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                    style: {
                        textShadow: '0 0 3px black'
                    }
                }
            }
          }},
            credits: {
                    enabled: false
                },
          series: []
      };

            vm.graficoEquipes = {
                options: {
                    chart: {
                        type: 'column'
                    },
                },
                title: {
                    text: 'Stacked column chart'
                },
                xAxis: {
                    categories: ['Apples', 'Oranges', 'Pears', 'Grapes', 'Bananas']
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Total fruit consumption'
                    }
                },
                tooltip: {
                     pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.1f}%</b> ({point.y:,.0f})<br/>',
                    shared: true
                },
                plotOptions: {
                    area: {
                stacking: 'percent',
                lineColor: '#ffffff',
                lineWidth: 1,
                marker: {
                    lineWidth: 1,
                    lineColor: '#ffffff'
                }
            }
                },
                series: [{
                    name: 'John',
                    data: [5, 3, 4, 7, 2]
                }, {
                    name: 'Jane',
                    data: [2, 2, 3, 2, 1]
                }, {
                    name: 'Joe',
                    data: [3, 4, 4, 2, 5]
                }]
            };

   }

})();
