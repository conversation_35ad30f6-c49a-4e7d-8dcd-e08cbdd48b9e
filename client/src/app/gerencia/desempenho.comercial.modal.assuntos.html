<div class="modal" id="modal-grupos-os" tabindex="-1" role="dialog" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog" style="width: 800px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmramal_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Gerenciar pesos de OS
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body">
				<div class="table">
					<div class="tr">
						<div class="td" style="width: 60%; padding-right: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Assuntos de OS</h3>
								</div>
								<div class="panel-body" style="padding: 0px; max-height: 300px; overflow-y: auto;">
									<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
										<thead ng-if="!DCC.carregando_dados_modal_grupos">
											<tr>
												<th style="border-left: none;">Assunto</th>
												<th class="align-center valign-middle" style="width: 15%;">Peso</th>
												<th class="align-center valign-middle" style="width: 80px;"></th>
											</tr>
										</thead>
										<tbody>
											<tr ng-if="DCC.carregando_dados_modal_grupos">
												<td class="align-center valign-middle" colspan="3" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
											</tr>
											<tr ng-if="!DCC.carregando_dados_modal_grupos" ng-repeat="assunto in DCC.assuntos_avaliados" class="pointer hoverable">
												<td class="valign-middle" style="border-left: none;">
													{{ assunto.assunto }}
												</td>
												<td class="align-center valign-middle">
													<span ng-if="!DCC.editando_assuntos.includes(assunto.id_assunto)">{{ assunto.peso }}</span>
													<input ng-if="DCC.editando_assuntos.includes(assunto.id_assunto)" type="text" class="form-control input-sm align-center" id="input-peso-assunto-{{assunto.id_assunto}}" value="{{ assunto.peso }}">
												</td>
												<td class="align-center valign-middle">
													<div ng-if="!DCC.editando_assuntos.includes(assunto.id_assunto)">
														<button  title="Editar peso" class="btn btn-vsm btn-primary"
															ng-click="$event.stopPropagation(); DCC.habilitar_edicao(assunto.id_assunto);"><i
																class="glyphicon glyphicon-pencil"></i></button>
														<button ng-disabled="assunto.permanente" title="Excluir assunto" class="btn btn-vsm btn-danger"
															ng-really-message="Deseja realmente excluir o assunto?"
															ng-really-click="DCC.excluir_assunto(assunto.id_assunto);"><i
																class="glyphicon glyphicon-trash"></i></button>
													</div>
													
													<div ng-if="DCC.editando_assuntos.includes(assunto.id_assunto)">
														<button title="Salvar peso" class="btn btn-vsm btn-success"
															ng-click="$event.stopPropagation(); DCC.salvar_edicao(assunto.id_assunto);"><i
																class="glyphicon glyphicon-ok"></i></button>
														<button title="Cancelar edição" class="btn btn-vsm btn-danger"
															ng-click="$event.stopPropagation(); DCC.cancelar_edicao(assunto.id_assunto);"><i
																class="glyphicon glyphicon-ban-circle"></i></button>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div class="td" style="padding-left: 5px;">

							<div class="panel panel-primary">
								<div class="panel-heading">
									<h3 class="panel-title">Assuntos ainda não agrupados</h3>
								</div>
								<div class="panel-body" style="padding: 0px; max-height: 300px; overflow-y: auto;">
									<input type="text" class="form-control" ng-model="search" placeholder="Pesquisar...">
									<table class="table table-bordered full-width spaced-td" style="margin-bottom: 0;">
										<thead ng-if="!DCC.carregando_dados_modal_grupos">
											<tr>
												<th class="valign-middle" style="padding-left: 10px;">Assunto</th>
												<th class="align-center valign-middle" style="width: 60px;"></th>
											</tr>
										</thead>
										<tbody>
											<tr ng-if="DCC.carregando_dados_modal_grupos">
												<td colspan="2" class="align-center valign-middle" style="padding: 10px;"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
											</tr>
											<tr ng-if="!DCC.carregando_dados_modal_grupos" ng-repeat="assunto in DCC.assuntos_nao_avaliados | filter:search">
												<td class="valign-middle" style="border-left: none; padding-left: 10px !important;">{{ assunto.assunto }}</td>
												<td class="align-center valign-middle">
													<button title="Adicionar assunto ao grupo selecionado" class="btn btn-vsm btn-success" ng-really-message="Deseja realmente adicionar este assunto à lista?" ng-really-click="DCC.adicionar_assunto(assunto.id, assunto.assunto);"><i
															class="glyphicon glyphicon-plus"></i></button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>

						</div>
					</div>
				</div>
				
			</div>
			<div class="modal-footer">
				<button class="btn btn-default" data-toggle="modal" data-target="#modal-grupos-os">Fechar</button>
			</div>
		</div>
	</div>
</div>