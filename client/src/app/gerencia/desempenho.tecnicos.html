<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-briefcase"></i> Gerencia</li>
	<li class="active"><i class="glyphicon glyphicon-check"></i> An<PERSON>lise de desempenho: Técnicos de Campo</li>

</ol>

<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%;">
	<div class="table filter-section-container" style="width: auto;">
		<div class="tr">

			<div class="td centered" style="border-right: 1px solid #CCC; padding-right: 5px;">
				<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-tecnicos-os"><i
						class="glyphicon glyphicon-cog"></i> Gerenciar técnicos</button>
			</div>

			<div class="td centered" style="border-right: 1px solid #CCC; padding-right: 5px; padding-left: 5px;">
				<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-grupos-os">
					<i class="glyphicon glyphicon-cog"></i> Gerenciar grupos de OS
				</button>
			</div>

			<div class="td" style="border-right: 1px solid #CCC;">
				<div class="input-group">
					<span class="input-group-addon">
						Fechadas de:
					</span>
					<input type="date" class="form-control" ng-model="DTC.filter.data_fechamento_inicio" />
					<span class="input-group-addon">
						a
					</span>
					<input type="date" class="form-control" ng-model="DTC.filter.data_fechamento_fim" />
				</div>

        <div class="input-group" style="margin-top: 7px;">
          <span class="input-group-addon">
            Técnico:
          </span>
          <select class="form-control" ng-model="DTC.filter.tecnico" ng-init="DTC.filter.tecnico = 'todos'">
            <option value="todos">Todos</option>
            <option value="internos">Internos</option>
            <option value="terceirizados">Terceirizados</option>
            <option disabled>------------------------</option>
            <option ng-repeat="tecnico in DTC.tecnicos.exibidos" value="{{tecnico.id}}">{{tecnico.nome}}</option>
          </select>
        </div>

			</div>

			<div class="td centered" style="border-right: 1px solid #CCC; padding-right: 10px;">
				<div class="input-group">
          <span class="input-group-addon">
            Cidade:
          </span>
          <select class="form-control" ng-model="DTC.filter.cidade" ng-init="DTC.filter.cidade = 'todas'"
            ng-change="DTC.getEquipes(DTC.cidade)">
            <option value="todas"> Todas as Cidades</option>
            <option value="Águas da Prata">Águas da Prata</option>
            <option value="Andradas">Andradas</option>
            <option value="Campestre">Campestre</option>
            <option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
            <option value="Poços de Caldas">Poços de Caldas</option>
            <option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
            <option value="São João da Boa Vista">São João da Boa Vista</option>
            <option value="Vargem Grande do Sul">Vargem Grande do Sul</option>
          </select>
        </div>

				<div class="input-group" style="margin-top: 7px;">
          <span class="input-group-addon">
            Bairro:
          </span>
          <input class="form-control" ng-model="DTC.filter.bairro" ng-init="DTC.filter.bairro = ''" placeholder="Todos os bairros">
        </div>
			</div>

			<div class="td centered" style="padding-left: 10px;">
				<div class="input-group">

					<button class="btn btn-default" title="Atualizar" ng-click="DTC.get_os_list(); DTC.getGraficoDesempenhoData();"><i
							class="glyphicon glyphicon-repeat"></i> Atualizar</button>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="panel panel-primary">
	<div class="panel-heading clearfix" style="padding-left: 5px;">
		<span class="pull-right">
			<button class="btn btn-default btn-sm" ng-click="DTC.get_os_list({download: true, downloadTarget: 'os_tecnicos'})">
				<i class="glyphicon glyphicon-export"></i> Exportar
			</button>
		</span>
		<h3 class="panel-title"><i class="glyphicon glyphicon-list"></i> Resumo</h3>
	</div>
	<div class="panel-body" style="padding: 0px;">
		<table ng-if="DTC.carregando_os_list" class="table spaced-td no-margin">
			<tr>
				<td class="align-center valign-middle"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
			</tr>
		</table>
		<table ng-if="!DTC.carregando_os_list && (DTC.os_tecnicos | numkeys) == 0" class="table spaced-td no-margin">
			<tr class="bg-warning">
				<td>Não foram encontradas OS através do filtro especificado.</td>
			</tr>
		</table>
		<table ng-if="!DTC.carregando_os_list && (DTC.os_tecnicos | numkeys) > 0" class="table table-striped table-bordered spaced-td no-margin">
			<thead>
				<tr>
					<th>Técnico</th>
					<th class="align-center valign-middle" ng-repeat="diagnostico in DTC.grupos_diagnosticos" ng-if="diagnostico != 'Diagnósticos ignorados'">
						<a href="#" ng-click="DTC.sortOsTecnicos(diagnostico);">{{diagnostico}} <span ng-if="DTC.sortColumn === diagnostico" class="fa fa-caret-down" ng-class="DTC.sortOrder === 'asc' ? 'fa-caret-down' : 'fa-caret-up'"></span></a>
					</th>
					<th class="align-center valign-middle">
						<a href="#" ng-click="DTC.sortOsTecnicos('Total')">Total <span ng-if="DTC.sortColumn === 'Total'" class="fa fa-caret-down" ng-class="DTC.sortOrder === 'asc' ? 'fa-caret-down' : 'fa-caret-up'"></span></a>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="tecnico in DTC.os_tecnicos_arr">
					<td>{{tecnico.nome}}</td>
					<td class="align-center valign-middle" ng-repeat="diagnostico in DTC.grupos_diagnosticos" ng-if="diagnostico != 'Diagnósticos ignorados'">
						{{ tecnico.os[diagnostico].quantidade }} <span class="text-primary"><b>({{  tecnico.os[diagnostico].soma_ponderada  }})</b></span>
					</td>
					<td class="align-center valign-middle">
						{{ tecnico.total }} <span class="text-primary"><b>({{ tecnico.total_ponderado }})</b></span>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="full-width" ng-if="!DTC.carregando_os_list && (DTC.os_tecnicos | numkeys) > 0">
			<div class="pull-right" style="padding: 2px; padding-top: 4px; padding-right: 5px;">
				<table>
					<tr>
						<td><div class="legenda dark"></div></td>
						<td>Soma simples</td>
						<td><div class="legenda primary"></div></td>
						<td class="text-primary"><b>(Soma ponderada)</b></td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>

<div class="panel panel-primary">
	<div class="panel-heading clearfix" style="padding-left: 5px;">
		<span class="pull-right">
			<button class="btn btn-default btn-sm" ng-click="DTC.get_os_list({download: true, downloadTarget: 'os_list'})">
				<i class="glyphicon glyphicon-export"></i> Exportar
			</button>
		</span>
		<h3 class="panel-title"><i class="glyphicon glyphicon-info-sign"></i> Informações das OS</h3>
	</div>
	<div class="panel-body" style="padding: 0px; padding-bottom: 5px;">
		<table ng-if="DTC.carregando_os_list" class="table spaced-td no-margin">
			<tr>
				<td class="align-center valign-middle"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
			</tr>
		</table>
		<table ng-if="!DTC.carregando_os_list && (DTC.os_list | numkeys) == 0" class="table spaced-td no-margin">
			<tr class="bg-warning">
				<td>Não foram encontradas OS através do filtro especificado.</td>
			</tr>
		</table>
		<table ng-if="!DTC.carregando_os_list && (DTC.os_list | numkeys) > 0" class="table table-striped table-bordered spaced-td no-margin">
			<thead>
				<tr>
					<th>ID</th>
					<th class="align-center valign-middle">Aberta em</th>
					<th class="align-center valign-middle">Técnico</th>
					<th class="align-center valign-middle">Fechada em</th>
					<th class="align-center valign-middle">Diagnóstico da auditoria</th>
					<th class="align-center valign-middle">Cidade</th>
					<th class="align-center valign-middle">Bairro</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="os in DTC.os_list_paged">
					<td><b>{{os.id}}</b></td>
					<td class="align-center valign-middle">
						<span class="label label-primary">
							{{os.data_abertura | amDateFormat:'DD/MM/YYYY'}}
						</span>
					</td>
					<td class="align-center valign-middle">
						{{os.tecnico}}
					</td>
					<td class="align-center valign-middle">
						<span class="label label-default">
							{{os.data_fechamento | amDateFormat:'DD/MM/YYYY'}}
						</span>
					</td>
					<td class="align-center valign-middle">
						{{os.diagnostico_auditoria}}
					</td>
					<td class="align-center valign-middle">
						{{os.cidade}}
					</td>
					<td class="align-center valign-middle">
						{{os.bairro}}
					</td>
				</tr>
			</tbody>
		</table>

		<div class="text-center">
			<div class="text-center">
				<uib-pagination total-items="DTC.pagination.size" ng-model="DTC.pagination.page" ng-change="DTC.pageChanged()"
					items-per-page="DTC.pagination.itemsPerPage" max-size="9" previous-text="Anterior" next-text="Próximo"
					boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm" style="margin-bottom: 5px;">
				</uib-pagination>
			</div>
			<div class="text-center" style="margin-top: 0px;">
				Página <span class="badge">{{ DTC.pagination.page}}</span> de <span class="badge">{{ DTC.pagination.pages}}</span>
				de <span class="badge">{{ DTC.pagination.size}}</span> registro(s)</span>
			</div>
		</div>
	</div>
</div>

<div style="height: 1px; background: #AAA; width: 100%; margin: 15px 0px;"></div>

<div style="width: 100%; margin-bottom: 10px;">

	<div style="margin-top: 15px; width: 100%; text-align: center; color: #F00; font-size: 11pt;">
		* Os filtros no topo da página controlam também os resultados do gráfico (<b>exceto pelo filtro de data</b>)
	</div>

	<div style="width: 100%; text-align: center; margin: 10px 0px;">
		<span style="font-size: 13pt;">Gráfico de desempenho dos técnicos</span>
	</div>

	<div class="input-group" style="width: 350px; margin: 0 auto;">
		<div class="input-group-addon">De</div>
		<input type="month" class="form-control" min="2021-04" ng-model="DTC.filter.graficoDesempenho.periodoInicial" ng-change="DTC.getGraficoDesempenhoData();">
		<div class="input-group-addon">a</div>
		<input type="month" class="form-control" min="2021-05" ng-model="DTC.filter.graficoDesempenho.periodoFinal" ng-change="DTC.getGraficoDesempenhoData();">
	</div>

	<div style="width: 100%; text-align: center; margin-top: 15px;">
		<div class="btn-group btn-group-grafico-desempenho" role="group" aria-label="...">

			<button type="button" class="btn" ng-class="DTC.tipoPontuacaoGrafico == 'simples' ? 'btn-primary' : 'btn-default'" ng-click="DTC.changeTipoPontuacaoGrafico('simples');">Pontuação simples</button>

			<button type="button" class="btn" ng-class="DTC.tipoPontuacaoGrafico == 'ponderada' ? 'btn-primary' : 'btn-default'" ng-click="DTC.changeTipoPontuacaoGrafico('ponderada');">Pontuação ponderada</button>

		</div>
	</div>
</div>

<div id="graficoDesempenho"></div>

<div ng-include="'app/gerencia/desempenho.tecnicos.modal.grupos.html'"></div>
<div ng-include="'app/gerencia/desempenho.tecnicos.modal.tecnicos.html'"></div>
