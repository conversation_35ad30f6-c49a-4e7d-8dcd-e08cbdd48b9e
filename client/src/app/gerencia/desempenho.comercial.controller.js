(function () {
	'use strict';

	angular
		.module('app')
		.controller('DesempenhoComercialController', DesempenhoComercialController);

	/** @ngInject */
	function DesempenhoComercialController($window, $http, API_CONFIG, $filter, $timeout, $scope) {

		var vm = this;

		// 'simples' ou 'ponderada' (alternável pelo botão do gráfico)
		vm.tipoPontuacaoGrafico = 'ponderada';

		vm.dados_analise_arr = [];
		vm.sortOrder = '';
		vm.sortColumn = '';
		vm.sortDadosAnalise = sortDadosAnalise;

		vm.filter = {
			cidade: 'todas',
			funcionario: 'todos'
		};
		vm.lastFilterRequested = {};

		vm.exportDisabled = true;

		var inicio = new Date();
		var fim = new Date();
		inicio.setDate(1);

		inicio.setHours(0, 0, 0, 0);
		fim.setHours(0, 0, 0, 0);

		vm.filter.data_fechamento_inicio = inicio;
		vm.filter.data_fechamento_fim = fim;

		vm.graficoDesempenho = null;
		vm.graficoDesempenhoSubtitle = null;
		vm.changeTipoPontuacaoGrafico = changeTipoPontuacaoGrafico;
		vm.getGraficoDesempenhoData = getGraficoDesempenhoData;

		vm.novo_grupo = {
			nome: '',
			peso: 1
		};

		vm.funcionarios = {
			exibidos: [
			],
			nao_exibidos: [
			]
		};

		vm.editando_assuntos = [];
		vm.editando_funcionarios = [];

		vm.grupo_selecionado = {};

		vm.assuntos_avaliados = [];
		vm.assuntos_nao_avaliados = [];

		vm.assuntos_os = [];

		vm.get_lista_servicos = get_lista_servicos;

		vm.adicionar_assunto = adicionar_assunto;
		vm.excluir_assunto = excluir_assunto;
		vm.habilitar_edicao = habilitar_edicao;
		vm.cancelar_edicao = cancelar_edicao;
		vm.salvar_edicao = salvar_edicao;

		vm.editando_assuntos = [];

		vm.listar_funcionarios = listar_funcionarios;
		vm.adicionar_funcionario = adicionar_funcionario;
		vm.excluir_funcionario = excluir_funcionario;

		vm.pageChanged = pageChanged;

		vm.carregando_dados_modal_grupos = false;
		vm.carregando_dados_modal_funcionarios = false;
		vm.carregando_lista_servicos = false;

		vm.dados_analise = {};
		vm.lista_servicos = [];
		vm.lista_servicos_paged = [];

		vm.pagination = {
			itemsPerPage: 20
		};

		activate();

		function activate() {
			listar_funcionarios();
			initializeDesempenhoChart();
			get_lista_servicos();

			$(document).on('shown.bs.modal', '#modal-grupos-os', function (e) {
				listar_assuntos_os();
			});

			$(document).on('shown.bs.modal', '#modal-funcionarios-os', function (e) {
				vm.editouFuncionarios = false;
			});

			$(document).on('hidden.bs.modal', '#modal-funcionarios-os', function (e) {
				if(vm.editouFuncionarios) {
					reloadOsData();
				}
			});
		}

		function reloadOsData() {
			get_lista_servicos();
			getGraficoDesempenhoData();
		}

		function pageChanged() {
			vm.pagination.pages = Math.ceil(vm.lista_servicos.length / vm.pagination.itemsPerPage);

			vm.lista_servicos_paged = vm.lista_servicos.slice(
				(vm.pagination.page - 1) * vm.pagination.itemsPerPage,
				vm.pagination.page * vm.pagination.itemsPerPage
			);
		}

		function get_lista_servicos(options) {
			var downloadCsv = false;
			if (options && options.download && options.download === true) {
				downloadCsv = true;
			}

			var downloadTarget = '';
			if (options && options.downloadTarget) {
				downloadTarget = options.downloadTarget;
			}

			var filterToRequest = {};
			if (!downloadCsv) {
				vm.carregando_lista_servicos = true;
				vm.dados_analise = {};
				vm.pagination.page = 1;
				vm.exportDisabled = false;

				angular.copy(vm.filter, vm.lastFilterRequested);
				filterToRequest = vm.filter;
			}
			else {
				filterToRequest = vm.lastFilterRequested
			}

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/servicos-list',
				data: { filter: filterToRequest, downloadCsv: downloadCsv, downloadTarget: downloadTarget },
				responseType: downloadCsv ? 'arraybuffer' : 'text',
				ignoreLoadingBar: !downloadCsv
			}).then(function (response) {

				if (downloadCsv) {
					var filename = '';
					if (downloadTarget == 'lista_servicos')
						filename = 'relatorio_comercial_lista_servicos_';

					else if (downloadTarget == 'dados_analise')
						filename = 'relatorio_comercial_resumo_servicos_';

					filename += '_' + filterToRequest.data_fechamento_inicio.toLocaleDateString('pt-BR') + '_a_' + filterToRequest.data_fechamento_fim.toLocaleDateString('pt-BR') + '.csv';

					var headers = response.headers();
					var contentType = headers['content-type'];

					var blob = new Blob([response.data], {
						type: contentType
					});
					if (navigator.msSaveBlob)
						navigator.msSaveBlob(blob, filename);
					else {
						var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
						if (saveBlob === undefined) {
							var linkElement = document.createElement('a');
							try {
								var blob = new Blob([response.data], { type: contentType });
								var url = window.URL.createObjectURL(blob);
								linkElement.setAttribute('href', url);
								linkElement.setAttribute("download", filename);

								var clickEvent = new MouseEvent("click", {
									"view": window,
									"bubbles": true,
									"cancelable": false
								});
								linkElement.dispatchEvent(clickEvent);
							} catch (ex) {
								console.log(ex);
							}
						}

					}

					return;
				}

				vm.carregando_lista_servicos = false;
				angular.copy(response.data.dados_analise, vm.dados_analise);
				angular.copy(response.data.assuntos_os, vm.assuntos_os);
				angular.copy(response.data.lista_servicos, vm.lista_servicos);

				// Converte objeto para array
				vm.dados_analise_arr = Object.keys(vm.dados_analise).map(function (key) {
					return vm.dados_analise[key];
				});

				sortDadosAnalise('Total');

				vm.pagination.size = vm.lista_servicos.length;
				pageChanged();
				
			}, function (error) {
				console.log(error);
			});
		}


		function sortDadosAnalise(sortColumn) {
			if (sortColumn == vm.sortColumn) {
				vm.sortOrder = vm.sortOrder == 'asc' ? 'desc' : 'asc';
			}
			else {
				vm.sortOrder = 'desc';
			}

			vm.sortColumn = sortColumn;

			vm.dados_analise_arr.sort(customSort);
		}

		function customSort(a, b) {
			if (vm.sortColumn !== 'Total') {
				var valorA = a.dados_analise[vm.sortColumn].quantidade;
				var valorB = b.dados_analise[vm.sortColumn].quantidade;
			}
			else {
				var valorA = a.total_ponderado;
				var valorB = b.total_ponderado;
			}

			if (valorA == valorB)
				return 0
			else if (valorA > valorB)
				return vm.sortOrder == 'asc' ? 1 : -1;
			else
				return vm.sortOrder == 'asc' ? -1 : 1;
		}

		function getGraficoDesempenhoData() {
			setGraficoDesempenhoLoading();

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/grafico',
				data: { filter: vm.filter },
				ignoreLoadingBar: true
			}).then(function (response) {
				vm.graficoDesempenho.setSubtitle({text: null});
				var desempenhoSeries = getDesempenhoSeries(response.data);
				setDesempenhoChart(desempenhoSeries);
				
			}, function (error) {
				console.log(error);
			});
		}

		function listar_funcionarios() {
			vm.carregando_dados_modal_funcionario = true;

			$http({
				method: 'GET',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/listar-funcionarios'
			}).then(function (response) {
				vm.carregando_dados_modal_funcionario = false;

				vm.funcionarios = response.data
			}, function (error) {
				console.log(error);
			});
		}

		function adicionar_funcionario(id_funcionario, funcionario) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/adicionar-funcionario',
				data: {
					id_funcionario: id_funcionario,
					funcionario: funcionario
				}
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O funcionário foi adicionado com sucesso.');
					listar_funcionarios();
					reloadOsData();
				}
				else {
					alert('Ocorreu um erro ao adicionar o funcionário');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_funcionario(id_funcionario) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/excluir-funcionario',
				data: { id_funcionario: id_funcionario }
			}).then(function (response) {
				if (response.data.status == 'success') {
					alert('O funcionário foi excluído com sucesso.');
					listar_funcionarios();
					reloadOsData();
				}
				else {
					alert('Ocorreu um erro ao excluir o funcionário');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function listar_assuntos_os() {
			vm.carregando_dados_modal_grupos = true;
			vm.grupos_os = [];
			vm.assuntos_avaliados = [];
			vm.assuntos_nao_avaliados = [];

			$http({
				method: 'GET',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/assuntos'
			}).then(function (response) {
				vm.carregando_dados_modal_grupos = false;
				vm.assuntos_avaliados = response.data.assuntos_avaliados;
				vm.assuntos_nao_avaliados = response.data.assuntos_nao_avaliados;

				window.setTimeout(function () {
					if (vm.grupo_selecionado.hasOwnProperty('id')) {
						var grupo_selecionado = vm.grupo_selecionado;
						vm.grupo_selecionado = {};
						selecionar_grupo(grupo_selecionado);
					}
				}, 500);
			}, function (error) {
				console.log(error);
			});
		}

		function cancelar_edicao(id_assunto) {
			var index = vm.editando_assuntos.indexOf(id_assunto);
			vm.editando_assuntos.splice(index, 1);
		}

		function habilitar_edicao(id_assunto) {
			vm.editando_assuntos.push(id_assunto);
			angular.element('#input-peso-assunto-' + id_assunto).focus();
		}

		function salvar_edicao(id_assunto) {
			var peso = parseInt($('#input-peso-assunto-' + id_assunto).val().trim());

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/editar-assunto',
				data: {
					id_assunto: id_assunto,
					peso: peso
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('As alterações foram salvas com sucesso.');
					listar_assuntos_os();
					reloadOsData();
				}
				else {
					alert('Ocorreu um erro ao alterar as informações do grupo.');
				}
			}, function (error) {
				console.log(error);
			});
			

			// $http();
			//		onSuccess: reloadRelatorio();

			cancelar_edicao(id_assunto);
		}

		function adicionar_assunto(id_assunto, assunto) {
			var peso = $window.prompt('Especifique o peso do assunto:', 1);

			peso = peso.replace(/\D/g,'');

			if(peso == '' || peso < 1) {
				alert('Peso inválido!');
				return false;
			}

			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/adicionar-assunto',
				data: {
					id_assunto: id_assunto,
					assunto: assunto,
					peso: peso
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O assunto foi adicionado com sucesso');
					listar_assuntos_os();
					reloadOsData();
				}
				else {
					alert('Ocorreu um erro ao adicionar o assunto.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function excluir_assunto(id_assunto) {
			$http({
				method: 'POST',
				url: API_CONFIG.url + '/gerencia/desempenho-comercial/excluir-assunto',
				data: {
					id_assunto: id_assunto
				}
			}).then(function (response) {
				if (response.data && response.data.status && response.data.status == 'success') {
					alert('O assunto foi excluído com sucesso');
					listar_assuntos_os();
					reloadOsData();
				}
				else {
					alert('Ocorreu um erro ao excluir o assunto.');
				}
			}, function (error) {
				console.log(error);
			});
		}

		function getDesempenhoSeries(dadosGrafico) {
			var tipoPontuacao = vm.tipoPontuacaoGrafico;

			var indexPontuacao = '';
			if(tipoPontuacao == 'simples')
				indexPontuacao = 'pontos_simples';
			else if (tipoPontuacao == 'ponderada')
				indexPontuacao = 'pontos_ponderados'

			var desempenhoSeries = [];
			for(var funcionarioId in dadosGrafico) {
				var funcionario = dadosGrafico[funcionarioId];
				var funcionarioObj = {
					name: funcionario.funcionario,
					data: []
				};
				
				if(funcionarioObj.name == 'Média') {
					funcionarioObj.color = '#0000FF';
				}
				
				else if(funcionarioObj.name == '25% acima da média') {
					funcionarioObj.color = '#00FF00';
				}
				
				if(funcionarioObj.name == '25% abaixo da média') {
					funcionarioObj.color = '#FF0000';
				}
				for(var i = 0; i < funcionario[indexPontuacao].length; i++) {
					var pontuacao = funcionario[indexPontuacao][i];
					var pontuacaoObj = {
						x: Date.UTC(pontuacao.ano, pontuacao.mes-1, 1),
						y: pontuacao.pontos
					}
					funcionarioObj.data.push(pontuacaoObj);
				}

				desempenhoSeries.push(funcionarioObj);
			}

			return desempenhoSeries;
		}

		function setDesempenhoChart(series) {
			clearDesempenhoSeries();
			vm.graficoDesempenhoSubtitle = 'Pontuação ' + vm.tipoPontuacaoGrafico;

			series.forEach(function(serie){
				vm.graficoDesempenho.addSeries(serie);
			});
		}

		function setGraficoDesempenhoLoading() {
			if(!isDesempenhoChartInitialized())
				return false;

			clearDesempenhoSeries();

			vm.graficoDesempenho.setSubtitle({text: 'Carregando...'});
		}

		function clearDesempenhoSeries() {
			if(!isDesempenhoChartInitialized())
				return false;

			while (vm.graficoDesempenho.series.length) {
				vm.graficoDesempenho.series[0].remove();
			}
		}

		function isDesempenhoChartInitialized() {
			return (vm.graficoDesempenho
				&& typeof vm.graficoDesempenho === 'object');
		}

		function initializeDesempenhoChart() {
			var today = new Date();
			var currentMonth = today.getMonth();
			var currentYear = today.getFullYear();

			// Subtract 6 months from current date, to get initial chart date (years rollback automatically if needed)
			today.setMonth(currentMonth - 6);
			var initialMonth = today.getMonth();
			var initialYear = today.getFullYear();

			vm.filter.graficoDesempenho = {
				periodoInicial: new Date(initialYear, initialMonth),
				periodoFinal: new Date(currentYear, currentMonth)
			}

			vm.graficoDesempenho = Highcharts.chart('graficoDesempenho', {

				// Desabilitados título e subtítulo (adicionados direto no HTML, para melhorar o layout)
				title: { text: null },
				subtitle: { text: null },
			
				yAxis: {
					title: {
						text: 'Pontuação'
					}
				},
			
				legend: {
					layout: 'vertical',
					align: 'right',
					verticalAlign: 'middle'
				},
			
				xAxis: {
					tickWidth: 0,
					type: 'datetime',
					labels: {
						dateTimeLabelFormats: {
							month: '%b'
						}
					}
				},
			
				series: [],
			
				responsive: {
					rules: [{
						condition: {
							maxWidth: 800
						},
						chartOptions: {
							legend: {
								layout: 'horizontal',
								align: 'center',
								verticalAlign: 'bottom'
							}
						}
					}]
				}
			
			});

			getGraficoDesempenhoData();
		}

		function changeTipoPontuacaoGrafico(tipo) {
			var tipoAtual = vm.tipoPontuacaoGrafico;

			vm.tipoPontuacaoGrafico = tipo;

			if(vm.tipoPontuacaoGrafico != tipoAtual) {
				getGraficoDesempenhoData();
			}
		}

	}

})();