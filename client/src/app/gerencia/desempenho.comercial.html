<ol class="breadcrumb">
	<li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
	<li><i class="glyphicon glyphicon-briefcase"></i> Gerencia</li>
	<li class="active"><i class="glyphicon glyphicon-check"></i> Análise de desempenho: Comercial</li>

</ol>

<div class="barra align-center" style="padding: 10px; display: flex; justify-content: flex-end; width: 100%;">
	<div class="table filter-section-container" style="width: auto;">
		<div class="tr">

			<div class="td centered" style="border-right: 1px solid #CCC; padding-right: 10px;">
				<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-funcionarios-os"><i
						class="glyphicon glyphicon-cog"></i> Gerenciar funcionários</button>

						<br>
						<br>

				<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-grupos-os">
					<i class="glyphicon glyphicon-cog"></i> Gerenciar assuntos de OS
				</button>
			</div>

			<div class="td" style="border-right: 1px solid #CCC; width: 470px;">
				<div class="input-group">
					<span class="input-group-addon">
						Análise de:
					</span>
					<input type="date" class="form-control" ng-model="DCC.filter.data_fechamento_inicio" />
					<span class="input-group-addon">
						a
					</span>
					<input type="date" class="form-control" ng-model="DCC.filter.data_fechamento_fim" />
				</div>

				<br>

				<table class="full-width">
					<tr>
						<td style="width: 45%; padding-right: 5px;">
							<div class="input-group">
								<span class="input-group-addon">
									Cidade:
								</span>
								<select class="form-control" ng-model="DCC.filter.cidade"
									ng-change="DCC.getEquipes(DCC.cidade)">
									<option value="todas"> Todas as Cidades</option>
									<option value="Poços de Caldas">Poços de Caldas</option>
									<option value="Andradas">Andradas</option>
									<option value="Campestre">Campestre</option>
									<option value="Santo Antônio do Jardim">Santo Antônio do Jardim</option>
									<option value="Espírito Santo do Pinhal">Espírito Santo do Pinhal</option>
									<option value="São João da Boa Vista">São João da Boa Vista</option>
								</select>
							</div>
						</td>
						<td style="padding-left: 5px;">
							<div class="input-group">
								<span class="input-group-addon">
									Usuário:
								</span>
								<select class="form-control" ng-model="DCC.filter.funcionario">
									<option value="todos">Todos</option>
									<option ng-repeat="funcionario in DCC.funcionarios.exibidos" value="{{funcionario.id}}">{{funcionario.nome}}</option>
								</select>
							</div>
						</td>
					</tr>
				</table>
				
			</div>

			<div class="td centered">
				<div class="input-group">

					<button class="btn btn-default" title="Atualizar" ng-click="DCC.get_lista_servicos(); DCC.getGraficoDesempenhoData();"><i
							class="glyphicon glyphicon-repeat"></i> Atualizar</button>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="panel panel-primary">
	<div class="panel-heading clearfix" style="padding-left: 5px;">
		<span class="pull-right">
			<button class="btn btn-default btn-sm" ng-click="DCC.get_lista_servicos({download: true, downloadTarget: 'dados_analise'})" ng-disabled="DCC.exportDisabled">
				<i class="glyphicon glyphicon-export"></i> Exportar
			</button>
		</span>
		<h3 class="panel-title"><i class="glyphicon glyphicon-list"></i> Resumo</h3>
	</div>
	<div class="panel-body" style="padding: 0px;">
		<table ng-if="DCC.carregando_lista_servicos" class="table spaced-td no-margin">
			<tr>
				<td class="align-center valign-middle"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
			</tr>
		</table>
		<table ng-if="!DCC.carregando_lista_servicos && (DCC.dados_analise | numkeys) == 0" class="table spaced-td no-margin">
			<tr class="bg-warning">
				<td>Não foram encontradas OS através do filtro especificado.</td>
			</tr>
		</table>
		<table ng-if="!DCC.carregando_lista_servicos && (DCC.dados_analise | numkeys) > 0" class="table table-striped table-bordered spaced-td no-margin">
			<thead>
				<tr>
					<th>Usuário</th>
					<th class="align-center valign-middle" ng-repeat="assunto in DCC.assuntos_os">
						<a href="#" ng-click="DCC.sortDadosAnalise(assunto.assunto);">
							{{assunto.assunto}} <span ng-if="DCC.sortColumn === assunto.assunto" class="fa fa-caret-down" ng-class="DCC.sortOrder === 'asc' ? 'fa-caret-down' : 'fa-caret-up'"></span>
						</a>
					</th>
					<th class="align-center valign-middle">
						<a href="#" ng-click="DCC.sortDadosAnalise('Total')">
							Total <span ng-if="DCC.sortColumn === 'Total'" class="fa fa-caret-down" ng-class="DCC.sortOrder === 'asc' ? 'fa-caret-down' : 'fa-caret-up'"></span>
						</a>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="funcionario in DCC.dados_analise_arr">
					<td>{{funcionario.nome}}</td>
					<td class="align-center valign-middle" ng-repeat="assunto in DCC.assuntos_os">
						{{ funcionario.dados_analise[assunto.assunto].quantidade }} <span class="text-primary"><b>({{  funcionario.dados_analise[assunto.assunto].soma_ponderada  }})</b></span>
					</td>
					<td class="align-center valign-middle">
						{{ funcionario.total }} <span class="text-primary"><b>({{ funcionario.total_ponderado }})</b></span>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="full-width" ng-if="!DCC.carregando_lista_servicos && (DCC.dados_analise | numkeys) > 0">
			<div class="pull-right" style="padding: 2px; padding-top: 4px; padding-right: 5px;">
				<table>
					<tr>
						<td><div class="legenda dark"></div></td>
						<td>Soma simples</td>
						<td><div class="legenda primary"></div></td>
						<td class="text-primary"><b>(Soma ponderada)</b></td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>

<div class="panel panel-primary">
	<div class="panel-heading clearfix" style="padding-left: 5px;">
		<span class="pull-right">
			<button class="btn btn-default btn-sm" ng-click="DCC.get_lista_servicos({download: true, downloadTarget: 'lista_servicos'})" ng-disabled="DCC.exportDisabled">
				<i class="glyphicon glyphicon-export"></i> Exportar
			</button>
		</span>
		<h3 class="panel-title"><i class="glyphicon glyphicon-info-sign"></i> Serviços realizados</h3>
	</div>
	<div class="panel-body" style="padding: 0px; padding-bottom: 5px;">
		<table ng-if="DCC.carregando_lista_servicos" class="table spaced-td no-margin">
			<tr>
				<td class="align-center valign-middle"><img class="spinner-30" src="assets/images/spinner-blue.gif" /></td>
			</tr>
		</table>
		<table ng-if="!DCC.carregando_lista_servicos && (DCC.lista_servicos | numkeys) == 0" class="table spaced-td no-margin">
			<tr class="bg-warning">
				<td>Não foram encontradas OS através do filtro especificado.</td>
			</tr>
		</table>
		<table ng-if="!DCC.carregando_lista_servicos && (DCC.lista_servicos | numkeys) > 0" class="table table-striped table-bordered spaced-td no-margin">
			<thead>
				<tr>
					<th class="align-center valign-middle">Data</th>
					<th class="align-center valign-middle">Funcionário</th>
					<th class="align-center valign-middle">Assunto</th>
					<th class="align-center valign-middle">Peso</th>
					<th class="align-center valign-middle">Cidade</th>
					<th class="align-center valign-middle">Cliente</th>
					<th class="align-center valign-middle">Contrato</th>
					<th class="align-center valign-middle">OS</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="servico in DCC.lista_servicos_paged">
					<td class="align-center valign-middle">
						<span class="label label-primary">
							{{servico.data | amDateFormat:'DD/MM/YYYY'}}
						</span>
					</td>
					<td class="align-center valign-middle">
						{{servico.funcionario}}
					</td>
					<td class="align-center valign-middle">
						<span class="label label-default">
							{{servico.assunto}}
						</span>
					</td>
					<td class="align-center valign-middle">
						{{servico.peso}}
					</td>
					<td class="align-center valign-middle">
						{{servico.cidade}}
					</td>
					<td class="align-center valign-middle">
						{{servico.cliente}}
					</td>
					<td class="align-center valign-middle">
						{{servico.id_contrato}}
					</td>
					<td><b>{{servico.id_os}}</b></td>
				</tr>
			</tbody>
		</table>

		<div class="text-center">
			<div class="text-center">
				<uib-pagination total-items="DCC.pagination.size" ng-model="DCC.pagination.page" ng-change="DCC.pageChanged()"
					items-per-page="DCC.pagination.itemsPerPage" max-size="9" previous-text="Anterior" next-text="Próximo"
					boundary-links="true" first-text="Primeiro" last-text="Último" rotate="false" class="pagination-sm" style="margin-bottom: 5px;">
				</uib-pagination>
			</div>
			<div class="text-center" style="margin-top: 0px;">
				Página <span class="badge">{{ DCC.pagination.page}}</span> de <span class="badge">{{ DCC.pagination.pages}}</span>
				de <span class="badge">{{ DCC.pagination.size}}</span> registro(s)</span>
			</div>
		</div>
	</div>
</div>

<div style="height: 1px; background: #AAA; width: 100%; margin: 15px 0px;"></div>

<div style="width: 100%; margin-bottom: 10px;">

	<div style="margin-top: 15px; width: 100%; text-align: center; color: #F00; font-size: 11pt;">
		* Os filtros no topo da página controlam também os resultados do gráfico (<b>exceto pelo filtro de data</b>)
	</div>

	<div style="width: 100%; text-align: center; margin: 10px 0px;">
		<span style="font-size: 13pt;">Gráfico de desempenho dos usuários</span>
	</div>

	<div class="input-group" style="width: 350px; margin: 0 auto;">
		<div class="input-group-addon">De</div>
		<input type="month" class="form-control" min="2021-04" ng-model="DCC.filter.graficoDesempenho.periodoInicial" ng-change="DCC.getGraficoDesempenhoData();">
		<div class="input-group-addon">a</div>
		<input type="month" class="form-control" min="2021-05" ng-model="DCC.filter.graficoDesempenho.periodoFinal" ng-change="DCC.getGraficoDesempenhoData();">
	</div>

	<div style="width: 100%; text-align: center; margin-top: 15px;">
		<div class="btn-group btn-group-grafico-desempenho" role="group" aria-label="...">

			<button type="button" class="btn" ng-class="DCC.tipoPontuacaoGrafico == 'simples' ? 'btn-primary' : 'btn-default'" ng-click="DCC.changeTipoPontuacaoGrafico('simples');">Pontuação simples</button>

			<button type="button" class="btn" ng-class="DCC.tipoPontuacaoGrafico == 'ponderada' ? 'btn-primary' : 'btn-default'" ng-click="DCC.changeTipoPontuacaoGrafico('ponderada');">Pontuação ponderada</button>

		</div>
	</div>
</div>

<div id="graficoDesempenho"></div>

<div ng-include="'app/gerencia/desempenho.comercial.modal.assuntos.html'"></div>
<div ng-include="'app/gerencia/desempenho.comercial.modal.funcionarios.html'"></div>