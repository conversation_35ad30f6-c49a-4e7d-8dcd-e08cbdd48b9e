'use strict';

angular.module('app')

	.factory('Formatter', function () {
		var service = {
			mac: mac
		};
		return service;

		function mac(str) {
			str = str.replace(/[^A-Fa-f0-9]/g, '').replace(/(.{2})/g, "$1:").toUpperCase().substr(0, 17);
			return str;
		}
	})

	.factory('Validator', function () {
		var service = {
			mac: mac
		}
		return service;

		function mac(str) {
			var regex = /^([0-9A-F]{2}[:-]?){5}([0-9A-F]{2})$/;
			return regex.test(str.toUpperCase());
		}
	})

	.factory('TimeRestriction', function ($localStorage) {
		var service = {
			verify: verify
		}
		return service;

		function verify(key, period) {
			var data = angular.fromJson( $localStorage[key] );
      		if(data){
        		var minutes = Math.round(Math.abs((new Date(data['lastExec']).getTime()-new Date().getTime())/(60*1000)));    
        		if(minutes > period){
					$localStorage[key] = angular.toJson( {'lastExec': new Date()} );
					return true;
				} else {
					return false;
				}
			} else {
				$localStorage[key] = angular.toJson( {'lastExec': new Date()} );
				return true;
			}	
		}	
	})

	.factory('AlertService', function ($uibModal) {
		var service = {
			alert: alert,
			error: error,
			loading: loading,
			loaded: loaded,
			success: success
		};
		return service;

		function loading(message) {
			angular.element('#loading-modal-message').html(message);
			angular.element('#loading-modal').modal('show');
		}

		function loaded(status, message) {
			status = status || null;
			message = message || null;

			$('#loading-modal').modal('hide');

			if (status && status === 'success' && message)
				success(message);

			else if (status && status == 'error' && message)
				error(message);
			
			else
				error('Ocorreu um erro ao realizar o procedimento');
		}

		function error(message) {
			alert({
				title: 'Erro!',
				html: message
			});
		}

		function success(message) {
			alert({
				title: 'Sucesso!',
				html: message
			});
		}

		function alert(options) {
			var defaultOptions = {
				title: '',
				html: '',
				buttons: [
					// {
					// 	text: 'Sim',
					// 	action: function () {

					// 	}
					// }
				],
				cancelBtnText: 'OK',
				cancelBtnColor: 'primary',
				windowClass: ''
			}

			options = Object.assign(defaultOptions, options);

			var ModalInstanceCtrl = function ($scope, $uibModalInstance) {

				options.buttons.forEach(function (value, index, array) {
					$scope['fn_' + index] = function () {
						value.action();
						$uibModalInstance.close();
					}
				});

				$scope.cancel = function () {
					$uibModalInstance.dismiss('cancel');
				};
			};

			//Corrige erro ao minificar a diretiva
			ModalInstanceCtrl.$inject = ['$scope', '$uibModalInstance'];

			var modalHtml = '';

			if (options.title)
				modalHtml += '<div class="modal-header" style="padding-left: 20px; font-size: 10pt;"><b>' + options.title + '</b></div>'

			modalHtml += '<div class="modal-body" style="padding-bottom: 15px; width: ' + options.width + 'px">' + options.html + '</div>';

			modalHtml += '<div class="modal-footer">';

			options.buttons.forEach(function (value, index, array) {
				var color = value.color ? value.color : 'primary'
				modalHtml += '<button class="btn btn-' + color + '" ng-click="fn_' + index + '()">' + value.text + '</button>';
			});

			modalHtml += '<button class="btn btn-' + options.cancelBtnColor + '" ng-click="cancel()">' + options.cancelBtnText + '</button></div>';

			var modalInstance = $uibModal.open({
				template: modalHtml,
				controller: ModalInstanceCtrl,
				windowClass: 'on-top faded vcentered'
			});

			modalInstance.result.then(function () {

			}, function () {
				//Modal dismissed
			});
		}

	});