(function () {
    'use strict';

    angular
        .module('app')
        .factory('httpRequestInterceptor', httpRequestInterceptor);

	function httpRequestInterceptor($q, $location, $rootScope, $localStorage) {
    	return {
        	'responseError': function(rejection) {
        		console.log(rejection);
            	// do something on error
            	if(rejection.status === 401){
                    //$rootScope.operador = {"username":""};
                    //$rootScope.eventoSelecionado = {id: ''};
                    //delete $localStorage.currentUser;
                    //delete $localStorage.evento;
                    //$location.path('/login');  
            		if((rejection.data.mensagem == 'Sessão expirada') || (rejection.data.mensagem == 'Token não encontrado') || (rejection.data.mensagem == 'Token inválido')){
            			$rootScope.operador = {"username":""};
                        $rootScope.eventoSelecionado = {id: ''};
                        delete $localStorage.currentUser;
                        delete $localStorage.evento;
            			$location.path('/login');  
            		}
            	}
            	return $q.reject(rejection);
         	},
         	/*
            'request': function(config) {
           		config.headers = config.headers || {};
           		if ($localStorage.currentUser) {
               		config.headers.Authorization = 'Bearer ' + $localStorage.currentUser.token;
            	}
            	return config;
       		}
            */
     	};
    } 	
})();
