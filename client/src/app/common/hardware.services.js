'use strict';

angular.module('app')

    .factory('HardwareJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/hardwares', {},
            {
                getHardwares: {method: 'GET', isArray: true}
            });
    })

    .factory('HardwareService', function ($http, $q, API_CONFIG) {
        var service = {
            hardwares: [],
            getHardwares: getHardwares
        };
        return service;

		function getHardwares() {
            var def = $q.defer();

            $http.get(API_CONFIG.url + '/hardwares', { cache: true})
                .then(function(data) {
                    service.hardwares = data;
                    def.resolve(data);
                }, function(error) {
                    def.reject("Ocorreu um erro ao recuperar a lista de hardwares");
                });
            return def.promise;
        }

    });