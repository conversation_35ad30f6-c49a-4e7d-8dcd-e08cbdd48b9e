'use strict';

angular.module('app')

    .factory('ModoJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/modos.json', {},
            {
                getModos: {method: 'GET', isArray: true}
            });
    })

    .factory('ModoService', function ($http, $q, API_CONFIG) {
        var service = {
            modos: [],
            getModos: getModos
        };
        return service;

		function getModos() {
            var def = $q.defer();

            $http.get(API_CONFIG.url + '/modos.json')
                .then(function(response) {
                    service.modos = response.data;
                    def.resolve(response.data);
                }, function(error) {
                    def.reject("Ocorreu um erro ao recuperar a lista de modos de operação");
                });
            return def.promise;
        }

    });
