'use strict';

angular.module('app')

    .factory('PopsMapasService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:popid/mapas/:id', {},
            {
                getMapas: {
                    method: 'GET',
                    isArray: true
                },
                insertMapa: {
                    method: 'POST',
                    params: {popid: '@popid'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                },
                deleteMapa: {
                    method: 'DELETE',
                    params: {popid: '@popid', id: '@id'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })

    .factory('PopsMapasComboService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:popid/mapas.combo', {},
            {
                getMapas: {
                    method: 'GET',
                    isArray: true
                }
            }
        );
    });
