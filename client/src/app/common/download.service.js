'use strict';

angular.module('app')

    .factory('DownloadService', function ($http, API_CONFIG) {
        var service = {
            startDownload: startDownload
        };
        return service;

        function startDownload(tipo, pasta, host, arquivo) {
            var urlApi = API_CONFIG.url + '/download?tipo=' + tipo
                + "&pasta=" + pasta + "&host=" + host + "&arquivo=" + arquivo;
            $http({
                method: 'GET',
                url: API_CONFIG.url + '/download',
                params: { tipo: tipo, pasta: pasta, host: host, arquivo: arquivo },
                withCredentials: true,
                responseType: 'arraybuffer'
            }).then(function (response) {

                headers = response.headers;
                var filename = arquivo;
                var contentType = headers['content-type'];

                var blob = new Blob([response.data], {
                    type: contentType
                });
                if (navigator.msSaveBlob)
                    navigator.msSaveBlob(blob, filename);
                else {
                    // Try using other saveBlob implementations, if available
                    var saveBlob = navigator.webkitSaveBlob || navigator.mozSaveBlob || navigator.saveBlob;
                    if (saveBlob === undefined) {
                        var linkElement = document.createElement('a');
                        try {
                            var blob = new Blob([response.data], { type: contentType });
                            var url = window.URL.createObjectURL(blob);
                            linkElement.setAttribute('href', url);
                            linkElement.setAttribute("download", filename);

                            var clickEvent = new MouseEvent("click", {
                                "view": window,
                                "bubbles": true,
                                "cancelable": false
                            });
                            linkElement.dispatchEvent(clickEvent);
                        } catch (ex) {
                            console.log(ex);
                        }
                    }

                }

            }, function (error) {
                console.log(error);
            });
        };

    });