'use strict';

angular.module('app')

    .factory('ServicosTiposJSONService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/servicos/tipos.json', {},
            {
                getServicosTipos: {method: 'GET', isArray: true}
            });
    })

    .factory('ServicosTiposService', function ($http, $q, API_CONFIG) {
        var service = {
            tipos: [],
            getTipos: getTipos
        };
        return service;

		function getTipos(filtro) {
            var def = $q.defer();

            $http.get(API_CONFIG.url + '/servicos/tipos.json'+filtro)
                .then(function(data) {
                    service.tipos = data;
                    def.resolve(data);
                }, function(error) {
                    def.reject("Ocorreu um erro ao recuperar a lista de tipos");
                });
            return def.promise;
        }

    });