'use strict';

angular.module('app')

    .filter('trustAsResourceUrl', ['$sce', function ($sce) {
        return function (val) {
            return $sce.trustAsResourceUrl(val);
        };
    }])

    .filter('trustAsHTML', ['$sce', function($sce){
        return function(text) {
            return $sce.trustAsHtml(text);
        };
    }])
    .filter('bytes', function() {
        return function(bytes, precision) {
            if (bytes === 0) { return '0 b' }
            if (isNaN(parseFloat(bytes)) || !isFinite(bytes)) return '-';
            if (typeof precision === 'undefined') precision = 1;
      
            var units = ['b', 'Kb', 'Mb', 'Gb', 'Tb', 'Pb'],
                number = Math.floor(Math.log(bytes) / Math.log(1024)),
                val = (bytes / Math.pow(1024, Math.floor(number))).toFixed(precision);
      
            return  (val.match(/\.0*$/) ? val.substr(0, val.indexOf('.')) : val) +  ' ' + units[number];
        }
      })

    .filter('protocol', function() {
        return function(number) {
            switch(number){
                case 0:
                    return 'HOPOPT (0)';
                    break;
                case 1:
                    return 'ICMP (1)';
                    break;
                case 2:
                    return 'IGMP (2)';
                    break;
                case 3:
                    return 'GGP (3)';
                    break;
                case 4:
                    return 'IPV4 (4)';
                    break;
                case 6:
                    return 'TCP (6)';
                    break;
                case 7:
                    return 'CBT (7)';
                    break;
                case 8: 
                    return 'EGP (8)';
                    break;
                case 9:
                    return 'IGP (9)';
                    break;
                case 17:
                    return 'UDP (17)';
                    break;
                case 27:
                    return 'RDP (27)';
                    break;
                case 41:
                    return 'IPV6 (41)';
                    break;
                case 58:
                    return 'IPv6-ICMP (58)';
                    break;
                case 92:
                    return 'MTP (92)';
                    break;
                case 115:
                    return 'L2TP (115)';
                    break;
                default:
                    return number;
                    break;    
            }
        }
    })

    .filter('propsFilter', function () {
        return function (items, props) {
            var out = [];

            if (angular.isArray(items)) {
                items.forEach(function (item) {
                    var itemMatches = false;

                    var keys = Object.keys(props);
                    for (var i = 0; i < keys.length; i++) {
                        var prop = keys[i];
                        var text = props[prop].toLowerCase();
                        if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                            itemMatches = true;
                            break;
                        }
                    }

                    if (itemMatches) {
                        out.push(item);
                    }
                });
            } else {
                // Let the output be the input untouched
                out = items;
            }

            return out;
        };
    })
    
    .filter('numkeys', function () {
        return function (object) {
            return Object.keys(object).length;
        }
    })

    .filter('abbreviateDay', function () {
        return function (input) {
            if (!input) return '';
            var daysOfWeek = ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'];
            var dayIndex = new Date(input).getDay();
            return daysOfWeek[dayIndex];
        };
    });