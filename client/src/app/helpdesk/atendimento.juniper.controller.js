(function () {
    'use strict';

    angular
        .module('app')
        .controller('AtendimentoJuniperController', AtendimentoJuniperController);

    /** @ngInject */
    function AtendimentoJuniperController($http, $interval, API_CONFIG, 
      $rootScope, $base64, $resource, AtendimentoService, Contrato, APICalls, appSocket) {

        var vm = this;

        vm.logs = [];
        vm.contrato = Contrato;
        vm.fila = [];
        vm.itemfila = -1;
        vm.busca = busca;
        vm.campo = 'usuario';
        vm.limpa = limpa;
        vm.atualizaLogs = atualizaLogs;
        vm.consultaAnm = consultaAnm;
        vm.mostraSenha = mostraSenha;
        vm.processa_fila = processa_fila;
        vm.editacdo = editacdo;
        vm.salvacdo = salvacdo;
        vm.cancelacdo = cancelacdo;
        vm.gravasinal = gravasinal;
        vm.getHistSinal = getHistSinal;
        vm.getFtthLogs = getFtthLogs;
        vm.getAutotesteTestes = getAutotesteTestes;
        vm.getWanStatus = getWanStatus;
        vm.sinal = [];
        vm.ftthlogs = [];
        vm.estrutura = {};
        vm.wan = {};
        vm.queue = {
          name: '',
          upload: 0,
          download: 0,
          technology: ''
        };
        vm.atualizaPing = atualizaPing;
        vm.pausarPing = pausarPing;
        vm.cancelarPing = cancelarPing;
        vm.testandoPing = 0;

       vm.ping = {
          sourceip : '',
          targetip : '',
          packetsize : 0,
          packetsSent: 0,
          packetsReceived: 0,
          packetsLost: 0,
          packetsLostPerc: 0,
          min: 0,
          max: 0,
          avg: 0,
          sum: 0,
          packets : []
        };
        var pingIntervalPromise;
        vm.graficoPing = {};
       
        vm.atualizaTrafego = atualizaTrafego;
        vm.pausarTrafego = pausarTrafego;
        vm.cancelarTrafego = cancelarTrafego;
        vm.testandoTrafego = 0;

        vm.trafego = {
          timestampUnix: 0,
          name: '',
          remoteMac: '',
          mtu: 0,
          inputBytes: 0,
          inputBitsPerSecond: 0,
          outputBytes: 0,
          outputBitsPerSecond: 0,
          inputPackets: 0,
          inputPacketsPerSecond: 0,
          outputPackets: 0,
          outputPacketsPerSecond: 0,
          minInput: 0,
          maxInput: 0,
          avgInput: 0,
          sumInput: 0,
          minOutput: 0,
          maxOutput: 0,
          avgOutput: 0,
          sumOutput: 0,
          samples: 0
        };
    
        vm.atualizaTorch = atualizaTorch;
        vm.pausarTorch = pausarTorch;
        vm.cancelarTorch = cancelarTorch;
        vm.testandoTorch = 0;
        vm.torch = {
          timeout: '10',
          connections: []
        }  
        var torchIntervalPromise;

        vm.queue = {
          name: '',
          upload: 0,
          download: 0,
          technology: ''
        };

        var trafegoIntervalPromise;
        vm.graficoTrafego = {};

        vm.getFila = getFila;
        vm.getContrato = getContrato;
        vm.getOnuInfo = getOnuInfo;

        vm.senhaVisivel = false;
        vm.localizado = null;
        vm.atualizandolog = false;
        vm.atualizandoffthlogs = false;
        vm.atualizandofila = false;
        vm.atualizandocontrato = false;
        vm.atualizandoonu = false;
        vm.atualizandowan = false;
        vm.editandocdo = false;
        vm.buscainicial = false;
        vm.termos = '';
        vm.anm = '';
        vm.avisos = [];
        vm.debug = [];
        vm.processando_fila = false;
        vm.fila_processada = false;

        activate();

        function activate(){
          getFila(vm.contrato.username);
          if(vm.contrato.tipohost == 'Porta GPON OLT' || vm.contrato.tipohost == 'FTTA'){
            getHistSinal();
            getFtthLogs();
            getOnuInfo(); 
          }
     
          getAutotesteTestes();
          atualizaLogs(vm.contrato.username);
          getWanStatus(vm.contrato.username);

          
        }

        function mostraSenha(){
          if(vm.senhaVisivel == true){
            vm.senhaVisivel = false;
          } else {
            vm.senhaVisivel = true;
          }
          
        }

       function editacdo(){
         vm.editandocdo = true;
         vm.estrutura.cdo = angular.copy(vm.contrato.cdo);
          vm.estrutura.cat = angular.copy(vm.contrato.cat);
          vm.estrutura.porta = angular.copy(vm.contrato.porta);
       } 

      function cancelacdo(){
        vm.editandocdo = false;
      } 

       function salvacdo(){
        $http({
              url: API_CONFIG.url + '/ftth/estrutura',
              method: "POST",
              data: { 
                'username' : vm.contrato.username, 
                'cat' : vm.estrutura.cat, 
                'cdo' : vm.estrutura.cdo,
                'porta' : vm.estrutura.porta
              },
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.editandocdo = false;
           vm.contrato.cdo = angular.copy(vm.estrutura.cdo);
           vm.contrato.cat = angular.copy(vm.estrutura.cat);
           vm.contrato.porta = angular.copy(vm.estrutura.porta);
          },
          function(response) {
            vm.editandocdo = false;
            vm.contrato.cdo = angular.copy(vm.estrutura.cdo);
           vm.contrato.cat = angular.copy(vm.estrutura.cat);
           vm.contrato.porta = angular.copy(vm.estrutura.porta);
          });
        
      } 

      function gravasinal(){
        var comentario = prompt("Digite um comentário", "");
        if (comentario != null && comentario !== '') {
          $http({
            url: API_CONFIG.url + '/ftth/sinal',
            method: "POST",
            data: { 
              'username' : vm.contrato.username, 
              'rx' : vm.anm.sinal.RxPower, 
              'tx' : vm.anm.sinal.TxPower,
              'tx_bias' : vm.anm.sinal.CurrTxBias,
              'comentario' : comentario
            },
            ignoreLoadingBar: true
      })
      .then(function(response) {
         getHistSinal();
        },
        function(response) {
          getHistSinal();
        });
        }  
      }

      function atualizaLogs(){
         vm.atualizandolog = true;
         $http({
               url: API_CONFIG.url + '/concentrador/logs',
               method: "POST",
               data: { 'usuario' : vm.contrato.username},
               ignoreLoadingBar: true
         })
         .then(function(response) {
            vm.logs = response.data;
            vm.atualizandolog = false;
           },
           function(response) {
             vm.atualizandolog = false;
           });
       }

       function getHistSinal(){
        vm.sinal = []; 
        vm.atualizandosinal = true;
        $http({
              url: API_CONFIG.url + '/ftth/sinal?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.sinal = response.data;
           vm.atualizandosinal = false;
          },
          function(response) {
            vm.atualizandosinal = false;
          });
      }

      function getAutotesteTestes(){
        vm.testes = []; 
        vm.atualizandotestes = true;
        $http({
              url: API_CONFIG.url + '/autoteste/testes?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.testes = response.data;
           vm.atualizandotestes = false;
          },
          function(response) {
            vm.atualizandotestes = false;
          });
      }

      function getFtthLogs(){
        vm.ftthlogs = []; 
        vm.atualizandoftthlogs = true;
        $http({
              url: API_CONFIG.url + '/ftth/logs?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.ftthlogs = response.data;
           vm.atualizandoftthlogs = false;
          },
          function(response) {
            vm.atualizandoftthlogs = false;
          });
      }

      function processa_fila(username){
        
         vm.processando_fila = true;
         vm.itemfila++;
         if(vm.itemfila < vm.fila.length){
         
           vm.fila[vm.itemfila].status = 'Processando';
           $http({
             url: API_CONFIG.url + '/ftth/fila',
             method: "POST",
             data: { 'idi' : vm.fila[vm.itemfila].idi, 'operador' : $rootScope.operador.username},
             ignoreLoadingBar: true
           })
           .then(function(response) {
             vm.fila[vm.itemfila].status = 'Finalizado';
             vm.fila[vm.itemfila].retornos = response.data.retornos;
             vm.fila[vm.itemfila].erros = response.data.erros;
             processa_fila(username);
           });
        } else{
          vm.processando_fila = false;  
          vm.fila_processada = true;
          vm.itemfila = -1;
        }
      }

      function getFila(username){
        vm.fila = [];
        vm.itemfila = -1;
        vm.fila_processada = false;
        vm.processando_fila = false;
        vm.atualizandofila = true;
        $http({
          url: API_CONFIG.url + '/anm/fila?username=' + username,
          method: "GET",
          ignoreLoadingBar: true
        })
        .then(function(response) {
          vm.fila = response.data;
          vm.atualizandofila = false;
        });
      }
      
      function getContrato(){
        
        vm.atualizandocontrato = true;
        if(vm.campo == 'usuario'){
          var data = {'usuario' : vm.termos}
        } else {
          var data = {'mac' : vm.termos}
        }

        $http({
          url: API_CONFIG.url + '/concentrador/contrato',
          method: "POST",
          data: data,
          ignoreLoadingBar: true
        })
        .then(function(response) {
          vm.contrato = response.data;
          //se o cliente foi localizado
          vm.atualizandocontrato = false;
          if(vm.contrato.hasOwnProperty('nomeassinante')){
            vm.localizado = true;
            vm.estrutura.cdo = angular.copy(vm.contrato.cdo);
            vm.estrutura.cat = angular.copy(vm.contrato.cat);
            vm.estrutura.porta = angular.copy(vm.contrato.porta);
            
            if(vm.buscainicial){
              getFila(vm.contrato.username);
              if(vm.contrato.tipohost == 'Porta GPON OLT' || vm.contrato.tipohost == 'FTTA'){
                getHistSinal();
                getFtthLogs();
                getOnuInfo(); 
                //getOnuSinal();
               
              }
         
              getAutotesteTestes();
              atualizaLogs(vm.contrato.username);
              vm.buscainicial = false;
            }

              vm.graficoPing = Highcharts.stockChart('graficoPing', {
              chart: {
                zoomType: 'x',
                height: 300
              },

              navigator: {
                height: 15
              },
          
              time: {
                useUTC: false
              },
          
              rangeSelector: {
                buttons: [{
                    count: 1,
                    type: 'minute',
                    text: '1M'
                }, {
                    count: 5,
                    type: 'minute',
                    text: '5M'
                }, 
                {
                  count: 10,
                  type: 'minute',
                  text: '10M'
                },
                {
                  count: 30,
                  type: 'minute',
                  text: '30M'
                },
                {
                  count: 1,
                  type: 'hour',
                  text: '1H'
                },
                {
                    type: 'all',
                    text: 'Todos'
                }],
                inputEnabled: false,
                selected: 5
            },
          
            title: {
                text: 'PING'
            },
          
            credits: {
              enabled: false
            },
            xAxis: {
              minRange: 1000,
              tickInterval: 60000
            },
            yAxis: {
              floor: 0,
              opposite: false,
              labels: {
                  align: 'right',
                  x: -5
              },
              title: {
                text: 'RTT (ms)'
              },
            },
            exporting: {
                enabled: true
            },
          
    
              lang: {
                loading: 'Aguarde...',
                months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                exportButtonTitle: "Exportar",
                printChart: "Imprimir",
                viewFullscreen: "Tela cheia",
                rangeSelectorFrom: "De",
                rangeSelectorTo: "Até",
                rangeSelectorZoom: "Periodo",
                downloadPNG: 'Baixar imagem PNG',
                downloadJPEG: 'Baixar imagem JPEG',
                downloadPDF: 'Baixar documento PDF',
                downloadSVG: 'Baixar imagem SVG',
                resetZoom: "Resetar",
                resetZoomTitle: "Resetar",
                thousandsSep: ".",
                decimalPoint: ','
                }
              
            });

            vm.graficoTrafego = Highcharts.stockChart('graficoTrafego', {
              chart: {
                zoomType: 'x',
                height: 300
              },

              navigator: {
                height: 15
              },
          
              time: {
                useUTC: false
              },
          
              rangeSelector: {
                buttons: [{
                    count: 1,
                    type: 'minute',
                    text: '1M'
                }, {
                    count: 5,
                    type: 'minute',
                    text: '5M'
                }, 
                {
                  count: 10,
                  type: 'minute',
                  text: '10M'
                },
                {
                  count: 30,
                  type: 'minute',
                  text: '30M'
                },
                {
                  count: 1,
                  type: 'hour',
                  text: '1H'
                },
                {
                    type: 'all',
                    text: 'Todos'
                }],
                inputEnabled: false,
                selected: 5
            },
          
            title: {
                text: 'Tráfego'
            },
          
            legend: {
              itemDistance: 50
            },
          
            plotOptions: {
              series: {
                tooltip : {
                  valueDecimals: 2,
                  valueSuffix: ' Mb/s'
                }
              }
            },
            
            credits: {
              enabled: false
            },
            xAxis: {
              minRange: 1000,
              tickInterval: 60000,
              crosshair: true,
              
            },
            yAxis: {
          
              floor: 0,
              opposite: false,
              minRange:10,
              labels: {
                  align: 'right',
                  x: -5
              },
              title: {
                text: 'Mb/s'
              },
              plotLines: []
            },
            exporting: {
                enabled: true
            },
          
            lang: {
                loading: 'Aguarde...',
                months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                exportButtonTitle: "Exportar",
                printChart: "Imprimir",
                viewFullscreen: "Tela cheia",
                rangeSelectorFrom: "De",
                rangeSelectorTo: "Até",
                rangeSelectorZoom: "Periodo",
                downloadPNG: 'Baixar imagem PNG',
                downloadJPEG: 'Baixar imagem JPEG',
                downloadPDF: 'Baixar documento PDF',
                downloadSVG: 'Baixar imagem SVG',
                resetZoom: "Resetar",
                resetZoomTitle: "Resetar",
                thousandsSep: ".",
                decimalPoint: ','
                }
              
          });
          getWanStatus(vm.contrato.username);
 
          } else {
            vm.localizado = false;
          }

          
        });
      }

 
      
      function getOnuInfo(){
        if(vm.contrato.tipohost == 'Porta GPON OLT' || vm.contrato.tipohost == 'FTTA'){
          consultaAnm(vm.contrato.serial, vm.contrato.escopo);  
        }  
      }

      /*
      function getOnuSinal(){
          vm.atualizandoonu = true;
          if(vm.contrato.serial !== ''){
            $http({
              url: API_CONFIG.url + '/onu',
              method: "POST",
              data: { 
                'comando' : 'SINAL',
                'service' : vm.contrato.escopo,
                'onu_id'  : vm.contrato.serial
              },
              ignoreLoadingBar: true
            })
            .then(function(response) {
              //vm.onu.sinal = response.data;
              vm.anm.sinal = response.data;
              getOnuDistancia();
            },
            function(response) {
            });  
          }
      }

      function getOnuProcessamento(){
        vm.atualizandoonu = true;
        if(vm.contrato.serial !== ''){
          $http({
            url: API_CONFIG.url + '/onu',
            method: "POST",
            data: { 
              'comando' : 'PROCESSAMENTO',
              'service' : vm.contrato.escopo,
              'onu_id'  : vm.contrato.serial
            },
            ignoreLoadingBar: true
          })
          .then(function(response) {
            vm.onu.processamento = response.data;
            getOnuDistancia();
          },
          function(response) {
          });  
        }
      }

      function getOnuDistancia(){
        vm.atualizandoonu = true;
        if(vm.contrato.serial !== ''){
          $http({
            url: API_CONFIG.url + '/onu',
            method: "POST",
            data: { 
              'comando' : 'DISTANCIA',
              'service' : vm.contrato.escopo,
              'onu_id'  : vm.contrato.serial
            },
            ignoreLoadingBar: true
          })
          .then(function(response) {
            //vm.onu.distancia = response.data;
            vm.anm.dist = response.data;
            getOnuStatus();
          },
          function(response) {
          });  
        }
      }

      function getOnuStatus(){
        vm.atualizandoonu = true;
        if(vm.contrato.serial !== ''){
          $http({
            url: API_CONFIG.url + '/onu',
            method: "POST",
            data: { 
              'comando' : 'STATUS',
              'service' : vm.contrato.escopo,
              'onu_id'  : vm.contrato.serial
            },
            ignoreLoadingBar: true
          })
          .then(function(response) {
            //vm.onu.status = response.data;
            vm.anm.status = response.data;
           getOnuLanStatus();
          },
          function(response) {
          });  
        }
      }

      function getOnuLanStatus(){
        vm.atualizandoonu = true;
        if(vm.contrato.serial !== ''){
          $http({
            url: API_CONFIG.url + '/onu',
            method: "POST",
            data: { 
              'comando' : 'LANSTATUS',
              'service' : vm.contrato.escopo,
              'onu_id'  : vm.contrato.serial
            },
            ignoreLoadingBar: true
          })
          .then(function(response) {
            //vm.onu.lanstatus = response.data;
            vm.anm.lanstatus = response.data;
            vm.atualizandoonu = false;
          },
          function(response) {
          });  
        }
      }
      */

      function getWanStatus(usuario){
        vm.atualizandowan = true;
        AtendimentoService.wan({usuario: usuario}, function (response){
          vm.wan = response;
          vm.wan.status = 'Conectado';
          vm.atualizandowan = false;
          if((!vm.wan.hasOwnProperty('connectionStartTimeUnix')) || (vm.wan.ip == null)){
            vm.wan.status = 'Desconectado';
          } else {
            vm.wan.status = 'Conectado';
            getPlano(vm.wan.plan);
            vm.graficoPing = Highcharts.stockChart('graficoPing', {
              chart: {
                zoomType: 'x',
                height: 280
              },
              navigator: {
                height: 10
              },
              time: {
                useUTC: false
              },
          
              rangeSelector: {
                buttons: [{
                    count: 1,
                    type: 'minute',
                    text: '1M'
                }, {
                    count: 5,
                    type: 'minute',
                    text: '5M'
                }, 
                {
                  count: 10,
                  type: 'minute',
                  text: '10M'
                },
                {
                  count: 30,
                  type: 'minute',
                  text: '30M'
                },
                {
                  count: 1,
                  type: 'hour',
                  text: '1H'
                },
                {
                    type: 'all',
                    text: 'Todos'
                }],
                inputEnabled: false,
                selected: 5
            },
          
            title: {
                text: 'PING'
            },
          
            credits: {
              enabled: false
            },
            xAxis: {
              minRange: 1000,
              tickInterval: 60000
            },
            yAxis: {
              floor: 0,
              opposite: false,
              labels: {
                  align: 'right',
                  x: -5
              },
              title: {
                text: 'RTT (ms)'
              },
            },
            exporting: {
                enabled: true
            },
          
    
              lang: {
                loading: 'Aguarde...',
                months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                exportButtonTitle: "Exportar",
                printChart: "Imprimir",
                viewFullscreen: "Tela cheia",
                rangeSelectorFrom: "De",
                rangeSelectorTo: "Até",
                rangeSelectorZoom: "Periodo",
                downloadPNG: 'Baixar imagem PNG',
                downloadJPEG: 'Baixar imagem JPEG',
                downloadPDF: 'Baixar documento PDF',
                downloadSVG: 'Baixar imagem SVG',
                resetZoom: "Resetar",
                resetZoomTitle: "Resetar",
                thousandsSep: ".",
                decimalPoint: ','
                }
              
            });

            vm.graficoTrafego = Highcharts.stockChart('graficoTrafego', {
              chart: {
                zoomType: 'x',
                height: 280
              },
          
              navigator: {
                height: 10
              },

              time: {
                useUTC: false
              },
          
              rangeSelector: {
                buttons: [{
                    count: 1,
                    type: 'minute',
                    text: '1M'
                }, {
                    count: 5,
                    type: 'minute',
                    text: '5M'
                }, 
                {
                  count: 10,
                  type: 'minute',
                  text: '10M'
                },
                {
                  count: 30,
                  type: 'minute',
                  text: '30M'
                },
                {
                  count: 1,
                  type: 'hour',
                  text: '1H'
                },
                {
                    type: 'all',
                    text: 'Todos'
                }],
                inputEnabled: false,
                selected: 5
            },
          
            title: {
                text: 'Tráfego'
            },
          
            legend: {
              itemDistance: 50
            },
          
            plotOptions: {
              series: {
                tooltip : {
                  valueDecimals: 2,
                  valueSuffix: ' Mb/s'
                }
              }
            },
            
            credits: {
              enabled: false
            },
            xAxis: {
              minRange: 1000,
              tickInterval: 60000,
              crosshair: true,
              
            },
            yAxis: {
          
              floor: 0,
              opposite: false,
              minRange:10,
              labels: {
                  align: 'right',
                  x: -5
              },
              title: {
                text: 'Mb/s'
              },
              plotLines: []
            },
            exporting: {
                enabled: true
            },
          
            lang: {
                loading: 'Aguarde...',
                months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                exportButtonTitle: "Exportar",
                printChart: "Imprimir",
                viewFullscreen: "Tela cheia",
                rangeSelectorFrom: "De",
                rangeSelectorTo: "Até",
                rangeSelectorZoom: "Periodo",
                downloadPNG: 'Baixar imagem PNG',
                downloadJPEG: 'Baixar imagem JPEG',
                downloadPDF: 'Baixar documento PDF',
                downloadSVG: 'Baixar imagem SVG',
                resetZoom: "Resetar",
                resetZoomTitle: "Resetar",
                thousandsSep: ".",
                decimalPoint: ','
                }
              
          });
          }

        }, function(error) {
          vm.wan.status = 'Erro ao obter o status';
          vm.atualizandowan = false;
          //usuario nao conectado
          if(error.status==404){
            vm.wan.status = 'Desconectado';
            vm.atualizandowan = false;
          }
        });
      }

      function getPlano(url){
        var Plan = $resource(url, null,
          {
            get: {
                headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token},
                method: 'GET',
                ignoreLoadingBar: true
            },
        });

        Plan.get().$promise.then(function(plan) {
          vm.queue = plan;
          if(vm.queue.hasOwnProperty('download')){
            vm.graficoTrafego.yAxis[0].options.minRange = (vm.queue.download / 100000) + (10 * ((vm.queue.download / 100000) / 100));
            vm.graficoTrafego.yAxis[0].options.plotLines = [
              {
                value: vm.queue.download / 100000,
                color: 'blue',
                dashStyle: 'shortdash',
                width: 2,
                label: {
                  text: 'Queue Download ' + (vm.queue.download / 100000) + 'MB'
                }  
              },
              {
                value: vm.queue.upload / 100000,
                color: 'blue',
                dashStyle: 'shortdash',
                width: 2,
                label: {
                  text: 'Queue Upload ' + (vm.queue.upload / 100000) + 'MB'
                }  
              },
            ];  
          };  
        }, function(error) {
          //usuario nao conectado
          if(error.status==404){
            
          }
        });
      }

      function busca(campo, termos){
         vm.fila = [];
         vm.processando_fila = false;
         vm.localizado = null;
         vm.logs = [];
         vm.sinal = [];
         vm.avisos = [];
         vm.ftthlogs = [];
         vm.contrato= {};
         vm.senhaVisivel = false;
         vm.buscainicial = true;
         vm.anm = '';
         getContrato();
         
        

        }

        function limpa(){
          vm.termos = '';
          vm.campo = 'usuario';
          vm.localizado = null;
          vm.logs = [];
          vm.sinal = [];
          vm.ftthlogs = [];
          vm.contrato= {};
          vm.senhaVisivel = false;
       }

    function consultaAnm(serial, servico){  
        vm.atualizandoonu = true;
        vm.anm = '';
        if(serial !== ''){
          $http({
            url: API_CONFIG.url + '/anm',
            method: "POST",
            data: { 
              'service' : servico,
              'onu_id'  : serial
            },
            ignoreLoadingBar: true
          })
          .then(function(response) {
            vm.anm = response.data;
            vm.atualizandoonu = false;
          },
          function(response) {
          });  
        }
    }
    
    

    function atualizaPing(usuario) {
      vm.testandoPing = 1;
      vm.graficoPing.addSeries({
        name: 'Ping',
        id: 'ping',
        type: 'area',
        threshold: null,
        color: '#64b448'
      });
      
      appSocket.emit('start_ping', {'host' : vm.wan.ip, 'username': $rootScope.operador.username});    

      /*
      APICalls.pingIntervalPromise = $interval(function(){
          AtendimentoService.ping({usuario: usuario}, {count: 1}, function (response){
            vm.ping.sourceIp = response.sourceIp;
            vm.ping.targetIp = response.targetIp;
            vm.ping.packetSize = response.packetSize;
            vm.ping.packetsSent++;
            vm.ping.packetsReceived = vm.ping.packetsReceived + response.packetsReceived;
            vm.ping.packetsLost = vm.ping.packetsSent - vm.ping.packetsReceived;
            vm.ping.packetsLostPerc = Number((vm.ping.packetsLost / vm.ping.packetsSent ) * 100).toFixed(2);
            var pacote = [];
            if(response.packets[0].rtt === undefined){
               pacote =  [response.packets[0].dateTimeUnix * 1000, 'Perdido'];
            } else {
              pacote = [ response.packets[0].dateTimeUnix * 1000, response.packets[0].rtt];    
              vm.ping.sum = vm.ping.sum + response.packets[0].rtt;
              vm.ping.avg = Number((vm.ping.sum / vm.ping.packetsSent).toFixed(3));
            }

            vm.ping.packets = vm.ping.packets.concat([pacote]);

            var shift = vm.graficoPing.series[0].data.length > 600; 

            if(response.packets[0].rtt !== undefined){
              vm.graficoPing.series[0].addPoint(pacote, true, shift, false);
                if((response.packets[0].rtt < vm.ping.min) || (vm.ping.packetsSent == 1)){
                vm.ping.min = response.packets[0].rtt;
              }

              if(response.packets[0].rtt > vm.ping.max){
                vm.ping.max = response.packets[0].rtt;
              }

            } else {
              vm.graficoPing.series[0].addPoint([pacote[0], null], true, shift, false);
            }    

            //Rola o quadro de resultados para baixo
            jQuery( function(){
              var pre = jQuery("#pingresults");
              pre.scrollTop( pre.prop("scrollHeight") );
            });
          });
      }, 1000);   
      */
    }

    appSocket.on('pingTest', function(data){
      if(vm.testandoPing == 1){
        vm.ping.packetsSent++;
        var pacote = [];
  
        if(data.rtt === 'lost'){
          pacote =  [data.currentTimeUnix * 1000, 'Perdido'];
        } else {
          pacote = [ data.currentTimeUnix * 1000, data.rtt];    
          vm.ping.packetsReceived++;
          vm.ping.sum = vm.ping.sum + data.rtt;
          vm.ping.avg = Number((vm.ping.sum / vm.ping.packetsSent).toFixed(2));
          if((data.rtt < vm.ping.min) || (vm.ping.min == 0)){
            vm.ping.min = data.rtt;
          }
  
          if(data.rtt > vm.ping.max){
            vm.ping.max = data.rtt;
          }
        }
  
        vm.ping.packetsLost = vm.ping.packetsSent - vm.ping.packetsReceived;
        vm.ping.packetsLostPerc = Number((vm.ping.packetsLost / vm.ping.packetsSent ) * 100).toFixed(2);
  
        var shift = vm.graficoPing.series[0].data.length > 600; 
  
        if(data.rtt !== 'lost'){
          vm.graficoPing.series[0].addPoint(pacote, true, shift, false);
        } else {
          vm.graficoPing.series[0].addPoint([pacote[0], null], true, shift, false);
        }   
  
        vm.ping.packets = vm.ping.packets.concat(data);
  
        //Rola o quadro de resultados para baixo
        jQuery( function(){
          var pre = jQuery("#pingresults");
          pre.scrollTop( pre.prop("scrollHeight") );
        });
      }
      
    });  

    function pausarPing(){
    //  $interval.cancel(APICalls.pingIntervalPromise);
    //  APICalls.pingIntervalPromise = null;
      vm.testandoPing = 2;
      appSocket.emit('stop_ping', {});  
    }

    function cancelarPing(){
      vm.testandoPing = 0;
      //$interval.cancel(APICalls.pingIntervalPromise);
      //APICalls.pingIntervalPromise = null;
      appSocket.emit('stop_ping', {});    
      vm.ping = {
        sourceip : '',
        targetip : '',
        packetsize : 0,
        packetsSent: 0,
        packetsReceived: 0,
        packetsLost: 0,
        packetsLostPerc: 0,
        min: 0,
        max: 0,
        avg: 0,
        sum: 0,
        packets : []
      };
      vm.graficoPing.series[0].remove()
    }

    appSocket.on('trafficTest', function(response){
      if(vm.testandoTrafego == 1){

        vm.trafego.timestampUnix = response.timestampUnix;
        vm.trafego.inputBitsPerSecond = response.inputBitsPerSecond;
        vm.trafego.outputBitsPerSecond = response.outputBitsPerSecond;
        vm.trafego.inputPacketsPerSecond = response.inputPacketsPerSecond;
        vm.trafego.outputPacketsPerSecond = response.outputPacketsPerSecond;
        vm.trafego.inputBytes = response.inputBytes * 8;
        vm.trafego.outputBytes = response.outputBytes * 8;


        vm.trafego.samples++;

        var shift = vm.graficoTrafego.series[0].data.length > 600;
        if((vm.trafego.inputBitsPerSecond < vm.trafego.minInput) || (vm.trafego.samples == 1)){
          vm.trafego.minInput = vm.trafego.inputBitsPerSecond;
        }
        
        if(vm.trafego.inputBitsPerSecond > vm.trafego.maxInput){
          vm.trafego.maxInput = vm.trafego.inputBitsPerSecond;
        }

        vm.trafego.sumInput = vm.trafego.sumInput + vm.trafego.inputBitsPerSecond;
        vm.trafego.avgInput = Number((vm.trafego.sumInput / vm.trafego.samples).toFixed(3));

        if((vm.trafego.outputBitsPerSecond < vm.trafego.minOutput) || (vm.trafego.samples == 1)){
          vm.trafego.minOutput = vm.trafego.outputBitsPerSecond;
        }
       
        if(vm.trafego.outputBitsPerSecond > vm.trafego.maxOutput){
         vm.trafego.maxOutput = vm.trafego.outputBitsPerSecond;
        }

        vm.trafego.sumOutput = vm.trafego.sumOutput + vm.trafego.outputBitsPerSecond;
        vm.trafego.avgOutput = Number((vm.trafego.sumOutput / vm.trafego.samples).toFixed(3));

        vm.graficoTrafego.series[0].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.outputBitsPerSecond / 1000000 ], true, shift, false);
        vm.graficoTrafego.series[1].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.inputBitsPerSecond / 1000000 ], true, shift, false);

      }
    });    

    function atualizaTrafego(usuario) {
      vm.testandoTrafego = 1;
      vm.graficoTrafego.addSeries({
          name: 'Download',
          id: 'download',
          type: 'area',
          threshold: null,
          color: '#64b448'
        });

      vm.graficoTrafego.addSeries(  
         {
          name: 'Upload',
          id: 'upload',
          type: 'area',
          threshold: null,
          color: '#FF0000'
      }   );
      
      appSocket.emit('start_traffic', {'subscriber' : vm.contrato.username, 'username': $rootScope.operador.username});    


    /*
      APICalls.trafegoIntervalPromise = $interval(function(){
          AtendimentoService.interface({usuario: usuario}, function (response){
             vm.trafego.timestampUnix = response.timestampUnix;
             vm.trafego.inputBitsPerSecond = response.inputBitsPerSecond;
             vm.trafego.outputBitsPerSecond = response.outputBitsPerSecond;

             vm.trafego.samples++;

             var shift = vm.graficoTrafego.series[0].data.length > 600;
            
             if((vm.trafego.inputBitsPerSecond < vm.trafego.minInput) || (vm.trafego.samples == 1)){
               vm.trafego.minInput = vm.trafego.inputBitsPerSecond;
             }
             
             if(vm.trafego.inputBitsPerSecond > vm.trafego.maxInput){
               vm.trafego.maxInput = vm.trafego.inputBitsPerSecond;
             }

             vm.trafego.sumInput = vm.trafego.sumInput + vm.trafego.inputBitsPerSecond;
             vm.trafego.avgInput = Number((vm.trafego.sumInput / vm.trafego.samples).toFixed(3));

             if((vm.trafego.outputBitsPerSecond < vm.trafego.minOutput) || (vm.trafego.samples == 1)){
               vm.trafego.minOutput = vm.trafego.outputBitsPerSecond;
             }
            
             if(vm.trafego.outputBitsPerSecond > vm.trafego.maxOutput){
              vm.trafego.maxOutput = vm.trafego.outputBitsPerSecond;
             }

             vm.trafego.sumOutput = vm.trafego.sumOutput + vm.trafego.outputBitsPerSecond;
             vm.trafego.avgOutput = Number((vm.trafego.sumOutput / vm.trafego.samples).toFixed(3));

             vm.graficoTrafego.series[0].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.outputBitsPerSecond / 1000000 ], true, shift, false);
             vm.graficoTrafego.series[1].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.inputBitsPerSecond / 1000000 ], true, shift, false);
          });
      }, 1000);   
      */
   
    }

    function pausarTrafego(){
      //$interval.cancel(APICalls.trafegoIntervalPromise);
      //APICalls.trafegoIntervalPromise = null;
      vm.testandoTrafego = 2;
      appSocket.emit('stop_traffic', {});  
    }

    function cancelarTrafego(){
      vm.testandoTrafego = 0;
      //$interval.cancel(APICalls.trafegoIntervalPromise);
      //APICalls.trafegoIntervalPromise = null;
      appSocket.emit('stop_traffic', {});  
      vm.trafego = {
        timestampUnix: 0,
        name: '',
        remoteMac: '',
        mtu: 0,
        inputBytes: 0,
        inputBitsPerSecond: 0,
        outputBytes: 0,
        outputBitsPerSecond: 0,
        inputPackets: 0,
        inputPacketsPerSecond: 0,
        outputPackets: 0,
        outputPacketsPerSecond: 0,
        minInput: 0,
        maxInput: 0,
        avgInput: 0,
        sumInput: 0,
        minOutput: 0,
        maxOutput: 0,
        avgOutput: 0,
        sumOutput: 0,
        samples: 0
      };
  
      vm.graficoTrafego.series[0].remove()
      vm.graficoTrafego.series[1].remove()
    }

    function atualizaTorch(usuario) {
      if(vm.testandoTorch == 0){
        AtendimentoService.torchpost(
          {
            usuario: usuario,
            timeout: vm.torch.timeout
          }, function (response){
          vm.testandoTorch = 1;
          APICalls.torchIntervalPromise = $interval(function(){
            AtendimentoService.torchget(
              {
                usuario: usuario,
                display: 'connections',
                timeout: vm.torch.timeout
              }, function (response){
              vm.torch.connections = response.connections;
            });  
          }, 1000);
        });  
      }
    }

    function pausarTorch(){
      $interval.cancel(APICalls.torchIntervalPromise);
      APICalls.torchIntervalPromise = null;
      vm.testandoTorch = 2;
    }

    function cancelarTorch(){
      vm.testandoTorch = 0;
      $interval.cancel(APICalls.torchIntervalPromise);
      APICalls.torchIntervalPromise = null;
      vm.torch.connections = [];
      AtendimentoService.torchdelete(
        {
          usuario: vm.contrato.username,
        }, function (response){
        });  

    }

  }

})();
