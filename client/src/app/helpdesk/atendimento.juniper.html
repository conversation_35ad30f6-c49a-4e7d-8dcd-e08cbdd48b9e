<div class="alert alert-warning" role="alert"><span class="glyphicon glyphicon-info"></span> <strong>Atenção</strong>
  <hr class="message-inner-separator">
  <p>Este usuário está autenticado no <strong>JUNIPER</strong>. As ferramentas de monitoramento (Ping, Tráfego, Torch, etc) estão disponíveis neste módulo do NOC, não sendo necessária a utilização do Winbox.</p>
</div>

<!-- CONTRATO -->
<div class="panel panel-primary" ng-if="AC.contrato.hasOwnProperty('nomeassinante')">
    <div class="panel-heading clearfix">
        <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandocontrato"> <button class="btn btn-default btn-sm" ng-click="AJC.getContrato(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
        </span>
       <h3 class="panel-title" style="margin-top: 5px;"><strong>Contrato</strong></h3>
    </div>
   <div class="panel-body">
    <table class="table table-bordered">
            <thead>
            <tr>
            <th class="vert-align text-center">Cliente</th>
            <th class="vert-align text-center">Contrato</th>
            <th class="vert-align text-center">Serviço</th>
            <th class="vert-align text-center">Tipo Serviço</th>
            <th class="vert-align text-center">Nome Plano</th>
            <th class="vert-align text-center">Plano Down</th>
            <th class="vert-align text-center">Plano Up</th>
            <th class="vert-align text-center">Senha</th>
            </tr>
            </thead>
            <tbody>
              <tr>
              <td class="vert-align text-center">{{AC.contrato.nomeassinante}}</td>
              <td class="vert-align text-center"><span class="label label-default"
                ng-class="[{'label-success': AC.contrato.situacaocontrato == 'Conectado/Ativo'},
                {'label-danger': AC.contrato.situacaocontrato == 'Inadimplente' || AC.contrato.situacaocontrato == 'Cancelado'},
                {'label-warning': AC.contrato.situacaocontrato == 'Conectado/Inadimplente' || AC.contrato.situacaocontrato == 'Pausado' ||
              AC.contrato.situacaocontrato == 'Aguardando Conexão'}]">{{AC.contrato.situacaocontrato}}</span></td>
              <td class="vert-align text-center"><span ng-if="AC.contrato.tipohost!=='Radio AP 5G - ACL Radius'">{{AC.contrato.escopo}}</span>
                <a ng-if="AC.contrato.tipohost=='Radio AP 5G - ACL Radius'" href="http://{{AC.contrato.ipadmin}}:8922" target="blank_">{{AC.contrato.escopo}}</a>
              </td>  
              <td class="vert-align text-center">{{AC.contrato.tipohost}}</td>
              <td class="vert-align text-center">{{AC.contrato.plano}}</td>
              <td class="vert-align text-center">{{AC.contrato.down}}</td>
              <td class="vert-align text-center">{{AC.contrato.up}}</td>
              <td class="vert-align text-center"><span class="label label-default" style="cursor: pointer;" ng-click="AJC.mostraSenha()" ng-if="AC.contrato.senha !== '' && AC.contrato.senha !== undefined && !AJC.senhaVisivel">Mostrar Senha</span><span ng-if="AJC.senhaVisivel==true">{{AC.contrato.senha}} <span class="label label-danger" style="cursor: pointer;" ng-click="AJC.mostraSenha()">x</span></span></td>
            </tr>
            </tbody>
            </table>
            <div class="row">
            <div class="col-md-4">
              <table class="table table-bordered">
                <thead>
                <tr>
                  <th class="vert-align text-center">Pacote</th>
                  <th class="vert-align text-center">Status</th>
                </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="pacote in AC.contrato.pacotes">
                  <td class="vert-align text-center">{{pacote.nomepacote}}</td>
                  <td class="vert-align text-center"><span class="label label-default"
                    ng-class="[{'label-success': pacote.situacaopacote == 'Ativo'},
                    {'label-danger': pacote.situacaopacote == 'Inativo'}]">{{pacote.situacaopacote}}</span></td>
                </tr>
                </tbody>
               </table>
              </div>   
            </div>

            <div class="alert alert-success" role="alert" ng-if="AC.contrato.situacaocontrato == 'Conectado/Ativo' || AC.contrato.situacaocontrato == 'Conectado/Inadimplente'"><span class="glyphicon glyphicon-ok"></span> <strong>Resultado do checklist</strong>
              <hr class="message-inner-separator">
              <p>Não há pendências no contrato</p></div>

            <div class="alert alert-danger" role="alert" ng-if="AC.contrato.situacaocontrato == 'Inadimplente'"><span class="glyphicon glyphicon-remove"></span> <strong>Resultado do checklist</strong>
                <hr class="message-inner-separator">
                <p>O contrato do cliente encontra-se <strong>{{AC.contrato.situacaocontrato}}</strong>. Nenhum serviço estará disponível até que a regularização financeira seja efetuada</p></div>
             <div class="alert alert-warning" role="alert" ng-if="AC.contrato.situacaocontrato == 'Aguardando Conexão'"><span class="glyphicon glyphicon-info"></span> <strong>Resultado do checklist</strong>
                  <hr class="message-inner-separator">
                  <p>O contrato do cliente está OK. Pode ocorrer do serviço de ativação ainda não ter sido realizado ou a ordem de serviço não foi atualizada no sistema</p></div>

    </div>
  </div>
<!-- FIM CONTRATO-->



<!-- PROVISIONAMENTO -->
  
  <div class="panel panel-primary" ng-if="AJC.fila.length > 0 && AC.contrato.hasOwnProperty('nomeassinante')">
    <div class="panel-heading clearfix">
        <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandofila"> <button class="btn btn-default btn-sm" ng-click="AJC.getFila(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
        </span>
       <h3 class="panel-title" style="margin-top: 5px;"><strong>Provisionamento de equipamento pendente</strong></h3>
    </div>
    <div class="panel-body">
        <table class="table table-bordered" ng-if="AJC.fila.length > 0">
            <thead>
            <tr>
              <th class="vert-align text-center">Status</th>
              <th class="vert-align text-center">Data/Hora</th>
              <th class="vert-align text-center">MAC</th>
              <th class="vert-align text-center">Modelo</th>
              <th class="vert-align text-center">Serial</th>
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">Sit. Equipamento</th>
              <th class="vert-align text-center">Escopo</th>
            <th class="vert-align text-center">Wifi</th>
            <th class="vert-align text-center">IPTV</th>
            <th class="vert-align text-center">STB</th>
            <th class="vert-align text-center">Erros</th>
            <th class="vert-align text-center">Retorno</th>
            </tr>
            </thead>
          <tbody>
            <tr ng-repeat="fila in AJC.fila">
              <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-default': fila.status == 'Na Fila'}, {'label-warning': fila.status == 'Processando'}, {'label-danger': fila.erros > 0}, {'label-success': fila.status == 'Finalizado' && fila.erros == 0}]">{{fila.status}}</span></td>
              <td class="vert-align text-center">{{fila.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
              <td class="vert-align text-center">{{fila.mac}}</td>
              <td class="vert-align text-center">{{fila.modelo}}</td>
              <td class="vert-align text-center">{{fila.serial}}</td>
              <td class="vert-align text-center"><a href="/materiais/patrimonios/{{fila.patrimonio}}" target="_blank">{{fila.patrimonio}}</a></td>
              <td class="vert-align text-center">
                <span class="label label-danger" ng-if="fila.situacaoequipamento==0">Desativado</span>  
                <span class="label label-success" ng-if="fila.situacaoequipamento==1">Ativado</span>  
                </td>
              <td class="vert-align text-center">{{fila.escopo}}</td>
              <td class="vert-align text-center"><span class="label label-default" ng-if="fila.wifi==0">Não</span>  
                <span class="label label-default" ng-if="fila.wifi==1">Sim</span></td>
              <td class="vert-align text-center"><span class="label label-default" ng-if="fila.iptv==0">Não</span>  
                <span class="label label-default" ng-if="fila.iptv==1">Sim</span></td>
              <td class="vert-align text-center">
                  <span class="label label-default">{{fila.stb}}</span>  
                </td>
              <td class="vert-align text-center"><span class="label label-default">{{fila.erros}}</span></td>
              <td class="vert-align text-center">
                <span ng-repeat="item in fila.retornos">{{item}}<br></span></td>
          </tr>
          </tbody>
          </table>
          
          <button ng-hide="AJC.processando_fila" ng-if="(AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA') && AJC.fila.length > 0 && !AJC.fila_processada" class="btn btn-warning" title="Processar Fila de Alterações" ng-click="AJC.processa_fila(AC.contrato.username)"><i class="glyphicon glyphicon-play-circle"></i> Processar Fila</button><span ng-if="AJC.processando_fila"><img src="assets/images/ajax-loader.gif"> Processando fila...aguarde</span>
    </div>
  </div>

<!-- FIM PROVISIONAMENTO -->

    
<!-- ONU -->
<div class="panel panel-primary" ng-if="AC.contrato.hasOwnProperty('nomeassinante')" ng-hide="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !== 'FTTA' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !==undefined">
    <div class="panel-heading clearfix">
        <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandoonu"> <button class="btn btn-default btn-sm" ng-click="AJC.getOnuInfo()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
        </span>
       <h3 class="panel-title" style="margin-top: 5px;"><strong>ONU</strong></h3>
    </div>
   <div class="panel-body">

      <ul class="nav nav-tabs">
          <li class="active">
            <a data-target="#equipamento" data-toggle="tab">
              <i class="glyphicon glyphicon-hdd"></i> Equipamento instalado </a>
              
          </li>
         
      </ul>
      <div id="equipamento" class="tab-pane fade in active">
        <div class="tab-content">

      <span ng-if="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !== 'FTTA' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !==undefined">Plano contratado não utiliza ONU</span>
      <table class="table table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
          <thead>
          <tr>
          <th class="vert-align text-center">Marca</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">CDO</th>
          <th class="vert-align text-center">CAT</th>
          <th class="vert-align text-center">Porta CAT</th>
          <th class="vert-align text-center">Ação</th>
          </tr>
          </thead>
          <tbody>
            <tr>
                  <td class="vert-align text-center">{{AC.contrato.marca}}</td>
                  <td class="vert-align text-center">{{AC.contrato.modelo}}</td>
                  <td class="vert-align text-center">{{AC.contrato.macaddress}}</td>
                  <td class="vert-align text-center">{{AC.contrato.serial}}</td>
                  <td class="vert-align text-center"><a href="/materiais/patrimonios/{{AC.contrato.patrimonio}}" target="_blank">{{AC.contrato.patrimonio}}</a></td>
                  <td class="vert-align text-center"><span ng-if="!AJC.editandocdo">{{AJC.contrato.cdo}}</span><input ng-if="AJC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AJC.estrutura.cdo"></td>
                  <td class="vert-align text-center"><span ng-if="!AJC.editandocdo">{{AJC.contrato.cat}}</span><input ng-if="AJC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AJC.estrutura.cat"></td>
                  <td class="vert-align text-center"><span ng-if="!AJC.editandocdo">{{AJC.contrato.porta}}</span><input ng-if="AJC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AJC.estrutura.porta"></td>
                  <td class="vert-align text-center"><button class="btn btn-default btn-sm" ng-if="!AJC.editandocdo" title="Edita informações (CDO, CAT, Porta CAT)" ng-click="AJC.editacdo()"><i class="glyphicon glyphicon-edit"></i></button><button ng-if="AJC.editandocdo" title="Grava alterações" class="btn btn-success btn-sm" ng-click="AJC.salvacdo()"><i class="glyphicon glyphicon-floppy-saved"></i></button> <button ng-if="AJC.editandocdo" title="Cancela alterações" class="btn btn-danger btn-sm" ng-click="AJC.cancelacdo()"><i class="glyphicon glyphicon-ban-circle"></i></button></td> 
          </tr>
        </tbody>
        </table> 
        </div> 
    </div>

    <div ng-if="AC.contrato.situacaocontrato == 'Conectado/Ativo'">

    <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#optical" data-toggle="tab">
            <i class="glyphicon glyphicon-flash"></i> Optical status</a>
        </li>
        <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandoonu"> <button class="btn btn-default btn-sm" ng-click="AJC.getOnuInfo()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
        </span>
       
    </ul>
    <div id="optical" class="tab-pane fade in active">
      <div class="tab-content">

    <span ng-if="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !== 'FTTA' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !==undefined">Plano contratado não utiliza ONU</span>
    <table class="table table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
        <thead>
        <tr>
          <th class="vert-align text-center">Estado Operacional</th>
          <th class="vert-align text-center">Distância</th>
          <th class="vert-align text-center">Último Desligamento</th>
        <th class="vert-align text-center">RxPower</th>
        <th class="vert-align text-center">TxPower</th>
        <th class="vert-align text-center">CurrTxBias</th>
        </tr>
        </thead>
        <tbody>
          <tr>
              <td class="vert-align text-center"><span class="label label-default"
                ng-class="[{'label-success': AJC.anm.dist.OperState == 'UP'},
                {'label-danger': AJC.anm.dist.OperState !== 'UP'}]">{{AJC.anm.dist.OperState}}</span></td>
              <td class="vert-align text-center">{{AJC.anm.dist.Length}}m</td>
              <td class="vert-align text-center">{{AJC.anm.status.LASTOFFTIME | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
              <td class="vert-align text-center">{{AJC.anm.sinal.RxPower}} <span class="label label-default"
                ng-class="[{'label-success': AJC.anm.sinal.RxPowerR == 'Normal'},
                {'label-danger': AJC.anm.sinal.RxPowerR !== 'Normal'}]">{{AJC.anm.sinal.RxPowerR}}</span></td>
              <td class="vert-align text-center">{{AJC.anm.sinal.TxPower}} <span class="label label-default"
                ng-class="[{'label-success': AJC.anm.sinal.TxPowerR == 'Normal'},
                {'label-danger': AJC.anm.sinal.TxPowerR !== 'Normal'}]">{{AJC.anm.sinal.TxPowerR}}</span></td>
              <td class="vert-align text-center">{{AJC.anm.sinal.CurrTxBias}} <span class="label label-default"
                ng-class="[{'label-success': AJC.anm.sinal.CurrTxBiasR == 'Normal'},
                {'label-danger': AJC.anm.sinal.CurrTxBiasR !== 'Normal'}]">{{AJC.anm.sinal.CurrTxBiasR}}</span></td>
        </tr>
      </tbody>
      </table> 
      </div> 
  </div>



  <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#lanstatus" data-toggle="tab">
          <i class="glyphicon glyphicon-list-alt"></i> LAN status</a>
          
      </li>
     
  </ul>
  <div id="lanstatus" class="tab-pane fade in active">
    <div class="tab-content">
            <table class="table table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
                <thead>
                <tr>
                  <th class="vert-align text-center">Porta</th>
                  <th class="vert-align text-center">VLANID</th>
                  <th class="vert-align text-center">Estado Operacional</th>

                </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="porta in AJC.anm.lanstatus.lans">
                      <td class="vert-align text-center"><span class="badge">{{$index+1}}</span></td>
                      <td class="vert-align text-center"><strong>{{porta.PVID}}</strong></td>
                      <td class="vert-align text-center"><span class="label label-default"
                        ng-class="[{'label-success': porta.OperStatus == 'UP'},
                        {'label-danger': porta.OperStatus !== 'UP'}]">{{porta.OperStatus}}</span>
                        </td>
                </tr>
              </tbody>
              </table>  
 
    </div> 
</div>

    </div>

  <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#histsinal" data-toggle="tab">
          <i class="glyphicon glyphicon-object-align-bottom"></i> Histórico de sinal</a>
      </li>
      <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandosinal"> <button class="btn btn-default btn-sm" ng-disabled = "AJC.anm.sinal == undefined || AJC.anm == ''" ng-click="AJC.gravasinal()"><i class="glyphicon glyphicon-floppy-saved"></i> Gravar Sinal Atual</button> <button class="btn btn-default btn-sm" ng-click="AJC.getHistSinal()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
      </span>
     
  </ul>
  <div id="histsinal" class="tab-pane fade in active">
    <div class="tab-content">
        <div class="pre-scrollable ng-scope" style="height:80px;">
            <table class="table table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
                <thead>
                <tr>
                  <th class="vert-align text-center">Data</th>
                  <th class="vert-align text-center">Rx</th>
                  <th class="vert-align text-center">Tx</th>
                  <th class="vert-align text-center">TxBias</th>
                  <th class="vert-align text-center">Comentário</th>
                </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in AJC.sinal">
                      <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                      <td class="vert-align text-center">{{item.rx}}</td>
                      <td class="vert-align text-center">{{item.tx}}</td>
                      <td class="vert-align text-center">{{item.tx_bias}}</td>
                      <td class="vert-align text-center">{{item.comentario}}</td>
                </tr>
              </tbody>
              </table>  
              </div>
  
    </div> 
  </div>


  <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#logsprovisionamento" data-toggle="tab">
          <i class="glyphicon glyphicon-console"></i> Logs de provisionamento</a>
          
      </li>
     
  </ul>
  <div id="logsprovisionamento" class="tab-pane fade in active">
    <div class="tab-content">
        <div class="pre-scrollable ng-scope" style="height:120px;">
            <table class="table table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
                <thead>
                <tr>
                  <th class="vert-align text-center">Data Processamento</th>
                  <th class="vert-align text-center">Data Alteração</th>
                  <th class="vert-align text-center">Operador</th>
                  <th class="vert-align text-center">Patrimônio</th>
                  <th class="vert-align text-center">Modelo</th>
                  <th class="vert-align text-center">Serial</th>
                  <th class="vert-align text-center">MAC</th>
                  <th class="vert-align text-center">Sit. Equipamento</th>
                  <th class="vert-align text-center">Escopo</th>
                  <th class="vert-align text-center">Wi-fi</th>
                  <th class="vert-align text-center">IPTV</th>
                  <th class="vert-align text-center">STB</th>
                  <th class="vert-align text-center">Log</th>
                  <th class="vert-align text-center">Detalhes</th>
                </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in AJC.ftthlogs" ng-class="[{'success': item.erro == 0}, {'danger' : item.erro == 1}]">
                    <td class="vert-align text-center">{{item.data_processamento | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{item.data_alteracao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                      <td class="vert-align text-center">{{item.operador}}</td>
                      <td class="vert-align text-center">{{item.patrimonio}}</td>
                      <td class="vert-align text-center">{{item.modelo}}</td>
                      <td class="vert-align text-center">{{item.serial}}</td>
                      <td class="vert-align text-center">{{item.mac}}</td>
                      <td class="vert-align text-center">
                        <span class="label label-danger" ng-if="item.situacaoequipamento==0">Desativado</span>  
                        <span class="label label-success" ng-if="item.situacaoequipamento==1">Ativado</span> 
                      </td>
                      <td class="vert-align text-center">{{item.escopo}}</td>
                      <td class="vert-align text-center"><span class="label label-default" ng-if="item.wifi==0">Não</span>  
                        <span class="label label-default" ng-if="item.wifi==1">Sim</span></td>
                      <td class="vert-align text-center"><span class="label label-default" ng-if="item.iptv==0">Não</span>  
                        <span class="label label-default" ng-if="item.iptv==1">Sim</span></td>
                      <td class="vert-align text-center"> <span class="label label-default">{{item.stb}}</span> </td>
                      <td class="vert-align text-center">{{item.log}}</td>
                      <td class="vert-align text-center">{{item.detalhes}}</td>
                </tr>
              </tbody>
              </table>  
              </div>
  
    </div> 
  </div>
  </div>
 </div>
 <!-- FIM ONU -->


 <!-- CPE -->

        <div class="panel panel-primary" ng-if="AC.contrato.hasOwnProperty('nomeassinante') && ((AC.contrato.situacaocontrato == 'Conectado/Ativo') || (AC.contrato.situacaocontrato == 'Conectado/Inadimplente'))">
            <div class="panel-heading clearfix">
               
               <h3 class="panel-title" style="margin-top: 5px;"><strong>CPE</strong></h3>
            </div>
           <div class="panel-body">
              <ul class="nav nav-tabs">
                  <li class="active">
                    <a data-target="#wanstatus" data-toggle="tab">
                      <i class="glyphicon glyphicon-globe"></i> WAN Status</a>
                  </li>
                  <span class="pull-right">
                      <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandowan"> <button class="btn btn-default btn-sm" ng-click="AJC.getWanStatus(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
                  </span>
              </ul>
              <div id="wanstatus" class="tab-pane fade in active">
                <div class="tab-content">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                          <!--<th class="vert-align text-center">Tipo autenticação</th>-->
                          <th class="vert-align text-center">Status</th>
                          <th class="vert-align text-center">IPv4</th>
                          <th class="vert-align text-center">IPv6</th>
                          <th class="vert-align text-center">Router</th>
                        </tr>
                        </thead>
                        <tbody>
                          <tr>
                              <!--<td class="vert-align text-center"></td>-->
                              <td class="vert-align text-center"><span class="label label-default"
                                ng-class="[{'label-success': AJC.wan.status == 'Conectado'},
                                {'label-danger': AJC.wan.status == 'Desconectado'}]">{{AJC.wan.status}}</span></td>
                              <td class="vert-align text-center">{{AJC.wan.ip}}</td>
                              <td class="vert-align text-center">{{AJC.wan.ipv6Prefix}}</td>
                              <td class="vert-align text-center">{{AJC.wan.router}}</td>
                        </tr>
                      </tbody>
                      </table>  
                </div>
              </div>
              <ul class="nav nav-tabs">
                  <li class="active">
                    <a data-target="#logs" data-toggle="tab">
                      <i class="glyphicon glyphicon-globe"></i> Logs de autenticação</a>
                  </li>
                  <span class="pull-right">
                      <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandolog"> <button class="btn btn-default btn-sm" ng-click="AJC.atualizaLogs(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
                  </span>
              </ul>
              <div id="logs" class="tab-pane fade in active">
                <div class="tab-content">
            <div class="pre-scrollable ng-scope" style="height:100px;">
              <table class="table table-bordered">
                  <thead>
                  <tr>
                  <th class="vert-align text-center">Data/Hora</th>
                  <th class="vert-align text-center">Router</th>
                  <th class="vert-align text-center">Mensagem</th>
                
                  </tr>
                  </thead>
                  
                  <tbody>
                    <tr ng-repeat="log in AJC.logs">
                    <td class="vert-align text-center">{{log.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{log.concentrador}}</td>
                    <td class="vert-align text-center">{{log.mensagem}}</td>
                  </tr>
                  </tbody>
                  
                  </table>
                </div>
                </div>  
            </div>
          </div>
        </div>  

<!-- FIM CPE -->
     
<!-- ANALISE DE REDE BASICA -->
<div class="panel panel-primary" ng-hide="!AC.contrato.hasOwnProperty('nomeassinante') || ((AC.contrato.situacaocontrato !== 'Conectado/Ativo') || (AC.contrato.situacaocontrato !== 'Conectado/Inadimplente')) || AJC.wan.status !== 'Conectado'"> 
        <div class="panel-heading clearfix">
            
           <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Básica</strong></h3>
        </div>
     <div class="panel-body">
    <!-- PING -->
           <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#ping" data-toggle="tab">
                <i class="glyphicon glyphicon-equalizer"></i> Ping </a>
                
            </li>
            <span class="pull-right">
                <img src="assets/images/ajax-loader.gif" ng-show="AJC.testandoPing === 1"> <button class="btn btn-default btn-sm" ng-click="AJC.atualizaPing(AC.contrato.username)" ng-disabled="AJC.testandoPing===1"><i class="glyphicon glyphicon-play"></i> Iniciar Ping</button> <button class="btn btn-default btn-sm" ng-click="AJC.pausarPing()" ng-disabled="AJC.testandoPing===2 || AJC.testandoPing===0"><i class="glyphicon glyphicon-pause"></i> Pausar Ping</button> <button class="btn btn-default btn-sm" ng-click="AJC.cancelarPing()" ng-disabled="AJC.testandoPing===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Ping</button>
            </span>
            
        </ul>
        <div id="ping" class="tab-pane fade in active">
          <div class="tab-content">
              <div class="row">
                <div class="col-md-2">
                    <table class="table table-bordered table-sm" style="margin-bottom: 0px;">
                        <thead>
                        <tr>
                        <th class="vert-align text-center" style="width: 200px;">Respostas</th>
                        </tr>
                        </thead>
                      </table>  
                <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="pingresults">   
                  <table class="table table-bordered table-sm">
                     
                      <tbody>
                        <tr ng-repeat="ping in AJC.ping.packets">
                         <td class="vert-align text-center" style="width: 200px;"><span ng-if="ping.rtt !=='lost'">{{ping.rtt}} ms</span> <span ng-if="ping.rtt =='lost'">Perdido</span></td>
                      </tr>
                      </tbody>
                      </table> 
                      </div>
                      <table class="table table-bordered">
                          <tbody>
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Enviados</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.packetsSent}}</td>
                        </tr>  
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Recebidos</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.packetsReceived}}</td>
                        </tr>
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Perdidos</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.packetsLost}} ({{AJC.ping.packetsLostPerc}}%)</td>
                        </tr>  
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Min</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.min}} ms</td>
                        </tr>
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Med</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.avg}} ms</td>
                        </tr>
                        <tr>
                          <td class="vert-align text-center" style="width: 25%;"><strong>Max</strong></td>
                          <td class="vert-align text-center" style="width: 75%;">{{AJC.ping.max}} ms</td>
                        </tr>
                      </tbody>
                  </table>
                      </div>
                      <div class="col-md-10">
                             <div id="graficoPing"></div>
                      </div>
      
                      </div>
          </div>  
        </div>
        <!-- FIM PING -->
        <!--<hr class="message-inner-separator">-->
        <!-- TRAFEGO -->
        <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#trafego" data-toggle="tab">
                <i class="glyphicon glyphicon-random"></i> Tráfego instantâneo </a>
                
            </li>
            <span class="pull-right">
                <img src="assets/images/ajax-loader.gif" ng-show="AJC.testandoTrafego === 1"> <button class="btn btn-default btn-sm" ng-click="AJC.atualizaTrafego(AC.contrato.username)" ng-disabled="AJC.testandoTrafego===1"><i class="glyphicon glyphicon-play"></i> Iniciar Trafego</button> <button class="btn btn-default btn-sm" ng-click="AJC.pausarTrafego()" ng-disabled="AJC.testandoTrafego===2 || AJC.testandoTrafego===0"><i class="glyphicon glyphicon-pause"></i> Pausar Trafego</button> <button class="btn btn-default btn-sm" ng-click="AJC.cancelarTrafego()" ng-disabled="AJC.testandoTrafego===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Trafego</button>
            </span>
        </ul>
        <div id="trafego" class="tab-pane fade in active">
            <div class="tab-content">
                <div class="row">
                    <div class="col-md-2">
                        <table class="table table-bordered">
                            <colgroup span="2"></colgroup>
                            <tr>
                              <th colspan="2" scope="colgroup" class="vert-align text-center label-success"><strong>Download</strong></th>
                            </tr>
                            <tr>
                              <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                              <td class="vert-align text-center" width="80%">{{AJC.trafego.outputBitsPerSecond | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.minOutput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.avgOutput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.maxOutput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.outputPacketsPerSecond}} pps</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.outputBytes | bytes}}</td>
                            </tr>
                            <colgroup span="2"></colgroup>
                            <tr>
                              <th colspan="2" scope="colgroup" class="vert-align text-center label-danger"><strong>Upload</strong></th>
                            </tr>
                            <tr>
                              <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                              <td class="vert-align text-center" width="80%">{{AJC.trafego.inputBitsPerSecond | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.minInput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.avgInput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.maxInput | bytes}}/s</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.inputPacketsPerSecond}} pps</td>
                            </tr>
                            <tr>
                                <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                                <td class="vert-align text-center" width="80%">{{AJC.trafego.inputBytes | bytes}}</td>
                            </tr>
                            </table>
                     </div>
                    <div class="col-md-10">
                      <div id="graficoTrafego"></div>
                    </div>
                 </div>     
                
            </div>
        </div>      


     </div>    
</div>   
<!-- FIM ANALISE DE REDE BASICO -->


<!-- ANALISE DE REDE AVANÇADA -->
<div class="panel panel-primary" ng-hide="!AC.contrato.hasOwnProperty('nomeassinante') || ((AC.contrato.situacaocontrato !== 'Conectado/Ativo') || (AC.contrato.situacaocontrato !== 'Conectado/Inadimplente')) || AJC.wan.status !== 'Conectado'"> 
  <div class="panel-heading clearfix">
      
     <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Avançada</strong></h3>
  </div>
<div class="panel-body">
<!-- TORCH -->

     <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#torch" data-toggle="tab">
          <i class="glyphicon glyphicon-equalizer"></i> Conexões instantâneas </a>
          
      </li>
      <span class="pull-right">
      <form class="form-inline" role="form">
          <div class="form-group">
              Janela de Monitoramento:
              <select class="form-control" ng-model="AJC.torch.timeout">
                <option value="10">10 segundos</option>
                <option value="60">1 minuto</option>
                <option value="300">5 minutos</option>
                <option value="600">10 minutos</option>
                <option value="900">15 minutos</option>
            </select>
          </div>
          <img src="assets/images/ajax-loader.gif" ng-show="AJC.testandoTorch === 1"> <button class="btn btn-default btn-sm" ng-click="AJC.atualizaTorch(AC.contrato.username)" ng-disabled="AJC.testandoTorch===1"><i class="glyphicon glyphicon-play"></i> Iniciar Torch</button> <button class="btn btn-default btn-sm" ng-click="AJC.pausarTorch()" ng-disabled="AJC.testandoTorch===2 || AJC.testandoTorch===0"><i class="glyphicon glyphicon-pause"></i> Pausar Torch</button> <button class="btn btn-default btn-sm" ng-click="AJC.cancelarTorch()" ng-disabled="AJC.testandoTorch===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Torch</button>
        </form>    
        </span>
  </ul>

  <div id="torch" class="tab-pane fade in active">
    <div class="tab-content">
          <div class="pre-scrollable" style="height:280px;overflow-y: scroll;" id="pingresults">   
            <table class="table table-bordered">
                  <thead>
                  <tr>
                  <th class="vert-align text-center">Protocolo</th>
                  <th class="vert-align text-center">IP Version</th>
                  <th class="vert-align text-center">IP Origem</th>
                  <th class="vert-align text-center">Porta Origem</th>
                  <th class="vert-align text-center">IP Destino</th>
                  <th class="vert-align text-center">Porta Destino</th>
                  <th class="vert-align text-center">Total Octets</th>
                 </tr>
                  </thead>
                 <tbody>
              <tr ng-repeat="torch in AJC.torch.connections">
                  <td class="vert-align text-center" style="width: 200px;">{{torch.protocol | protocol}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.ipVersion}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.sourceAddress}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.sourcePort}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.destinationAddress}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.destinationPort}}</td>
                  <td class="vert-align text-center" style="width: 200px;">{{torch.totalOctets}}</td>
              </tr>
              </tbody>
            </table>
         </div>
    </div>  
  </div>
  <!-- FIM TORCH -->

</div>    
</div>   
<!-- FIM ANALISE DE REDE AVANÇADA -->
     

<!-- TESTES CAMPO -->

<div class="panel panel-primary" ng-if="AC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">
      <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="AJC.atualizandotestes"> <button class="btn btn-default btn-sm" ng-click="AJC.getAutotesteTestes()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
      </span>
     <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Testes realizados em campo</h3>
  </div>
 <div class="panel-body">
    <div class="pre-scrollable ng-scope" style="height:180px;">
    <table class="table table-bordered">
        <thead>
        <tr>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Técnico</th>
          <th class="vert-align text-center">Plano</th>
          <th class="vert-align text-center">Plano Down</th>
          <th class="vert-align text-center">Plano Up</th>
          <th class="vert-align text-center">Teste</th>
          <th class="vert-align text-center">Status</th>
          <th class="vert-align text-center">Resultados</th>
        </tr>
        </thead>
        <tbody>
          <tr ng-repeat="item in AJC.testes">
            <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{item.tecnico}}</td>
            <td class="vert-align text-center">{{item.plano_nome}}</td>
            <td class="vert-align text-center">{{item.plano_down}}</td>
            <td class="vert-align text-center">{{item.plano_up}}</td>
            <td class="vert-align text-center">{{item.teste}}</td>
            <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-danger': item.status == 'nok'}, {'label-success': item.status == 'ok'}]">{{item.status}}</span></td>
            <td class="vert-align text-center">{{item.resultados}}</td>
        </tr>
      </tbody>
      </table>  
      </div>
  </div>
</div>  

<!-- FIM TESTES CAMPO-->     