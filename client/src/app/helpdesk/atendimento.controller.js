(function () {
  'use strict';

  angular
      .module('app')
      .controller('AtendimentoController', AtendimentoController);

  /** @ngInject */
  function AtendimentoController($http, $interval, API_CONFIG, 
    $rootScope, $base64, $resource, AtendimentoService, Contrato) {

      var vm = this;

      vm.contrato = {};
      vm.busca = busca;
      vm.campo = 'usuario';
      vm.limpa = limpa;
      vm.localizado = null;
      vm.buscainicial = false;
      vm.termos = '';
      vm.alertas = [];

      activate();

      function activate(){
        
      }

    function getContrato(){
      angular.copy({}, Contrato);
      vm.atualizandocontrato = true;
      if(vm.campo == 'usuario'){
        var data = {'usuario' : vm.termos}
      } else {
        var data = {'mac' : vm.termos}
      }

      $http({
        url: API_CONFIG.url + '/concentrador/contrato',
        method: "POST",
        data: data,
        ignoreLoadingBar: true
      })
      .then(function(response) {
        vm.contrato = response.data;
        angular.copy(vm.contrato, Contrato);
        //se o cliente foi localizado
        vm.atualizandocontrato = false;
        if(vm.contrato.hasOwnProperty('nomeassinante')){
          vm.localizado = true;
         getAlertas();
          if(vm.buscainicial){
            vm.buscainicial = false;
          }

        } else {
          vm.localizado = false;
        }

        
      });
    }

    function getAlertas(){
      console.log('getAlertas');
      vm.atualizandoalertas = true;
      $http({
        url: API_CONFIG.url + '/alertas',
        method: "POST",
        data: {username: vm.contrato.username},
        ignoreLoadingBar: true
      })
      .then(function(response) {
        vm.alertas = response.data;
      });
    }
    
    function busca(campo, termos){
       vm.localizado = null;
       vm.contrato= {};
       vm.buscainicial = true;
       getContrato();
      }

    function limpa(){
        vm.termos = '';
        vm.campo = 'usuario';
        vm.localizado = null;
        vm.contrato= {};
     }
}

})();
