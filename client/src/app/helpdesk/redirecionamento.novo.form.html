<div class="modal" id="frmnovoredirecionamento" tabindex="-1" role="dialog" aria-labelledby="frmnovoredirecionamento"
	aria-hidden="true" modal="showModal" close="cancel()" style="z-index: 1041;">

	<div class="modal-dialog" style="width: 500px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmnovoredirecionamento_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Novo redirecionamento
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 15px;">
				<div class="row">
					<div class="tab-content">
						<div id="novoredirecionamento_dados" class="tab-pane fade in active">

							<table class="table bottom-spaced align-center valign-middle"
								style="width: 80%; margin: 0 auto;">
								<tbody>
									<tr>
										<td style="width: 70%; padding-right: 5px;">
											<label for="description">Descrição/serviço:</label>
											<input class="form-control align-center font11" type="text"
												placeholder="(opcional)" id="novoredirecionamento_description"
												ng-model="HDC.novoRedirecionamento.description">
										</td>
										<td style="padding-left: 5px;">
											<label for="novoredirecionamento_ativo">Ativo:</label>
											<select disabled id="novoredirecionamento_ativo"
												ng-model="HDC.novoRedirecionamento.enabled" class="form-control">
												<option selected value="true">Sim</option>
												<option value="false">Não</option>
											</select>
										</td>
									</tr>
									<tr>
										<td style="padding-right: 5px;">
											<label for="externalIP">IP público:</label>
											<input disabled class="form-control align-center font9" type="text"
												id="novoredirecionamento_externalIP"
												ng-model="HDC.currentTr069Config.connection.externalIP">
										</td>
										<td style="padding-left: 5px;">
											<label for="externalPort">Porta pública:</label>
											<input ng-disabled="HDC.currentTr069Config.connection.cgnat"
												class="form-control align-center font11" type="text"
												id="novoredirecionamento_externalPort" maxlength="5"
												ng-model="HDC.novoRedirecionamento.externalPort">
										</td>
									</tr>
								</tbody>
							</table>

							<div class="horizontal-divider"></div>

							<table class="table bottom-spaced align-center valign-middle"
								style="width: 80%; margin: 0 auto;">
								<tbody>
									<tr>
										<td style="padding: 0px !important;">
											<label for="internalDevice">Dispositivo interno:</label>
										</td>
										<td style="padding: 0px !important;">
											<button class="btn btn-default pull-right" style="margin-bottom: 5px;"
												ng-click="HDC.getAssocDevices();"><i
													class="glyphicon glyphicon-refresh btn-sm-icon"></i> Atualizar
												lista</button>
											<img src="assets/images/ajax-loader.gif" class="pull-right"
												ng-show="HDC.atualizandoAssocDevices"
												style="margin: 6px 4px 0px 0px !important;">
										</td>
									</tr>
									<tr>
										<td colspan="2">

											<select class="form-control font9" name="internalDevice"
												id="frmnovoredirecionamento_internalDevice"
												ng-model="HDC.selectedFrmNovoRedirecionamentoDevice"
												ng-change="HDC.fillDeviceDetails('frmnovoredirecionamento');">
												<option hidden selected value="">Selecione aqui ou preencha manualmente
													abaixo
												</option>
												<optgroup label="Dispositivos conectados (hostname - IP - MAC):">
													<option disabled ng-if="HDC.currentTr069Config.totalAssociatedDevices == 0"
														value="none">Não há dispositivos conectados</option>
													<option
														ng-repeat="(id, device) in HDC.currentTr069Config.associatedDevices"
														value="{{id}}">{{device.interface == 'ethernet' ? '(Cabo): ' :
														(device.interface == 'wlan' ? '(Wi-Fi): ' :
														'')}}{{device.hostname ? device.hostname :
														'[desconhecido]'}} - {{device.ip}} -
														{{device.mac}}
													</option>
												</optgroup>
											</select>
										</td>
									</tr>
									<tr>
										<td style="width: 50%; padding-right: 5px;">
											<label for="internalIP">IP interno:</label>
											<div class="input-group"><span class="input-group-addon">{{HDC.currentTr069Config.lanIpRangeStr}}</span>
												<input class="form-control font11" type="text"
													id="novoredirecionamento_internalIP"
													ng-model="HDC.novoRedirecionamento.internalIP" maxlength="3">
											</div>
										</td>
										<td style="padding-left: 5px;">
											<label for="mac">MAC:</label>
											<input class="form-control align-center font11" type="text"
												id="novoredirecionamento_mac" ng-model="HDC.novoRedirecionamento.mac"
												maxlength="17">
										</td>
									</tr>
									<tr>
										<td colspan="2">
											<label for="internalPort">Porta interna:</label>
											<input class="form-control align-center font11" type="text"
												id="novoredirecionamento_internalPort"
												ng-model="HDC.novoRedirecionamento.internalPort" maxlength="5"
												style="width: 30%; margin: 0 auto;">
										</td>
									</tr>

									<tr>
										<td colspan="2" class="font9">
											* Você pode selecionar o dispositivo interno através da lista de
											dispositivos conectados (logo acima), ou pode preencher manualmente o IP e o
											MAC do
											dispositivo.
										</td>
									</tr>

								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<!-- Modal Footer -->
			<div class="modal-footer">
				<button class="btn btn-primary" ng-really-message="Deseja realmente adicionar este redirecionamento?"
					ng-really-click="HDC.adicionarRedirecionamento()"><i
						class="fa fa-check btn-icon"></i>Adicionar</button>
			</div>
		</div>
	</div>