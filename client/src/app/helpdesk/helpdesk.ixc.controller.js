(function () {
  'use strict';

  angular
    .module('app')
    .controller('HelpDeskIxcController', HelpDeskIxcController);

  /** @ngInject */
  function HelpDeskIxcController($http, $interval, API_CONFIG, PORTAL_ASSINANTE_API, GENIE_API, MIKROTIK_API, GENIE_ACS, PROVISION_API, ZTE_API, RAS_SERVER,
    $rootScope, $base64, $resource, AtendimentoService, Contrato, APICalls, toaster, AuthorizationService,
    appSocket, nocwsSocket, helpdeskSocket, pingwsSocket, authMonitorSocket, authwsSocket, $filter, $window, $location, $localStorage, AlertService, Validator, Formatter, TimeRestriction, $routeParams) {

    var vm = this;

    vm.userRoles = $rootScope.operador.role;
    vm.logs = [];
    vm.rsyslog = true
    vm.contrato = Contrato;
    vm.fila = [];
    vm.onusNaoAutorizadas = [];
    vm.itemfila = -1;
    vm.getContrato = getContrato;
    vm.campo = 'login';
    vm.limpa = limpa;
    vm.atualizaLogs = atualizaLogs;
    vm.consultaAnm = consultaAnm;
    vm.atualizaSinalOnu = atualizaSinalOnu;
    vm.atualizaStatusOnu = atualizaStatusOnu
    vm.atualizaVlansOnu = atualizaVlansOnu
    vm.atualizaLansOnu = atualizaLansOnu
    vm.mostraSenha = mostraSenha;
    vm.gravasinal = gravasinal;
    vm.getHistSinal = getHistSinal;
    vm.getFtthLogs = getFtthLogs;
    vm.getAutotesteTestes = getAutotesteTestes;
    vm.getWanStatus = getWanStatus;
    vm.getOnuConfig = getOnuConfig;
    vm.getBlacklist = getBlacklist;
    vm.removeBlacklist = removeBlacklist;
    vm.getIpv6List = getIpv6List;
    vm.getDHCPIP = getDHCPIP;
    vm.openONUInterface = openONUInterface;
    vm.openGenieACS = openGenieACS;
    vm.prosseguirVerificacoes = prosseguirVerificacoes;
    vm.startMeshConfig = startMeshConfig;
    vm.cancelMeshConfig = cancelMeshConfig;
    vm.alertasIxc = [];
    vm.sinal = [];
    vm.ftthlogs = [];
    vm.onu_logs = [];
    vm.estrutura = {};
    vm.onu = {};
    vm.wan = {};
    vm.ipv6list = [];
    vm.getChartV6 = getChartV6;
    vm.queue = {
      name: '',
      upload: 0,
      download: 0,
      technology: ''
    };
    vm.atualizaPing = atualizaPing;
    vm.atualizaPingV6 = atualizaPingV6;

    vm.pausarPing = pausarPing;
    vm.cancelarPing = cancelarPing;
    vm.pausarPingV6 = pausarPingV6;
    vm.cancelarPingV6 = cancelarPingV6;
    vm.testandoPing = 0;

    vm.loadedOnuAlarmsFirstTime = false;
    vm.loadingOnuAlarms = false;
    vm.loadingOnuAlarmsSerial = '';

    // *********** INICIO - << TR069 CONFIGS **************

    vm.getTr069Config = getTr069Config;
    vm.saveWifi = saveWifi;
    vm.setWifiEnabled = setWifiEnabled;
    vm.selecionarRedirecionamento = selecionarRedirecionamento;
    vm.adicionarRedirecionamento = adicionarRedirecionamento;
    vm.editarRedirecionamento = editarRedirecionamento;
    vm.excluirRedirecionamento = excluirRedirecionamento;
    vm.initFrmnovoredirecionamento = initFrmnovoredirecionamento;
    vm.getAssocDevices = getAssocDevices;
    vm.fillDeviceDetails = fillDeviceDetails;
    vm.setOperationMode = setOperationMode;
    vm.getOperationModeLog = getOperationModeLog;
    vm.redoPortMappings = redoPortMappings;
    vm.redoPppoe = redoPppoe;
    vm.resetOnuAdminPassword = resetOnuAdminPassword;
    vm.operationModeLogs = [];
    vm.redirecionamentoSelecionado = {};
    vm.redirecionamentoDefault = {
      description: '',
      enabled: "true",
      externalIP: '',
      externalPort: '',
      internalIP: '',
      internalPort: '',
      mac: ''
    };
    vm.novoRedirecionamento = {};
    vm.portalAPIError = null;
    vm.atualizando_ip_dhcp = false;
    vm.currentTr069Config = { totalAssociatedDevices: 0 };
    vm.tr069Config = {
      wifi: {
        1: {
          ssid: '',
          passphrase: ''
        },
        5: {
          ssid: '',
          passphrase: ''
        }
      },
      portMappings: [],
      operationMode: ''
    };
    vm.atualizandoTr069 = false;
    vm.atualizandoAssocDevices = false;
    vm.atualizandoOperationMode = false;
    vm.atualizandoconfig = false;
    vm.selectedFrmRedirecionamentoDevice = '';
    vm.selectedFrmNovoRedirecionamentoDevice = '';
    vm.meshStatus = { status: 'loading' };

    // *********** TR069 CONFIGS >> - FIM **************

    vm.ping = {
      sourceip: '',
      targetip: '',
      packetsize: 0,
      packetsSent: 0,
      packetsReceived: 0,
      packetsLost: 0,
      packetsLostPerc: 0,
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      packets: []
    };
    var pingIntervalPromise;
    vm.graficoPing = {};

    vm.atualizaTrafego = atualizaTrafego;
    vm.pausarTrafego = pausarTrafego;
    vm.cancelarTrafego = cancelarTrafego;
    vm.testandoTrafego = 0;

    vm.trafego = {
      timestampUnix: 0,
      name: '',
      remoteMac: '',
      mtu: 0,
      inputBytes: 0,
      inputBitsPerSecond: 0,
      outputBytes: 0,
      outputBitsPerSecond: 0,
      inputPackets: 0,
      inputPacketsPerSecond: 0,
      outputPackets: 0,
      outputPacketsPerSecond: 0,
      minInput: 0,
      maxInput: 0,
      avgInput: 0,
      sumInput: 0,
      minOutput: 0,
      maxOutput: 0,
      avgOutput: 0,
      sumOutput: 0,
      samples: 0
    };

    vm.atualizaTorch = atualizaTorch;
    vm.pausarTorch = pausarTorch;
    vm.cancelarTorch = cancelarTorch;
    vm.testandoTorch = 0;
    vm.torch = {
      timeout: '10',
      connections: []
    }
    var torchIntervalPromise;

    vm.queue = {
      name: '',
      upload: 0,
      download: 0,
      technology: ''
    };

    vm.graficoTrafego = {};

    vm.getContrato = getContrato;
    vm.onuReauth = onuReauth;
    vm.getOnuInfo = getOnuInfo;

    vm.onuAlarms = [];

    vm.senhaVisivel = false;
    vm.localizado = null;
    vm.atualizandolog = false;
    vm.atualizandoffthlogs = false;
    vm.atualizandofila = false;
    vm.atualizandocontrato = false;
    vm.atualizandoonu = false;
    vm.atualizandowan = false;
    vm.atualizandosinalonu = false;
    vm.atualizandoRoutersPesquisa = false;
    vm.atualizandoRoutersCliente = false;
    vm.editandocdo = false;
    vm.buscainicial = false;
    vm.termos = '';
    vm.anm = '';
    vm.avisos = [];
    vm.debug = [];
    vm.processando_fila = false;
    vm.fila_processada = false;

    vm.sortBy = 'protocol';
    vm.sortOrder = '+';
    vm.sort = sort;

    vm.ipoeativo = false;
    vm.alterarIpoe = alterarIpoe;

    vm.alocarRouter = alocarRouter;
    vm.desalocarRouter = desalocarRouter;
    vm.getRoutersPesquisa = getRoutersPesquisa;
    vm.getRoutersAlocados = getRoutersAlocados;
    vm.routers_pesquisa = [];
    vm.routers_pesquisa_done = false;
    vm.routers_alocados = [];

    vm.statusCGNAT = {};
    vm.getStatusCGNAT = getStatusCGNAT;
    vm.removerCGNAT = removerCGNAT;

    vm.gerenciarEmails = gerenciarEmails;
    vm.alertasGerais = [];
    vm.getAlertsInterval = 0;

    vm.provisions = [];
    vm.getProvisions = getProvisions;
    vm.getJobs = getJobs;
    vm.provisionClick = provisionClick;
    vm.vlanRewrite = vlanRewrite;

    vm.adicionarMulticast = adicionarMulticast;
    vm.removerMulticast = removerMulticast;

    activate();

    angular.element(document).on('shown.bs.modal', '#frmredirecionamento', function () {
      if (vm.tr069Config.operationMode == 'bridge') {
        window.setTimeout(function () {
          angular.element('#frmredirecionamento').modal('hide');
          AlertService.error('O modo de operação da ONU está como "bridge". Não será possível editar um redirecionamento de portas.');
        }, 100);
        return false;
      }
    });

    angular.element(document).on('hidden.bs.modal', '#frmredirecionamento', function () {
      vm.selectedFrmRedirecionamentoDevice = '';
    });

    angular.element(document).on('hidden.bs.modal', '#frmnovoredirecionamento', function () {
      vm.selectedFrmNovoRedirecionamentoDevice = '';
    });

    angular.element(document).on('keyup', '#novoredirecionamento_mac, #frmredirecionamento_mac', function () {
      $(this).val(Formatter.mac($(this).val()));
    });

    angular.element(document).ready(function () {
      angular.element('#input__helpdesk__search_contrato').focus();
    });

    function activate() {
      // atualizaOnusNaoAutorizadas();
      resetFrmnovoredirecionamento();
      if ($routeParams.username) {
        vm.termos = $routeParams.username;
        getContrato();
      }
    }

    function sort(field) {
      if (field == vm.sortBy) {
        if (vm.sortOrder == '+') {
          vm.sortOrder = '-';
        } else {
          vm.sortOrder = '+';
        }
      }

      vm.sortBy = field;
      //        pageChanged();
    }

    helpdeskSocket.on('cgnat_status_response', function (response) {
      if (response.usuario != vm.contrato.login)
        return;

      angular.copy(response, vm.statusCGNAT);

      switch (vm.statusCGNAT.status) {
        case 'PENDENTE':
          vm.statusCGNAT.statusMessage = 'há uma solicitação em aberto para remoção deste CGNAT. Aguarde...';
          break;
        case 'EXECUTADO':
          vm.statusCGNAT.statusMessage = 'o CGNAT deste cliente foi removido. Motivo: ' + vm.statusCGNAT.motivo;
          break;
        case 'NEGADO':
          vm.statusCGNAT.statusMessage = 'a solicitação de remoção deste CGNAT foi negada. Motivo: ' + vm.statusCGNAT.observacao;
          break;
        case 'RESTAURADO':
          vm.statusCGNAT.statusMessage = 'este CGNAT foi restaurado ao cliente. Motivo: ' + vm.statusCGNAT.observacao;
          break;
      }
    });

    function redoPppoe() {
      AlertService.loading('Reconfigurando a conexão PPPoE...');

      $http({
        url: GENIE_API.url + '/device/redo-pppoe/' + vm.currentTr069Config.identifier,
        method: "POST",
        ignoreLoadingBar: true
      }).then(function (response) {
        var status = response.data && response.data.status ? response.data.status : 'error';
        var message
        if (status === 'success') {
          message = 'A conexão PPPoE foi reconfigurada. A autenticação deve ocorrer dentro dos próximos 10 segundos.'
        }
        else {
          message = 'Ocorreu um erro ao reconfigurar o PPPoE.'
        }
        AlertService.loaded(status, message);
      })
        .catch(function (err) {
          console.log(err);

        });
    }

    function resetOnuAdminPassword() {
      AlertService.loading('Reconfigurando a senha de admin...');

      $http({
        url: GENIE_API.url + '/device/reset-password/' + vm.currentTr069Config.identifier,
        method: "POST",
        ignoreLoadingBar: true
      }).then(function (response) {
        var status = response.data && response.data.status ? response.data.status : 'error';
        var message
        if (status === 'success') {
          message = 'A senha de admin foi reconfigurada na ONU.<br><br>Nome de usuário: <b>' + response.data.newUsername + '</b><br>Senha: <b>' + response.data.newPassword + '</b>';
        }
        else {
          message = 'Ocorreu um erro ao resetar a senha de admin.';
        }
        AlertService.loaded(status, message);
      })
        .catch(function (err) {
          console.log(err);

        });
    }

    function redoPortMappings() {
      AlertService.loading('Reconfigurando redirecionamentos de portas (poderá levar de 20 a 60 segundos)...');

      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/port-mapping/redo',
        method: "POST",
        data: { username: vm.contrato.login },
        ignoreLoadingBar: true
      }).then(function (response) {
        var status = response.data && response.data.status ? response.data.status : 'error';
        var message
        if (status === 'success') {
          message = 'Os redirecionamentos de portas foram reconfigurados com sucesso.'
        }
        else {
          message = 'Ocorreu um erro ao reconfigurar os redirecionamentos.'
        }
        AlertService.loaded(status, message);
      })
        .catch(function (err) {
          console.log(err);

        });
    }

    function prosseguirVerificacoes() {
      vm.exibirVerificacoes = true;
      setTimeout(function () {
        $('html, body').animate({
          scrollTop: $("#div-verificacoes").offset().top - 170
        }, 500);
        resetHighcharts();
      }, 100);
    }

    function getStatusCGNAT() {
      helpdeskSocket.emit('cgnat_status', { 'username': vm.contrato.login });
    }

    function removerCGNAT(username) {
      if (!vm.podeRemoverCgnat)
        return false

      if (vm.statusCGNAT.ativo != 1) {
        $window.alert('Este cliente não possui CGNAT ativo.');
        return;
      }

      else if (vm.statusCGNAT.status == 'PENDENTE') {
        $window.alert('Este cliente já possui uma solicitação de remoção de CGNAT em aberto. Aguarde...');
        return;
      }

      else if (vm.statusCGNAT.status == 'EXECUTADO') {
        $window.alert('O CGNAT deste cliente já foi removido. Será necessário que seja autenticado novamente, para receber um IP válido/público.');
        return;
      }

      var motivo;
      do {
        motivo = $window.prompt('Especifique o motivo da remoção do CGNAT:', "");

        if (motivo === null) {
          return;
        }
        else if (motivo.trim() === '') {
          $window.alert('É necessário especificar um motivo.');
        }
      } while (motivo === null || motivo.trim() === '');

      const data = {
        motivo: motivo,
        usuario: username,
        operador: $rootScope.operador.username,
        compat_tr069: (vm.contrato.tr069 == 1)
      };

      $http({
        url: API_CONFIG.url + '/network/cgnat/solicitar',
        method: "POST",
        data: data,
        ignoreLoadingBar: true
      }).then(function (response) {
        if (response.data.status === 'success') {
          $window.alert('A solicitação foi enviada ao COR.');
        }
        else {
          $window.alert('Houve um erro ao enviar a solicitação.');
        }
      })
        .catch(function (err) {
          console.log(err);

        });
    }

    function alocarRouter(patrimonio, username) {
      if (username.trim() !== '') {
        alert('Este roteador já está alocado para o cliente ' + username);
        return false;
      }

      $http({
        url: API_CONFIG.url + '/helpdesk/atendimento/alocarRouter',
        method: "POST",
        data: { patrimonio: patrimonio, username: vm.contrato.login },
        ignoreLoadingBar: true
      }).then(function (response) {
        if (response.data.status === 'success') {
          alert('O roteador foi alocado.');
          refreshRoutersContainer();
        }
        else {
          alert('Ocorreu um erro ao alocar.');
        }
      },
        function (response) {
        }
      );
    }

    function desalocarRouter(patrimonio) {
      $http({
        url: API_CONFIG.url + '/helpdesk/atendimento/desalocarRouter',
        method: "POST",
        data: { patrimonio: patrimonio },
        ignoreLoadingBar: true
      }).then(function (response) {
        if (response.data.status === 'success') {
          alert('O roteador foi desalocado.');
          refreshRoutersContainer();
        }
        else {
          alert('Ocorreu um erro ao desalocar.');
        }
      },
        function (response) {
        }
      );
    }

    function refreshRoutersContainer() {
      getRoutersAlocados();
      if (vm.routers_pesquisa_done)
        getRoutersPesquisa();
    }

    function getRoutersPesquisa() {
      var filtro = vm.router_pesquisa.filtro;
      var termos = vm.router_pesquisa.termos;

      if (filtro === 'patrimonio') {
        termos = termos.replace(/\D/g, '');
      }

      if (termos.trim() === '') {
        vm.routers_pesquisa = [];
        alert('O termo pesquisado é inválido.');
        return false;
      }

      vm.atualizandoRoutersPesquisa = true;

      $http({
        url: API_CONFIG.url + '/helpdesk/atendimento/routersPesquisa/' + filtro + '/' + termos,
        method: "GET",
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.atualizandoRoutersPesquisa = false;
        vm.routers_pesquisa = response.data;
        vm.routers_pesquisa_done = true;
      },
        function (response) {
          vm.atualizandoRoutersPesquisa = false;
        }
      );
    }

    function getRoutersAlocados() {
      const username = vm.contrato.login;

      vm.atualizandoRoutersCliente = true;

      $http({
        url: API_CONFIG.url + '/helpdesk/atendimento/routersAlocados/' + username,
        method: "GET",
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.atualizandoRoutersCliente = false;
        vm.routers_alocados = response.data;
      },
        function (response) {
          vm.atualizandoRoutersCliente = false;
        }
      );
    }

    function alterarIpoe() {
      vm.ipoeativo = !vm.ipoeativo;
      vm.contrato.processado = '';

      $http({
        url: API_CONFIG.url + '/ftth/ipoe',
        method: "PUT",
        data: {
          'username': vm.contrato.login,
          'operador': $rootScope.operador.username,
          'ativando': vm.ipoeativo
        },
        ignoreLoadingBar: true
      }).then(function (response) {
        //  alert(response.data);
      },
        function (response) {
          alert('erro');
          alert(response.data);
        }
      );
    }

    function mostraSenha() {
      if (vm.senhaVisivel == true) {
        vm.senhaVisivel = false;
      } else {
        vm.senhaVisivel = true;
      }

    }

    function gravasinal() {
      var comentario = prompt("Digite um comentário", "");
      if (comentario != null && comentario !== '') {
        $http({
          url: API_CONFIG.url + '/ftth/sinal',
          method: "POST",
          data: {
            'username': vm.contrato.login,
            'temperature': vm.onu.sinal.temperature,
            'voltage': vm.onu.sinal.voltage,
            'biascurrent': vm.onu.sinal.biascurrent,
            'sendpower': vm.onu.sinal.sendpower,
            'recvpower': vm.onu.sinal.recvpower,
            'oltrecvpower': vm.onu.sinal.oltrecvpower,
            'rttvalue': vm.onu.sinal.rttvalue,
            'comentario': comentario
          },
          ignoreLoadingBar: true
        })
          .then(function (response) {
            getHistSinal();
          },
            function (response) {
              getHistSinal();
            });
      }
    }

    function atualizaLogs() {
      vm.logs = [];
      vm.atualizandolog = true;
      //if (vm.contrato.transmissor !== null && vm.contrato.transmissor.includes("Esp")) {
      //  vm.rsyslog = false;
      //  appSocket.emit('get_log', { 'username': vm.contrato.login, 'juniper': '************' });
      //} else {
      vm.rsyslog = true;
      $http({
        url: API_CONFIG.url + '/concentrador/logs',
        method: "POST",
        data: { 'usuario': vm.contrato.login, 'circuitid': vm.contrato.circuitid },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.logs = response.data;
          vm.atualizandolog = false;
        },
          function (response) {
            vm.atualizandolog = false;
          });
      //}
    }

    appSocket.on('get_log', function (data) {
      vm.logs = data;
      vm.atualizandolog = false;
    });

    function getHistSinal() {
      vm.sinal = [];
      vm.atualizandosinal = true;
      $http({
        url: API_CONFIG.url + '/ftth/sinal?username=' + vm.contrato.login,
        method: "GET",
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.sinal = response.data;
          vm.atualizandosinal = false;
        },
          function (response) {
            vm.atualizandosinal = false;
          });
    }

    function getAutotesteTestes() {
      vm.testes = [];
      vm.atualizandotestes = true;
      $http({
        url: API_CONFIG.url + '/autoteste/testes?username=' + vm.contrato.login,
        method: "GET",
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.testes = response.data;
          vm.atualizandotestes = false;
        },
          function (response) {
            vm.atualizandotestes = false;
          });
    }

    function getFtthLogs() {
      vm.ftthlogs = [];
      vm.atualizandoftthlogs = true;
      $http({
        url: API_CONFIG.url + '/ftth/logs?username=' + vm.contrato.login,
        method: "GET",
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.ftthlogs = response.data;
          vm.atualizandoftthlogs = false;
        },
          function (response) {
            vm.atualizandoftthlogs = false;
          });
    }

    function getAlertas() {
      vm.atualizandoalertas = true;
      $http({
        url: API_CONFIG.url + '/alertas',
        method: "POST",
        data: { username: vm.contrato.login },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.alertas = response.data;
        });
    }

    function getProvisions(serial) {
      $http({
        url: PROVISION_API.url + "/provision/list/" + serial,
        headers: {
          'x-access-token': PROVISION_API.token,
        },
        method: "GET",
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.provisions = response.data;
      });
    };

    function vlanRewrite(serial) {

      if (TimeRestriction.verify('onu.' + serial + '.vlanWrite', 1) == true) {
        $http({
          url: PROVISION_API.url + "/onu/vlan/rewrite",
          headers: {
            'x-access-token': PROVISION_API.token,
          },
          data: { 'serial': vm.contrato.serial },
          method: "POST",
          ignoreLoadingBar: true
        }).then(function (response) {
          alert('Processo iniciado com sucesso.');
          console.log(response.data);
        });
      } else {
        //caso contrário, não permite
        alert('É necessário aguardar 2 minutos para repetir este processo.');
      }
    };

    function getJobs(item) {
      $http({
        url: PROVISION_API.url + "/provision/" + item.id + "/jobs",
        headers: {
          'x-access-token': PROVISION_API.token,
        },
        method: "GET",
        ignoreLoadingBar: true
      }).then(function (response) {
        item.jobs = response.data;
      });
    }

    function provisionClick(item) {
      if (item.hasOwnProperty('expanded')) {
        if (item['expanded'] == 1) {
          item['expanded'] = 0;
        } else {
          item['expanded'] = 1;
        }
      } else {

        item['expanded'] = 1;
      }

      if (item['expanded'] == 1) {
        getJobs(item);
      }

    }

    function getContrato() {
      if (vm.testandoPing !== 0) {
        cancelarPing();
      }

      if (vm.testandoTorch !== 0) {
        cancelarTorch();
      }

      if (vm.testandoTrafego !== 0) {
        cancelarTrafego();
      }
      vm.alertas = [];
      vm.localizado = null;
      if (vm.contrato.hasOwnProperty('serial')) {
        authwsSocket.emit('leave', { serial: vm.contrato.serial });
      }
      vm.contrato = {};
      vm.loadedOnuAlarmsFirstTime = false;
      vm.onuAlarms = [];
      vm.podeRemoverCgnat = false;
      vm.exibirVerificacoes = true;
      vm.alertasGerais = [];
      vm.wan = {};
      vm.onu = {};
      vm.atualizandocontrato = true;
      if (vm.campo == 'login') {
        var data = { 'login': vm.termos }
      } else if (vm.campo == 'mac') {
        var data = { 'mac': vm.termos }
      } else if (vm.campo == 'patrimonio') {
        var data = { 'patrimonio': vm.termos }
      } else {
        var data = { 'serial': vm.termos }
      }

      $http({
        url: API_CONFIG.url + '/ixc/contrato',
        method: "POST",
        data: data,
        ignoreLoadingBar: true
      })
        .then(function (response) {

          vm.contrato = response.data;
          // Só pode remover CGNAT se tiver pacotes que contenham IP FIX ou IP-FIX
          vm.podeRemoverCgnat = false;
          for (var i = 0; i < vm.contrato.pacotes.length; i++) {
            var objeto = vm.contrato.pacotes[i];
            if (objeto.descricao.toLowerCase().indexOf("ip fix") !== -1 || objeto.descricao.toLowerCase().indexOf("ip-fix") !== -1) {
              vm.podeRemoverCgnat = true;
              break;
            }
          }
          //se o cliente foi localizado
          vm.atualizandocontrato = false;
          vm.ipoeativo = vm.contrato.ipoeativo;
          if (vm.contrato.hasOwnProperty('cliente')) {
            // Caso esteja aguardando assinatura (ainda não tenha realizado o aceite eletrônico), exibir apenas o painel com as informações do contrato, e ocultar as outras verificações (equipamentos, autenticação etc):
            vm.exibirVerificacoes = (vm.contrato.status_internet != 'Aguardando Assinatura' && vm.contrato.status_internet != 'Financeiro em Atraso' && !vm.contrato.semiDedicado);

            vm.onu_logs = [];
            if (vm.contrato.hasOwnProperty('serial')) {
              //monitora os logs de provisionamento
              authwsSocket.emit('join', { serial: vm.contrato.serial });
              //Atualiza os dados tecnicos no IXC caso esteja em branco
              // if (vm.contrato.transmissor == '' || vm.contrato.interface == '' || vm.contrato.transmissor == null || vm.contrato.interface == null) {

              // Alterado para atualizar sempre. Pois quando o cliente trocasse de porta, a informação ficaria incorreta
              authwsSocket.emit('dados_tecnicos', { serial: vm.contrato.serial });

              getProvisions(vm.contrato.serial);

              // }
            }

            getStatusCGNAT();

            getDHCPIP();

            vm.onuAlarms = [];
            vm.alertasIxc = [];
            vm.alertasGerais = [];
            reloadAlarms();

            if (vm.getAlertsInterval != 0)
              clearInterval(vm.getAlertsInterval);
            vm.getAlertsInterval = setInterval(function () {
              reloadAlarms();
            }, 10000);

            vm.localizado = true;
            vm.estrutura.cdo = angular.copy(vm.contrato.cdo);
            vm.estrutura.cat = angular.copy(vm.contrato.cat);
            vm.estrutura.porta = angular.copy(vm.contrato.porta);
            getAlertas();
            refreshRoutersContainer();
            getHistSinal();
            getFtthLogs();
            getOnuInfo();
            getAutotesteTestes();
            atualizaLogs(vm.contrato.login);
            resetHighcharts();
            getWanStatus(vm.contrato.login);
          } else {
            vm.localizado = false;
          }


        });
    }

    function reloadAlarms() {
      getOnuAlarms();
      getAlertasGerais(vm.contrato.login);
      getAlertasIxc(vm.contrato.login);
      window.setTimeout(function () {
        adjustAlertsHeights();
      }, 500);
      window.setTimeout(function () {
        adjustAlertsHeights();
      }, 1500);
    }

    function getAlertasIxc(login) {
      $http({
        url: API_CONFIG.url + '/ixc/alertas/' + login,
        method: "GET",
        ignoreLoadingBar: true
      }).then(function (response) {
        var oldAlerts = [];
        angular.copy(vm.alertasIxc, oldAlerts);
        // Só atualiza se os dados forem diferentes da consulta anterior
        if (response.data && response.data.status == 'success' && JSON.stringify(oldAlerts) !== JSON.stringify(response.data.alertas)) {
          vm.alertasIxc = response.data.alertas;
        }
        else if (!response.data || response.data.status != 'success') {
          vm.alertasIxc = [];
        }
      },
        function (response) {
        }
      );
    }

    function adjustAlertsHeights() {
      var biggest = 0;
      $('.equal-height-alert').each(function (index) {
        var height = $(this).height();
        if (height > biggest)
          biggest = height;
      });
      $('.equal-height-alert').height(biggest);
    }

    function onuReauth() {
      var motivo;
      do {
        motivo = $window.prompt('Especifique o motivo do reprovisionamento da ONU:', "");

        if (motivo === null) {
          return;
        }
        else if (motivo.trim() === '') {
          $window.alert('É necessário especificar um motivo.');
        }
        else if (motivo.trim().toLowerCase() === 'teste') {
          motivo = $window.prompt('Certo. É um teste. Especifique o motivo do teste:', "");
        }
      } while (motivo === null || motivo.trim() === '' || motivo.trim().toLowerCase() === 'teste');

      $http({
        url: API_CONFIG.url + '/ftth/onuReauth',
        method: "PUT",
        data: {
          'operador': $rootScope.operador.username,
          'serial': vm.contrato.serial,
          'motivo': motivo
        },
        ignoreLoadingBar: true
      }).then(function (response) {
        if (response.data.status === true) {
          $window.alert('A ONU será reprovisionada em cerca de dois minutos.');
        } else {
          $window.alert('A ONU já está sendo reprovisionada. Aguarde...');
        }
        //remove das olts zte
        $http({
          url: ZTE_API.url + '/deauth',
          data: { 'serial': vm.contrato.serial },
          headers: {
            'x-access-token': ZTE_API.token
          },
          method: "POST",
          ignoreLoadingBar: true
        }).then(function (response) {
          if (response.data == 'Deauth OK') {
            toaster.pop('success', "ONU Desautorizada", "A ONU foi desautorizada com sucesso!");
          }
        });
      },
        function (response) {
          $window.alert('Ocorreu um erro ao reprovisionar a ONU.');
        }
      );
    }

    function getOnuInfo() {
      vm.onu = {};
      vm.currentTr069Config = { totalAssociatedDevices: 0 };
      vm.tr069Config = {
        wifi: {
          1: {
            ssid: '',
            passphrase: ''
          },
          5: {
            ssid: '',
            passphrase: ''
          }
        },
        portMappings: [],
        operationMode: ''
      };

      if (vm.contrato.tr069 == 1)
        getTr069Config(vm.contrato.login);

      atualizaSinalOnu(vm.contrato.serial);
      //atualizaStatusOnu(vm.contrato.serial);
      atualizaLansOnu(vm.contrato.serial);



    }

    function getOnuConfig(serial) {
      vm.onu.config = {};
      vm.atualizandoconfig = true;
      $http({
        url: ZTE_API.url + '/showconfigonu',
        data: { 'serial': serial },
        headers: {
          'x-access-token': ZTE_API.token
        },
        method: "POST",
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.onu.config = response.data;


        if (vm.onu.config['pon_onu']) {
          var vlans = {};
          var pon_onu = vm.onu.config['pon_onu'];
          pon_onu.forEach(function (value, index, array) {

            var test = value.match(/service (.*) gemport (.*) vlan (.*)/) || [""];
            if (test.length >= 3) {
              vlans[test[2]] = test[3];
            }
          });

          //vm.onu.lans[0]['PVID'] = vlans[1] + ', ' + vlans[3]
          //vm.onu.lans[1]['PVID'] = vlans[2] + ', ' + vlans[4]
          vlans = Object.values(vlans);
          vm.onu.lans[0]['PVID'] = vlans.join(", ");
        };

        vm.atualizandoconfig = false;
      });

    }


    appSocket.on('checkAuth', function (response) {

      vm.atualizandowan = false;
      vm.wan = response;
      if ((!vm.wan.hasOwnProperty('router')) || (vm.wan.ipv4 == null)) {
        vm.wan.status = 'Desconectado';
      } else {
        vm.wan.status = 'Conectado';
        if (vm.wan.router == 'mk') {
          vm.wan.router_desc = 'Concentrador Mikrotik'
        }

        if (vm.wan.router == 'juniper') {
          vm.wan.router_desc = 'BNG Juniper'
        }
        getIpv6List();
      }
    });

    function getAlertasGerais(username) {
      $http({
        url: API_CONFIG.url + '/helpdesk/atendimento/alertasGerais/' + username,
        method: "GET",
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var oldAlertas = [];
          angular.copy(vm.alertasGerais, oldAlertas);

          if (response.data && response.data.status === 'success' && JSON.stringify(oldAlertas) !== JSON.stringify(response.data.alertas)) {
            vm.alertasGerais = response.data.alertas;
          }
          else if (!response.data || response.data.status !== 'success') {
            vm.alertasGerais = [];
          }
        },
          function (response) {
          });
    }

    function getWanStatus(usuario) {
      vm.atualizandowan = true;
      vm.wan = {};
      appSocket.emit('check_auth', { 'subscriber': vm.contrato.login, 'username': $rootScope.operador.username, 'circuitid': vm.contrato.circuitid });
    }

    function getBlacklist(serial) {
      vm.atualizandoblacklist = true;
      $http({
        url: API_CONFIG.nocws + '/checkblacklist',
        method: "POST",
        data: { "serial": serial },
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.atualizandoblacklist = false;
        if (response.data.LISTED == true) {
          vm.onu.blacklist = 'Sim';
        } else {
          vm.onu.blacklist = 'Não';
        }
      })
        .catch(function (err) {
          console.log(err);
        });
    };

    function removeBlacklist(serial) {
      vm.atualizandoblacklist = true;
      $http({
        url: API_CONFIG.nocws + '/removeblacklist',
        method: "POST",
        data: { "serial": serial },
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.getBlacklist(serial);
        vm.atualizandoblacklist = false;
      })
        .catch(function (err) {
          console.log(err);
        });
    };

    function getIpv6List() {
      vm.atualizandoipv6list = true;
      vm.ipv6list = [];
      appSocket.emit('ipv6list', { 'user': vm.contrato.login, 'username': $rootScope.operador.username });
    }

    appSocket.on('ipv6list', function (response) {

      vm.atualizandoipv6list = false;
      var list = response;

      for (var i = 0; i < list.length; i++) {
        var item = {
          'index': i + 1,
          'last_activity': list[i].last_activity,
          'source_address': list[i].source_address,
          'min_rtt': 0,
          'avg_rtt': 0,
          'max_rtt': 0,
          'sum_rtt': 0,
          'sent': 0,
          'received': 0,
          'lost': 0,
          'lost_perc': 0,
          'chart': null,
          'testing': 0,
          'packets': []
        }
        vm.ipv6list.push(item);
      }

    });

    function getPlano(url) {
      var Plan = $resource(url, null,
        {
          get: {
            headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
            method: 'GET',
            ignoreLoadingBar: true
          },
        });

      Plan.get().$promise.then(function (plan) {
        vm.queue = plan;
        if (vm.queue.hasOwnProperty('download')) {
          vm.graficoTrafego.yAxis[0].options.minRange = (vm.queue.download / 100000) + (10 * ((vm.queue.download / 100000) / 100));
          vm.graficoTrafego.yAxis[0].options.plotLines = [
            {
              value: vm.queue.download / 100000,
              color: 'blue',
              dashStyle: 'shortdash',
              width: 2,
              label: {
                text: 'Queue Download ' + (vm.queue.download / 100000) + 'MB'
              }
            },
            {
              value: vm.queue.upload / 100000,
              color: 'blue',
              dashStyle: 'shortdash',
              width: 2,
              label: {
                text: 'Queue Upload ' + (vm.queue.upload / 100000) + 'MB'
              }
            },
          ];
        };
      }, function (error) {
        //usuario nao conectado
        if (error.status == 404) {

        }
      });
    }

    function limpa() {
      vm.termos = '';
      vm.campo = 'usuario';
      vm.localizado = null;
      vm.logs = [];
      vm.sinal = [];
      vm.ftthlogs = [];
      vm.contrato = {};
      if (vm.testandoPing !== 0) {
        cancelarPing();
      }

      if (vm.testandoTorch !== 0) {
        cancelarTorch();
      }

      if (vm.testandoTrafego !== 0) {
        cancelarTrafego();
      }
      vm.senhaVisivel = false;
    }

    function consultaAnm(serial, servico) {
      vm.atualizandoonu = true;
      vm.anm = '';
      if (serial !== '') {
        $http({
          url: API_CONFIG.url + '/anm',
          method: "POST",
          data: {
            'service': servico,
            'onu_id': serial
          },
          ignoreLoadingBar: true
        })
          .then(function (response) {
            vm.anm = response.data;
            vm.atualizandoonu = false;
          },
            function (response) {
            });
      }
    }

    function atualizaPing(usuario) {
      vm.testandoPing = 1;
      vm.graficoPing.addSeries({
        name: 'Ping',
        id: 'ping',
        type: 'area',
        threshold: null,
        color: '#64b448'
      });

      appSocket.emit('start_ping', { 'host': vm.wan.ipv4, 'username': $rootScope.operador.username, 'juniper': vm.wan.router_ip });
    }

    function atualizaPingV6(item) {
      item.testing = 1;
      getChartV6(item.index);
      pingwsSocket.emit('start_ping_v6', { 'ip': item.source_address, 'username': $rootScope.operador.username });
    }

    pingwsSocket.on('pingTestV6', function (data) {
      var item = $filter('filter')(vm.ipv6list, { source_address: data.ip })[0];
      item.sent++;
      if (data.rtt !== 'lost') {
        item.received++;
      } else {
        item.lost++;
      }

      item.lost_perc = Number((item.lost / item.sent) * 100).toFixed(2);

      var pacote = [];

      if (data.rtt === 'lost') {
        pacote = [data.currentTimeUnix * 1000, 'Perdido'];
      } else {
        pacote = [data.currentTimeUnix * 1000, data.rtt];
        if ((data.rtt < item.min_rtt) || (item.min_rtt == 0)) {
          item.min_rtt = data.rtt;
        }

        if (data.rtt > item.max_rtt) {
          item.max_rtt = data.rtt;
        }

        item.sum_rtt = item.sum_rtt + data.rtt;
        item.avg_rtt = Number((item.sum_rtt / item.sent).toFixed(2));
      }

      var shift = item.chart.series[0].data.length > 600;

      if (data.rtt !== 'lost') {
        item.chart.series[0].addPoint(pacote, true, shift, false);
      } else {
        item.chart.series[0].addPoint([pacote[0], null], true, shift, false);
      }

      item.packets = item.packets.concat({ 'rtt': data.rtt, 'currentTimeUnix': data.currentTimeUnix });

      //Rola o quadro de resultados para baixo

      jQuery(function () {
        var pre = jQuery("#pingresultsv6_" + item.index);
        pre.scrollTop(pre.prop("scrollHeight"));
      });

    });



    appSocket.on('pingTest', function (data) {
      if (vm.testandoPing == 1) {
        vm.ping.packetsSent++;
        var pacote = [];

        if (data.rtt === 'lost') {
          pacote = [data.currentTimeUnix * 1000, 'Perdido'];
        } else {
          pacote = [data.currentTimeUnix * 1000, data.rtt];
          vm.ping.packetsReceived++;
          vm.ping.sum = vm.ping.sum + data.rtt;
          vm.ping.avg = Number((vm.ping.sum / vm.ping.packetsSent).toFixed(2));
          if ((data.rtt < vm.ping.min) || (vm.ping.min == 0)) {
            vm.ping.min = data.rtt;
          }

          if (data.rtt > vm.ping.max) {
            vm.ping.max = data.rtt;
          }
        }

        vm.ping.packetsLost = vm.ping.packetsSent - vm.ping.packetsReceived;
        vm.ping.packetsLostPerc = Number((vm.ping.packetsLost / vm.ping.packetsSent) * 100).toFixed(2);

        var shift = vm.graficoPing.series[0].data.length > 600;

        if (data.rtt !== 'lost') {
          vm.graficoPing.series[0].addPoint(pacote, true, shift, false);
        } else {
          vm.graficoPing.series[0].addPoint([pacote[0], null], true, shift, false);
        }

        vm.ping.packets = vm.ping.packets.concat(data);

        //Rola o quadro de resultados para baixo
        jQuery(function () {
          var pre = jQuery("#pingresults");
          pre.scrollTop(pre.prop("scrollHeight"));
        });
      }

    });

    function pausarPing() {
      vm.testandoPing = 2;
      appSocket.emit('stop_ping', {});
    }

    function pausarPingV6(item) {
      item.testing = 2;
      pingwsSocket.emit('stop_ping_v6', { 'ip': item.source_address });
    }

    function cancelarPing() {
      vm.testandoPing = 0;
      appSocket.emit('stop_ping', { 'username': $rootScope.operador.username });

      vm.ping = {
        sourceip: '',
        targetip: '',
        packetsize: 0,
        packetsSent: 0,
        packetsReceived: 0,
        packetsLost: 0,
        packetsLostPerc: 0,
        min: 0,
        max: 0,
        avg: 0,
        sum: 0,
        packets: []
      };
      vm.graficoPing.series[0].remove()
    }

    function cancelarPingV6(item) {
      pingwsSocket.emit('stop_ping_v6', { 'ip': item.source_address, 'username': $rootScope.operador.username });
      item.testing = 0;
      item.min_rtt = 0;
      item.avg_rtt = 0;
      item.max_rtt = 0;
      item.sum_rtt = 0;
      item.sent = 0;
      item.received = 0;
      item.lost = 0;
      item.lost_perc = 0;
      item.packets = [];
      item.chart = null;
    }

    authMonitorSocket.on('unauthorized', function (data) {
      vm.onusNaoAutorizadas = data;
    });


    authwsSocket.on('authlogs', function (response) {
      vm.onu_logs = vm.onu_logs.concat(response);

      //Rola o quadro de resultados para baixo
      jQuery(function () {
        var pre = jQuery("#onulogs");

        pre.scrollTop(pre.prop("scrollHeight"));
      });

    });

    authwsSocket.on('dados_tecnicos', function (response) {
      if (response.hasOwnProperty('transmissor')) {
        vm.contrato.transmissor = response.transmissor;
      };

      if (response.hasOwnProperty('interface')) {
        vm.contrato.interface = response.interface;
      }

      console.log('response:', response)

    });

    function atualizaSinalOnu(serial) {
      vm.atualizandosinalonu = true;
      vm.onu.sinal = {};
      if (vm.contrato.hasOwnProperty('olt_modelo')) {
        if (vm.contrato.olt_modelo == 'FBT') {
          nocwsSocket.emit('gponsignal', { 'serial': serial });
          atualizaStatusOnu(vm.contrato.serial);
          getBlacklist(vm.contrato.serial);
        } else {

          $http({
            url: ZTE_API.url + '/signal',
            data: { 'serial': serial },
            headers: {
              'x-access-token': ZTE_API.token
            },
            method: "POST",
            ignoreLoadingBar: true
          }).then(function (response) {

            vm.onu.sinal['sendpower'] = response.data["up"]["onu"];
            vm.onu.sinal['oltrecvpower'] = response.data["up"]["olt"];
            vm.onu.sinal['recvpower'] = response.data["down"]["onu"];

            $http({
              url: ZTE_API.url + '/showgpononu',
              data: { 'serial': serial },
              headers: {
                'x-access-token': ZTE_API.token
              },
              method: "POST",
              ignoreLoadingBar: true
            }).then(function (response) {

              vm.onu.sinal['temperature'] = response.data["temperature"];
              vm.onu.sinal['voltage'] = response.data["voltage"];
              vm.onu.sinal['biascurrent'] = response.data["bias"];
              vm.atualizandosinalonu = false;
            });
          });



          atualizaStatusOnu(vm.contrato.serial);


        }
      } else {
        nocwsSocket.emit('gponsignal', { 'serial': serial });
        getBlacklist(vm.contrato.serial);
      };
    };

    /*
    nocwsSocket.on('onusignal', function(response){
      vm.atualizandosinalonu = false;
      vm.onu.sinal = response;
    });
    */
    //"USERNAME","SERVICE","SERIAL","TYPE","TEMPERATURE","VOLTAGE","BIAS CURRENT","SEND POWER","RECV POWER","OLT RECV POWER","ONU RTT VALUE","LANPORTS"
    nocwsSocket.on('gponsignal', function (response) {
      vm.atualizandosinalonu = false;
      vm.onu.sinal = {
        temperature: response["TEMPERATURE"],
        voltage: response["VOLTAGE"],
        biascurrent: response["BIAS CURRENT"],
        sendpower: response["SEND POWER"],
        recvpower: response["RECV POWER"],
        oltrecvpower: response["OLT RECV POWER"],
        rttvalue: response["ONU RTT VALUE"]
      }
    });

    function atualizaStatusOnu(serial) {
      console.log('atualizando status');
      vm.atualizandostatusonu = true;
      vm.onu.status = {};

      if (vm.contrato.hasOwnProperty('olt_modelo')) {
        if (vm.contrato.olt_modelo == 'FBT') {
          nocwsSocket.emit('onustatus', { 'serial': serial });
        } else {
          $http({
            url: ZTE_API.url + '/showdetailonu',
            data: { 'serial': serial },
            headers: {
              'x-access-token': ZTE_API.token
            },
            method: "POST",
            ignoreLoadingBar: true
          }).then(function (response) {
            if (response.data['Phase state'] == 'working') {
              vm.onu.status["OperState"] = 'UP'
            } else {
              vm.onu.status = 'DOWN';
            }

            vm.onu.status["uptime"] = response.data["Online Duration"]
            vm.onu.sinal["rttvalue"] = response.data["ONU Distance"]

            vm.atualizandostatusonu = false;
          });
        }
      } else {
        nocwsSocket.emit('onustatus', { 'serial': serial });
      }
    }

    nocwsSocket.on('onustatus', function (response) {
      vm.atualizandostatusonu = false;
      vm.onu.status = response;
    });


    function atualizaVlansOnu(serial) {
      vm.atualizandovlansonu = true;
      nocwsSocket.emit('onuvlans', { 'serial': serial });
    }

    nocwsSocket.on('onuvlans', function (response) {
      vm.atualizandovlansonu = false;
      vm.onu.vlans = response;
    });

    function atualizaLansOnu(serial) {
      vm.atualizandolansonu = true;
      if (vm.contrato.hasOwnProperty('olt_modelo')) {
        if (vm.contrato.olt_modelo == 'FBT') {
          nocwsSocket.emit('onulans', { 'serial': serial });
        } else {
          $http({
            url: ZTE_API.url + '/showethonu',
            data: { 'serial': serial },
            headers: {
              'x-access-token': ZTE_API.token
            },
            method: "POST",
            ignoreLoadingBar: true
          }).then(function (response) {
            vm.atualizandolansonu = false;
            vm.onu.lans = response.data;
            if (vm.contrato.olt_modelo == 'ZTE') {
              getOnuConfig(vm.contrato.serial);
            }
          });
        }
      } else {
        nocwsSocket.emit('onulans', { 'serial': serial });
      }
    }

    nocwsSocket.on('onulans', function (response) {
      vm.atualizandolansonu = false;
      vm.onu.lans = response;
    });

    appSocket.on('trafficTest', function (response) {
      if (vm.testandoTrafego == 1) {

        vm.trafego.timestampUnix = response.timestampUnix;
        vm.trafego.inputBitsPerSecond = response.inputBitsPerSecond;
        vm.trafego.outputBitsPerSecond = response.outputBitsPerSecond;
        vm.trafego.inputPacketsPerSecond = response.inputPacketsPerSecond;
        vm.trafego.outputPacketsPerSecond = response.outputPacketsPerSecond;
        vm.trafego.inputBytes = response.inputBytes * 8;
        vm.trafego.outputBytes = response.outputBytes * 8;


        vm.trafego.samples++;

        var shift = vm.graficoTrafego.series[0].data.length > 600;
        if ((vm.trafego.inputBitsPerSecond < vm.trafego.minInput) || (vm.trafego.samples == 1)) {
          vm.trafego.minInput = vm.trafego.inputBitsPerSecond;
        }

        if (vm.trafego.inputBitsPerSecond > vm.trafego.maxInput) {
          vm.trafego.maxInput = vm.trafego.inputBitsPerSecond;
        }

        vm.trafego.sumInput = vm.trafego.sumInput + vm.trafego.inputBitsPerSecond;
        vm.trafego.avgInput = Number((vm.trafego.sumInput / vm.trafego.samples).toFixed(3));

        if ((vm.trafego.outputBitsPerSecond < vm.trafego.minOutput) || (vm.trafego.samples == 1)) {
          vm.trafego.minOutput = vm.trafego.outputBitsPerSecond;
        }

        if (vm.trafego.outputBitsPerSecond > vm.trafego.maxOutput) {
          vm.trafego.maxOutput = vm.trafego.outputBitsPerSecond;
        }

        vm.trafego.sumOutput = vm.trafego.sumOutput + vm.trafego.outputBitsPerSecond;
        vm.trafego.avgOutput = Number((vm.trafego.sumOutput / vm.trafego.samples).toFixed(3));

        vm.graficoTrafego.series[0].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.outputBitsPerSecond / 1000000], true, shift, false);
        vm.graficoTrafego.series[1].addPoint([vm.trafego.timestampUnix * 1000, vm.trafego.inputBitsPerSecond / 1000000], true, shift, false);

      }
    });

    function atualizaTrafego(usuario, circuitid) {
      if (vm.testandoTrafego == 0) {
        vm.testandoTrafego = 1;
        vm.graficoTrafego.addSeries({
          name: 'Download',
          id: 'download',
          type: 'area',
          threshold: null,
          color: '#64b448'
        });

        vm.graficoTrafego.addSeries({
          name: 'Upload',
          id: 'upload',
          type: 'area',
          threshold: null,
          color: '#FF0000'
        });
      } else {
        vm.testandoTrafego = 1;
      }

      appSocket.emit('start_traffic', { 'subscriber': vm.wan.username_sensitive, 'auth_type': vm.wan.auth_type.toLowerCase(), 'circuitid': vm.contrato.circuitid, 'username': $rootScope.operador.username, 'juniper': vm.wan.router_ip });

    }

    function pausarTrafego() {
      vm.testandoTrafego = 2;
      appSocket.emit('stop_traffic', {});
    }

    function cancelarTrafego() {
      vm.testandoTrafego = 0;
      appSocket.emit('stop_traffic', { 'username': $rootScope.operador.username });
      vm.trafego = {
        timestampUnix: 0,
        name: '',
        remoteMac: '',
        mtu: 0,
        inputBytes: 0,
        inputBitsPerSecond: 0,
        outputBytes: 0,
        outputBitsPerSecond: 0,
        inputPackets: 0,
        inputPacketsPerSecond: 0,
        outputPackets: 0,
        outputPacketsPerSecond: 0,
        minInput: 0,
        maxInput: 0,
        avgInput: 0,
        sumInput: 0,
        minOutput: 0,
        maxOutput: 0,
        avgOutput: 0,
        sumOutput: 0,
        samples: 0
      };

      vm.graficoTrafego.get('download').remove();
      vm.graficoTrafego.get('upload').remove();
    }

    function atualizaTorch() {
      if (vm.testandoTorch == 0) {
        AtendimentoService.torchpost(
          {
            usuario: vm.wan.username_sensitive,
            circuitid: vm.contrato.circuitid,
            ipoeativo: vm.contrato.ipoeativo,
            timeout: vm.torch.timeout,
            ipv4: vm.wan.ipv4,
            ipv6: vm.wan.ipv6,
            mac: vm.wan.mac,
            router: vm.wan.router_ip
          }, function (response) {
            vm.testandoTorch = 1;
            APICalls.torchIntervalPromise = $interval(function () {
              AtendimentoService.torchget(
                {
                  usuario: vm.wan.username_sensitive,
                  circuitid: vm.contrato.circuitid,
                  ipoeativo: vm.contrato.ipoeativo,
                  display: 'connections',
                  timeout: vm.torch.timeout,
                  ipv4: vm.wan.ipv4,
                  ipv6: vm.wan.ipv6,
                  mac: vm.wan.mac,
                  router: vm.wan.router_ip,
                  sort: vm.sortOrder + vm.sortBy
                }, function (response) {
                  vm.torch.connections = response.connections;
                });
            }, 1000);
          });
      }
    }

    function pausarTorch() {
      $interval.cancel(APICalls.torchIntervalPromise);
      APICalls.torchIntervalPromise = null;
      vm.testandoTorch = 2;
    }

    function cancelarTorch() {
      vm.testandoTorch = 0;
      $interval.cancel(APICalls.torchIntervalPromise);
      APICalls.torchIntervalPromise = null;
      vm.torch.connections = [];
      AtendimentoService.torchcancel(
        {
          usuario: vm.contrato.login,
          circuitid: vm.contrato.circuitid,
          ipoeativo: vm.contrato.ipoeativo,
          timeout: vm.torch.timeout,
          ipv4: vm.wan.ipv4,
          ipv6: vm.wan.ipv6,
          mac: vm.wan.mac,
          router: vm.wan.router_ip
        }, function (response) {
        });

    }

    function getChartV6(index) {
      var item = $filter('filter')(vm.ipv6list, { index: index })[0];
      if (item.chart == null) {
        var chart = Highcharts.stockChart('graficoPingV6_' + index, {
          chart: {
            zoomType: 'x',
            height: 300
          },

          navigator: {
            height: 15
          },

          time: {
            useUTC: false
          },

          rangeSelector: {
            buttons: [{
              count: 1,
              type: 'minute',
              text: '1M'
            }, {
              count: 5,
              type: 'minute',
              text: '5M'
            },
            {
              count: 10,
              type: 'minute',
              text: '10M'
            },
            {
              count: 30,
              type: 'minute',
              text: '30M'
            },
            {
              count: 1,
              type: 'hour',
              text: '1H'
            },
            {
              type: 'all',
              text: 'Todos'
            }],
            inputEnabled: false,
            selected: 5
          },

          title: {
            text: 'PING ' + item.source_address + ' (' + vm.contrato.login + ')'
          },

          credits: {
            enabled: false
          },
          xAxis: {
            minRange: 1000,
            tickInterval: 60000
          },
          yAxis: {
            floor: 0,
            opposite: false,
            labels: {
              align: 'right',
              x: -5
            },
            title: {
              text: 'RTT (ms)'
            },
          },
          exporting: {
            enabled: true
          },

          lang: {
            loading: 'Aguarde...',
            months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
            weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
            shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
            exportButtonTitle: "Exportar",
            printChart: "Imprimir",
            viewFullscreen: "Tela cheia",
            rangeSelectorFrom: "De",
            rangeSelectorTo: "Até",
            rangeSelectorZoom: "Periodo",
            downloadPNG: 'Baixar imagem PNG',
            downloadJPEG: 'Baixar imagem JPEG',
            downloadPDF: 'Baixar documento PDF',
            downloadSVG: 'Baixar imagem SVG',
            resetZoom: "Resetar",
            resetZoomTitle: "Resetar",
            thousandsSep: ".",
            decimalPoint: ','
          }


        });

        item.chart = chart;

        item.chart.addSeries({
          name: 'Ping',
          id: 'ping',
          type: 'area',
          threshold: null,
          color: '#64b448'
        });

      }
    }

    function gerenciarEmails() {
      openInNewTab('/emails?login=' + vm.contrato.login);
    }

    function openInNewTab(url) {
      var win = window.open(url, '_blank');
      win.focus();
    }

    function getTr069Config() {
      vm.atualizandoTr069 = true;

      vm.onuNotFound = false;
      vm.portalAPIError = false;

      vm.operationModeLogs = [];

      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/device-config/' + vm.contrato.login + '/' + vm.contrato.mac,
        method: "GET",
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.atualizandoTr069 = false;
          if (response.data.status == 'success') {

            angular.copy(response.data.config, vm.tr069Config);
            angular.copy(response.data.config, vm.currentTr069Config);

            if (vm.tr069Config.wifi[1].autoChannel) {
              vm.tr069Config.wifi[1].selectedChannel = 'auto';
              vm.currentTr069Config.wifi[1].selectedChannel = 'auto';
            }
            else {
              vm.tr069Config.wifi[1].selectedChannel = vm.tr069Config.wifi[1].channel.toString();
              vm.currentTr069Config.wifi[1].selectedChannel = vm.tr069Config.wifi[1].channel.toString();
            }

            if (vm.contrato['5G'] == 1) {
              if (vm.tr069Config.wifi[5].autoChannel) {
                vm.tr069Config.wifi[5].selectedChannel = 'auto';
                vm.currentTr069Config.wifi[5].selectedChannel = 'auto';
              }
              else {
                vm.tr069Config.wifi[5].selectedChannel = vm.tr069Config.wifi[5].channel.toString();
                vm.currentTr069Config.wifi[5].selectedChannel = vm.tr069Config.wifi[5].channel.toString();
              }
            }

            if (vm.currentTr069Config.associatedDevices.status && vm.currentTr069Config.associatedDevices.status == 'error') {
              vm.currentTr069Config.associatedDevices = {};
              vm.currentTr069Config.totalAssociatedDevices = 0;
            }
            else {
              vm.currentTr069Config.totalAssociatedDevices = Object.keys(vm.currentTr069Config.associatedDevices).length
            }

            updateLanDevicesUptime(false);

            // Preenchendo o prefixo do IP interno, para o modal de adicionar redirecionamento de porta
            var splittedIp = vm.currentTr069Config.lanIpRange.split('.')
            splittedIp.pop()
            vm.currentTr069Config.lanIpRangeStr = splittedIp.join('.') + '.';

            if (vm.contrato.mesh)
              getMeshStatus();
          }
          else if (response.data.status == 'NOT_FOUND') {
            vm.onuNotFound = true;
          }
          else {
            vm.portalAPIError = true;
          }

          getOperationModeLog();
        },
          function (response) {
            vm.portalAPIError = true;
          });
    }

    function getMeshStatus() {
      if (!vm.currentTr069Config.hasOwnProperty('identifier') || vm.currentTr069Config.identifier == 'undefined' || typeof vm.currentTr069Config.identifier == 'undefined') {
        return false;
      }

      vm.meshStatus.status = 'loading';

      $http({
        url: GENIE_API.url + '/mesh/status/' + vm.currentTr069Config.identifier,
        method: 'GET',
        ignoreLoadingBar: true
      })
        .then(function (response) {
          if (response && response.data && response.data.status) {
            angular.copy(response.data, vm.meshStatus);

            if (response.data.status !== 'configured'
              || response.data.totalConfiguredRouters < response.data.totalMeshRouters) {
              window.setTimeout(function () {
                if (vm.contrato.mesh)
                  getMeshStatus();
              }, 10000);
            }
          }
        },
          function (response) {
          });
    }

    function startMeshConfig() {
      vm.meshStatus.status = 'loading';

      $http({
        url: GENIE_API.url + '/mesh/enable',
        method: 'POST',
        data: {
          login: vm.contrato.login,
          identifier: vm.currentTr069Config.identifier
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          if (response && response.data && response.data.status && response.data.status == 'success') {
            alert('O Mesh foi ativado na ONU do cliente ' + vm.contrato.login + '.');
          }
          else {
            alert('Ocorreu um erro ao ativar o Mesh na ONU do cliente' + vm.contrato.login + '. Caso o erro persista, contacte o COR.');
          }

          getMeshStatus();
        },
          function (response) {
          });

      alert('O Mesh será ativado na ONU do cliente.');
    }

    function cancelMeshConfig() {
      vm.meshStatus.status = 'loading';

      $http({
        url: GENIE_API.url + '/mesh/cancel/' + vm.currentTr069Config.identifier,
        method: 'POST',
        ignoreLoadingBar: true
      })
        .then(function (response) {
          if (response && response.data && response.data.status && response.data.status == 'success') {
            alert('A configuração do mesh foi cancelada com sucesso.');
          }
          else {
            alert('Ocorreu um erro ao tentar cancelar a configuração. Caso o erro persista, contacte o COR.');
          }

          getMeshStatus();
        },
          function (response) {
          });
    }

    function getAssocDevices() {
      if (vm.atualizandoAssocDevices)
        return;

      vm.atualizandoAssocDevices = true;

      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/associated-devices/' + vm.currentTr069Config.identifier,
        method: 'GET',
        ignoreLoadingBar: true
      })
        .then(function (response) {
          vm.atualizandoAssocDevices = false;

          if (response.data.status && response.data.status == 'error') {
            vm.currentTr069Config.associatedDevices = {};
            vm.currentTr069Config.totalAssociatedDevices = 0;
          }
          else {
            vm.currentTr069Config.associatedDevices = response.data.associatedDevices;

            vm.currentTr069Config.totalAssociatedDevices = Object.keys(vm.currentTr069Config.associatedDevices).length

            updateLanDevicesUptime(false);
          }
        },
          function (response) {
            vm.atualizandoAssocDevices = false;
          });
    }

    function updateLanDevicesUptime(increaseTime) {
      if (typeof increaseTime === 'undefined')
        increaseTime = true;

      if (!vm.currentTr069Config.associatedDevices)
        return

      for (var index in vm.currentTr069Config.associatedDevices) {
        var device = vm.currentTr069Config.associatedDevices[index];
        if (device.uptime > 0)
          vm.currentTr069Config.associatedDevices[index].uptime = device.uptime + 10;

        vm.currentTr069Config.associatedDevices[index].uptimeText = secondsToText(vm.currentTr069Config.associatedDevices[index].uptime);
      }
    }

    function fillDeviceDetails(source) {
      var deviceID;
      if (source == 'frmnovoredirecionamento')
        deviceID = vm.selectedFrmNovoRedirecionamentoDevice;
      else if (source == 'frmredirecionamento')
        deviceID = vm.selectedFrmRedirecionamentoDevice;

      const device = vm.currentTr069Config.associatedDevices[deviceID];
      const deviceIPSplitted = device.ip.split('.');
      const deviceIP = deviceIPSplitted[deviceIPSplitted.length - 1];

      if (source === 'frmnovoredirecionamento') {
        vm.novoRedirecionamento.internalIP = deviceIP;
        vm.novoRedirecionamento.mac = device.mac;
      }
      else if (source === 'frmredirecionamento') {
        vm.redirecionamentoSelecionado.internalIP = deviceIP;
        vm.redirecionamentoSelecionado.mac = device.mac;
      }
    }

    function setWifiEnabled(ssidIndex, boolean) {
      if (vm.atualizandoTr069 || vm.tr069Config.operationMode == 'bridge')
        return false;

      vm.tr069Config.wifi[ssidIndex].enabled = boolean;
    }

    function getOperationModeLog() {
      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/operation-mode-log/' + vm.contrato.login,
        method: 'GET',
        ignoreLoadingBar: true
      })
        .then(function (response) {
          if (response.data.status === 'success') {
            vm.operationModeLogs = response.data.logs;
          }
        }, function (response) {

        });
    }

    function setOperationMode(mode) {
      const identifier = vm.currentTr069Config.identifier;

      vm.atualizandoOperationMode = mode;
      AlertService.alert({
        html: 'O modo de operação está sendo alterado para "<b>' + mode + '</b>". Este procedimento deve demorar de 2 a 4 minutos.'
      });
      // AlertService.loading('Alterando modo de operação para "' + mode + '"');

      vm.operationModeLogs.unshift({
        timestamp: new Date(),
        noc_operator: $rootScope.operador.username,
        mode: mode,
        status: 'executing'
      });

      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/operation-mode/' + identifier + '/' + vm.contrato.login,
        method: 'POST',
        data: {
          mode: mode,
          waitForBoot: true,
          noc_operator: $rootScope.operador.username
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var status = response.data.status;
          vm.atualizandoOperationMode = false;
          getTr069Config();
          // AlertService.loaded(status, 'O modo de operação foi alterado com sucesso!');
          if (status === 'success') {
            AlertService.success('O modo de operação da ONU terminou de ser alterado para "<b>' + mode + '</b>"');
          }
          else if (status === 'error') {
            AlertService.error('Ocorreu um erro ao tentar alterar o modo de operação da ONU para "<b>' + mode + '</b>". Você pode tentar novamente. Caso o erro persista, contacte o COR.');
          }
        }, function (response) {

        });
    }

    function saveWifi() {
      if (vm.tr069Config.operationMode == 'bridge')
        return false;

      const currentSSID_2G = vm.currentTr069Config.wifi[1].ssid
      const newSSID_2G = vm.tr069Config.wifi[1].ssid
      const currentPW_2G = vm.currentTr069Config.wifi[1].passphrase
      const newPW_2G = vm.tr069Config.wifi[1].passphrase

      if (newSSID_2G.length < 1) {
        AlertService.error('<b>Wi-Fi 2.4GHz</b>: é nessário inserir um SSID para a rede.');
        return false;
      }
      else if (newPW_2G != currentPW_2G && newPW_2G.length < 8) {
        AlertService.error('<b>Wi-Fi 2.4GHz</b>: a senha deve possuir pelo menos 8 caracteres.');
        return false;
      }

      const currentSSID_5G = vm.currentTr069Config.wifi[5].ssid
      const newSSID_5G = vm.tr069Config.wifi[5].ssid
      const currentPW_5G = vm.currentTr069Config.wifi[5].passphrase
      const newPW_5G = vm.tr069Config.wifi[5].passphrase

      if (vm.contrato['5G'] == 1 && newSSID_5G.length < 1) {
        AlertService.error('<b>Wi-Fi 5.8GHz</b>: é nessário inserir um SSID para a rede.');
        return false;
      }
      else if (vm.contrato['5G'] == 1 && newPW_5G != currentPW_5G && newPW_5G.length < 8) {
        AlertService.error('<b>Wi-Fi 5.8GHz</b>: a senha deve possuir pelo menos 8 caracteres.');
        return false;
      }

      AlertService.loading('Salvando as alterações do Wi-Fi...');

      // Configurações de canais Wi-Fi
      var autoChannel_2g, channel_2g, autoChannel_5g, channel_5g;

      if (vm.tr069Config.wifi[1].selectedChannel == 'auto') {
        autoChannel_2g = true;
        channel_2g = null;
      }
      else {
        autoChannel_2g = false;
        channel_2g = parseInt(vm.tr069Config.wifi[1].selectedChannel);
      }

      if (vm.tr069Config.wifi[5].selectedChannel == 'auto') {
        autoChannel_5g = true;
        channel_5g = null;
      }
      else {
        autoChannel_5g = false;
        channel_5g = parseInt(vm.tr069Config.wifi[5].selectedChannel);
      }

      const identifier = vm.currentTr069Config.identifier;
      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/wifi/' + identifier + '/' + vm.contrato.login,
        method: 'POST',
        data: {
          noc_operator: $rootScope.operador.username,
          userID: null,
          active_2g: vm.tr069Config.wifi[1].enabled,
          ssid_2g: vm.tr069Config.wifi[1].ssid,
          password_2g: vm.tr069Config.wifi[1].passphrase,
          autoChannel_2g: autoChannel_2g,
          channel_2g: channel_2g,
          autoChannel_5g: autoChannel_5g,
          channel_5g: channel_5g,
          active_5g: vm.tr069Config.wifi[5].enabled,
          ssid_5g: vm.tr069Config.wifi[5].ssid,
          password_5g: vm.tr069Config.wifi[5].passphrase,
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var status = response.data.status;
          var message;
          if (status === 'success') {
            message = 'As configurações do Wi-Fi foram salvas. Caso a ONU não esteja conectada no momento, as alterações serão aplicadas assim que ela se conectar à nossa rede.';
          }
          else {
            message = 'Não foi possível salvar as configurações do Wi-Fi.';
          }
          AlertService.loaded(status, message);
          getTr069Config(vm.contrato.login);
        }, function (response) {
          AlertService.loaded('error', 'Não foi possível contactar a API.');
        });
    }

    function selecionarRedirecionamento(redirecionamento) {
      angular.copy(redirecionamento, vm.redirecionamentoSelecionado)
      vm.redirecionamentoSelecionado.enabled = vm.redirecionamentoSelecionado.enabled.toString();
      var splittedIP = vm.redirecionamentoSelecionado.internalIP.split('.');
      vm.redirecionamentoSelecionado.internalIP = splittedIP[splittedIP.length - 1];
    }

    function resetFrmnovoredirecionamento() {
      angular.copy(vm.redirecionamentoDefault, vm.novoRedirecionamento);
    }

    function initFrmnovoredirecionamento() {
      if (vm.tr069Config.operationMode == 'bridge') {
        window.setTimeout(function () {
          angular.element('#frmnovoredirecionamento').modal('hide');
          AlertService.error('O modo de operação da ONU está como "bridge". Não será possível adicionar um redirecionamento de portas.');
        }, 100);
        return false;
      }

      const cgnat = vm.currentTr069Config.connection.cgnat;
      const cgnatIP = cgnat ? vm.currentTr069Config.connection.cgnatIP : null;
      const ipFix = vm.currentTr069Config.connection.ip_fix;
      const ipFixAddress = vm.currentTr069Config.connection.ip_fix_address;
      const ipFixo = vm.currentTr069Config.connection.ip_fixo;
      const ipFixoAddress = vm.currentTr069Config.connection.ip_fixo_address;

      vm.redirecionamentoDefault.externalIP = vm.currentTr069Config.connection.externalIP;

      angular.copy(vm.redirecionamentoDefault, vm.novoRedirecionamento);

      getAssocDevices();

      if (cgnat)
        getFreePort();
    }

    function getFreePort() {
      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/port-mapping/free-port/' + vm.contrato.login + '/' + vm.currentTr069Config.connection.externalIP,
        method: "GET"
      })
        .then(function (response) {
          if (response.data.status === 'success')
            vm.novoRedirecionamento.externalPort = response.data.freePort;
        },
          function (response) {

          });
    }

    function adicionarRedirecionamento() {
      if (vm.tr069Config.operationMode == 'bridge') {
        window.setTimeout(function () {
          angular.element('#frmnovoredirecionamento').modal('hide');
          AlertService.error('O modo de operação da ONU está como "bridge". Não será possível adicionar um redirecionamento de portas.');
        }, 100);
        return false;
      }

      if (!validatePortMappingConfig(vm.novoRedirecionamento))
        return false;

      AlertService.loading('Adicionando redirecionamento de porta...');

      const identifier = vm.currentTr069Config.identifier;
      const username = vm.contrato.login;
      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/port-mapping/' + identifier + '/' + username,
        method: 'POST',
        data: {
          noc_operator: $rootScope.operador.username,
          city: vm.contrato.cidade,
          cgnatIP: vm.currentTr069Config.connection.cgnatIP,
          externalIP: vm.novoRedirecionamento.externalIP,
          externalPort: vm.novoRedirecionamento.externalPort,
          description: vm.novoRedirecionamento.description,
          internalPort: vm.novoRedirecionamento.internalPort,
          internalIP: vm.novoRedirecionamento.internalIP,
          mac: vm.novoRedirecionamento.mac,
          cgnat: vm.currentTr069Config.connection.cgnat
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var status = response.data.status;
          var errorMessage = status == 'error' ? response.data.message : null;
          var message;

          if (status == 'success') {
            message = 'O redirecionamento de porta foi adicionado. Caso a ONU não esteja conectada no momento, as alterações serão aplicadas assim que ela se conectar à nossa rede.';
            if (!vm.currentTr069Config.connection.cgnat && !vm.currentTr069Config.connection.ip_fix && !vm.currentTr069Config.connection.ip_fixo)
              message += ' <b>Este cliente não possui os pacotes "IP Fix" ou "IP Fixo" contratados. Será necessário alertar o cliente de que seu IP público pode ser alterado quando reconectar-se. Caso o cliente deseje um endereço fixo, será necessário contratar um destes dois pacotes.</b>';
            $('#frmnovoredirecionamento').modal('hide');
          }
          else if (status == 'error') {
            if (errorMessage == 'INTERNAL_PORT_USED') {
              message = 'Já existe um redirecionamento de porta para <b>esta porta interna (' + vm.novoRedirecionamento.internalPort + ')</b>, para <b>este dispositivo (' + vm.currentTr069Config.lanIpRangeStr + vm.novoRedirecionamento.internalIP + ')</b>.';
            }
            else if (errorMessage == 'EXTERNAL_PORT_USED') {
              message = 'Já existe um redirecionamento para esta <b>porta pública (' + vm.novoRedirecionamento.externalPort + ')</b>.';
            }
            else if (errorMessage == 'EXTERNAL_PORT_RESERVED') {
              message = 'A <b>porta pública ' + vm.novoRedirecionamento.externalPort + '</b> é uma porta reservada para nossos serviços internos. Não será possível realizar um redirecionamento utilizando esta porta pública. Escolha outra porta.';
            }
          }
          else {
            message = 'Não foi possível adicionar o redirecionamento de porta.';
          }

          AlertService.loaded(status, message);
          getTr069Config(vm.contrato.login);
        }, function (response) {
          AlertService.loaded('error', 'Não foi possível contactar a API.');
        });
    }

    function editarRedirecionamento() {
      if (!validatePortMappingConfig(vm.redirecionamentoSelecionado))
        return false;

      AlertService.loading('Salvando alterações...');

      const identifier = vm.currentTr069Config.identifier;
      const username = vm.contrato.login;
      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/port-mapping/' + identifier + '/' + username,
        method: 'PUT',
        data: {
          noc_operator: $rootScope.operador.username,
          portID: vm.redirecionamentoSelecionado.id,
          active: vm.redirecionamentoSelecionado.enabled,
          externalIP: vm.redirecionamentoSelecionado.externalIP,
          externalPort: vm.redirecionamentoSelecionado.externalPort,
          description: vm.redirecionamentoSelecionado.description,
          internalPort: vm.redirecionamentoSelecionado.internalPort,
          internalIP: vm.redirecionamentoSelecionado.internalIP,
          mac: vm.redirecionamentoSelecionado.mac
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var status = response.data.status;
          var errorMessage = status == 'error' ? response.data.message : null;
          var message;
          if (status == 'success') {
            message = 'As alterações no redirecionamento foram salvas. Caso a ONU não esteja conectada no momento, as alterações serão aplicadas assim que ela se conectar à nossa rede.';
            $('#frmnovoredirecionamento').modal('hide');
          }
          else if (status == 'error') {
            if (errorMessage == 'INTERNAL_PORT_USED') {
              message = 'Já existe um redirecionamento de porta para <b>esta porta interna (' + vm.redirecionamentoSelecionado.internalPort + ')</b>, para <b>este dispositivo (' + vm.currentTr069Config.lanIpRangeStr + vm.redirecionamentoSelecionado.internalIP + ')</b>.';
            }
            else if (errorMessage == 'EXTERNAL_PORT_USED') {
              message = 'Já existe um redirecionamento para esta <b>porta pública (' + vm.redirecionamentoSelecionado.externalPort + ')</b>.';
            }
            else if (errorMessage == 'EXTERNAL_PORT_RESERVED') {
              message = 'A <b>porta pública ' + vm.redirecionamentoSelecionado.externalPort + '</b> é uma porta reservada para nossos serviços internos. Não será possível realizar um redirecionamento utilizando esta porta pública. Escolha outra porta.';
            }
          }
          else {
            message = 'Não foi possível salvar as alterações do redirecionamento.';
          }
          AlertService.loaded(status, message);
          getTr069Config(vm.contrato.login);
        }, function (response) {
          AlertService.loaded('error', 'Não foi possível contactar a API.');
        });
    }

    function excluirRedirecionamento(portID) {
      if (vm.tr069Config.operationMode == 'bridge') {
        window.setTimeout(function () {
          AlertService.error('O modo de operação da ONU está como "bridge". Não será possível excluir um redirecionamento de portas.');
        }, 100);
        return false;
      }

      AlertService.loading('Excluindo redirecionamento de porta...');

      const identifier = vm.currentTr069Config.identifier;
      const username = vm.contrato.login;

      $http({
        url: PORTAL_ASSINANTE_API.url + '/noc/port-mapping/' + identifier + '/' + username,
        method: 'DELETE',
        data: {
          noc_operator: $rootScope.operador.username,
          city: vm.contrato.cidade,
          portID: portID,
          externalPort: vm.currentTr069Config.portMappings[portID].externalPort
        },
        ignoreLoadingBar: true
      })
        .then(function (response) {
          var status = response.data.status;
          var message;
          if (status == 'success') {
            message = 'O redirecionamento de porta foi excluído. Caso a ONU não esteja conectada no momento, as alterações serão aplicadas assim que ela se conectar à nossa rede.';
            $('#frmnovoredirecionamento').modal('hide');
          }
          else {
            message = 'Não foi possível excluir o redirecionamento de porta.'
          }
          AlertService.loaded(status, message);
          getTr069Config(vm.contrato.login);
        }, function (response) {
          AlertService.loaded('error', 'Não foi possível contactar a API.');
        });
    }

    function validatePortMappingConfig(portMapping) {
      var internalIP = parseInt(portMapping.internalIP);
      var internalPort = parseInt(portMapping.internalPort);
      var externalPort = parseInt(portMapping.externalPort);
      var mac = portMapping.mac;

      if (isNaN(externalPort) || externalPort < 1 || externalPort > 65535) {
        AlertService.error('A porta pública deve ser um número entre 1 e 65535.');
        return false;
      }

      if (isNaN(internalIP) || internalIP < 2 || internalIP > 254) {
        AlertService.error('O IP interno deve ser um número entre 2 e 254.');
        return false;
      }

      if (!Validator.mac(mac)) {
        AlertService.error('O MAC inserido é inválido.');
        return false;
      }

      if (isNaN(internalPort) || internalPort < 1 || internalPort > 65535) {
        AlertService.error('A porta interna deve ser um número entre 1 e 65535.');
        return false;
      }

      return true;
    }

    function getOnuAlarms() {
      if (!vm.contrato.olt_modelo || !vm.contrato.serial){
        return;
      }

      if (!vm.loadedOnuAlarmsFirstTime) {
        vm.loadedOnuAlarmsFirstTime = true;
        // Só exibe o loading spinner no primeiro carregamento - para não ficar sendo exibido nos carregamentos definidos via setInterval
        vm.loadingOnuAlarms = true;
      }

      // O controle abaixo é para evitar de buscar o mesmo serial enquanto já está buscando - pois essa função é executada em intervalo de 10s e pode demorar para responder
      if (vm.loadingOnuAlarmsSerial == vm.contrato.serial) {
        return;
      }
      vm.loadingOnuAlarmsSerial = vm.contrato.serial;

      var consultingSerial = vm.contrato.serial;

      $http({
        url: API_CONFIG.url + '/ftth/onu-alarms/' + vm.contrato.olt_modelo + '/' + vm.contrato.serial,
        method: 'GET',
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.loadingOnuAlarmsSerial = '';
        if (consultingSerial !== vm.contrato.serial){
          return
        }

        var oldAlarms = [];
        angular.copy(vm.onuAlarms, oldAlarms);
        if (response.data && response.data.status === 'success' && JSON.stringify(oldAlarms) !== JSON.stringify(response.data.alarms)) {
          vm.onuAlarms = response.data.alarms;
        }
        else if (!response.data || response.data.status != 'success') {
          vm.onuAlarms = [];
        }

        vm.loadingOnuAlarms = false;

        window.setTimeout(function () {
          adjustAlertsHeights();
        }, 500);
        window.setTimeout(function () {
          adjustAlertsHeights();
        }, 1500);
      },
        function (response) {

        });
    }

    function getDHCPIP() {
      vm.contrato.ip_dhcp = null;
      vm.atualizando_ip_dhcp = true;

      $http({
        url: MIKROTIK_API.url + '/dhcp/get-ip',
        method: 'POST',
        data: {
          cidade: vm.contrato.cidade,
          serial: vm.contrato.serial,
          mac: vm.contrato.mac
        },
        ignoreLoadingBar: true
      }).then(function (response) {
        vm.atualizando_ip_dhcp = false;
        if (response.data && response.data.status === 'success') {
          vm.contrato.ip_dhcp = response.data.ip
        }
      }, function (response) {
        vm.atualizando_ip_dhcp = false;
      })
        .catch(function (err) {
          console.log(err);

        });
    }

    function openONUInterface(connection) {
      var targetIp = null

      switch (connection) {
        case 'pppoe':
          targetIp = vm.wan.ipv4 ? vm.wan.ipv4 : null;
          break;
        case 'dhcp':
          targetIp = vm.contrato.ip_dhcp ? vm.contrato.ip_dhcp : null;
          break;
      }

      if (!targetIp) {
        alert('Não foi possível encontrar o IP da conexão ' + connection.toUpperCase());
        return false;
      }

      openInNewTab('https://' + targetIp + ':8922')
    }

    function openGenieACS(connection) {
      var identifier = vm.tr069Config.identifier;

      openInNewTab(GENIE_ACS.url + '/#!/devices/' + identifier);
    }

    function resetHighcharts() {
      vm.graficoPing = Highcharts.stockChart('graficoPing', {
        chart: {
          zoomType: 'x',
          height: 300
        },

        navigator: {
          height: 15
        },

        time: {
          useUTC: false
        },

        rangeSelector: {
          buttons: [{
            count: 1,
            type: 'minute',
            text: '1M'
          }, {
            count: 5,
            type: 'minute',
            text: '5M'
          },
          {
            count: 10,
            type: 'minute',
            text: '10M'
          },
          {
            count: 30,
            type: 'minute',
            text: '30M'
          },
          {
            count: 1,
            type: 'hour',
            text: '1H'
          },
          {
            type: 'all',
            text: 'Todos'
          }],
          inputEnabled: false,
          selected: 5
        },

        title: {
          text: 'PING'
        },

        credits: {
          enabled: false
        },
        xAxis: {
          minRange: 1000,
          tickInterval: 60000
        },
        yAxis: {
          floor: 0,
          opposite: false,
          labels: {
            align: 'right',
            x: -5
          },
          title: {
            text: 'RTT (ms)'
          },
        },
        exporting: {
          enabled: true
        },


        lang: {
          loading: 'Aguarde...',
          months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
          weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
          shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
          exportButtonTitle: "Exportar",
          printChart: "Imprimir",
          viewFullscreen: "Tela cheia",
          rangeSelectorFrom: "De",
          rangeSelectorTo: "Até",
          rangeSelectorZoom: "Periodo",
          downloadPNG: 'Baixar imagem PNG',
          downloadJPEG: 'Baixar imagem JPEG',
          downloadPDF: 'Baixar documento PDF',
          downloadSVG: 'Baixar imagem SVG',
          resetZoom: "Resetar",
          resetZoomTitle: "Resetar",
          thousandsSep: ".",
          decimalPoint: ','
        }

      });

      vm.graficoTrafego = Highcharts.stockChart('graficoTrafego', {
        chart: {
          zoomType: 'x',
          height: 300
        },

        navigator: {
          height: 15
        },

        time: {
          useUTC: false
        },

        rangeSelector: {
          buttons: [{
            count: 1,
            type: 'minute',
            text: '1M'
          }, {
            count: 5,
            type: 'minute',
            text: '5M'
          },
          {
            count: 10,
            type: 'minute',
            text: '10M'
          },
          {
            count: 30,
            type: 'minute',
            text: '30M'
          },
          {
            count: 1,
            type: 'hour',
            text: '1H'
          },
          {
            type: 'all',
            text: 'Todos'
          }],
          inputEnabled: false,
          selected: 5
        },

        title: {
          text: 'Tráfego'
        },

        legend: {
          itemDistance: 50
        },

        plotOptions: {
          series: {
            tooltip: {
              valueDecimals: 2,
              valueSuffix: ' Mb/s'
            }
          }
        },

        credits: {
          enabled: false
        },
        xAxis: {
          minRange: 1000,
          tickInterval: 60000,
          crosshair: true,

        },
        yAxis: {

          floor: 0,
          opposite: false,
          minRange: 10,
          labels: {
            align: 'right',
            x: -5
          },
          title: {
            text: 'Mb/s'
          },
          plotLines: []
        },
        exporting: {
          enabled: true
        },

        lang: {
          loading: 'Aguarde...',
          months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
          weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
          shortMonths: ['Jan', 'Feb', 'Mar', 'Abr', 'Maio', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
          exportButtonTitle: "Exportar",
          printChart: "Imprimir",
          viewFullscreen: "Tela cheia",
          rangeSelectorFrom: "De",
          rangeSelectorTo: "Até",
          rangeSelectorZoom: "Periodo",
          downloadPNG: 'Baixar imagem PNG',
          downloadJPEG: 'Baixar imagem JPEG',
          downloadPDF: 'Baixar documento PDF',
          downloadSVG: 'Baixar imagem SVG',
          resetZoom: "Resetar",
          resetZoomTitle: "Resetar",
          thousandsSep: ".",
          decimalPoint: ','
        }

      });
    }

    function adicionarMulticast() {
      var port = angular.element('#porta_add_vlan_multicast').val();

      AlertService.loading('Adicionando VLAN Multicast na porta ' + port + '...');

      var interfaceSplit = vm.contrato.interface.split('-');
      var slot = interfaceSplit[2];
      var pon = interfaceSplit[3];

      $http({
        url: PROVISION_API.url + "/onu/vlan",
        headers: {
          'x-access-token': PROVISION_API.token,
        },
        method: "POST",
        data: {
          olt: vm.contrato.ip_transmissor,
          serial: vm.contrato.serial,
          slot: slot,
          pon: pon,
          vlan_type: "multicast",
          vlan_id: "2",
          vlan_port: port,
          port: port
        },
        ignoreLoadingBar: true,
        withCredentials: false
      }).then(function (response) {
        console.log('response:', response)
        var message = null;
        if (response && response.data && response.data.status && response.data.status == 'OK') {
          message = 'A VLAN 2 Multicast foi adicionada na porta ' + port;
        }
        else {
          message = 'Ocorreu um erro ao adicionar a VLAN de Multicast';
        }
        AlertService.loaded('success', message);
        angular.element('#frmvlanmulticast').modal('hide');
      }).catch(function (err) {
        console.log(err);
        AlertService.loaded('error', 'Ocorreu um erro ao adicionar a VLAN de Multicast');
        angular.element('#frmvlanmulticast').modal('hide');
      });
    }

    function removerMulticast() {
      var port = angular.element('#porta_del_vlan_multicast').val();

      AlertService.loading('Removendo VLAN Multicast da porta ' + port + '...');

      $http({
        url: PROVISION_API.url + "/onu/vlan",
        headers: {
          'x-access-token': PROVISION_API.token,
        },
        method: "DELETE",
        data: {
          olt: vm.contrato.ip_transmissor,
          serial: vm.contrato.serial,
          port: port
        },
        ignoreLoadingBar: true,
        withCredentials: false
      }).then(function (response) {
        console.log('response:', response)
        var message = null;
        if (response && response.data && response.data.status && response.data.status == 'OK') {
          message = 'A VLAN 2 Multicast foi removida da porta ' + port;
        }
        else {
          message = 'Ocorreu um erro ao remover a VLAN de Multicast';
        }
        AlertService.loaded('success', message);
        angular.element('#frmvlanmulticast').modal('hide');
      }).catch(function (err) {
        console.log(err);
        AlertService.loaded('error', 'Ocorreu um erro ao remover a VLAN de Multicast');
        angular.element('#frmvlanmulticast').modal('hide');
      });
    }

  }

  function secondsToText(seconds) {
    if ([0, 86400, 86401, 86410, 86411].includes(seconds))
      return '[indefinido]';

    var days = Math.floor(seconds / 86400);
    seconds = seconds - (days * 86400);

    var hours = Math.floor(seconds / 3600);
    seconds = seconds - (hours * 3600);

    var minutes = Math.floor(seconds / 60);
    seconds = seconds - (minutes * 60);

    var result = '';

    if (days > 0)
      result += days + ' dia(s), ';
    if (days > 0 || hours > 0)
      result += hours + ' hora(s), ';
    if (days > 0 || hours > 0 || minutes > 0)
      result += minutes + ' minuto(s) e ';

    result += seconds + ' segundos(s)';

    return result;
  }



})();


