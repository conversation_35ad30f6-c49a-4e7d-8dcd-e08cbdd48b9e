    <div class="panel panel-primary">
        <div class="panel-heading clearfix">
            <span class="pull-right">
                <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandocontrato"> <button class="btn btn-default btn-sm" ng-click="AMC.getContrato(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
            </span>
           <h3 class="panel-title" style="margin-top: 5px;"><span class="badge">1</span> Informações do Contrato</h3>
        </div>
       <div class="panel-body">
        <table class="table table-striped table-hover table-bordered">
                <thead>
                <tr>
                <th class="vert-align text-center">Cliente</th>
                <th class="vert-align text-center">Contrato</th>
                <th class="vert-align text-center">Serviço</th>
                <th class="vert-align text-center">Tipo Serviço</th>
                <th class="vert-align text-center">Nome Plano</th>
                <th class="vert-align text-center">Plano Down</th>
                <th class="vert-align text-center">Plano Up</th>
                <th class="vert-align text-center">Senha</th>
                </tr>
                </thead>
                <tbody>
                  <tr>
                  <td class="vert-align text-center">{{AC.contrato.nomeassinante}}</td>
                  <td class="vert-align text-center"><span class="label label-default"
                    ng-class="[{'label-success': AC.contrato.situacaocontrato == 'Conectado/Ativo'},
                    {'label-danger': AC.contrato.situacaocontrato == 'Inadimplente' || AC.contrato.situacaocontrato == 'Cancelado'},
                    {'label-warning': AC.contrato.situacaocontrato == 'Conectado/Inadimplente' || AC.contrato.situacaocontrato == 'Pausado' ||
                  AC.contrato.situacaocontrato == 'Aguardando Conexão'}]">{{AC.contrato.situacaocontrato}}</span></td>
                  <td class="vert-align text-center"><span ng-if="AC.contrato.tipohost!=='Radio AP 5G - ACL Radius'">{{AC.contrato.escopo}}</span>
                    <a ng-if="AC.contrato.tipohost=='Radio AP 5G - ACL Radius'" href="http://{{AC.contrato.ipadmin}}:8922" target="blank_">{{AC.contrato.escopo}}</a>
                  </td>  
                  <td class="vert-align text-center">{{AC.contrato.tipohost}}</td>
                  <td class="vert-align text-center">{{AC.contrato.plano}}</td>
                  <td class="vert-align text-center">{{AC.contrato.down}}</td>
                  <td class="vert-align text-center">{{AC.contrato.up}}</td>
                  <td class="vert-align text-center"><span class="label label-default" style="cursor: pointer;" ng-click="AMC.mostraSenha()" ng-if="AC.contrato.senha !== '' && AC.contrato.senha !== undefined && !AMC.senhaVisivel">Mostrar Senha</span><span ng-if="AMC.senhaVisivel==true">{{AC.contrato.senha}} <span class="label label-danger" style="cursor: pointer;" ng-click="AMC.mostraSenha()">x</span></span></td>
                </tr>
                </tbody>
                </table>
  
                <!--<span><i class="glyphicon glyphicon-ok-sign" style="font-size:20px; color:green;"></i> Não há pendências no contrato</span> -->
        </div>
      </div>
    
    
    <!-- INICIO FILA FTTH -->
    
      
      <div class="panel panel-primary">
        <div class="panel-heading clearfix">
            <span class="pull-right">
                <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandofila"> <button class="btn btn-default btn-sm" ng-click="AMC.getFila(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
            </span>
           <h3 class="panel-title" style="margin-top: 5px;"><span class="badge">2</span> Provisionamento de Equipamento Pendentes</h3>
        </div>
        <div class="panel-body">
          <span ng-if="AMC.fila.length == 0">Sem alterações pendentes</span>
    
            <table class="table table-striped table-hover table-bordered" ng-if="AMC.fila.length > 0">
                <thead>
                <tr>
                  <th class="vert-align text-center">Status</th>
                  <th class="vert-align text-center">Data/Hora</th>
                  <th class="vert-align text-center">MAC</th>
                  <th class="vert-align text-center">Modelo</th>
                  <th class="vert-align text-center">Serial</th>
                  <th class="vert-align text-center">Patrimônio</th>
                  <th class="vert-align text-center">Sit. Equipamento</th>
                  <th class="vert-align text-center">Escopo</th>
                <th class="vert-align text-center">Wifi</th>
                <th class="vert-align text-center">IPTV</th>
                <th class="vert-align text-center">STB</th>
                <th class="vert-align text-center">Erros</th>
                <th class="vert-align text-center">Retorno</th>
                </tr>
                </thead>
              <tbody>
                <tr ng-repeat="fila in AMC.fila">
                  <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-default': fila.status == 'Na Fila'}, {'label-warning': fila.status == 'Processando'}, {'label-danger': fila.erros > 0}, {'label-success': fila.status == 'Finalizado' && fila.erros == 0}]">{{fila.status}}</span></td>
                  <td class="vert-align text-center">{{fila.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{fila.mac}}</td>
                  <td class="vert-align text-center">{{fila.modelo}}</td>
                  <td class="vert-align text-center">{{fila.serial}}</td>
                  <td class="vert-align text-center"><a href="/materiais/patrimonios/{{fila.patrimonio}}" target="_blank">{{fila.patrimonio}}</a></td>
                  <td class="vert-align text-center">
                    <span class="label label-danger" ng-if="fila.situacaoequipamento==0">Desativado</span>  
                    <span class="label label-success" ng-if="fila.situacaoequipamento==1">Ativado</span>  
                    </td>
                  <td class="vert-align text-center">{{fila.escopo}}</td>
                  <td class="vert-align text-center"><span class="label label-default" ng-if="fila.wifi==0">Não</span>  
                    <span class="label label-default" ng-if="fila.wifi==1">Sim</span></td>
                  <td class="vert-align text-center"><span class="label label-default" ng-if="fila.iptv==0">Não</span>  
                    <span class="label label-default" ng-if="fila.iptv==1">Sim</span></td>
                  <td class="vert-align text-center">
                      <span class="label label-default">{{fila.stb}}</span>  
                    </td>
                  <td class="vert-align text-center"><span class="label label-default">{{fila.erros}}</span></td>
                  <td class="vert-align text-center">
                    <span ng-repeat="item in fila.retornos">{{item}}<br></span></td>
              </tr>
              </tbody>
              </table>
              <button ng-hide="AMC.processando_fila" ng-if="(AMC.contrato.tipohost == 'Porta GPON OLT' || AMC.contrato.tipohost == 'FTTA') && AMC.fila.length > 0 && !AMC.fila_processada" class="btn btn-warning" title="Processar Fila de Alterações" ng-click="AMC.processa_fila(AMC.contrato.username)"><i class="glyphicon glyphicon-play-circle"></i> Processar Fila</button><span ng-if="AMC.processando_fila"><img src="assets/images/ajax-loader.gif"> Processando fila...aguarde</span>
        </div>
      </div>
    
    
    <!-- FIM FILA FTTH -->
    
    <div class="row">
      <div class="col-md-8">
    <div class="panel panel-primary">
        <div class="panel-heading clearfix">
            <span class="pull-right">
                <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandoonu"> <button class="btn btn-default btn-sm" ng-click="AMC.getOnuInfo()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
            </span>
           <h3 class="panel-title" style="margin-top: 5px;"><span class="badge">3</span> Informações FTTH</h3>
        </div>
       <div class="panel-body">
          <span ng-if="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !== 'FTTA' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !==undefined">Plano contratado não utiliza ONU</span>
          <table class="table table-striped table-hover table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
              <thead>
              <tr>
              <th class="vert-align text-center">Estado Operacional</th>
              <th class="vert-align text-center">Distância</th>
              <th class="vert-align text-center">Último Desligamento</th>
              <th class="vert-align text-center">RxPower</th>
              <th class="vert-align text-center">TxPower</th>
              <th class="vert-align text-center">CurrTxBias</th>
              <th class="vert-align text-center">MAC</th>
              <th class="vert-align text-center">Serial</th>
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">CDO</th>
              <th class="vert-align text-center">CAT</th>
              <th class="vert-align text-center">Porta CAT</th>
              <th class="vert-align text-center">Ação</th>
              </tr>
              </thead>
              <tbody>
                <tr>
                    <td class="vert-align text-center"><span class="label label-default"
                      ng-class="[{'label-success': AMC.anm.dist.OperState == 'UP'},
                      {'label-danger': AMC.anm.dist.OperState !== 'UP'}]">{{AMC.anm.dist.OperState}}</span></td>
                    <td class="vert-align text-center">{{AMC.anm.dist.Length}}m</td>
                    <td class="vert-align text-center">{{AMC.anm.status.LASTOFFTIME | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{AMC.anm.sinal.RxPower}} <span class="label label-default"
                      ng-class="[{'label-success': AMC.anm.sinal.RxPowerR == 'Normal'},
                      {'label-danger': AMC.anm.sinal.RxPowerR !== 'Normal'}]">{{AMC.anm.sinal.RxPowerR}}</span></td>
                    <td class="vert-align text-center">{{AMC.anm.sinal.TxPower}} <span class="label label-default"
                      ng-class="[{'label-success': AMC.anm.sinal.TxPowerR == 'Normal'},
                      {'label-danger': AMC.anm.sinal.TxPowerR !== 'Normal'}]">{{AMC.anm.sinal.TxPowerR}}</span></td>
                    <td class="vert-align text-center">{{AMC.anm.sinal.CurrTxBias}} <span class="label label-default"
                      ng-class="[{'label-success': AMC.anm.sinal.CurrTxBiasR == 'Normal'},
                      {'label-danger': AMC.anm.sinal.CurrTxBiasR !== 'Normal'}]">{{AMC.anm.sinal.CurrTxBiasR}}</span></td>
                      <td class="vert-align text-center">{{AC.contrato.macaddress}}</td>
                      <td class="vert-align text-center">{{AC.contrato.serial}}</td>
                      <td class="vert-align text-center"><a href="/materiais/patrimonios/{{AC.contrato.patrimonio}}" target="_blank">{{AC.contrato.patrimonio}}</a></td>
                      <td class="vert-align text-center"><span ng-if="!AMC.editandocdo">{{AMC.contrato.cdo}}</span><input ng-if="AMC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AMC.estrutura.cdo"></td>
                      <td class="vert-align text-center"><span ng-if="!AMC.editandocdo">{{AMC.contrato.cat}}</span><input ng-if="AMC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AMC.estrutura.cat"></td>
                      <td class="vert-align text-center"><span ng-if="!AMC.editandocdo">{{AMC.contrato.porta}}</span><input ng-if="AMC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number" ng-model="AMC.estrutura.porta"></td>
                      <td class="vert-align text-center"><button class="btn btn-default btn-sm" ng-if="!AMC.editandocdo" title="Edita informações (CDO, CAT, Porta CAT)" ng-click="AMC.editacdo(AMC.contrato.cdo, AMC.contrato.cat, AMC.contrato.porta)"><i class="glyphicon glyphicon-edit"></i></button><button ng-if="AMC.editandocdo" title="Grava alterações" class="btn btn-success btn-sm" ng-click="AMC.salvacdo(AC.contrato)"><i class="glyphicon glyphicon-floppy-saved"></i></button> <button ng-if="AMC.editandocdo" title="Cancela alterações" class="btn btn-danger btn-sm" ng-click="AMC.cancelacdo()"><i class="glyphicon glyphicon-ban-circle"></i></button></td> 
              </tr>
            </tbody>
            </table>  
        </div>
      </div>
      </div>
      <div class="col-md-4">
    
      <div class="panel panel-primary" >
          <div class="panel-heading clearfix">
              <span class="pull-right">
                  <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandosinal"> <button class="btn btn-default btn-sm" ng-disabled = "AMC.anm.sinal == undefined || AMC.anm == ''" ng-click="AMC.gravasinal()"><i class="glyphicon glyphicon-floppy-saved"></i> Gravar Sinal Atual</button> <button class="btn btn-default btn-sm" ng-click="AMC.getHistSinal(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
              </span>
             <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Histórico de Sinal</h3>
          </div>
         <div class="panel-body">
            <span ng-if="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !=='FTTA' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !==undefined">Plano contratado não utiliza ONU</span>
            <div class="pre-scrollable ng-scope" style="height:80px;">
            <table class="table table-striped table-hover table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
                <thead>
                <tr>
                  <th class="vert-align text-center">Data</th>
                  <th class="vert-align text-center">Rx</th>
                  <th class="vert-align text-center">Tx</th>
                  <th class="vert-align text-center">TxBias</th>
                  <th class="vert-align text-center">Comentário</th>
                </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in AMC.sinal">
                      <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                      <td class="vert-align text-center">{{item.rx}}</td>
                      <td class="vert-align text-center">{{item.tx}}</td>
                      <td class="vert-align text-center">{{item.tx_bias}}</td>
                      <td class="vert-align text-center">{{item.comentario}}</td>
                </tr>
              </tbody>
              </table>  
              </div>
          </div>
        </div>  
      </div>
    
    
    
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading clearfix">
                    <span class="pull-right">
                        <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandopppoe"> <button class="btn btn-default btn-sm" ng-click="AMC.getPppoe(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
                    </span>
                   <h3 class="panel-title" style="margin-top: 5px;"><span class="badge">4</span> WAN Status</h3>
                </div>
               <div class="panel-body">
                  <table class="table table-striped table-hover table-bordered">
                      <thead>
                      <tr>
                      <th class="vert-align text-center" style="width: 130px;">Status</th>
                      <th class="vert-align text-center" style="width: 200px;">Concentrador</th>
                      <th class="vert-align text-center" style="width: 150px;">IPv4</th>
                      <th class="vert-align text-center" style="width: 150px;">IPv6</th>
                      <th class="vert-align text-center" style="width: 150px;">Status Bound</th>
                      </tr>
                      </thead>
                      <tbody>
                        <tr ng-repeat="concentrador in AMC.concentradores" ng-class="{'success': concentrador.localizado !== ''}">
                        <td class="vert-align text-center" style="width: 130px;"><span class="label label-default" ng-class="[{'label-success': concentrador.status == 'Localizado'}, {'label-default': concentrador.status == 'Não localizado'}, {'label-default': concentrador.status == 'Ocioso'}, {'label-warning': concentrador.status == 'Pesquisando'}]">{{concentrador.status}}</span></td>
                        <td class="vert-align text-center" style="width: 200px;">{{concentrador.ip}}</td>
                        <td class="vert-align text-center" style="width: 150px;"><b>{{concentrador.localizado}} <img src="assets/images/ajax-loader.gif" ng-show="concentrador.status=='Pesquisando'"><i class="glyphicon glyphicon-remove" ng-show="concentrador.status=='Não localizado'"></i></b></td>
                        <td class="vert-align text-center" style="width: 150px;"><b>{{concentrador.ip6}} <img src="assets/images/ajax-loader.gif" ng-show="concentrador.status=='Pesquisando'"><i class="glyphicon glyphicon-remove" ng-show="concentrador.status=='Não localizado'"></i></b></td>
                        <td class="vert-align text-center" style="width: 150px;"><b>{{concentrador.bound_status}} <img src="assets/images/ajax-loader.gif" ng-show="concentrador.status=='Pesquisando'"><i class="glyphicon glyphicon-remove" ng-show="concentrador.status=='Não localizado'"></i></b></td>
                      
                      </tr>
                      </tbody>
                      </table> 
                </div>
              </div>
        </div>      
        
        <div class="col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading clearfix">
                    <span class="pull-right">
                        <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandolog"> <button class="btn btn-default btn-sm" ng-click="AMC.atualizaLogs(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
                    </span>
                   <h3 class="panel-title" style="margin-top: 5px;"><span class="badge">5</span> Logs de Concentradores</h3>
                </div>
               <div class="panel-body">
                    <div class="pre-scrollable ng-scope" style="height:360px;">
          <table class="table table-striped table-hover table-bordered">
          <thead>
          <tr>
          <th class="vert-align text-center">Data/Hora</th>
          <th class="vert-align text-center">Concentrador</th>
          <th class="vert-align text-center">Mensagem</th>
        
          </tr>
          </thead>
          
          <tbody>
            <tr ng-repeat="log in AMC.logs">
            <td class="vert-align text-center">{{log.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{log.concentrador}}</td>
            <td class="vert-align text-center">{{log.mensagem}}</td>
          </tr>
          </tbody>
          </div>
          </table>
        </div>
        </div>
        </div>
    </div>
         
         
    
    
          </div>
        </div> 
    </div>
    </div>
    
    <div class="panel panel-primary" >
      <div class="panel-heading clearfix">
          <span class="pull-right">
              <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandotestes"> <button class="btn btn-default btn-sm" ng-click="AMC.getAutotesteTestes(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
          </span>
         <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Testes realizados em campo</h3>
      </div>
     <div class="panel-body">
        <div class="pre-scrollable ng-scope" style="height:180px;">
        <table class="table table-striped table-hover table-bordered">
            <thead>
            <tr>
              <th class="vert-align text-center">Data</th>
              <th class="vert-align text-center">Técnico</th>
              <th class="vert-align text-center">Plano</th>
              <th class="vert-align text-center">Plano Down</th>
              <th class="vert-align text-center">Plano Up</th>
              <th class="vert-align text-center">Teste</th>
              <th class="vert-align text-center">Status</th>
              <th class="vert-align text-center">Resultados</th>
            </tr>
            </thead>
            <tbody>
              <tr ng-repeat="item in AMC.testes">
                <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                <td class="vert-align text-center">{{item.tecnico}}</td>
                <td class="vert-align text-center">{{item.plano_nome}}</td>
                <td class="vert-align text-center">{{item.plano_down}}</td>
                <td class="vert-align text-center">{{item.plano_up}}</td>
                <td class="vert-align text-center">{{item.teste}}</td>
                <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-danger': item.status == 'nok'}, {'label-success': item.status == 'ok'}]">{{item.status}}</span></td>
                <td class="vert-align text-center">{{item.resultados}}</td>
            </tr>
          </tbody>
          </table>  
          </div>
      </div>
    </div>  
    
        <div class="panel panel-primary" >
            <div class="panel-heading clearfix">
                <span class="pull-right">
                    <img src="assets/images/ajax-loader.gif" ng-show="AMC.atualizandoftthlogs"> <button class="btn btn-default btn-sm" ng-click="AMC.getFtthLogs(AC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button> 
                </span>
               <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Log de alterações executadas no equipamento</h3>
            </div>
           <div class="panel-body">
              <span ng-if="AC.contrato.tipohost !== 'Porta GPON OLT' && AC.contrato.tipohost !=='' && AC.contrato.tipohost !=='FTTA' && AC.contrato.tipohost !==undefined">Plano contratado não utiliza ONU</span>
              <div class="pre-scrollable ng-scope" style="height:180px;">
              <table class="table table-striped table-hover table-bordered" ng-if="AC.contrato.tipohost == 'Porta GPON OLT' || AC.contrato.tipohost == 'FTTA'">
                  <thead>
                  <tr>
                    <th class="vert-align text-center">Data Processamento</th>
                    <th class="vert-align text-center">Data Alteração</th>
                    <th class="vert-align text-center">Operador</th>
                    <th class="vert-align text-center">Patrimônio</th>
                    <th class="vert-align text-center">Modelo</th>
                    <th class="vert-align text-center">Serial</th>
                    <th class="vert-align text-center">MAC</th>
                    <th class="vert-align text-center">Sit. Equipamento</th>
                    <th class="vert-align text-center">Escopo</th>
                    <th class="vert-align text-center">Wi-fi</th>
                    <th class="vert-align text-center">IPTV</th>
                    <th class="vert-align text-center">STB</th>
                    <th class="vert-align text-center">Log</th>
                    <th class="vert-align text-center">Detalhes</th>
                  </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="item in AMC.ftthlogs" ng-class="[{'success': item.erro == 0}, {'danger' : item.erro == 1}]">
                      <td class="vert-align text-center">{{item.data_processamento | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                      <td class="vert-align text-center">{{item.data_alteracao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                        <td class="vert-align text-center">{{item.operador}}</td>
                        <td class="vert-align text-center">{{item.patrimonio}}</td>
                        <td class="vert-align text-center">{{item.modelo}}</td>
                        <td class="vert-align text-center">{{item.serial}}</td>
                        <td class="vert-align text-center">{{item.mac}}</td>
                        <td class="vert-align text-center">
                          <span class="label label-danger" ng-if="item.situacaoequipamento==0">Desativado</span>  
                          <span class="label label-success" ng-if="item.situacaoequipamento==1">Ativado</span> 
                        </td>
                        <td class="vert-align text-center">{{item.escopo}}</td>
                        <td class="vert-align text-center"><span class="label label-default" ng-if="item.wifi==0">Não</span>  
                          <span class="label label-default" ng-if="item.wifi==1">Sim</span></td>
                        <td class="vert-align text-center"><span class="label label-default" ng-if="item.iptv==0">Não</span>  
                          <span class="label label-default" ng-if="item.iptv==1">Sim</span></td>
                        <td class="vert-align text-center"> <span class="label label-default">{{item.stb}}</span> </td>
                        <td class="vert-align text-center">{{item.log}}</td>
                        <td class="vert-align text-center">{{item.detalhes}}</td>
                  </tr>
                </tbody>
                </table>  
                </div>
            </div>
          </div>  
    
          