<div class="modal" id="frmredirecionamento" tabindex="-1" role="dialog" aria-labelledby="frmredirecionamento"
	aria-hidden="true" modal="showModal" close="cancel()" style="z-index: 1041;">

	<div class="modal-dialog" style="width: 500px;">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmredirecionamento_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Editar redirecionamento
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 15px;">
				<div class="row">
					<div class="tab-content">
						<div id="frmredirecionamento_dados" class="tab-pane fade in active">

							<table class="table bottom-spaced align-center valign-middle"
								style="width: 80%; margin: 0 auto;">
								<tbody>
									<tr>
										<td style="width: 70%; padding-right: 5px;">
											<label for="description">Descrição/serviço:</label>
											<input class="form-control align-center font11" type="text"
												placeholder="(opcional)" id="frmredirecionamento_description"
												ng-model="HDC.redirecionamentoSelecionado.description">
										</td>
										<td style="padding-left: 5px;">
											<label for="frmredirecionamento_ativo">Ativo:</label>
											<select id="frmredirecionamento_ativo"
												ng-model="HDC.redirecionamentoSelecionado.enabled" class="form-control">
												<option selected value="true">Sim</option>
												<option value="false">Não</option>
											</select>
										</td>
									</tr>
									<tr>
										<td style="padding-right: 5px;">
											<label for="externalIP">IP público:</label>
											<input disabled class="form-control align-center font9" type="text"
												id="frmredirecionamento_externalIP"
												ng-model="HDC.currentTr069Config.connection.externalIP">
										</td>
										<td style="padding-left: 5px;">
											<label for="externalPort">Porta pública:</label>
											<input ng-disabled="HDC.currentTr069Config.connection.cgnat"
												class="form-control align-center font11" type="text"
												id="frmredirecionamento_externalPort" maxlength="5"
												ng-model="HDC.redirecionamentoSelecionado.externalPort">
										</td>
									</tr>
								</tbody>
							</table>

							<div class="horizontal-divider"></div>

							<table class="table bottom-spaced align-center valign-middle"
								style="width: 80%; margin: 0 auto;">
								<tbody>
									<tr>
										<td style="padding: 0px !important;">
											<label for="internalDevice">Dispositivo interno:</label>
										</td>
										<td style="padding: 0px !important;">
											<button class="btn btn-default pull-right" style="margin-bottom: 5px;"
												ng-click="HDC.getAssocDevices();"><i
													class="glyphicon glyphicon-refresh btn-sm-icon"></i> Atualizar
												lista</button>
											<img src="assets/images/ajax-loader.gif" class="pull-right"
												ng-show="HDC.atualizandoAssocDevices"
												style="margin: 6px 4px 0px 0px !important;">
										</td>
									</tr>
									<tr>
										<td colspan="2">
											<select class="form-control font9" name="internalDevice"
												id="frmredirecionamento_internalDevice"
												ng-model="HDC.selectedFrmRedirecionamentoDevice"
												ng-change="HDC.fillDeviceDetails('frmredirecionamento');">
												<option hidden selected value="">Selecione aqui ou preencha manualmente
													abaixo
												</option>
												<optgroup label="Dispositivos conectados (hostname - IP - MAC):">
													<option disabled ng-if="HDC.currentTr069Config.totalAssociatedDevices == 0"
														value="none">Não há dispositivos conectados</option>
													<option
														ng-repeat="(id, device) in HDC.currentTr069Config.associatedDevices"
														value="{{id}}">{{device.interface == 'ethernet' ? '(Cabo): ' :
														(device.interface == 'wlan' ? '(Wi-Fi): ' :
														'')}}{{device.hostname ?
														device.hostname :
														'[desconhecido]'}} - {{device.ip}} -
														{{device.mac}}
													</option>
												</optgroup>
											</select>
										</td>
									</tr>
									<tr>
										<td style="width: 50%; padding-right: 5px;">
											<label for="internalIP">IP interno:</label>
											<div class="input-group"><span class="input-group-addon">{{HDC.currentTr069Config.lanIpRangeStr}}</span>
												<input class="form-control font11" type="text"
													id="frmredirecionamento_internalIP"
													ng-model="HDC.redirecionamentoSelecionado.internalIP" maxlength="3">
											</div>
										</td>
										<td style="padding-left: 5px;">
											<label for="mac">MAC:</label>
											<input class="form-control align-center font11" type="text"
												id="frmredirecionamento_mac"
												ng-model="HDC.redirecionamentoSelecionado.mac" maxlength="17">
										</td>
									</tr>
									<tr>
										<td colspan="2">
											<label for="internalPort">Porta interna:</label>
											<input class="form-control align-center font11" type="text"
												id="frmredirecionamento_internalPort"
												ng-model="HDC.redirecionamentoSelecionado.internalPort" maxlength="5"
												style="width: 30%; margin: 0 auto;">
										</td>
									</tr>

									<tr>
										<td colspan="2" class="font9">
											* Você pode selecionar o dispositivo interno através da lista de
											dispositivos conectados (logo acima), ou pode preencher manualmente o IP e o
											MAC do
											dispositivo.
										</td>
									</tr>

								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<!-- Modal Footer -->
			<div class="modal-footer">
				<button class="btn btn-primary" ng-really-message="Deseja realmente salvar as alterações?"
					ng-really-click="HDC.editarRedirecionamento();"><i class="fa fa-check btn-icon"></i>Salvar</button>
			</div>
		</div>
	</div>