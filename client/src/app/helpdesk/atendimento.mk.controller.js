(function () {
    'use strict';

    angular
        .module('app')
        .controller('AtendimentoMkController', AtendimentoMkController);

    /** @ngInject */
    function AtendimentoMkController($http, API_CONFIG, $rootScope, Contrato) {

        var vm = this;

        vm.concentradores = [];
        vm.logs = [];
        vm.contrato = Contrato;
        vm.fila = [];
        vm.itemfila = -1;
        vm.busca = busca;
        vm.campo = 'usuario';
        vm.limpa = limpa;
        vm.atualizaLogs = atualizaLogs;
        vm.consultaAnm = consultaAnm;
        vm.mostraSenha = mostraSenha;
        vm.processa_fila = processa_fila;
        vm.editacdo = editacdo;
        vm.salvacdo = salvacdo;
        vm.cancelacdo = cancelacdo;
        vm.gravasinal = gravasinal;
        vm.getHistSinal = getHistSinal;
        vm.getFtthLogs = getFtthLogs;
        vm.getAutotesteTestes = getAutotesteTestes;
        vm.sinal = [];
        vm.ftthlogs = [];
        vm.estrutura = {};
        vm.getFila = getFila;
        vm.getContrato = getContrato;
        vm.getOnuInfo = getOnuInfo;
        vm.getPppoe = getPppoe;

        vm.senhaVisivel = false;
        vm.localizado = false;
        vm.atualizandolog = false;
        vm.atualizandoffthlogs = false;
        vm.atualizandofila = false;
        vm.atualizandocontrato = false;
        vm.atualizandoonu = false;
        vm.editandocdo = false;
        vm.buscainicial = false;
        vm.termos = '';
        vm.anm = '';
        vm.avisos = [];
        vm.debug = [];
        vm.processando_fila = false;
        vm.fila_processada = false;

        activate();

        function activate(){
            getConcentradores();
            getFila(vm.contrato.username);
            if(vm.contrato.tipohost == 'Porta GPON OLT' || vm.contrato.tipohost == 'FTTA'){
              getHistSinal();
              getFtthLogs();
              getOnuInfo(); 
            }
            
            getAutotesteTestes();
            atualizaLogs(vm.contrato.username);
        }

        function mostraSenha(){
          if(vm.senhaVisivel == true){
            vm.senhaVisivel = false;
          } else {
            vm.senhaVisivel = true;
          }
          
        }

        function getConcentradores(){
          $http({
            method: 'GET',
            url: API_CONFIG.url + '/concentrador/concentradores.json'
          }).then(function successCallback(response) {
             for (var i = 0; i < response.data.length; i++) { 
               vm.concentradores[i] = {
                 ip: response.data[i],
                 status: 'Ocioso',
                 localizado: ''
               };
              }
              getPppoe();
          }, function errorCallback(response) {

          });
       }

       function editacdo(cdo, cat, porta){
         vm.editandocdo = true;
         vm.estrutura.cdo = angular.copy(cdo);
          vm.estrutura.cat = angular.copy(cat);
          vm.estrutura.porta = angular.copy(porta);
       } 

      function cancelacdo(){
        vm.editandocdo = false;
      } 

       function salvacdo(){
        $http({
              url: API_CONFIG.url + '/ftth/estrutura',
              method: "POST",
              data: { 
                'username' : vm.contrato.username, 
                'cat' : vm.estrutura.cat, 
                'cdo' : vm.estrutura.cdo,
                'porta' : vm.estrutura.porta
              },
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.editandocdo = false;
           vm.contrato.cdo = angular.copy(vm.estrutura.cdo);
           vm.contrato.cat = angular.copy(vm.estrutura.cat);
           vm.contrato.porta = angular.copy(vm.estrutura.porta);
          },
          function(response) {
            vm.editandocdo = false;
            vm.contrato.cdo = angular.copy(vm.estrutura.cdo);
            vm.contrato.cat = angular.copy(vm.estrutura.cat);
            vm.contrato.porta = angular.copy(vm.estrutura.porta);
          });
        
      } 

      function gravasinal(){
        var comentario = prompt("Digite um comentário", "");
        if (comentario != null && comentario !== '') {
          $http({
            url: API_CONFIG.url + '/ftth/sinal',
            method: "POST",
            data: { 
              'username' : vm.contrato.username, 
              'rx' : vm.anm.sinal.RxPower, 
              'tx' : vm.anm.sinal.TxPower,
              'tx_bias' : vm.anm.sinal.CurrTxBias,
              'comentario' : comentario
            },
            ignoreLoadingBar: true
      })
      .then(function(response) {
         getHistSinal();
        },
        function(response) {
          getHistSinal();
        });
        }  
      }

      function atualizaLogs(){
         vm.atualizandolog = true;
         $http({
               url: API_CONFIG.url + '/concentrador/logs',
               method: "POST",
               data: { 'usuario' : vm.contrato.username},
               ignoreLoadingBar: true
         })
         .then(function(response) {
            vm.logs = response.data;
            vm.atualizandolog = false;
           },
           function(response) {
             vm.atualizandolog = false;
           });
       }

       function getHistSinal(){
        vm.sinal = []; 
        vm.atualizandosinal = true;
        $http({
              url: API_CONFIG.url + '/ftth/sinal?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.sinal = response.data;
           vm.atualizandosinal = false;
          },
          function(response) {
            vm.atualizandosinal = false;
          });
      }

      function getAutotesteTestes(){
        vm.testes = []; 
        vm.atualizandotestes = true;
        $http({
              url: API_CONFIG.url + '/autoteste/testes?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.testes = response.data;
           vm.atualizandotestes = false;
          },
          function(response) {
            vm.atualizandotestes = false;
          });
      }

      function getFtthLogs(){
        vm.ftthlogs = []; 
        vm.atualizandoftthlogs = true;
        $http({
              url: API_CONFIG.url + '/ftth/logs?username=' + vm.contrato.username,
              method: "GET",
              ignoreLoadingBar: true
        })
        .then(function(response) {
           vm.ftthlogs = response.data;
           vm.atualizandoftthlogs = false;
          },
          function(response) {
            vm.atualizandoftthlogs = false;
          });
      }

      function processa_fila(username){
        
         vm.processando_fila = true;
         vm.itemfila++;
         if(vm.itemfila < vm.fila.length){
         
           vm.fila[vm.itemfila].status = 'Processando';
           $http({
             url: API_CONFIG.url + '/ftth/fila',
             method: "POST",
             data: { 'idi' : vm.fila[vm.itemfila].idi, 'operador' : $rootScope.operador.username},
             ignoreLoadingBar: true
           })
           .then(function(response) {
             vm.fila[vm.itemfila].status = 'Finalizado';
             vm.fila[vm.itemfila].retornos = response.data.retornos;
             vm.fila[vm.itemfila].erros = response.data.erros;
             processa_fila(username);
           });
        } else{
          vm.processando_fila = false;  
          vm.fila_processada = true;
          vm.itemfila = -1;
        }
      }

      function getFila(username){
        vm.fila = [];
        vm.itemfila = -1;
        vm.fila_processada = false;
        vm.processando_fila = false;
        vm.atualizandofila = true;
        $http({
          url: API_CONFIG.url + '/anm/fila?username=' + username,
          method: "GET",
          ignoreLoadingBar: true
        })
        .then(function(response) {
          vm.fila = response.data;
          vm.atualizandofila = false;
        });
      }
      
      function getContrato(){
        /*
        if(vm.buscainicial){
          for (var i = 0; i < vm.concentradores.length; i++) {
            vm.concentradores[i].status = 'Ocioso'
            vm.concentradores[i].localizado = '';
            vm.concentradores[i].ip6 = '';
            vm.concentradores[i].bound_status = '';
          };
        }  
        vm.atualizandocontrato = true;
        if(vm.campo == 'usuario'){
          var data = {'usuario' : vm.termos}
        } else {
          var data = {'mac' : vm.termos}
        }

        $http({
          url: API_CONFIG.url + '/concentrador/contrato',
          method: "POST",
          data: data,
          ignoreLoadingBar: true
        })
        .then(function(response) {
          vm.contrato = response.data;
          vm.estrutura.cdo = angular.copy(vm.contrato.cdo);
          vm.estrutura.cat = angular.copy(vm.contrato.cat);
          vm.estrutura.porta = angular.copy(vm.contrato.porta);
          vm.atualizandocontrato = false;
          if(vm.buscainicial){
            getFila(vm.contrato.username);
            if(vm.contrato.tipohost == 'Porta GPON OLT' || vm.contrato.tipohost == 'FTTA'){
              getHistSinal();
              getFtthLogs();
              getOnuInfo(); 
            }
            getPppoe();
            getAutotesteTestes();
            atualizaLogs(vm.contrato.username);
            vm.buscainicial = false;
          }
        });
        */
      }
      
      function getOnuInfo(){
          consultaAnm(vm.contrato.serial, vm.contrato.escopo);  
      }

      function getPppoe(){
        vm.localizado = false;
        for (var i = 0; i < vm.concentradores.length; i++) {
          if(!vm.localizado){
            vm.concentradores[i].status = 'Pesquisando';
            vm.concentradores[i].localizado = '';
            vm.concentradores[i].ip6 = '';
            vm.concentradores[i].bound_status = '';
          }
        }
        
        for (var i = 0; i < vm.concentradores.length; i++) {
          if(!vm.localizado){             

            $http({
                  url: API_CONFIG.url + '/concentrador/global',
                  method: "POST",
                  data: { 'usuario' : vm.contrato.username, 'concentrador': vm.concentradores[i].ip},
                  ignoreLoadingBar: true
            })
            .then(function(response) {

                   if(response.data.status === 'OK'){
                        vm.localizado = true;
                        var result = vm.concentradores.filter(function( obj ) {
                          return obj.ip == response.data.concentrador;
                        });
                        result[0].localizado = response.data.ip;
                        result[0].ip6 = response.data.ip6;
                        result[0].bound_status = response.data.bound_status;
                        result[0].status = 'Localizado';

                   } else {
                     var result = vm.concentradores.filter(function( obj ) {
                       return obj.ip == response.data.concentrador;
                     });
                     result[0].status = 'Não localizado';
                     result[0].ip6 = '';
                     result[0].bound_status = '';
                   }
              },
              function(response) {

              });


           }
           }
      }


       function busca(campo, termos){
         /*
         vm.fila = [];
         vm.processando_fila = false;
         vm.localizado = false;
         vm.logs = [];
         vm.sinal = [];
         vm.avisos = [];
         vm.ftthlogs = [];
         vm.contrato= {};
         vm.senhaVisivel = false;
         vm.buscainicial = true;
         vm.anm = '';
         getContrato();
         
        */

        }

        function limpa(){
          vm.termos = '';
          vm.campo = 'usuario';
          vm.localizado = false;
          vm.logs = [];
          vm.sinal = [];
          vm.ftthlogs = [];
          vm.contrato= {};
          vm.senhaVisivel = false;
          for (var i = 0; i < vm.concentradores.length; i++) {
            vm.concentradores[i].status = 'Ocioso'
            vm.concentradores[i].localizado = '';
            vm.concentradores[i].ip6 = '';
            vm.concentradores[i].bound_status = '';
          };
        }

        function consultaAnm(serial, servico){
          vm.atualizandoonu = true;
          vm.anm = '';
          if(serial !== ''){
            $http({
              url: API_CONFIG.url + '/anm',
              method: "POST",
              data: { 
                'service' : servico,
                'onu_id'  : serial
              },
              ignoreLoadingBar: true
            })
            .then(function(response) {
              vm.anm = response.data;
              vm.atualizandoonu = false;
            },
            function(response) {

            });  

          }
    }
  }

})();
