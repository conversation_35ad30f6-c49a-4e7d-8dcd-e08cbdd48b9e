<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
    <li><a href="/helpdesk/atendimento"><i class="glyphicon glyphicon-warning-sign"></i> Helpdesk</a></li>
</ol>

  <div class="barra ng-scope">
    <div class="form-group">
     <div class="form-group pull-left">
        <form class="form-inline" role="form">
                <div class="form-group">
                    <select class="form-control" ng-model="AC.campo">
                        <option value="usuario">Usuário</option>
                        <option value="mac">MAC</option>  
                    </select>
                </div>
                <div class="form-group">
                <input size="40" maxlength="40" class="form-control" type="text" ng-model="AC.termos">

                <button class="btn btn-default" title="Pesquisar" ng-click="AC.busca(AC.campo, AC.termos)" ng-disabled="AC.termos == '' || AC.atualizandocontrato"><i class="glyphicon glyphicon-search" ng-if="!AC.atualizandocontrato"></i><img src="assets/images/ajax-loader.gif" ng-show="AC.atualizandocontrato"> Pesquisar</button>
                <button class="btn btn-default" ng-click="AC.limpa()" ng-disabled="AC.termos == ''">
                                    <span class="glyphicon glyphicon-refresh"></span> Limpar
                                </button>
                </div>
              </form>
              
        </div>
</div>
</div>
<div class="alert alert-danger" role="alert" ng-if="AC.localizado==false">
  <span class="glyphicon glyphicon-ok"></span> 
  <strong>Não encontrado</strong>
  <hr class="message-inner-separator">
  <p>Usuário não localizado</p>
</div>

<div ng-include="'app/helpdesk/atendimento.mk.html'" ng-controller="AtendimentoMkController as AMC" ng-if="AC.localizado==true && AC.contrato.nasmarca!=='JUNIPER'"></div>
<div ng-include="'app/helpdesk/atendimento.juniper.html'" ng-controller="AtendimentoJuniperController as AJC" ng-if="AC.localizado==true && AC.contrato.nasmarca=='JUNIPER'"></div>