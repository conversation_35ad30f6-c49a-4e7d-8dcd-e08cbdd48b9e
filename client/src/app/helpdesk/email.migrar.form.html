<div class="modal" id="frmmigraremail" tabindex="-1" role="dialog" aria-labelledby="frmmigraremail" aria-hidden="true"
	modal="showModal" close="cancel()" style="z-index: 1045;">

	<div class="modal-dialog modal-md">
		<div class="modal-content">

			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" id="frmmigraremail_fechar">
					<span aria-hidden="true">&times;</span>
					<span class="sr-only">Fechar</span>
				</button>
				<h4 class="modal-title">
					Migrar e-mail para outro contrato
				</h4>
			</div>

			<!-- Modal Body -->
			<div class="modal-body" style="padding-bottom: 15px;">
				<div class="row">
					<div style="text-align: center; font-size: 10pt;">
						Migrando e-mail:
						<div style="max-width: 318px; border: 1px solid #000; border-radius: 3px; padding: 5px 10px;margin: 0 auto; margin-top: 5px;">
							<b>{{ELC.migracao.email}}</b>
						</div>

						<label class="pointer" for="contrato_nova_senha" style="margin-top: 10px;">
							<input type="checkbox" id="contrato_nova_senha" ng-model="ELC.migracao.contratoNovo.gerar_nova_senha">
							Gerar uma nova senha ao migrar
						</label>

						<div ng-if="ELC.migracao.contratoNovo.gerar_nova_senha"
							style="max-width: 180px; border: 1px solid #333; border-radius: 5px; font-weight: bold; margin: 0 auto;">
							<span style="font-size: 9pt;">Nova senha:</span>
							<br>
							<span style="font-size: 13pt; letter-spacing: 3px; ">{{ELC.migracao.contratoNovo.nova_senha}}</span>
						</div>
					<div class="contrato-data-table-container" style="border-color: #888;">
						<table class="table table-bordered table-sm contrato-data-table">
							<tr>
								<td colspan="2" style="font-size: 11pt; text-align: center; background: #CCC;">
									<b>Contrato atual</b>
								</td>
							</tr>
							<tr>
								<td><b>ID do contrato</b>:</td>
								<td style="text-align: center;">#<b>{{ELC.migracao.contratoAtual.id_contrato}}</b></td>
							</tr>
							<tr>
								<td><b>Cliente</b>:</td>
								<td>{{ELC.cliente_nome}}</td>
							</tr>
							<tr>
								<td><b>CPF/CNPJ</b>:</td>
								<td>{{ELC.cnpj_cpf}}</td>
							</tr>
							<tr>
								<td><b>Login</b>:</td>
								<td>{{ELC.migracao.contratoAtual.logins[0]}}</td>
							</tr>
							<tr>
								<td><b>Endereço</b>:</td>
								<td>{{ELC.migracao.contratoAtual.endereco}}{{ELC.migracao.contratoAtual.endereco.trim() !== '' && ELC.migracao.contratoAtual.numero.trim() !== '' ? ', ' : ''}}{{ELC.migracao.contratoAtual.numero}}</td>
							</tr>
						</table>
					</div>

					<div class="horizontal-divider" style="margin: 0 auto; margin-top: 20px; width: 90%;"></div>

					<div class="contrato-data-table-container" style="border-color: #379E18; margin-bottom: 10px;">
						<table class="table table-bordered table-sm contrato-data-table">
							<tr>
								<td colspan="2" style="font-size: 11pt; text-align: center; background: #379E18; color: #FFF;">
									<b>Novo contrato</b>
								</td>
							</tr>
							<tr>
								<td><b>ID do contrato</b>:</td>
								<td>
									<div class="input-group">
										<span class="input-group-addon">#</span>
										<input class="form-control" id="novo_contrato_id" ng-model="ELC.migracao.contratoNovo.id" style="height: 30px; text-align: center; font-size: 11pt; font-weight: bold;">
										<span class="input-group-addon">
											<button class="btn btn-sm" ng-click="ELC.getNovoContrato();"><i class="glyphicon glyphicon-search"></i></button>
										</span>
									</div>
								</td>
							</tr>
							<tr>
								<td><b>Cliente</b>:</td>
								<td>{{ELC.migracao.contratoNovo.cliente_nome}}</td>
							</tr>
							<tr>
								<td><b>CPF/CNPJ</b>:</td>
								<td>{{ELC.migracao.contratoNovo.cnpj_cpf}}</td>
							</tr>
							<tr>
								<td><b>Login</b>:</td>
								<td>{{ELC.migracao.contratoNovo.login}}</td>
							</tr>
							<tr>
								<td><b>Endereço</b>:</td>
								<td>{{ELC.migracao.contratoNovo.endereco}}{{ELC.migracao.contratoNovo.endereco.trim() !== '' && ELC.migracao.contratoNovo.numero.trim() !== '' ? ', ' : ''}}{{ELC.migracao.contratoNovo.numero}}</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
			<!-- Modal Footer -->
			<div class="modal-footer">
				<button ng-disabled="!ELC.migracao.contratoNovo.loaded" class="btn btn-primary" ng-really-message="Deseja realmente migrar o e-mail <b>{{ELC.migracao.email}}</b>" ng-really-click="ELC.migrar();"><i class="fa fa-check btn-icon"></i>Executar migração</button>
			</div>
		</div>
	</div>