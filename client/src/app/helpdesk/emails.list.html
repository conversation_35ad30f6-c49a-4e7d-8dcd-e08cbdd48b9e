<!-- <div ng-include="'app/basicos/navbar.html'"></div> -->
<ol class="breadcrumb">

	<li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
	<li><a href="/helpdesk"><i class="glyphicon glyphicon-edit"></i> Helpdesk</a></li>
	<li><i class="glyphicon glyphicon-envelope"></i> Gerenciar emails</li>
</ol>
<div class="table barra align-center" style="padding-top: 7px; margin-top: 15px;">
	<div class="tr">
		<div class="td pull-left">
			<div class="form-group">
				<form ng-submit="ELC.getEmailsList({})">
					<div class="form-inline" role="form">
						Pesquisar cliente por:
						<select class="form-control" ng-model="ELC.campo" onChange="$('#termo_pesquisa').focus();">
							<option value="cnpj_cpf">CPF/CNPJ</option>
							<option value="login">Login</option>
							<option value="ie_identidade">RG/I.E.</option>
							<option value="id">Código do cliente</option>
							<option value="id_contrato">Número do contrato</option>
							<option value="email">Endereço de e-mail</option>
						</select>
						<input type="text" class="form-control" id="termo_pesquisa" placeholder="Pesquisar..."
							ng-model="ELC.termo" style="width: 180px;">
						<button type="submit" class="btn btn-default" title="Pesquisar"
							ng-disabled="ELC.pesquisandoCliente">
							<!-- ngIf: !ELC.pesquisandoCliente --><i class="glyphicon glyphicon-search ng-scope"
								ng-if="!ELC.pesquisandoCliente" style="margin-right: 3px;"></i>
							<!-- end ngIf: !ELC.pesquisandoCliente --><img src="assets/images/ajax-loader.gif"
								ng-show="ELC.pesquisandoCliente" class="ng-hide">
							Pesquisar
						</button>
				</form>
			</div>
		</div>
	</div>
	<div class="td pull-left" style="vertical-align: middle; text-align: left;">
		<div style="font-size: 12pt; padding: 5px 0px; padding-left: 32px; font-weight: bold;">
			{{(ELC.atualizandoEmails) ? 'Carregando... ' : ((ELC.cliente_nome == null) ? 'Selecione um cliente...' :
			ELC.id_cliente + ' - ' + ELC.cliente_nome)}}
		</div>
	</div>
	<div class="td pull-right">
		<img src="assets/images/ajax-loader.gif" ng-show="ELC.atualizandoEmails" class="ng-hide" style="">
		<button class="btn btn-default btn-sm" ng-disabled="ELC.id_cliente == null" ng-click="ELC.atualizarEmails();"><i
				class="glyphicon glyphicon-refresh" style="margin-right: 3px;"></i>
			Atualizar
		</button>
	</div>
</div>
</div>

<div class="barra">
	<div class="table" style="width: 500px; min-height: 53px;">
		<div class="tr" ng-repeat="group in ELC.contratos_grupos">
			<div class="td valign-middle"
				style="font-size: 11pt; width: 100px; padding-left: 32px; padding-right: 20px; vertical-align: middle;">
				<span ng-if="$index == 0"><b>Contrato:</b></span>
			</div>
			<div ng-repeat="contrato in group" class="td valign-middle" style="padding: 8px 0px;">
				<div class="contrato-item align-center pointer"
					ng-class="{'selecionado': contrato.id_contrato==ELC.contratoSelecionado.id_contrato}"
					ng-click="ELC.selecionarContrato(contrato);" id="contrato-item-{{contrato.id_contrato}}">
					<b>#{{contrato.id_contrato}} - {{contrato.logins[0]}}</b>
					<div ng-if="contrato.status != 'P'">
						{{contrato.endereco}}, {{contrato.numero}}
						<br />
						{{contrato.bairro}} - {{contrato.cidade | uppercase}}
					</div>
					<div ng-if="contrato.status == 'P'">
						<br />
						<b>[PRÉ CONTRATO]</b>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="warning-row" ng-show="!ELC.atualizandoEmails" ng-if="ELC.id_cliente == null && ELC.contratos.length == 0">
	Ainda não foi selecionado um cliente...
</div>

<div class="warning-row" ng-show="!ELC.atualizandoEmails" ng-if="ELC.id_cliente != null && ELC.contratos.length < 1">
	Este cliente ainda não possui contratos.
</div>

<div class="warning-row" ng-show="!ELC.atualizandoEmails"
	ng-if="ELC.contratos.length > 0 && ELC.contratoSelecionado != null && (ELC.contratoSelecionado.emails == null || ELC.contratoSelecionado.emails == '' ||(ELC.contratoSelecionado.emails | json) == '{}')&& ELC.contratoSelecionado.quantidade_maxima_emails > 0">
	Este contrato ainda não possui e-mails cadastrados.
</div>

<div class="warning-row" ng-show="!ELC.atualizandoEmails"
	ng-if="ELC.contratos.length > 0 && ELC.contratoSelecionado != null && (ELC.contratoSelecionado.emails == null || ELC.contratoSelecionado.emails == '' ||(ELC.contratoSelecionado.emails | json) == '{}') && ELC.contratoSelecionado.quantidade_maxima_emails == 0">
	Este contrato não possui um pacote de e-mails.
</div>

<div class="warning-row" ng-show="!ELC.atualizandoEmails"
	ng-if="ELC.contratos.length > 0 && ELC.contratoSelecionado == null">
	Selecione um contrato...
</div>

<div class="warning-row" ng-show="ELC.atualizandoEmails">
	Carregando...
</div>

<div ng-if="ELC.contratos.length > 0 && ELC.contratoSelecionado.emails.length > 0" ng-show="!ELC.atualizandoEmails"
	class="table-responsive">
	<span class="counter pull-right"></span>
	<table class="table table-striped table-bordered valign-middle align-center">
		<thead>
			<tr>
				<th colspan="7" style="font-size: 10pt;">E-mails do contrato:</th>
			</tr>
			<tr>
				<th>Status</th>
				<th>Endereço</th>
				<th>Nova senha</th>
				<th>Espaço utilizado</th>
				<th>Enviadas hoje</th>
				<th>Antispam</th>
				<th>Último login</th>
				<th>Migrar</th>
				<th>Excluir</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-repeat="email in ELC.contratoSelecionado.emails">
				<td><span class="badge">{{email.active == 0 ? 'Inativo' : 'Ativo'}}</span></td>
				<td>{{email.mailadd}}</td>
				<td ng-class="{'shown-password-td': email.password.length == 6}">
					<span ng-if="email.password.length == 6" style="font-size: 10pt; letter-spacing: 2px;">
						<b>{{email.password}}</b>
					</span>
					<button class="btn btn-sm btn-primary"
						ng-really-message="Deseja realmente gerar uma nova senha para o e-mail <b>{{email.mailadd}}</b>?"
						ng-really-click="ELC.resetarSenha(email.id);" title="Gerar nova senha">
						<i class="glyphicon glyphicon-retweet" ng-class="{'btn-icon': email.password.length != 6}"></i>
						<span ng-if="email.password.length != 6">
							Gerar
						</span>
					</button>
				</td>
				<td>{{(email.quota_used/1024/1024/1024).toFixed(2) + 'GB'}} de {{(email.quota/1024/1024/1024).toFixed(2) + 'GB'}}
					({{(email.quota_used/(email.quota/100)).toFixed(2)}}%)
				</td>
				<td>{{email.enviadas_hoje}} de {{email.msgdia}}</td>
				<td><span class="badge">{{email.antispam == 0 ? 'Inativo' : 'Ativo'}}</span></td>
				<td>{{email.lastlogin ? ELC.formatTimestamp(email.lastlogin) : ''}}</td>
				<td>
					<button class="btn btn-sm btn-primary" title="Migrar e-mail para outro contrato" data-toggle="modal"
						data-target="#frmmigraremail" ng-click="ELC.initFrmMigrarEmail(email.mailadd);">
						<i class="glyphicon glyphicon-share-alt"></i>
					</button>
				</td>
				<td>
					<button class="btn btn-sm btn-danger" title="Excluir e-mail"
						ng-really-message="Tem certeza de que deseja excluir o e-mail <b>{{email.mailadd}}</b>?"
						ng-really-click="ELC.excluir(email.id);">
						<i class="glyphicon glyphicon-trash"></i>
					</button>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="barra align-center" style="min-height: 0px; padding: 10px 0px;">
	<div ng-if="ELC.contratos.length > 0 && ELC.contratoSelecionado != null"
		style="margin-bottom: 10px; font-size: 10pt;">
		<span ng-show="!ELC.atualizandoEmails">Total de e-mails:
			<b>
				{{ELC.contratoSelecionado.emails && ELC.contratoSelecionado.emails.length>0 ?
				ELC.contratoSelecionado.emails.length :
				'0'}}/{{ELC.contratoSelecionado.quantidade_maxima_emails}}
			</b>
		</span>
	</div>

	<button ng-disabled="ELC.contratoSelecionado == null || ((ELC.contratoSelecionado.emails && ELC.contratoSelecionado.emails.length>1 ?
			ELC.contratoSelecionado.emails.length :
			0) >= ELC.contratoSelecionado.quantidade_maxima_emails) || ELC.contratoSelecionado.status == 'D' || ELC.contratoSelecionado.status == 'I'" class="btn btn-primary" ng-click="ELC.initFrmNovoEmail();"><i
			class="glyphicon glyphicon-plus btn-icon"></i>Adicionar e-mail</button>
	<br>
	<br>
	<p ng-if="ELC.contratoSelecionado.status == 'D' || ELC.contratoSelecionado.status == 'I'" class="text-danger">
		O contrato não se encontra ativo.
		<br>
		Não é possível adicionar contas de e-mail.
	</p>
</div>

<div ng-include="'app/helpdesk/email.novo.form.html'"></div>
<div ng-include="'app/helpdesk/email.migrar.form.html'"></div>