'use strict';

angular.module('app')

    .factory('appSocket', function (socketFactory, API_CONFIG) {
        return socketFactory({
            ioSocket: io.connect(API_CONFIG.netconfws, { secure: true })
        });
    })

    .factory('nocwsSocket', function (socketFactory, API_CONFIG) {
        return socketFactory({
            ioSocket: io.connect(API_CONFIG.nocws, { secure: true, transports: ['websocket'] })
        });
    })

    .factory('helpdeskSocket', function (socketFactory, API_CONFIG) {
        return socketFactory({
            ioSocket: io.connect(API_CONFIG.helpdeskws, { secure: true, transports: ['websocket'] })
        });
    })

    .factory('pingwsSocket', function (socketFactory, API_CONFIG) {
        return socketFactory({
            ioSocket: io.connect(API_CONFIG.pingws, { secure: true, transports: ['websocket'] })
        });
    })

    .factory('authwsSocket', function (socketFactory, API_CONFIG) {
        return socketFactory({
            ioSocket: io.connect(API_CONFIG.authws, { secure: true, transports: ['websocket'] })
        });
    })

    .factory('AtendimentoService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.api_juniper_url, null,
            {
                ping: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario/ping',
                    params: { 'usuario': '@usuario' },
                    headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
                    transformRequest: function (param) {
                        return $.param(param);
                    },
                    method: 'POST',
                    ignoreLoadingBar: true
                },
                wan: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario',
                    params: { 'usuario': '@usuario' },
                    headers: {
                        'Authorization': 'Basic ' + API_CONFIG.api_juniper_token
                    },
                    method: 'GET',
                    ignoreLoadingBar: true
                },
                interface: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario/interface',
                    params: { 'usuario': '@usuario' },
                    headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
                    method: 'GET',
                    ignoreLoadingBar: true
                },
                torchpost: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario/traffic',
                    params: { 'usuario': '@usuario' },
                    headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
                    method: 'POST',
                    ignoreLoadingBar: true
                },
                torchget: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario/traffic',
                    params: { 'usuario': '@usuario' },
                    headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
                    method: 'GET',
                    ignoreLoadingBar: true,
                    isArray: false
                },
                torchcancel: {
                    url: API_CONFIG.api_juniper_url + '/pppoe-users/:usuario/torch_cancel',
                    params: { 'usuario': '@usuario' },
                    headers: { 'Authorization': 'Basic ' + API_CONFIG.api_juniper_token },
                    method: 'POST',
                    ignoreLoadingBar: true,
                    isArray: false
                }
            }
        );
    })

    .service('APICalls', function () {
        return {
            pingIntervalPromise: null,
            torchIntervalPromise: null,
            trafegoIntervalPromise: null
        };
    })


    .service('Contrato', function () {
        return {};
    });