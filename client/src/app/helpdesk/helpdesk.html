<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
  <li><a href="/helpdesk"><i class="glyphicon glyphicon-warning-sign"></i> Helpdesk</a></li>
</ol>

<div class="barra ng-scope">

  <div class="table">
    <div class="tr">
      <div class="td">
        <div class="form-group">
          <div class="form-group pull-left">
            <form class="form-inline" role="form">
              <div class="form-group">
                <select class="form-control" ng-model="HDC.campo">
                  <option value="usuario">Usuário</option>
                  <option value="mac">MAC</option>
                </select>
              </div>
              <div class="form-group">
                <input size="40" maxlength="40" class="form-control" type="text" ng-model="HDC.termos"
                  id="input__helpdesk__search_contrato">

                <button class="btn btn-default" title="Pesquisar" ng-click="HDC.getContrato()"
                  ng-disabled="HDC.termos == '' || HDC.atualizandocontrato"><i class="glyphicon glyphicon-search"
                    ng-if="!HDC.atualizandocontrato"></i><img src="assets/images/ajax-loader.gif"
                    ng-show="HDC.atualizandocontrato"> Pesquisar</button>
                <button class="btn btn-default" ng-click="HDC.limpa()" ng-disabled="HDC.termos == ''">
                  <span class="glyphicon glyphicon-refresh"></span> Limpar
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="td align-right valign-middle">
        <div class="form-group" style="margin: 0px;">
          <button class="btn btn-primary" ng-disabled="!HDC.contrato.hasOwnProperty('nomeassinante')"
            ng-click="HDC.gerenciarEmails();">
            <i class="glyphicon glyphicon-envelope"></i>
            Gerenciar e-mails
          </button>
        </div>
      </div>
    </div>
  </div>
</div>



<div class="alert alert-danger" role="alert" ng-if="HDC.localizado==false">
  <span class="glyphicon glyphicon-ok"></span>
  <strong>Não encontrado</strong>
  <hr class="message-inner-separator">
  <p>Usuário não localizado</p>
</div>
<div ng-if="HDC.contrato.hasOwnProperty('nomeassinante')" class="hd-alerts-container">
  <div class="hd-alert"
    ng-class="[{'bg-success-dark': HDC.reconexoes < 3}, {'bg-warning-dark': HDC.reconexoes >= 3 && HDC.reconexoes <= 5}, {'bg-danger-dark': HDC.reconexoes > 5}]">
    Reconexões nas últimas 24h:
    <br>
    <span class="hd-alert-data">
      {{HDC.reconexoes}}
    </span>
  </div>
</div>
<!-- ONUS NÃO AUTORIZADAS -->

<div class="panel panel-default" style="margin-bottom: 0px">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoOnusNaoAutorizadas"> <button
        class="btn btn-default btn-sm" ng-click="HDC.atualizaOnusNaoAutorizadas()"><i
          class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><strong>ONUs Não Autorizadas</strong></h3>
  </div>
  <div class="panel-body">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">OLT</th>
          <th class="vert-align text-center">IP OLT</th>
          <th class="vert-align text-center">Placa</th>
          <th class="vert-align text-center">Porta</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">Username</th>
          <th class="vert-align text-center">OLT (Interfocus)</th>
          <th class="vert-align text-center">Placa (Interfocus)</th>
          <th class="vert-align text-center">Porta (Interfocus)</th>
        </tr>
      </thead>
      <tbody>

        <tr ng-repeat="onu in HDC.onusNaoAutorizadas">
          <td class="vert-align text-center">{{onu.data_alarme | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</span></td>
          <td class="vert-align text-center">{{onu.olt}}</span></td>
          <td class="vert-align text-center">{{onu.olt_ip}}</span></td>
          <td class="vert-align text-center">{{onu.placa}}</td>
          <td class="vert-align text-center">{{onu.porta}}</td>
          <td class="vert-align text-center">{{onu.serial}}</td>
          <td class="vert-align text-center">{{onu.mac}}</td>
          <td class="vert-align text-center">{{onu.patrimonio}}</td>
          <td class="vert-align text-center">{{onu.modelo}}</td>
          <td class="vert-align text-center">{{onu.username}}</td>
          <td class="vert-align text-center">{{onu.if_olt}}</td>
          <td class="vert-align text-center">{{onu.if_placa}}</td>
          <td class="vert-align text-center">{{onu.if_porta}}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- FIM ONUS NÃO AUTORIZADAS-->



<!-- ALERTAS DE REDE -->

<div class="alert alert-danger" ng-repeat="alerta in HDC.alertas" role="alert"><span
    class="glyphicon glyphicon-remove"></span> <strong>{{alerta.titulo}}</strong>
  <div class="pre-scrollable" style="height:150px;">
    <pre class="alert alert-danger" style="border: none;">{{alerta.mensagem}}</pre>
  </div>
</div>

<!-- FIM ALERTAS DE REDE -->
<!-- CONTRATO -->

<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandocontrato"> <button class="btn btn-default btn-sm"
        ng-click="HDC.getContrato(HDC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><strong>Contrato</strong></h3>
  </div>
  <div class="panel-body">
    <table class="table table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Cliente</th>
          <th class="vert-align text-center">Contrato</th>
          <th class="vert-align text-center">Serviço</th>
          <th class="vert-align text-center">Tipo Serviço</th>
          <th class="vert-align text-center">Nome Plano</th>
          <th class="vert-align text-center">Plano Down</th>
          <th class="vert-align text-center">Plano Up</th>
          <th class="vert-align text-center">Senha</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="vert-align text-center">{{HDC.contrato.nomeassinante}}</td>
          <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-success': HDC.contrato.situacaocontrato == 'Conectado/Ativo'},
                {'label-danger': HDC.contrato.situacaocontrato == 'Inadimplente' || HDC.contrato.situacaocontrato == 'Cancelado'},
                {'label-warning': HDC.contrato.situacaocontrato == 'Conectado/Inadimplente' || HDC.contrato.situacaocontrato == 'Pausado' ||
              HDC.contrato.situacaocontrato == 'Aguardando Conexão'}]">{{HDC.contrato.situacaocontrato}}</span></td>
          <td class="vert-align text-center"><span
              ng-if="HDC.contrato.tipohost!=='Radio AP 5G - ACL Radius'">{{HDC.contrato.escopo}}</span>
            <a ng-if="HDC.contrato.tipohost=='Radio AP 5G - ACL Radius'" href="http://{{HDC.contrato.ipadmin}}:8922"
              target="blank_">{{HDC.contrato.escopo}}</a>
          </td>
          <td class="vert-align text-center">{{HDC.contrato.tipohost}}</td>
          <td class="vert-align text-center">{{HDC.contrato.plano}}</td>
          <td class="vert-align text-center">{{HDC.contrato.down}}</td>
          <td class="vert-align text-center">{{HDC.contrato.up}}</td>
          <td class="vert-align text-center"><span class="label label-default" style="cursor: pointer;"
              ng-click="HDC.mostraSenha()"
              ng-if="HDC.contrato.senha !== '' && HDC.contrato.senha !== undefined && !HDC.senhaVisivel">Mostrar
              Senha</span><span ng-if="HDC.senhaVisivel==true">{{HDC.contrato.senha}} <span class="label label-danger"
                style="cursor: pointer;" ng-click="HDC.mostraSenha()">x</span></span></td>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-4">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="vert-align text-center">Pacote</th>
              <th class="vert-align text-center">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="pacote in HDC.contrato.pacotes">
              <td class="vert-align text-center">{{pacote.nomepacote}}</td>
              <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-success': pacote.situacaopacote == 'Ativo'},
                    {'label-danger': pacote.situacaopacote == 'Inativo'}]">{{pacote.situacaopacote}}</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="alert alert-success" role="alert"
      ng-if="HDC.contrato.situacaocontrato == 'Conectado/Ativo' || HDC.contrato.situacaocontrato == 'Conectado/Inadimplente'">
      <span class="glyphicon glyphicon-ok"></span> <strong>Resultado do checklist</strong>
      <hr class="message-inner-separator">
      <p>Não há pendências no contrato</p>
    </div>

    <div class="alert alert-danger" role="alert" ng-if="HDC.contrato.situacaocontrato == 'Inadimplente'"><span
        class="glyphicon glyphicon-remove"></span> <strong>Resultado do checklist</strong>
      <hr class="message-inner-separator">
      <p>O contrato do cliente encontra-se <strong>{{HDC.contrato.situacaocontrato}}</strong>. Nenhum serviço estará
        disponível até a regularização do contrato.</p>
    </div>
    <div class="alert alert-warning" role="alert" ng-if="HDC.contrato.situacaocontrato == 'Aguardando Conexão'"><span
        class="glyphicon glyphicon-info"></span> <strong>Resultado do checklist</strong>
      <hr class="message-inner-separator">
      <p>O contrato do cliente está OK. Pode ocorrer do serviço de ativação ainda não ter sido realizado ou a ordem de
        serviço não foi atualizada no sistema</p>
    </div>

  </div>
</div>
<!-- FIM CONTRATO-->


<!-- PROVISIONAMENTO -->
<div class="panel panel-primary" ng-if="HDC.fila.length > 0 && HDC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandofila"> <button class="btn btn-default btn-sm"
        ng-click="HDC.getFila(HDC.contrato.username); HDC.getRoutersAlocados(HDC.contrato.username);"><i
          class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><strong>Provisionamento de equipamento pendente</strong></h3>
  </div>
  <div class="panel-body">

    <div class="container alocar-router">
      <div class="barra table no-margin" style="min-height: 0px;">
        <div class="tr">
          <div class="td valign-middle align-right half-width">
            <h5>Alocar novo roteador com TR069:</h5>
          </div>
          <div class="td centered half-width">
            <div class="form-group pull-right no-margin">
              <form class="form-inline" role="form">
                <div class="form-group">
                  <select class="form-control" ng-model="HDC.router_pesquisa.filtro"
                    ng-init="HDC.router_pesquisa.filtro = 'patrimonio'">
                    <option value="patrimonio">Patrimonio</option>
                    <option value="mac">MAC</option>
                    <option value="serial">Serial</option>
                  </select>
                </div>
                <div class="form-group">
                  <input size="30" maxlength="30" class="form-control" type="text"
                    ng-model="HDC.router_pesquisa.termos">
                  <button class="btn btn-default" title="Pesquisar" ng-click="HDC.getRoutersPesquisa()"><i
                      class="glyphicon glyphicon-search btn-sm-icon" ng-if="!HDC.atualizandoRoutersPesquisa"></i><img
                      class="btn-sm-icon" src="assets/images/ajax-loader.gif"
                      ng-show="HDC.atualizandoRoutersPesquisa">Pesquisar</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <table class="table table-bordered no-margin" style="margin-bottom: 10px !important;">
        <thead ng-if="HDC.routers_pesquisa.length > 0">
          <tr>
            <th class="centered">Patrimônio</th>
            <th class="centered">MAC</th>
            <th class="centered">Serial</th>
            <th class="centered">Marca</th>
            <th class="centered">Modelo</th>
            <th class="centered" style="width: 8%;">Alocado</th>
            <th style="width: 6%;">
            </th>
          </tr>
        </thead>
        <tbody>
          <tr ng-if="HDC.routers_pesquisa.length < 1 && !HDC.routers_pesquisa_done">
            <td class="centered" colspan="6" style="padding: 4px;">
              <b>Faça uma busca acima</b>
            </td>
          </tr>
          <tr ng-if="HDC.routers_pesquisa.length < 1 && HDC.routers_pesquisa_done">
            <td class="centered" colspan="6" style="padding: 4px;">
              <b>A busca não encontrou resultados</b>
            </td>
          </tr>
          <tr ng-repeat="router in HDC.routers_pesquisa">
            <td class="centered">
              {{router.patrimonio}}
            </td>
            <td class="centered">
              {{router.mac}}
            </td>
            <td class="centered">
              {{router.serial}}
            </td>
            <td class="centered">
              {{router.marca}}
            </td>
            <td class="centered">
              {{router.modelo}}
            </td>
            <td class="centered">
              <span class="label label-default"
                ng-class="{'label-danger': router.username.trim().length > 0, 'label-success': router.username.trim().length === 0}">{{router.username.trim().length
                > 0 ? router.username : 'NÃO'}}</span>
            </td>
            <td class="centered">
              <button class="btn btn-vsm btn-success" ng-disabled="router.username.trim().length > 0"
                ng-class="{'disabled btn-danger': router.username.trim().length > 0}" title="Alocar roteador ao cliente"
                ng-really-message="Tem certeza de que deseja <b>alocar</b> este roteador a este cliente?"
                ng-really-click="HDC.alocarRouter(router.patrimonio, router.username)"><i
                  class="glyphicon glyphicon-arrow-down btn-sm-icon"></i>Alocar</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="barra table no-margin" style="min-height: 0px;">
        <div class="tr">
          <div class="td centered" style="padding-right: 147px;">
            <h5>Roteadores com TR069 já alocados ao cliente:</h5>
          </div>
        </div>
      </div>
      <table class="table table-bordered no-margin">
        <thead ng-if="HDC.routers_alocados.length > 0">
          <tr>
            <th class="centered">Patrimônio</th>
            <th class="centered">MAC</th>
            <th class="centered">Serial</th>
            <th class="centered">Marca</th>
            <th class="centered">Modelo</th>
            <th style="width: 5%;">
            </th>
          </tr>
        </thead>
        <tbody>
          <tr ng-if="HDC.routers_alocados.length < 1">
            <td class="centered" colspan="6" style="padding: 4px;">
              <b>Não há roteadores com TR069 alocados para este cliente</b>
            </td>
          </tr>
          <tr ng-repeat="router in HDC.routers_alocados">
            <td class="centered">
              {{router.patrimonio}}
            </td>
            <td class="centered">
              {{router.mac}}
            </td>
            <td class="centered">
              {{router.serial}}
            </td>
            <td class="centered">
              {{router.marca}}
            </td>
            <td class="centered">
              {{router.modelo}}
            </td>
            <td class="centered">
              <button class="btn btn-vsm btn-danger" title="Desalocar roteador do cliente"
                ng-really-message="Tem certeza de que deseja <b>desalocar</b> este roteador deste cliente?"
                ng-really-click="HDC.desalocarRouter(router.patrimonio)"><i
                  class="glyphicon glyphicon-remove"></i></button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <table class="table table-bordered" ng-if="HDC.fila.length > 0">
      <thead>
        <tr>
          <th class="vert-align text-center">Status</th>
          <th class="vert-align text-center">Data/Hora</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Sit. Equipamento</th>
          <th class="vert-align text-center">Escopo</th>
          <th class="vert-align text-center">Wifi</th>
          <th class="vert-align text-center">IPTV</th>
          <th class="vert-align text-center">STB</th>
          <th class="vert-align text-center">Erros</th>
          <th class="vert-align text-center">Retorno</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="fila in HDC.fila">
          <td class="vert-align text-center"><span class="label label-default"
              ng-class="[{'label-default': fila.status == 'Na Fila'}, {'label-warning': fila.status == 'Processando'}, {'label-danger': fila.erros > 0}, {'label-success': fila.status == 'Finalizado' && fila.erros == 0}]">{{fila.status}}</span>
          </td>
          <td class="vert-align text-center">{{fila.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center">{{fila.mac}}</td>
          <td class="vert-align text-center">{{fila.modelo}}</td>
          <td class="vert-align text-center">{{fila.serial}}</td>
          <td class="vert-align text-center"><a href="/materiais/patrimonios/{{fila.patrimonio}}"
              target="_blank">{{fila.patrimonio}}</a></td>
          <td class="vert-align text-center">
            <span class="label label-danger" ng-if="fila.situacaoequipamento==0">Desativado</span>
            <span class="label label-success" ng-if="fila.situacaoequipamento==1">Ativado</span>
          </td>
          <td class="vert-align text-center">{{fila.escopo}}</td>
          <td class="vert-align text-center"><span class="label label-default" ng-if="fila.wifi==0">Não</span>
            <span class="label label-default" ng-if="fila.wifi==1">Sim</span>
          </td>
          <td class="vert-align text-center"><span class="label label-default" ng-if="fila.iptv==0">Não</span>
            <span class="label label-default" ng-if="fila.iptv==1">Sim</span>
          </td>
          <td class="vert-align text-center">
            <span class="label label-default">{{fila.stb}}</span>
          </td>
          <td class="vert-align text-center"><span class="label label-default">{{fila.erros}}</span></td>
          <td class="vert-align text-center">
            <span ng-repeat="item in fila.retornos">{{item}}<br></span>
          </td>
        </tr>
      </tbody>
    </table>

    <button ng-hide="HDC.processando_fila" ng-if="HDC.fila.length > 0 && !HDC.fila_processada" class="btn btn-warning"
      title="Processar Fila de Alterações" ng-click="HDC.processa_fila(HDC.contrato.username)"><i
        class="glyphicon glyphicon-play-circle"></i> Processar Fila</button><span ng-if="HDC.processando_fila"><img
        src="assets/images/ajax-loader.gif"> Processando fila...aguarde</span>
  </div>
</div>

<!-- FIM PROVISIONAMENTO -->


<!-- ONU -->
<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('nomeassinante')"
  ng-hide="HDC.contrato.tipohost !== 'Porta GPON OLT' && HDC.contrato.tipohost !== 'FTTA' && HDC.contrato.tipohost !=='' && HDC.contrato.tipohost !==undefined">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <button class="btn btn-default btn-sm" ng-click="HDC.getOnuInfo()"><i class="glyphicon glyphicon-refresh"></i>
        Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><strong>ONU</strong></h3>
  </div>
  <div class="panel-body">

    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#equipamento" data-toggle="tab">
          <i class="glyphicon glyphicon-hdd"></i> Equipamento instalado </a>

      </li>

      <div class="row">
        <button class="btn btn-warning" ng-class="{disabled: HDC.contrato.tr069 != 1}"
          title="Executa novamente o provisionamento da ONU" ng-click="HDC.onuReauth()" style="margin-left: 10px;"><i
            class="glyphicon glyphicon-repeat"></i>
          Reprovisionar
          equipamento</button>
      </div>
    </ul>
    <div id="equipamento" class="tab-pane fade in active">
      <div class="tab-content">

        <span
          ng-if="HDC.contrato.tipohost !== 'Porta GPON OLT' && HDC.contrato.tipohost !== 'FTTA' && HDC.contrato.tipohost !=='' && HDC.contrato.tipohost !==undefined">Plano
          contratado não utiliza ONU</span>
        <table class="table table-bordered"
          ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
          <thead>
            <tr>
              <th class="vert-align text-center">Marca</th>
              <th class="vert-align text-center">Modelo</th>
              <th class="vert-align text-center">MAC</th>
              <th class="vert-align text-center">Serial</th>
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">CDO</th>
              <th class="vert-align text-center">CAT</th>
              <th class="vert-align text-center">Porta CAT</th>
              <th class="vert-align text-center">Ação</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="vert-align text-center">{{HDC.contrato.marca}}</td>
              <td class="vert-align text-center">{{HDC.contrato.modelo}}</td>
              <td class="vert-align text-center">{{HDC.contrato.macaddress}}</td>
              <td class="vert-align text-center">{{HDC.contrato.serial}}</td>
              <td class="vert-align text-center"><a href="/materiais/patrimonios/{{HDC.contrato.patrimonio}}"
                  target="_blank">{{HDC.contrato.patrimonio}}</a></td>
              <td class="vert-align text-center"><span ng-if="!HDC.editandocdo">{{HDC.contrato.cdo}}</span><input
                  ng-if="HDC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number"
                  ng-model="HDC.estrutura.cdo"></td>
              <td class="vert-align text-center"><span ng-if="!HDC.editandocdo">{{HDC.contrato.cat}}</span><input
                  ng-if="HDC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number"
                  ng-model="HDC.estrutura.cat"></td>
              <td class="vert-align text-center"><span ng-if="!HDC.editandocdo">{{HDC.contrato.porta}}</span><input
                  ng-if="HDC.editandocdo" size="2" maxlength="3" class="form-control input-sm" type="number"
                  ng-model="HDC.estrutura.porta"></td>
              <td class="vert-align text-center"><button class="btn btn-default btn-sm" ng-if="!HDC.editandocdo"
                  title="Edita informações (CDO, CAT, Porta CAT)" ng-click="HDC.editacdo()"><i
                    class="glyphicon glyphicon-edit"></i></button><button ng-if="HDC.editandocdo"
                  title="Grava alterações" class="btn btn-success btn-sm" ng-click="HDC.salvacdo()"><i
                    class="glyphicon glyphicon-floppy-saved"></i></button> <button ng-if="HDC.editandocdo"
                  title="Cancela alterações" class="btn btn-danger btn-sm" ng-click="HDC.cancelacdo()"><i
                    class="glyphicon glyphicon-ban-circle"></i></button></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div>

      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#optical" data-toggle="tab">
            <i class="glyphicon glyphicon-flash"></i> Optical status</a>
        </li>
        <!--
        <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandosinalonu"> <button class="btn btn-default btn-sm" ng-click="HDC.atualizaSinalOnu(HDC.contrato.serial)"><i class="glyphicon glyphicon-refresh"></i> Atualizar</button> 
        </span>
      -->
      </ul>
      <div id="optical" class="tab-pane fade in active">
        <div class="tab-content">

          <span
            ng-if="HDC.contrato.tipohost !== 'Porta GPON OLT' && HDC.contrato.tipohost !== 'FTTA' && HDC.contrato.tipohost !=='' && HDC.contrato.tipohost !==undefined">Plano
            contratado não utiliza ONU</span>
          <table class="table table-bordered"
            ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
            <thead>
              <tr>
                <th class="vert-align text-center">Estado Operacional</th>
                <th class="vert-align text-center">Distância</th>
                <th class="vert-align text-center">Último Desligamento</th>
                <th class="vert-align text-center">Send Power</th>
                <th class="vert-align text-center">Receive Power</th>
                <th class="vert-align text-center">OLT Receive Power</th>
                <th class="vert-align text-center">Temperature</th>
                <th class="vert-align text-center">Voltage</th>
                <th class="vert-align text-center">Bias Current</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandostatusonu"><span class="label label-default" ng-class="[{'label-success': HDC.onu.status.OperState == 'UP'},
            {'label-danger': HDC.onu.status == 'DOWN'}]">{{HDC.onu.status.OperState}}</span></td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.rttvalue}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandostatusonu">{{HDC.onu.status.LASTOFFTIME | amDateFormat:'DD/MM/YYYY
                  HH:mm:ss'}}
                </td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.sendpower}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.recvpower}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.oltrecvpower}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.temperature}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.voltage}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.biascurrent}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>



      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#lanstatus" data-toggle="tab">
            <i class="glyphicon glyphicon-list-alt"></i> LAN status</a>

        </li>

      </ul>
      <div id="lanstatus" class="tab-pane fade in active">
        <div class="tab-content">
          <table class="table table-bordered"
            ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
            <thead>
              <tr>
                <th class="vert-align text-center">Porta</th>
                <th class="vert-align text-center">VLANID</th>
                <th class="vert-align text-center">Estado Operacional</th>

              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="porta in HDC.onu.lans">
                <td class="vert-align text-center"><span class="badge">{{$index+1}}</span></td>
                <td class="vert-align text-center"><strong>{{porta.PVID}}</strong></td>
                <td class="vert-align text-center"><span class="label label-default" ng-class="[{'label-success': porta.OperStatus == 'UP'},
                        {'label-danger': porta.OperStatus !== 'UP'}]">{{porta.OperStatus}}</span>
                </td>
              </tr>
            </tbody>
          </table>

        </div>
      </div>

    </div>

    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#histsinal" data-toggle="tab">
          <i class="glyphicon glyphicon-object-align-bottom"></i> Histórico de sinal</a>
      </li>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandosinal"> <button class="btn btn-default btn-sm"
          ng-disabled="HDC.onu.sinal == undefined || HDC.onu == {}" ng-click="HDC.gravasinal()"><i
            class="glyphicon glyphicon-floppy-saved"></i> Gravar Sinal Atual</button> <button
          class="btn btn-default btn-sm" ng-click="HDC.getHistSinal()"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span>

    </ul>
    <div id="histsinal" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="pre-scrollable ng-scope" style="height:80px;">
          <table class="table table-bordered"
            ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
            <thead>
              <tr>
                <th class="vert-align text-center">Data</th>
                <th class="vert-align text-center">Distância</th>
                <th class="vert-align text-center">Send Power</th>
                <th class="vert-align text-center">Receive Power</th>
                <th class="vert-align text-center">OLT Receive Power</th>
                <th class="vert-align text-center">Temperature</th>
                <th class="vert-align text-center">Voltage</th>
                <th class="vert-align text-center">Bias Current</th>
                <th class="vert-align text-center">Comentário</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="item in HDC.sinal">
                <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                <td class="vert-align text-center">{{item.rttvalue}}</td>
                <td class="vert-align text-center">{{item.sendpower}}</td>
                <td class="vert-align text-center">{{item.recvpower}}</td>
                <td class="vert-align text-center">{{item.oltrecvpower}}</td>
                <td class="vert-align text-center">{{item.temperature}}</td>
                <td class="vert-align text-center">{{item.voltage}}</td>
                <td class="vert-align text-center">{{item.biascurrent}}</td>
                <td class="vert-align text-center">{{item.comentario}}</td>
              </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>

    <hr class="message-inner-separator">
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#logsprovisionamento" data-toggle="tab">
          <i class="glyphicon glyphicon-console"></i> Logs de provisionamento</a>

      </li>

    </ul>
    <div id="logsprovisionamento" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="pre-scrollable ng-scope" style="height:120px;">
          <table class="table table-bordered"
            ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
            <thead>
              <tr>
                <th class="vert-align text-center">Data Processamento</th>
                <th class="vert-align text-center">Data Alteração</th>
                <th class="vert-align text-center">Operador</th>
                <th class="vert-align text-center">Patrimônio</th>
                <th class="vert-align text-center">Modelo</th>
                <th class="vert-align text-center">Serial</th>
                <th class="vert-align text-center">MAC</th>
                <th class="vert-align text-center">Sit. Equipamento</th>
                <th class="vert-align text-center">Escopo</th>
                <th class="vert-align text-center">Wi-fi</th>
                <th class="vert-align text-center">IPTV</th>
                <th class="vert-align text-center">STB</th>
                <th class="vert-align text-center">Log</th>
                <th class="vert-align text-center">Detalhes</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="item in HDC.ftthlogs"
                ng-class="[{'success': item.erro == 0}, {'danger' : item.erro == 1}]">
                <td class="vert-align text-center">{{item.data_processamento | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                <td class="vert-align text-center">{{item.data_alteracao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                <td class="vert-align text-center">{{item.operador}}</td>
                <td class="vert-align text-center">{{item.patrimonio}}</td>
                <td class="vert-align text-center">{{item.modelo}}</td>
                <td class="vert-align text-center">{{item.serial}}</td>
                <td class="vert-align text-center">{{item.mac}}</td>
                <td class="vert-align text-center">
                  <span class="label label-danger" ng-if="item.situacaoequipamento==0">Desativado</span>
                  <span class="label label-success" ng-if="item.situacaoequipamento==1">Ativado</span>
                </td>
                <td class="vert-align text-center">{{item.escopo}}</td>
                <td class="vert-align text-center"><span class="label label-default" ng-if="item.wifi==0">Não</span>
                  <span class="label label-default" ng-if="item.wifi==1">Sim</span>
                </td>
                <td class="vert-align text-center"><span class="label label-default" ng-if="item.iptv==0">Não</span>
                  <span class="label label-default" ng-if="item.iptv==1">Sim</span>
                </td>
                <td class="vert-align text-center"> <span class="label label-default">{{item.stb}}</span> </td>
                <td class="vert-align text-center">{{item.log}}</td>
                <td class="vert-align text-center">{{item.detalhes}}</td>
              </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>

    <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
      <li class="active">
        <a href="#" data-target="#configs-wifi" data-toggle="tab">
          <i class="glyphicon glyphicon-signal"></i> Configurações Wi-Fi</a>
      </li>

      <!-- <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoTr069"> <button class="btn btn-default btn-sm"
          ng-click="HDC.getTr069Config(HDC.contrato.username);"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span> -->
    </ul>
    <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
      <div id="configs-wifi" class="tab-pane active">
        <div class="panel panel-default">
          <div class="panel-body align-center">
            <div ng-if="HDC.portalAPIError === true">
              Não foi possível buscar as configurações da ONU do cliente.
            </div>
            <div ng-if="HDC.onuNotFound === true">
              Esta ONU não consta no banco de dados do GenieACS. Caso ela tenha sido provisionada recentemente, atualize
              os dados em cerca de dois minutos.
            </div>
            <div ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true"
              class="wifi-config-container align-left">
              <table>
                <tr>
                  <td style="padding-left: 5px;">
                    <table class="table valign-middle" style="margin-bottom: 0px; width: 200px;">
                      <tr>
                        <td colspan="2" style="padding: 5px; text-align: center;"><b>Wi-Fi 2.4GHz</b></td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          <table class="radio-table" ng-class="{'disabled': HDC.tr069Config.operationMode == 'bridge'}"
                            style="margin: 5px 0px;">
                            <tr>
                              <td class="success" ng-class="{'active': HDC.tr069Config.wifi[1].enabled == 1}"
                                ng-click="HDC.setWifiEnabled(1, 1);">ATIVADO</td>
                              <td class="danger" ng-class="{'active': HDC.tr069Config.wifi[1].enabled == 0}"
                                ng-click="HDC.setWifiEnabled(1, 0);">DESATIVADO</td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td><b>SSID:</b></td>
                        <td>
                          <input ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069"
                            class="form-control" type="text" id="wifi-2g-ssid" ng-model="HDC.tr069Config.wifi[1].ssid">
                        </td>
                      </tr>
                      <tr>
                        <td><b>{{HDC.currentTr069Config.wifi[1].passphrase == '' ? 'Nova senha:' : 'Senha:'}}</b></td>
                        <td>
                          <input ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069"
                            class="form-control" type="text" id="wifi-2g-password"
                            ng-model="HDC.tr069Config.wifi[1].passphrase">
                        </td>
                      </tr>
                    </table>
                  </td>
                  <td class="valign-bottom">
                    <div style="height: 115px; width: 1px; background: #DDD; margin: 0px 20px;">&nbsp;</div>
                  </td>
                  <td>
                    <table class="table valign-middle" style="margin-bottom: 0px; width: 200px;">
                      <tr>
                        <td colspan="2" style="padding: 5px; text-align: center;"><b>Wi-Fi 5.8GHz</b></td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          <table class="radio-table" ng-class="{'disabled': HDC.tr069Config.operationMode == 'bridge'}"
                            style="margin: 5px 0px;">
                            <tr ng-if="HDC.contrato['5G'] == 1">
                              <td class="success" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 1}"
                                ng-click="HDC.setWifiEnabled(5, 1);">ATIVADO</td>
                              <td class="danger" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 0}"
                                ng-click="HDC.setWifiEnabled(5, 0);">DESATIVADO</td>
                            </tr>
                            <tr ng-if="HDC.contrato['5G'] != 1">
                              <td class="not-available" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 1}">
                                NÃO DISPONÍVEL
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td><b>SSID:</b></td>
                        <td>
                          <input
                            ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.contrato['5G'] != 1 ||  HDC.atualizandoTr069"
                            class="form-control" type="text" id="wifi-5g-ssid" ng-model="HDC.tr069Config.wifi[5].ssid">
                        </td>
                      </tr>
                      <tr>
                        <td><b>{{HDC.currentTr069Config.wifi[5].passphrase == '' ? 'Nova senha:' : 'Senha:'}}</b></td>
                        <td>
                          <input
                            ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.contrato['5G'] != 1 ||  HDC.atualizandoTr069"
                            class="form-control" type="text" id="wifi-5g-password"
                            ng-model="HDC.tr069Config.wifi[5].passphrase">
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr
                  ng-if="HDC.tr069Config.operationMode == 'router' && (HDC.currentTr069Config.wifi[1].passphrase == '' || HDC.currentTr069Config.wifi[5].passphrase == '')">
                  <td colspan="3" style="padding-top: 10px; padding-left: 5px;">
                    * Caso o campo "nova senha" seja deixado em branco, a senha atual do cliente <b>não será
                      alterada ou removida</b>.
                  </td>
                </tr>
                <tr ng-if="HDC.tr069Config.operationMode == 'bridge'">
                  <td colspan="3" style="padding-top: 10px; padding-left: 5px;">
                    * A ONU está em modo bridge. Não será possível alterar as configurações do Wi-Fi.
                  </td>
                </tr>
                <tr>
                  <td colspan="3" style="padding: 5px 0px 15px 0px; text-align: center;">
                    <button
                      ng-disabled="(HDC.tr069Config.wifi[1].ssid == HDC.currentTr069Config.wifi[1].ssid && HDC.tr069Config.wifi[1].passphrase == HDC.currentTr069Config.wifi[1].passphrase && HDC.tr069Config.wifi[1].enabled == HDC.currentTr069Config.wifi[1].enabled && HDC.tr069Config.wifi[5].ssid == HDC.currentTr069Config.wifi[5].ssid && HDC.tr069Config.wifi[5].passphrase == HDC.currentTr069Config.wifi[5].passphrase && HDC.tr069Config.wifi[5].enabled == HDC.currentTr069Config.wifi[5].enabled) || HDC.atualizandoTr069 || HDC.tr069Config.operationMode == 'bridge'"
                      class="btn"
                      ng-class="[(HDC.tr069Config.wifi[1].ssid == HDC.currentTr069Config.wifi[1].ssid && HDC.tr069Config.wifi[1].passphrase == HDC.currentTr069Config.wifi[1].passphrase && HDC.tr069Config.wifi[1].enabled == HDC.currentTr069Config.wifi[1].enabled && HDC.tr069Config.wifi[5].ssid == HDC.currentTr069Config.wifi[5].ssid && HDC.tr069Config.wifi[5].passphrase == HDC.currentTr069Config.wifi[5].passphrase && HDC.tr069Config.wifi[5].enabled == HDC.currentTr069Config.wifi[5].enabled) || HDC.atualizandoTr069 || HDC.tr069Config.operationMode == 'bridge' ? 'btn-default' : 'btn-primary', {'margin-top-10': HDC.currentTr069Config.wifi[1].passphrase != '' && HDC.currentTr069Config.wifi[5].passphrase != ''}]"
                      ng-really-message="Deseja realmente salvar as alterações do Wi-Fi?"
                      ng-really-click="HDC.saveWifi();"><i class="glyphicon glyphicon-ok btn-sm-icon"></i>
                      Salvar</button>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
      <li class="active">
        <a href="#" data-target="#configs-operation" data-toggle="tab">
          <i class="glyphicon glyphicon-cog"></i> Modo de operação</a>
      </li>

      <!-- <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoTr069"> <button class="btn btn-default btn-sm"
          ng-click="HDC.getTr069Config(HDC.contrato.username);"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span> -->
    </ul>
    <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
      <div id="configs-operation" class="tab-pane active">
        <div class="panel panel-default">
          <div class="panel-body">
            <div ng-if="HDC.portalAPIError === true" class="align-center" style="width: 100%;">
              Não foi possível buscar as configurações da ONU do cliente.
            </div>
            <div ng-if="HDC.onuNotFound === true" class="align-center" style="width: 100%;">
              Esta ONU não consta no banco de dados do GenieACS. Caso ela tenha sido provisionada recentemente, atualize
              os dados em cerca de dois minutos.
            </div>
            <table ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true" style="width: 100%;">
              <tr>
                <td align="center">

                  <div class="operation-config-container">
                    <table class="table valign-middle" style="margin-bottom: 0px;">
                      <tr>
                        <td style="padding: 5px; text-align: center;"><b>Modo router</b></td>
                      </tr>
                      <tr>
                        <td id="router-description" style="height: 147px;">
                          <p style="text-align: justify;">
                            Este é o <b>modo de operação padrão do modem</b>. Com este modo ativado, o modem fará
                            também o papel de um roteador,
                            <b>não necessitando de qualquer equipamento adicional na rede</b>.
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding: 5px; text-align: center;">
                          <button
                            ng-disabled="HDC.tr069Config.operationMode == 'router' || HDC.atualizandoTr069 || HDC.atualizandoOperationMode == 'router'"
                            class="btn"
                            ng-class="[{'btn-primary': HDC.tr069Config.operationMode != 'router'}, {'btn-default': HDC.tr069Config.operationMode == 'router'}, {'no-pointer': HDC.tr069Config.operationMode == 'router'}]"
                            ng-really-message="Deseja realmente alterar o modo de operação da ONU para router?"
                            ng-really-click="HDC.setOperationMode('router');"><i
                              ng-if="HDC.tr069Config.operationMode == 'router'"
                              class="glyphicon glyphicon-ok btn-sm-icon"></i>
                            {{HDC.tr069Config.operationMode == 'router' ? 'Ativado' : (HDC.atualizandoOperationMode ==
                            'router' ? 'Ativando...' : 'Ativar')}}</button>
                        </td>
                      </tr>
                    </table>
                  </div>

                </td>
                <td align="center">

                  <div class="operation-config-container">
                    <table class="table valign-middle" style="margin-bottom: 0px;">
                      <tr>
                        <td style="padding: 5px; text-align: center;"><b>Modo bridge</b></td>
                      </tr>
                      <tr>
                        <td id="bridge-description" style="height: 147px;">
                          <p style="text-align: justify;">
                            Este modo permite que o cliente utilize seu roteador pessoal para conectar à nossa rede.
                            Dessa
                            forma, <b>as configurações do
                              nosso modem (como rede Wi-Fi, redirecionamento de portas etc) serão desativadas e o
                              cliente
                              passará a gerenciar essas
                              configurações diretamente em seu roteador pessoal</b>.
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding: 5px; text-align: center;">
                          <button
                            ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069 || HDC.atualizandoOperationMode == 'bridge'"
                            class="btn"
                            ng-class="[{'btn-primary': HDC.tr069Config.operationMode != 'bridge'}, {'btn-default': HDC.tr069Config.operationMode == 'bridge'}, {'no-pointer': HDC.tr069Config.operationMode == 'bridge'}]"
                            ng-really-message="Deseja realmente alterar o modo de operação da ONU para bridge?"
                            ng-really-click="HDC.setOperationMode('bridge');"><i
                              ng-if="HDC.tr069Config.operationMode == 'bridge'"
                              class="glyphicon glyphicon-ok btn-sm-icon"></i>
                            {{HDC.tr069Config.operationMode == 'bridge' ? 'Ativado' : (HDC.atualizandoOperationMode ==
                            'bridge' ? 'Ativando...' : 'Ativar')}}</button>
                        </td>
                      </tr>
                    </table>
                  </div>

                </td>

                <td style="vertical-align: top;">
                  <div id="operation-mode-log-container">
                    <table id="operation-mode-log">
                      <thead>
                        <tr>
                          <th colspan="3">Log de alterações do modo de operação</th>
                        </tr>
                        <tr>
                          <th>Data</th>
                          <th>Operador</th>
                          <th>Descrição</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr ng-if="HDC.operationModeLogs.length == 0 && !HDC.atualizandoTr069">
                          <td colspan="3" class="bg-warning">
                            Não houve alterações do modo de operação.
                          </td>
                        </tr>
                        <tr ng-if="HDC.atualizandoTr069">
                          <td colspan="3" class="bg-warning">
                            Carregando...
                          </td>
                        </tr>
                        <tr ng-repeat="log in HDC.operationModeLogs"
                          ng-class="{'bg-success-dark': log.status && log.status == 'executing'}">
                          <td>{{ log.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss' }}</td>
                          <td>{{ log.noc_operator }}</td>
                          <td>
                            {{ log.status && log.status == 'executing' ? 'Você está alterando o modo de operação para '
                            : 'A ONU do cliente foi alterada para o modo ' }}<b>{{ log.mode }}</b>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </td>
              </tr>
            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- FIM ONU -->


<!-- CPE -->

<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">

    <h3 class="panel-title" style="margin-top: 5px;"><strong>CPE</strong></h3>
  </div>
  <div class="panel-body">
    <!--
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#ipoe" data-toggle="tab">
          <i class="glyphicon glyphicon-globe"></i> IPoE</a>
      </li>
    </ul>
    <div id="ipoe" class="tab-pane fade in active">
      <div class="tab-content">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="vert-align text-center">Ativar/desativar IPoE</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="vert-align text-center" style="text-align: center !important;">
                <div class="onoffswitch" style="margin: 0 auto; margin-top: 5px; margin-bottom: 5px;">
                  <input disabled type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="ipoe_status"
                    ng-checked="HDC.contrato.hasOwnProperty('nomeassinante') && HDC.ipoeativo == 1">
                  <label class="onoffswitch-label" for="ipoe_status"
                    ng-really-message="O modo de autenticação do cliente será alterado. Após essa alteração, será necessário que o cliente altere o modo de conexão em seu roteador (PPPoE / DHCP). Deseja realmente continuar?"
                    ng-really-click="HDC.alterarIpoe()">
                    <span class="onoffswitch-inner"></span>
                    <span class="onoffswitch-switch"></span>
                  </label>
                </div>
                <div style="color: #9f0000; margin: 5px 0px;"
                  ng-if="HDC.contrato.hasOwnProperty('nomeassinante') && (HDC.ipoeativo == 1 || HDC.ipoeativo == 0) && HDC.contrato.processado === ''">
                  O status do IPoE foi alterado. A fila da ONU ainda não foi processada para efetivar esta alteração.
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  -->
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#wanstatus" data-toggle="tab">
          <i class="glyphicon glyphicon-globe"></i> WAN Status</a>
      </li>
      <button class="btn"
        ng-class="{disabled: HDC.statusCGNAT.ativo != 1, 'btn-warning': HDC.statusCGNAT.solicitacaoExiste == 0 || (HDC.statusCGNAT.solicitacaoExiste == 1 && HDC.statusCGNAT.solicitacaoPendente), 'btn-success': HDC.statusCGNAT.status == 'EXECUTADO', 'btn-danger': (HDC.statusCGNAT.status == 'NEGADO' || HDC.statusCGNAT.status == 'RESTAURADO')}"
        title="Envia uma solicitação ao COR, para remoção do CGNAT deste cliente"
        ng-click="HDC.removerCGNAT(HDC.contrato.username)" style="margin-left: 10px;"><i
          class="glyphicon glyphicon-remove"></i>
        Solicitar remoção do CGNAT</button>
      <span ng-if="HDC.statusCGNAT.solicitacaoExiste == 1" style="margin-left: 10px;">
        (<b>Status:</b> {{HDC.statusCGNAT.statusMessage}})
      </span>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandowan"> <button class="btn btn-default btn-sm"
          ng-click="HDC.getWanStatus(HDC.contrato.username)"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span>
    </ul>
    <div id="wanstatus" class="tab-pane fade in active">
      <div class="tab-content">
        <table class="table table-bordered">
          <thead>
            <tr>
              <!--<th class="vert-align text-center">Tipo autenticação</th>-->
              <th class="vert-align text-center">Status</th>
              <th class="vert-align text-center">Autentic.</th>
              <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Uptime</th>
              <th class="vert-align text-center">IPv4</th>
              <th class="vert-align text-center">IPv6</th>
              <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">MAC CPE</th>
              <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">MTU</th>
              <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Download</th>
              <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Upload</th>
              <th class="vert-align text-center">BNG</th>
              <th class="vert-align text-center">BNG IP</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <!--<td class="vert-align text-center"></td>-->
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan"><span class="label label-default"
                  ng-class="[{'label-success': HDC.wan.status == 'Conectado'},
                                              {'label-danger': HDC.wan.status == 'Desconectado'}]">{{HDC.wan.status}}</span></td>
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan"><span class="label label-primary">{{HDC.wan.auth_type}}</span></td>
              <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">{{HDC.wan.uptime}}</td>
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan">{{HDC.wan.ipv4}}</td>
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan">{{HDC.wan.ipv6}}</td>
              <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                  src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.mac}}</td>
              <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                  src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.mtu}}</td>
              <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                  src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.down}}</td>
              <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                  src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.up}}</td>
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan">{{HDC.wan.router_desc}}</td>
              <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                  ng-show="HDC.atualizandowan">{{HDC.wan.router_ip}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#routers-tr069" data-toggle="tab">
          <i class="glyphicon glyphicon-globe"></i> Roteadores TR069</a>
      </li>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoRoutersCliente"> <button
          class="btn btn-default btn-sm" ng-click="HDC.getRoutersAlocados(HDC.contrato.username);"><i
            class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span>
    </ul>
    <div id="routers-tr069" class="tab-pane fade in active">
      <div class="tab-content">
        <table class="table table-bordered">
          <thead ng-if="HDC.routers_alocados.length > 0">
            <tr>
              <th class="centered">Patrimônio</th>
              <th class="centered">MAC</th>
              <th class="centered">Serial</th>
              <th class="centered">Marca</th>
              <th class="centered">Modelo</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-if="HDC.routers_alocados.length < 1">
              <td class="centered" colspan="6" style="padding: 4px;">
                <b>Não há roteadores com TR069 alocados para este cliente</b>
              </td>
            </tr>
            <tr ng-repeat="router in HDC.routers_alocados">
              <td class="centered">
                {{router.patrimonio}}
              </td>
              <td class="centered">
                {{router.mac}}
              </td>
              <td class="centered">
                {{router.serial}}
              </td>
              <td class="centered">
                {{router.marca}}
              </td>
              <td class="centered">
                {{router.modelo}}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#logs" data-toggle="tab">
          <i class="glyphicon glyphicon-globe"></i> Logs de autenticação</a>
      </li>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandolog"> <button class="btn btn-default btn-sm"
          ng-click="HDC.atualizaLogs(HDC.contrato.username)" ng-disabled="HDC.atualizandolog"><i
            class="glyphicon glyphicon-refresh"></i> Atualiza</button>
      </span>
    </ul>
    <div id="logs" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="pre-scrollable ng-scope" style="height:100px;">
          <table class="table table-bordered" ng-if="HDC.rsyslog == false">
            <thead>
              <tr>
                <th class="vert-align text-center">Mensagem</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="log in HDC.logs">
                <td class="vert-align text-center">{{log}}</td>
              </tr>
            </tbody>
          </table>

          <table class="table table-bordered" ng-if="HDC.rsyslog == true">
            <thead>
              <tr>
                <th class="vert-align text-center">Data/Hora</th>
                <th class="vert-align text-center">Router</th>
                <th class="vert-align text-center">Mensagem</th>

              </tr>
            </thead>

            <tbody>
              <tr ng-repeat="log in HDC.logs">
                <td class="vert-align text-center">{{log.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                <td class="vert-align text-center">{{log.concentrador}}</td>
                <td class="vert-align text-center">{{log.mensagem}}</td>
              </tr>
            </tbody>

          </table>
        </div>
      </div>
    </div>

    <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
      <li class="active">
        <a href="#" data-target="#configs-portmapping" data-toggle="tab">
          <i class="glyphicon glyphicon-share-alt"></i> Redirecionamentos de porta</a>
      </li>

      <!-- <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoTr069"> <button class="btn btn-default btn-sm"
              ng-click="HDC.getTr069Config(HDC.contrato.username);"><i class="glyphicon glyphicon-refresh"></i>
              Atualiza</button>
          </span> -->
    </ul>
    <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
      <div id="configs-portmapping" class="tab-pane active">
        <div class="panel panel-default">
          <div class="panel-body" style="text-align: center; padding: 0px; padding-bottom: 10px;">
            <div ng-if="HDC.portalAPIError === true">
              Não foi possível buscar as configurações da ONU do cliente.
            </div>
            <div ng-if="HDC.onuNotFound === true">
              Esta ONU não consta no banco de dados do GenieACS. Caso ela tenha sido provisionada recentemente, atualize
              os dados em cerca de dois minutos.
            </div>
            <table ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true"
              class="table table-bordered table-striped align-center valign-middle" style="margin-bottom: 10px;">
              <thead>
                <tr>
                  <th>Editar</th>
                  <th>Status</th>
                  <th>Serviço</th>
                  <th>IP externo</th>
                  <th>Porta externa</th>
                  <th>IP interno</th>
                  <th>Porta interna</th>
                  <th>MAC</th>
                  <th>Criado em</th>
                  <th>Remover</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-if="keys(HDC.tr069Config.portMappings).length == 0">
                  <td colspan="10" class="bg-warning">
                    Este cliente não possui redirecionamentos de porta.
                  </td>
                </tr>
                <tr ng-if="keys(HDC.tr069Config.portMappings).length > 0"
                  ng-repeat="mapping in HDC.tr069Config.portMappings">
                  <td>
                    <button class="btn btn-vsm btn-primary" title="Editar" data-toggle="modal"
                      data-target="#frmredirecionamento"
                      ng-click="HDC.selecionarRedirecionamento(mapping); HDC.initFrmRedirecionamento"><i
                        class="glyphicon glyphicon-pencil"></i></button>
                  </td>
                  <td><span class="label"
                      ng-class="[{'label-success': mapping.enabled && mapping.status == 'executed'}, {'label-danger': !mapping.enabled}, {'label-warning': mapping.status != 'executed'}]">{{mapping.enabled
                      && mapping.status == 'executed' ? 'Ativo' : (!mapping.enabled ? 'Inativo' : (mapping.status !=
                      'executed' ? 'Criando' : '[indefinido]'))}}</span>
                  </td>
                  <td>{{mapping.description}}</td>
                  <td>{{HDC.currentTr069Config.connection.externalIP}}</td>
                  <td>{{mapping.externalPort}}</td>
                  <td>{{mapping.internalIP}}</td>
                  <td>{{mapping.internalPort}}</td>
                  <td>{{mapping.mac}}</td>
                  <td>{{mapping.executedAt.substr(0, 10).split('-').reverse().join('/')}}</td>
                  <td>
                    <button class="btn btn-vsm btn-danger" title="Remover"
                      ng-really-message="Deseja realmente excluir o redirecionamento da porta <b>{{mapping.externalPort}}</b>?"
                      ng-really-click="HDC.excluirRedirecionamento(mapping.id);"><i
                        class="glyphicon glyphicon-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>

            <button ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true" ng-disabled="HDC.atualizandoTr069"
              class="btn btn-default" data-toggle="modal" data-target="#frmnovoredirecionamento"
              ng-click="HDC.initFrmnovoredirecionamento();"><i class="glyphicon glyphicon-plus btn-sm-icon"></i>
              Adicionar redirecionamento</button>

            <div
              ng-if="(!HDC.currentTr069Config.connection.cgnat && !HDC.currentTr069Config.connection.ip_fix && !HDC.currentTr069Config.connection.ip_fixo) && HDC.onuNotFound !== true"
              style="margin-top: 10px;">
              <b>* Este cliente não possui os pacotes "IP Fix" ou "IP Fixo" contratados. Será necessário alertar o
                cliente de que seu IP público pode ser alterado quando reconectar-se (seu IP público é necessário para
                acessar os equipamentos com redirecionamentos de portas). Caso o cliente deseje um endereço fixo,
                será necessário contratar um destes dois pacotes.</b>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- FIM CPE -->

<!-- ANALISE DE REDE BASICA -->
<div class="panel panel-primary" ng-hide="HDC.wan.router !=='juniper'">
  <div class="panel-heading clearfix">

    <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Básica</strong></h3>
  </div>
  <div class="panel-body">
    <!-- PING -->
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#ping" data-toggle="tab">
          <i class="glyphicon glyphicon-equalizer"></i> Ping </a>

      </li>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoPing === 1"> <button
          class="btn btn-default btn-sm" ng-click="HDC.atualizaPing(HDC.contrato.username)"
          ng-disabled="HDC.testandoPing===1"><i class="glyphicon glyphicon-play"></i> Iniciar Ping</button> <button
          class="btn btn-default btn-sm" ng-click="HDC.pausarPing()"
          ng-disabled="HDC.testandoPing===2 || HDC.testandoPing===0"><i class="glyphicon glyphicon-pause"></i> Pausar
          Ping</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarPing()"
          ng-disabled="HDC.testandoPing===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Ping</button>
      </span>

    </ul>
    <div id="ping" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="row">
          <div class="col-md-2">
            <table class="table table-bordered table-sm" style="margin-bottom: 0px;">
              <thead>
                <tr>
                  <th class="vert-align text-center" style="width: 200px;">Respostas</th>
                </tr>
              </thead>
            </table>
            <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="pingresults">
              <table class="table table-bordered table-sm">

                <tbody>
                  <tr ng-repeat="ping in HDC.ping.packets">
                    <td class="vert-align text-center" style="width: 200px;"><span
                        ng-if="ping.rtt !=='lost'">{{ping.rtt}} ms</span> <span ng-if="ping.rtt =='lost'">Perdido</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Enviados</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsSent}}</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Recebidos</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsReceived}}</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Perdidos</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsLost}}
                    ({{HDC.ping.packetsLostPerc}}%)</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Min</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.min}} ms</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Med</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.avg}} ms</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Max</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.max}} ms</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-10">
            <div id="graficoPing"></div>
          </div>

        </div>
      </div>
    </div>
    <!-- FIM PING -->
    <!--<hr class="message-inner-separator">-->
    <!-- TRAFEGO -->
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#trafego" data-toggle="tab">
          <i class="glyphicon glyphicon-random"></i> Tráfego instantâneo </a>

      </li>
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoTrafego === 1"> <button
          class="btn btn-default btn-sm" ng-click="HDC.atualizaTrafego(HDC.contrato.username, HDC.contrato.circuitid)"
          ng-disabled="HDC.testandoTrafego===1"><i class="glyphicon glyphicon-play"></i> Iniciar Trafego</button>
        <button class="btn btn-default btn-sm" ng-click="HDC.pausarTrafego()"
          ng-disabled="HDC.testandoTrafego===2 || HDC.testandoTrafego===0"><i class="glyphicon glyphicon-pause"></i>
          Pausar Trafego</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarTrafego()"
          ng-disabled="HDC.testandoTrafego===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Trafego</button>
      </span>
    </ul>
    <div id="trafego" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="row">
          <div class="col-md-2">
            <table class="table table-bordered">
              <colgroup span="2"></colgroup>
              <tr>
                <th colspan="2" scope="colgroup" class="vert-align text-center label-success"><strong>Download</strong>
                </th>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.outputBitsPerSecond | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.minOutput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.avgOutput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.maxOutput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.outputPacketsPerSecond}} pps</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.outputBytes | bytes}}</td>
              </tr>
              <colgroup span="2"></colgroup>
              <tr>
                <th colspan="2" scope="colgroup" class="vert-align text-center label-danger"><strong>Upload</strong>
                </th>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.inputBitsPerSecond | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.minInput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.avgInput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.maxInput | bytes}}/s</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.inputPacketsPerSecond}} pps</td>
              </tr>
              <tr>
                <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                <td class="vert-align text-center" width="80%">{{HDC.trafego.inputBytes | bytes}}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-10">
            <div id="graficoTrafego"></div>
          </div>
        </div>

      </div>
    </div>


  </div>
</div>
<!-- FIM ANALISE DE REDE BASICO -->

<!-- Lista de dispositivos da rede interna IPV6 -->

<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoipv6list"> <button class="btn btn-default btn-sm"
        ng-click="HDC.getIpv6List()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Lista de dispositivos da rede interna
      IPv6 | <b> {{HDC.ipv6list.length}} dispositivo(s) </b></h3>
  </div>
  <div class="panel-body">
    <uib-accordion close-others="false">
      <div uib-accordion-group class="panel-default" is-open="status.open" ng-repeat="item in HDC.ipv6list">
        <uib-accordion-heading>
          <img src="assets/images/ajax-loader.gif" ng-show="item.testing === 1">{{item.source_address}} <span
            style="font-size: 85%;">| Última atividade:</b> {{item.last_activity | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}
            | Média: {{item.avg_rtt}} ms | Perdidos: {{item.lost}} ({{item.lost_perc}}%)</span><i
            class="pull-right glyphicon"
            ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i><span
            class="pull-right"><br>

        </uib-accordion-heading>
        <p><b>Última atividade:</b> {{item.last_activity | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</p>
        <div>
          <span class="pull-left">
            <button class="btn btn-default btn-sm" ng-click="HDC.atualizaPingV6(item)" ng-disabled="item.testing===1"><i
                class="glyphicon glyphicon-play"></i> Iniciar Ping</button> <button class="btn btn-default btn-sm"
              ng-click="HDC.pausarPingV6(item)" ng-disabled="item.testing===2 || item.testing===0"><i
                class="glyphicon glyphicon-pause"></i> Pausar Ping</button> <button class="btn btn-default btn-sm"
              ng-click="HDC.cancelarPingV6(item)" ng-disabled="item.testing===0"><i
                class="glyphicon glyphicon-stop"></i> Cancelar Ping</button>
          </span>

        </div>
        &nbsp;
        <hr>
        <div class="row">
          <div class="col-md-2">
            <table class="table table-bordered table-sm" style="margin-bottom: 0px;">
              <thead>
                <tr>
                  <th class="vert-align text-center" style="width: 200px;">Respostas</th>
                </tr>
              </thead>
            </table>
            <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="pingresultsv6_{{item.index}}">
              <table class="table table-bordered table-sm">

                <tbody>
                  <tr ng-repeat="ping in item.packets">
                    <td class="vert-align text-center" style="width: 200px;"><span
                        ng-if="ping.rtt !=='lost'">{{ping.rtt}} ms</span> <span ng-if="ping.rtt =='lost'">Perdido</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Enviados</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.sent}}</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Recebidos</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.received}}</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Perdidos</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.lost}} ({{item.lost_perc}}%)</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Min</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.min_rtt}} ms</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Med</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.avg_rtt}} ms</td>
                </tr>
                <tr>
                  <td class="vert-align text-center" style="width: 25%;"><strong>Max</strong></td>
                  <td class="vert-align text-center" style="width: 75%;">{{item.max_rtt}} ms</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="col-md-10">
          <div id="graficoPingV6_{{item.index}}"></div>
        </div>
      </div>
    </uib-accordion>
  </div>
</div>

<!-- ANALISE DE REDE AVANÇADA -->
<div class="panel panel-primary" ng-hide="HDC.wan.router !=='juniper'">
  <div class="panel-heading clearfix">

    <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Avançada</strong></h3>
  </div>
  <div class="panel-body">
    <!-- TORCH -->

    <ul class="nav nav-tabs">
      <li class="active">
        <a data-target="#torch" data-toggle="tab">
          <i class="glyphicon glyphicon-equalizer"></i> Conexões instantâneas </a>

      </li>
      <span class="pull-right">
        <form class="form-inline" role="form">
          <div class="form-group">
            Janela de Monitoramento:
            <select class="form-control" ng-model="HDC.torch.timeout">
              <option value="10">10 segundos</option>
              <option value="60">1 minuto</option>
              <option value="300">5 minutos</option>
              <option value="600">10 minutos</option>
              <option value="900">15 minutos</option>
            </select>
          </div>
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoTorch === 1"> <button
            class="btn btn-default btn-sm" ng-click="HDC.atualizaTorch()" ng-disabled="HDC.testandoTorch===1"><i
              class="glyphicon glyphicon-play"></i> Iniciar Torch</button> <button class="btn btn-default btn-sm"
            ng-click="HDC.pausarTorch()" ng-disabled="HDC.testandoTorch===2 || HDC.testandoTorch===0"><i
              class="glyphicon glyphicon-pause"></i>
            Pausar Torch</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarTorch()"
            ng-disabled="HDC.testandoTorch===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Torch</button>
        </form>
      </span>
    </ul>

    <div id="torch" class="tab-pane fade in active">
      <div class="tab-content">
        <div class="pre-scrollable" style="height:280px;overflow-y: scroll;" id="pingresults">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="vert-align text-center" style="width: 10%;">
                  <a href="#" ng-click="HDC.sort('protocol')"> Protocolo
                    <span ng-show="HDC.sortBy == 'protocol' && HDC.sortOrder == '-'" class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'protocol' && HDC.sortOrder == '+'" class="fa fa-caret-up"></span>
                  </a>
                </th>
                <th class="vert-align text-center" style="width: 25%;">
                  <a href="#" ng-click="HDC.sort('source-address')"> IP Origem
                    <span ng-show="HDC.sortBy == 'source-address' && HDC.sortOrder == '-'"
                      class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'source-address' && HDC.sortOrder == '+'"
                      class="fa fa-caret-up"></span>
                  </a>
                </th>
                <th class="vert-align text-center" style="width: 10%;">
                  <a href="#" ng-click="HDC.sort('source-port')"> Porta Origem
                    <span ng-show="HDC.sortBy == 'source-port' && HDC.sortOrder == '-'" class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'source-port' && HDC.sortOrder == '+'" class="fa fa-caret-up"></span>
                  </a>
                </th>
                <th class="vert-align text-center" style="width: 25%;">
                  <a href="#" ng-click="HDC.sort('destination-address')"> IP Destino
                    <span ng-show="HDC.sortBy == 'destination-address' && HDC.sortOrder == '-'"
                      class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'destination-address' && HDC.sortOrder == '+'"
                      class="fa fa-caret-up"></span>
                  </a>
                </th>
                <th class="vert-align text-center" style="width: 10%;">
                  <a href="#" ng-click="HDC.sort('destination-port')"> Porta Destino
                    <span ng-show="HDC.sortBy == 'destination-port' && HDC.sortOrder == '-'"
                      class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'destination-port' && HDC.sortOrder == '+'"
                      class="fa fa-caret-up"></span>
                  </a>
                </th>
                <th class="vert-align text-center" style="width: 20%;">
                  <a href="#" ng-click="HDC.sort('total-octets')"> Total Octets
                    <span ng-show="HDC.sortBy == 'total-octets' && HDC.sortOrder == '-'"
                      class="fa fa-caret-down"></span>
                    <span ng-show="HDC.sortBy == 'total-octets' && HDC.sortOrder == '+'" class="fa fa-caret-up"></span>
                  </a>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="torch in HDC.torch.connections">
                <td class="vert-align text-center" style="width: 10%;">{{torch.protocol | protocol}}</td>
                <td class="vert-align text-center" style="width: 25%;">{{torch.sourceAddress}}</td>
                <td class="vert-align text-center" style="width: 10%;">{{torch.sourcePort}}</td>
                <td class="vert-align text-center" style="width: 25%;">{{torch.destinationAddress}}</td>
                <td class="vert-align text-center" style="width: 10%;">{{torch.destinationPort}}</td>
                <td class="vert-align text-center" style="width: 20%;">{{torch.totalOctets}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <!-- FIM TORCH -->

  </div>
</div>
<!-- FIM ANALISE DE REDE AVANÇADA -->


<!-- TESTES CAMPO -->

<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('nomeassinante')">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandotestes"> <button class="btn btn-default btn-sm"
        ng-click="HDC.getAutotesteTestes()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Testes realizados em campo</h3>
  </div>
  <div class="panel-body">
    <div class="pre-scrollable ng-scope" style="height:180px;">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="vert-align text-center">Data</th>
            <th class="vert-align text-center">Técnico</th>
            <th class="vert-align text-center">Plano</th>
            <th class="vert-align text-center">Plano Down</th>
            <th class="vert-align text-center">Plano Up</th>
            <th class="vert-align text-center">Teste</th>
            <th class="vert-align text-center">Status</th>
            <th class="vert-align text-center">Resultados</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="item in HDC.testes">
            <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{item.tecnico}}</td>
            <td class="vert-align text-center">{{item.plano_nome}}</td>
            <td class="vert-align text-center">{{item.plano_down}}</td>
            <td class="vert-align text-center">{{item.plano_up}}</td>
            <td class="vert-align text-center">{{item.teste}}</td>
            <td class="vert-align text-center"><span class="label label-default"
                ng-class="[{'label-danger': item.status == 'nok'}, {'label-success': item.status == 'ok'}]">{{item.status}}</span>
            </td>
            <td class="vert-align text-center">{{item.resultados}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- FIM TESTES CAMPO-->

<div ng-include="'app/helpdesk/redirecionamento.form.html'"></div>
<div ng-include="'app/helpdesk/redirecionamento.novo.form.html'"></div>

<div ng-include="'app/common/loading.modal.html'"></div>