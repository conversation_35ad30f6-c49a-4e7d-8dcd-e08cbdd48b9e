'use strict';

angular.module('app')

    .config(function ($routeProvider) {
        $routeProvider
            .when('/helpdesk/interfocus', {
                templateUrl: 'app/helpdesk/helpdesk.html',
                controller: 'HelpDeskController',
                controllerAs: 'HDC',
                title: 'Helpdesk',
                authorize: ['concentrador.read', 'concentrador.write']
            })

            .when('/helpdesk', {
                templateUrl: 'app/helpdesk/helpdesk.ixc.html',
                controller: 'HelpDeskIxcController',
                controllerAs: 'HDC',
                title: 'Helpdesk',
                authorize: ['concentrador.read', 'concentrador.write']
            })

            .when('/helpdesk/:username*', {
                templateUrl: 'app/helpdesk/helpdesk.ixc.html',
                controller: 'HelpDeskIxcController',
                controllerAs: 'HDC',
                title: 'Helpdesk',
                authorize: ['concentrador.read', 'concentrador.write']
            })

            .when('/emails', {
                templateUrl: 'app/helpdesk/emails.list.html',
                controller: 'EmailsListController',
                controllerAs: 'ELC',
                title: 'Gerenciar e-mails',
                authorize: ['concentrador.read', 'concentrador.write', 'comercial.read', 'comercial.write']
            })
        /*
        .when('/helpdesk/atendimento', {
            templateUrl: 'app/helpdesk/atendimento.html',
            controller: 'AtendimentoController',
            controllerAs: 'AC',
            title: 'Atendimento',
            authorize: ['concentrador.read', 'concentrador.write']
        })
        
        .when('/helpdesk/atendimento/mk', {
            templateUrl: 'app/helpdesk/atendimento.mk.html',
            controller: 'AtendimentoMkController',
            controllerAs: 'AMC',
            title: 'Atendimento',
            authorize: ['concentrador.read', 'concentrador.write']
        })

        .when('/helpdesk/atendimento/juniper', {
            templateUrl: 'app/helpdesk/atendimento.juniper.html',
            controller: 'AtendimentoJuniperController',
            controllerAs: 'AJC',
            title: 'Atendimento',
            authorize: ['concentrador.read', 'concentrador.write']
        })
        */
    });
