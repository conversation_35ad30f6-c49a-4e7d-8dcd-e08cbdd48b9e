(function () {
	'use strict';

	angular
		.module('app')
		.controller('EmailsListController', EmailsListController);

	/** @ngInject */
	function EmailsListController($http, API_CONFIG, $routeParams, $location, $scope, $filter, $rootScope, toaster, $window, $localStorage, AlertService) {

		$scope.onlyNumbers = /^\d+$/;

		var vm = this;

		vm.contratoSelecionado = null;
		vm.id_cliente = null;
		vm.cliente_nome = null;
		vm.contratos = [];
		vm.contratos_grupos = [];
		vm.campo = 'cnpj_cpf';
		vm.termo = '';
		vm.pesquisandoCliente = false;
		vm.cidade = 'pocos';
		vm.tecnologia = 'todas';
		vm.getEmailsList = getEmailsList;
		vm.atualizandoEmails = false;
		vm.checkAdicionar = checkAdicionar;
		vm.excluir = excluir;
		vm.migrar = migrar;
		vm.novo_email_default = {
			endereco: '',
			senha: '',
			dominio: ''
		};
		vm.novo_email = {
			valid: false,
			endereco: '',
			senha: '',
			dominio: ''
		};
		vm.initFrmNovoEmail = initFrmNovoEmail;
		vm.initFrmMigrarEmail = initFrmMigrarEmail;
		vm.getNovoContrato = getNovoContrato;
		vm.migracao = {};
		vm.selecionarContrato = selecionarContrato;
		vm.resetarSenha = resetarSenha;
		vm.atualizarEmails = atualizarEmails;

		vm.formatTimestamp = formatTimestamp;

		vm.lastTermo = null;
		vm.lastCampo = null;

		vm.formatEndereco = formatEndereco;
		vm.emailAddDisabled = emailAddDisabled;

		function emailAddDisabled() {
			AlertService.alert({
				title: 'Desabilitado',
				html: 'A função de adicionar e-mails está temporariamente desabilitada.'
			});
		}

		function resetMigracaoObj() {
			vm.migracao = {
				contratoNovo: {
					id: '',
					loaded: false,
					gerar_nova_senha: false,
					nova_senha: randomPassword(),
					endereco: '',
					numero: ''
				}
			};
		}

		activate();

		function activate() {
			// Caso já tenha pesquisado o cliente na tela do Helpdesk, já busca os e-mails deste cliente
			if ($routeParams != null && $routeParams.login != null) {
				vm.campo = 'login';
				vm.termo = $routeParams.login;
				getEmailsList();
			}
			else if ($routeParams != null && $routeParams.id_contrato != null) {
				vm.campo = 'id_contrato';
				vm.termo = $routeParams.id_contrato;
				getEmailsList();
			}
			else {
				$('#termo_pesquisa').focus();
			}

			resetMigracaoObj();

			resetNovoEmail();
		}

		function formatEndereco() {
			var input = $('#novoemail_endereco');
			var value = input.val().trim();
			var regex = new RegExp('^[a-zA-Z0-9]+([._-][a-zA-Z0-9]+)*$');

			input.val(value);

			if (regex.test(value)) {
				vm.novo_email.valid = true;
				input.css('border-color', '#0B0');
			}
			else {
				vm.novo_email.valid = false;
				input.css('border-color', '#B00');
			}
		}

		function resetarSenha(email_id) {
			var novaSenha = randomPassword();

			$http({
				url: API_CONFIG.url + '/emails/editar/' + email_id,
				method: 'PUT',
				data: {
					operador: $rootScope.operador,
					senha: novaSenha
				},
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					AlertService.alert({
						title: 'A senha foi gerada com sucesso!',
						html: '<div class="align-center">Nova senha:<br/><div style="font-size: 11pt; letter-spacing: 2px;"><b>' + novaSenha + '</b></div><br/><b>O cliente deve alterar esta senha dentro das próximas 24 horas.</b></div>'
					});
					atualizarEmails();
				}
				else {
					AlertService.alert({
						html: 'Ocorreu um erro ao gerar uma nova senha.'
					});
				}
			});
		}

		function randomPassword() {
			return Math.random().toString(36).slice(-6).toUpperCase();
		}

		function initFrmNovoEmail() {
			// Para contratos inativos
			if (vm.contratoSelecionado.status == 'D' || vm.contratoSelecionado.status == 'I')
				return false;

			angular.element('#frmnovoemail').modal('show');

			vm.novo_email.senha = randomPassword();

			switch (vm.contratoSelecionado.cidade.toUpperCase()) {
				case 'AGUAS DA PRATA':
				case 'ÁGUAS DA PRATA':
				case 'VARGEM GRANDE DO SUL':
					vm.novo_email.dominio = 'tmmail.com.br';
					break;
				case 'CALDAS':
				case 'POÇOS DE CALDAS':
					vm.novo_email.dominio = 'pocos-net.com.br';
					break;
				case 'ANDRADAS':
					vm.novo_email.dominio = 'andradas-net.com.br';
					break;
				case 'ESPÍRITO SANTO DO PINHAL':
					vm.novo_email.dominio = 'netpinhal.com.br';
					break;
				case 'SANTO ANTÔNIO DO JARDIM':
					vm.novo_email.dominio = 'jardim-net.com.br';
					break;
				case 'CAMPESTRE':
					vm.novo_email.dominio = 'campestre-net.com.br';
					break;
				case 'SÃO JOÃO DA BOA VISTA':
					vm.novo_email.dominio = 'netsaojoao.com.br';
					break;
			}

			$window.setTimeout(function () {
				$('#novoemail_endereco').focus();
			}, 100);
		}

		function initFrmMigrarEmail(email) {
			resetMigracaoObj();
			$('#novo_contrato_id').on('keydown change blur', function (e) {
				if ($(this).val().trim() !== '' && e.keyCode == 13)
					getNovoContrato();
				else {
					vm.migracao.contratoNovo.loaded = false;
					vm.migracao.contratoNovo.login = '';
					vm.migracao.contratoNovo.cnpj_cpf = '';
					vm.migracao.contratoNovo.cliente_nome = '';
					vm.migracao.contratoNovo.endereco = '';
					vm.migracao.contratoNovo.numero = '';
				}
			});
			vm.migracao.email = email;
			
			vm.contratos.forEach(function (contrato) {
				contrato.emails.forEach(function (emailData) {
					if (emailData.mailadd === email)
						vm.migracao.contratoAtual = contrato;
				});
			});

			window.setTimeout(function () {
				angular.element('#novo_contrato_id').focus();
			}, 300);
		}

		function getNovoContrato() {
			var id = vm.migracao.contratoNovo.id;
			if (id.trim() === '') {
				AlertService.alert({
					html: 'O <b>ID do contrato</b> não pode ficar em branco.'
				});
				return;
			}

			$http({
				url: API_CONFIG.url + '/emails/listar/id_contrato/' + id,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				
				if (response.data.status == 'not_found') {
					AlertService.alert({
						html: 'Não foi encontrado um contrato com este ID.'
					});
					return;
				}

				vm.migracao.contratoNovo.loaded = true;

				var contratoNovo = null;
				response.data.contratos.forEach(function (contrato) {
					if (contrato.id_contrato == id)
						contratoNovo = contrato
				});
				if (!contratoNovo) {
					AlertService.alert({
						html: 'Houve um erro ao consultar os dados deste contrato.'
					});
					return;
				}

				vm.migracao.contratoNovo.login = contratoNovo.logins[0];
				vm.migracao.contratoNovo.cnpj_cpf = response.data.cnpj_cpf;
				vm.migracao.contratoNovo.cliente_nome = response.data.nome;
				vm.migracao.contratoNovo.endereco = contratoNovo.endereco;
				vm.migracao.contratoNovo.numero = contratoNovo.numero;
			}).catch(function (err) {
					console.log(err);
				});
		}

		function selecionarContrato(contrato) {
			vm.contratoSelecionado = contrato;
		}

		function atualizarEmails() {
			vm.campo = vm.lastCampo;
			vm.termo = vm.lastTermo;

			getEmailsList({ refreshing: true });
		}

		function getEmailsList(options) {
			var termo = vm.termo.trim();

			if (termo.trim() === '') {
				return false;
			}

			if (vm.campo == 'ie_identidade') {
				if (termo.length < 5) {
					alert('O RG/I.E. digitado é muito curto.');
					return false;
				}
			}
			else if (vm.campo == 'cnpj_cpf') {
				termo = termo.replace(/\D/g, '');
			}

			if (termo != vm.lastTermo) {
				vm.contratoSelecionado = null;
			}
			
			vm.id_cliente = null;
			vm.cliente_nome = null;
			vm.contratos = [];
			vm.contratos_grupos = [];
			vm.contratoSelecionado = null;

			vm.lastTermo = termo;
			vm.lastCampo = vm.campo;

			vm.atualizandoEmails = true;

			$http({
				url: API_CONFIG.url + '/emails/listar/' + vm.campo + '/' + termo,
				method: "GET",
				ignoreLoadingBar: true
			}).then(function (response) {
				vm.atualizandoEmails = false;

				if (response.data.status == 'not_found') {
					AlertService.alert({
						html: 'Não foram encontrados resultados para a busca.'
					});
					return;
				}

				vm.id_cliente = response.data.id_cliente;
				vm.cliente_nome = response.data.nome;
				vm.cnpj_cpf = response.data.cnpj_cpf;
				angular.copy(response.data.contratos, vm.contratos);
				
				// Armazena um array dividido em grupos de 4 itens, para facilitar o posicionamento no layout
				angular.copy(chunk(response.data.contratos, 4), vm.contratos_grupos);

				window.setTimeout(function () {
					if (options != null && options.refreshing && vm.contratoSelecionado != null)
						$('#contrato-item-' + vm.contratoSelecionado.id_contrato).click();

					else if (vm.campo == 'login') {
						vm.contratos.forEach(function (contrato) {
							if (contrato.logins.includes(termo))
								$('#contrato-item-' + contrato.id_contrato).click();
						});
					}

					else if (vm.campo == 'id_contrato') {
						vm.contratos.forEach(function (contrato) {
							if (contrato.id_contrato == vm.termo)
								$('#contrato-item-' + contrato.id_contrato).click();
						});
					}

					else if (vm.campo == 'email') {
						vm.contratos.forEach(function (contrato) {
							contrato.emails.forEach(function (email) {
								if(email.mailadd == termo)
									$('#contrato-item-' + contrato.id_contrato).click();
							});
						});
					}

					else if (vm.contratos.length == 1)
						$('#contrato-item-' + vm.contratos[0].id_contrato).click();
				}, 150);
			})
				.catch(function (err) {
					console.log(err);
				});

			vm.id_cliente = null;
			vm.cliente_nome = null;
			vm.contratos = [];
			vm.contratos_grupos = [];
		}

		function formatTimestamp(timestamp) {
			var date = timestamp.split(' ')[0];
			var time = timestamp.split(' ')[1];

			var dateParts = date.split('-');
			var newDate = dateParts[2] + '/' + dateParts[1] + '/' + dateParts[0] + ' - ' + time + 'h';

			return newDate;
		}

		function resetNovoEmail() {
			angular.copy(vm.novo_email_default, vm.novo_email);
		}

		function checkAdicionar() {
			if (!vm.novo_email.valid) {
				AlertService.alert({
					title: 'Ops...',
					html: 'O endereço inserido é inválido.'
				});
				return false;
			}

			AlertService.alert({
				html: 'Deseja realmente adicionar o e-mail <b>' + vm.novo_email.endereco + '@' + vm.novo_email.dominio + '</b>?',
				buttons: [
					{
						text: 'Sim',
						action: adicionar
					}
				],
				cancelBtnText: 'Cancelar',
				cancelBtnColor: 'warning'
			});
		}

		function adicionar() {
			// Para contratos inativos
			if (vm.contratoSelecionado.status == 'D' || vm.contratoSelecionado.status == 'I')
				return false;

			$http({
				url: API_CONFIG.url + '/emails/inserir',
				method: "POST",
				data: {
					operador: $rootScope.operador,
					id_cliente: vm.id_cliente,
					id_contrato: vm.contratoSelecionado.id_contrato,
					endereco: vm.novo_email.endereco,
					senha: vm.novo_email.senha,
					dominio: vm.novo_email.dominio
				},
				ignoreLoadingBar: true
			}).then(function (response) {
				var mailadd = vm.novo_email.endereco + '@' + vm.novo_email.dominio;
				if (response.data.status == 'success') {
					AlertService.alert({
						title: 'Sucesso!',
						html: '<div class="align-center">O e-mail <b>' + mailadd + '</b> foi adicionado.<br/></br>Senha gerada:<br/><div style="font-size: 11pt; letter-spacing: 2px;"><b>' + vm.novo_email.senha + '</b></div><br/><b>O cliente deve alterar sua senha dentro das próximas 24 horas.</b></div>'
					});
				}
				else if (response.data.status == 'unavailable') {

					AlertService.alert({
						title: 'Ops...',
						html: 'O e-mail <b>' + mailadd + '</b> já existe. Escolha outro endereço.<br/><br/>Caso este endereço tenha sido excluído recentemente, aguarde 3 dias, a partir da exclusão, para poder criar outra conta com o mesmo endereço.'
					});
					return false;
				}
				else {
					AlertService.alert({
						html: 'Ocorreu um erro ao adicionar o e-mail.'
					});
					return false;
				}
				atualizarEmails();
				resetNovoEmail();
				$('#frmnovoemail_fechar').click();
			})
				.catch(function (err) {
					console.log(err);

				});
		}

		function migrar() {
			console.log('migrar()!');
			if (vm.migracao.contratoAtual.id_contrato == vm.migracao.contratoNovo.id) {
				AlertService.alert({
					html: 'O contrato de destino não pode ser o mesmo do atual'
				});
				return false;
			}

			var email = vm.migracao.email;
			var novo_contrato = vm.migracao.contratoNovo.id;
			var gerar_nova_senha = vm.migracao.contratoNovo.gerar_nova_senha;
			var nova_senha = vm.migracao.contratoNovo.nova_senha;
			

			$http({
				url: API_CONFIG.url + '/emails/migrar',
				method: "POST",
				data: {
					operador: $rootScope.operador,
					email: email,
					novo_contrato: novo_contrato,
					gerar_nova_senha: gerar_nova_senha,
					nova_senha: nova_senha
				},
				ignoreLoadingBar: true
			}).then(function (response) {
				console.log('MIGRAR RESPONSE >>:');
				console.log(response.data);
				if (response.data.status == 'success') {
					AlertService.alert({
						title: 'Sucesso!',
						html: 'O e-mail foi migrado para o contrato #<b>' + novo_contrato + '</b>.<br><br>Deseja carregar a lista de e-mails do contrato #<b>' + novo_contrato + '</b>?',
						buttons: [
							{
								text: 'Sim',
								action: function () {
									openInNewTab('/emails?id_contrato=' + novo_contrato);
								}
							}
						],
						cancelBtnText: 'Não'
					});
					$('#frmmigraremail').modal('hide');
					atualizarEmails();
				}
				else {
					AlertService.alert({
						html: 'Ocorreu um erro ao migrar o e-mail.'
					});
				}
			}).catch(function (err) {
				console.log(err);
			});
		}

		function excluir(email_id) {
			$http({
				url: API_CONFIG.url + '/emails/excluir/' + email_id,
				method: "POST",
				data: { operador: $rootScope.operador },
				ignoreLoadingBar: true
			}).then(function (response) {
				if (response.data.status == 'success') {
					AlertService.alert({
						html: 'O e-mail foi excluído.'
					});
					atualizarEmails();
				}
				else {
					AlertService.alert({
						html: 'Ocorreu um erro ao excluir o e-mail.'
					});
				}
			})
				.catch(function (err) {
					console.log(err);

				});
		}
	
	function openInNewTab(url) {
		var win = window.open(url, '_blank');
		win.focus();
	}

	}

	function chunk(arr, size) {
		var newArr = [];
		for (var i = 0; i < arr.length; i += size) {
			newArr.push(arr.slice(i, i + size));
		}
		return newArr;
	}

})();
