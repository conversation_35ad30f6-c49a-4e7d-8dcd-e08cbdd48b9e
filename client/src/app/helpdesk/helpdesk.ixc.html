<ol class="breadcrumb">
  <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
  <li><i class="glyphicon glyphicon-edit"></i> Atendimento</li>
  <li><a href="/helpdesk"><i class="glyphicon glyphicon-warning-sign"></i> Helpdesk</a></li>
</ol>

<div class="barra ng-scope">

  <div class="table">
    <div class="tr">
      <div class="td">
        <div class="form-group">
          <div class="form-group pull-left">
            <form class="form-inline" role="form">
              <div class="form-group">
                <select class="form-control" ng-model="HDC.campo">
                  <option value="login">Login</option>
                  <option value="serial">Serial</option>
                  <option value="patrimonio">Patrimônio</option>
                  <option value="mac">MAC</option>
                </select>
              </div>
              <div class="form-group">
                <input size="50" maxlength="100" class="form-control" type="text" ng-model="HDC.termos"
                  id="input__helpdesk__search_contrato">

                <button class="btn btn-default" title="Pesquisar" ng-click="HDC.getContrato()"
                  ng-disabled="HDC.termos == '' || HDC.atualizandocontrato"><i class="glyphicon glyphicon-search"
                    ng-if="!HDC.atualizandocontrato"></i><img src="assets/images/ajax-loader.gif"
                    ng-show="HDC.atualizandocontrato"> Pesquisar</button>
                <button class="btn btn-default" ng-click="HDC.limpa()" ng-disabled="HDC.termos == ''">
                  <span class="glyphicon glyphicon-refresh"></span> Limpar
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="td align-right valign-middle">
        <div class="form-group" style="margin: 0px;">
          <button class="btn btn-primary" ng-disabled="!HDC.contrato.hasOwnProperty('cliente')"
            ng-click="HDC.gerenciarEmails();">
            <i class="glyphicon glyphicon-envelope"></i>
            Gerenciar e-mails
          </button>
        </div>
      </div>
    </div>
  </div>


</div>
<div class="alert alert-danger" role="alert" ng-if="HDC.localizado==false">
  <span class="glyphicon glyphicon-remove"></span>
  <strong>Erro</strong>
  <hr class="message-inner-separator">
  <p>Login não encontrado</p>
</div>

<div ng-if="HDC.contrato.hasOwnProperty('cliente')" class="hd-alerts-container">
  <div ng-if="HDC.onuAlarms.length > 0 || HDC.loadingOnuAlarms">
    <fieldset class="alerts-fieldset">
      <legend align="center">
        <strong>Alarmes ONU</strong>
      </legend>

      <div ng-if="HDC.onuAlarms.length == 0 && HDC.loadingOnuAlarms">
        <div class="w-100 full-width text-center" style="display: flex; align-items: center; justify-content: center; min-width:90px; min-height: 52px;">
          <img class="spinner-30" src="assets/images/spinner-blue.gif" style="margin: 0 auto;"/>
        </div>
      </div>

      <div ng-if="HDC.onuAlarms.length > 0"
        ng-repeat="alarme in HDC.onuAlarms" class="hd-alert small equal-height-alert"
        ng-class="[{'bg-warning-dark': alarme.status == 'finalizado'}, {'bg-danger-dark': alarme.status == 'ativo'}]"
        title="Código: {{alarme.codigo}}&#10;Descrição: {{alarme.descricao}}">
        <div class="hd-alert-status {{alarme.status}}">({{alarme.status == 'ativo' ? 'ATIVO' : (alarme.status ==
          'finalizado' ? 'FINALIZADO' : 'INDEF.')}})</div>
        {{alarme.codigo.substring(0,11)}}
        <br>
        <span class="hd-alert-data">
          {{alarme.total}}
        </span>
        <br>
        {{alarme.status == 'ativo' ? 'Última ocorrência:' : (alarme.status == 'finalizado' ? 'Finalizado em:' : '')}}
        <br>
        <span>
          {{alarme.status == 'ativo' ? (alarme.ultima_ocorrencia | amDateFormat:'DD/MM/YYYY HH:mm:ss'): (alarme.status
          == 'finalizado' ? (alarme.finalizado_em | amDateFormat:'DD/MM/YYYY HH:mm:ss') : '')}}
        </span>
      </div>

    </fieldset>
  </div>

  <div ng-if="HDC.alertasIxc.length > 0">
    <fieldset class="alerts-fieldset">
      <legend align="center">
        <strong>Verificações IXC</strong>
      </legend>

      <div ng-repeat="alerta in HDC.alertasIxc" class="hd-alert bg-danger-dark equal-height-alert flex-alert"
        title="{{alerta.descricao}}">
        {{alerta.nome}}
        <br>
        <span class="ixc-alert-data">{{alerta.mensagem}}</span>
      </div>

    </fieldset>
  </div>

  <div ng-if="HDC.alertasGerais.length > 0">
    <fieldset class="alerts-fieldset">
      <legend align="center">
        <strong>Verificações gerais</strong>
      </legend>

      <div ng-repeat="alerta in HDC.alertasGerais" class="hd-alert equal-height-alert flex-alert bg-{{alerta.cor}}-dark"
        title="{{alerta.descricao}}">
        {{alerta.nome}}
        <br>
        <span class="hd-alert-data">
          {{alerta.mensagem}}
        </span>
      </div>

    </fieldset>
  </div>
</div>
<!-- ONUS NÃO AUTORIZADAS -->
<!--
<div class="panel panel-default" style="margin-bottom: 0px">
  <div class="panel-heading clearfix">

    <h3 class="panel-title" style="margin-top: 5px;"><strong> <svg class="blinking" width="8" height="5">
          <circle fill="#337ab7" r="2" cx="2" cy="2"></circle>
        </svg> ONUs Não Autorizadas</strong></h3>
  </div>
  <div class="panel-body">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Status</th>
          <th class="vert-align text-center">Login</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">OLT</th>
          <th class="vert-align text-center">IP OLT</th>
          <th class="vert-align text-center">Placa</th>
          <th class="vert-align text-center">Porta</th>

        </tr>
      </thead>
      <tbody>

        <tr ng-repeat="onu in HDC.onusNaoAutorizadas">
          <td class="vert-align text-center">{{onu.data_alarme | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</span></td>
          <td class="vert-align text-center">{{onu.status}}</td>
          <td class="vert-align text-center">{{onu.username}}</td>
          <td class="vert-align text-center">{{onu.serial}}</td>
          <td class="vert-align text-center">{{onu.mac}}</td>
          <td class="vert-align text-center">{{onu.patrimonio}}</td>
          <td class="vert-align text-center">{{onu.modelo}}</td>
          <td class="vert-align text-center">{{onu.olt}}</span></td>
          <td class="vert-align text-center">{{onu.olt_ip}}</span></td>
          <td class="vert-align text-center">{{onu.placa}}</td>
          <td class="vert-align text-center">{{onu.porta}}</td>

        </tr>
      </tbody>
    </table>
  </div>
</div>
-->

<!-- FIM ONUS NÃO AUTORIZADAS-->



<!-- ALERTAS DE REDE -->

<div class="alert alert-danger" ng-repeat="alerta in HDC.alertas" role="alert"><span
    class="glyphicon glyphicon-remove"></span> <strong>{{alerta.titulo}}</strong>
  <div class="pre-scrollable" style="height:150px;">
    <pre class="alert alert-danger" style="border: none;">{{alerta.mensagem}}</pre>
  </div>
</div>

<!-- FIM ALERTAS DE REDE -->
<!-- CONTRATO -->

<div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('cliente')">
  <div class="panel-heading clearfix">
    <span class="pull-right">
      <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandocontrato"> <button class="btn btn-default btn-sm"
        ng-click="HDC.getContrato(HDC.contrato.login)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
    </span>
    <h3 class="panel-title" style="margin-top: 5px;"><strong>Contrato</strong></h3>
  </div>
  <div class="panel-body">
    <table class="table table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Cliente</th>
          <th class="vert-align text-center">Cidade</th>
          <th class="vert-align text-center">Status Contrato</th>
          <th class="vert-align text-center">Status Acesso</th>
          <th class="vert-align text-center">Status Login</th>
          <th class="vert-align text-center">Plano</th>
          <th class="vert-align text-center">Download</th>
          <th class="vert-align text-center">Upload</th>
          <th class="vert-align text-center">Login</th>
          <th class="vert-align text-center">Senha</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="vert-align text-center">{{HDC.contrato.cliente}}</td>
          <td class="vert-align text-center">{{HDC.contrato.cidade}}</td>
          <td class="vert-align text-center">
            <span class="label label-default"
              ng-class="[{'label-success': HDC.contrato.status_contrato == 'Ativo'}, {'label-danger': HDC.contrato.status_contrato != 'Ativo'}]">
              {{HDC.contrato.status_contrato}}
            </span>
          </td>
          <td class="vert-align text-center">
            <span class="label label-default"
              ng-class="[{'label-success': HDC.contrato.status_internet == 'Ativo'}, {'label-danger': HDC.contrato.status_internet != 'Ativo'}]">
              {{HDC.contrato.status_internet}}
            </span>
          </td>
          <td class="vert-align text-center">
            <span class="label label-default"
              ng-class="[{'label-success': HDC.contrato.status_login == 'Ativo'}, {'label-danger': HDC.contrato.status_login == 'Inativo'}]">
              {{HDC.contrato.status_login}}
            </span>
          </td>
          <!--
          <td class="vert-align text-center"><span class="label label-default"
              ng-class="[{'label-success': HDC.contrato.status_internet == 'Ativo'},
                {'label-danger': HDC.contrato.status_internet == 'Inativo' || HDC.contrato.status_internet == 'Desativado' ||
                  HDC.contrato.status_internet == 'Bloqueio Automático' || HDC.contrato.status_internet == 'Bloqueio Manual' ||
                  HDC.contrato.status_internet == 'Financeiro em Atraso'
              }, {'label-warning': HDC.contrato.status_internet == 'Aguardando Assinatura'}]">{{HDC.contrato.status_internet}}</span></td>
          -->

          <td class="vert-align text-center">{{HDC.contrato.plano}}</td>
          <td class="vert-align text-center">{{HDC.contrato.download}}</td>
          <td class="vert-align text-center">{{HDC.contrato.upload}}</td>
          <td class="vert-align text-center">{{HDC.contrato.login}}</td>

          <td class="vert-align text-center"><span class="label label-default" style="cursor: pointer;"
              ng-click="HDC.mostraSenha()"
              ng-if="HDC.contrato.senha !== '' && HDC.contrato.senha !== undefined && !HDC.senhaVisivel">Mostrar
              Senha</span><span ng-if="HDC.senhaVisivel==true">{{HDC.contrato.senha}} <span class="label label-danger"
                style="cursor: pointer;" ng-click="HDC.mostraSenha()">x</span></span></td>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-4">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th colspan="2" class="vert-align text-center">Dados técnicos</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="vert-align text-center"><b>Transmissor:</b></td>
              <td ng-if="HDC.contrato.transmissor.startsWith('AP ')" class="vert-align text-center"><a
                  href="http://{{HDC.contrato.ip_transmissor}}:8922" target="_blank"><span
                    class="glyphicon glyphicon-new-window" style="margin-right: 5px;"></span>
                  {{HDC.contrato.transmissor}}</a></td>
              <td ng-if="!HDC.contrato.transmissor.startsWith('AP ')" class="vert-align text-center">
                {{HDC.contrato.transmissor}}</td>
            </tr>
            <tr ng-if="!HDC.contrato.transmissor.startsWith('AP ')">
              <td class="vert-align text-center"><b>Tipo OLT:</b></td>
              <td class="vert-align text-center">{{HDC.contrato.olt_modelo}}</td>
            </tr>
            <tr ng-if="!HDC.contrato.transmissor.startsWith('AP ')">
              <td class="vert-align text-center"><b>Interface:</b></td>
              <td class="vert-align text-center">{{HDC.contrato.interface.replace('0-0-', '')}}</td>
            </tr>
            <tr ng-if="!HDC.contrato.transmissor.startsWith('AP ')">
              <td class="vert-align text-center"><b>VLAN PPPoE:</b></td>
              <td class="vert-align text-center">{{HDC.contrato.vlan_pppoe}}</td>
            </tr>
            <tr ng-if="!HDC.contrato.transmissor.startsWith('AP ')">
              <td class="vert-align text-center"><b>VLAN IPTV:</b></td>
              <td class="vert-align text-center">{{HDC.contrato.vlan_iptv}}</td>
            </tr>

          </tbody>
        </table>
      </div>
      <div class="col-md-5">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th colspan="4" class="vert-align text-center" style="font-size: 9pt;">Equipamentos em comodato</th>
            </tr>
            <tr ng-if="HDC.contrato.comodatos.length > 0">
              <th class="vert-align text-center">Patrimônio</th>
              <th class="vert-align text-center">Descrição</th>
              <th class="vert-align text-center">MAC</th>
              <th class="vert-align text-center">Serial</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="comodato in HDC.contrato.comodatos">
              <td class="vert-align text-center">{{comodato.patrimonio}}</td>
              <td class="vert-align text-center">{{comodato.descricao}}</td>
              <td class="vert-align text-center">{{comodato.mac}}</td>
              <td class="vert-align text-center">{{comodato.serial}}</td>
            </tr>
            <tr ng-if="HDC.contrato.comodatos.length == 0">
              <td colspan="4" class="vert-align text-center">Não há equipamentos em comodato</td>
            </tr>
            <tr ng-if="!HDC.contrato.macInComodatos && HDC.contrato.mac.trim().length > 0">
              <td colspan="4" class="vert-align text-center" style="background: #DC615E; color: #FFF; font-size: 9pt;">
                <b>Erro: o MAC do login não consta nos comodatos do contrato</b>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col-md-3">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th class="vert-align text-center">Pacote</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="pacote in HDC.contrato.pacotes">
              <td class="vert-align text-center">{{pacote.descricao}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="alert alert-success" role="alert" ng-if="HDC.contrato.status_internet == 'Ativo' && !HDC.contrato.semiDedicado">
      <span class="glyphicon glyphicon-ok"></span>
      <strong style="font-size: 10pt;">Resultado do checklist</strong>
      <hr class="message-inner-separator">
      <p>Não há pendências no contrato</p>
    </div>

    <div class="alert alert-danger" role="alert" ng-if="HDC.contrato.status_internet == 'Inativo' || HDC.contrato.status_internet == 'Desativado' ||
    HDC.contrato.status_internet == 'Bloqueio Automático' || HDC.contrato.status_internet == 'Bloqueio Manual'"><span
        class="glyphicon glyphicon-remove"></span>
      <strong>Resultado do checklist</strong>
      <hr class="message-inner-separator">
      <p>O contrato do cliente encontra-se <strong>{{HDC.contrato.status_internet}}</strong>. Nenhum serviço estará
        disponível até a regularização do contrato.</p>
    </div>

    <div class="alert alert-danger" role="alert"
      ng-if="HDC.contrato.status_internet == 'Aguardando Assinatura' || HDC.contrato.status_internet == 'Financeiro em Atraso' || HDC.contrato.semiDedicado">
      <span class="glyphicon glyphicon-info"></span>
      <strong style="font-size: 10pt;">Resultado do checklist</strong>
      <hr class="message-inner-separator">

      <p ng-if="HDC.contrato.status_internet == 'Aguardando Assinatura'" style="font-size: 10pt;">O cliente ainda não
        realizou o aceite eletrônico (<a href="#" data-toggle="modal" data-target="#frminstrucoes"><b>VER
            INSTRUÇÕES</b></a>).</p>

      <p ng-if="HDC.contrato.status_internet == 'Financeiro em Atraso'" style="font-size: 10pt;">O cliente possui
        faturas em atraso e sua velocidade foi reduzida.</p>

      <p ng-if="HDC.contrato.semiDedicado" style="font-size: 10pt;">
        <b>Alerta! Cliente Semi-Dedicado</b>: Este cliente possui atendimento VIP. Priorize o atendimento e ofereça a melhor experiência possível.
      </p>

      <p style="margin-top: 10px;">
        <button class="btn btn-primary" ng-if="!HDC.exibirVerificacoes"
          ng-click="HDC.prosseguirVerificacoes();"><b>PROSSEGUIR COM AS VERIFICAÇÕES</b><span
            class="glyphicon glyphicon-arrow-right" style="margin-left: 10px;"></span></button>
      </p>
    </div>

  </div>
</div>
<!-- FIM CONTRATO-->

<div ng-if="HDC.exibirVerificacoes" id="div-verificacoes">
  <!-- ONU -->
  <div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('cliente')">
    <div class="panel-heading clearfix">
      <span class="pull-right">
        <button class="btn btn-default btn-sm" ng-click="HDC.getOnuInfo()"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span>
      <h3 class="panel-title" style="margin-top: 5px;"><strong>ONU</strong></h3>
    </div>
    <div class="panel-body">

      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#equipamento" data-toggle="tab">
            <i class="glyphicon glyphicon-hdd"></i> Equipamento instalado </a>

        </li>

        <div class="row" ng-if="HDC.contrato.interface">
          <button class="btn btn-warning" title="Executa novamente o provisionamento da ONU" ng-click="HDC.onuReauth()"
            style="margin-left: 10px;"><i class="glyphicon glyphicon-repeat"></i>
            Reprovisionar equipamento
          </button>
        </div>
      </ul>
      <div id="equipamento" class="tab-pane fade in active">
        <div class="tab-content">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="vert-align text-center">Modelo</th>
                <th class="vert-align text-center">MAC</th>
                <th class="vert-align text-center">Serial</th>
                <th class="vert-align text-center">Patrimônio</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="vert-align text-center">{{HDC.contrato.modelo}}</td>
                <td class="vert-align text-center">{{HDC.contrato.mac}}</td>
                <td class="vert-align text-center">{{HDC.contrato.serial}}</td>
                <td class="vert-align text-center">{{HDC.contrato.patrimonio}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>

        <ul class="nav nav-tabs">
          <li class="active">
            <a data-target="#optical" data-toggle="tab">
              <i class="glyphicon glyphicon-flash"></i> Optical status</a>
          </li>
          <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandosinalonu"> <button
              class="btn btn-default btn-sm" ng-click="HDC.atualizaSinalOnu(HDC.contrato.serial)"><i
                class="glyphicon glyphicon-refresh"></i> Atualizar</button>
          </span>

        </ul>
        <div id="optical" class="tab-pane fade in active">
          <div class="tab-content">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center">Estado Operacional</th>
                  <th class="vert-align text-center">Distância</th>
                  <th class="vert-align text-center" ng-if="HDC.contrato.olt_modelo !== 'ZTE'">Último Desligamento</th>
                  <th class="vert-align text-center" ng-if="HDC.contrato.olt_modelo == 'ZTE'">Uptime</th>
                  <th class="vert-align text-center">Send Power</th>
                  <th class="vert-align text-center">Receive Power</th>
                  <th class="vert-align text-center">OLT Receive Power</th>
                  <th class="vert-align text-center">Temperature</th>
                  <th class="vert-align text-center">Voltage</th>
                  <th class="vert-align text-center">Bias Current</th>
                  <th class="vert-align text-center" ng-if="HDC.contrato.olt_modelo !== 'ZTE'">Blacklist</th>
                </tr>
              </thead>
              <tbody>
                <tr>

                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandostatusonu"><span class="label label-default" ng-class="[{'label-success': HDC.onu.status.OperState == 'UP'},
              {'label-danger': HDC.onu.status == 'DOWN'}]">{{HDC.onu.status.OperState}}</span></td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.rttvalue}}</td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo !== 'ZTE'"><img
                      src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandostatusonu">{{HDC.onu.status.LASTOFFTIME | amDateFormat:'DD/MM/YYYY
                    HH:mm:ss'}}
                  </td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo == 'ZTE'"><img
                      src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandostatusonu">{{HDC.onu.status.uptime}}
                  </td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.sendpower}}</td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.recvpower}}</td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.oltrecvpower}}</td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.temperature}}</td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.voltage}}</td>
                  <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                      ng-show="HDC.atualizandosinalonu">{{HDC.onu.sinal.biascurrent}}</td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo !== 'ZTE'"><img
                      src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandoblacklist">
                    <span class="label label-default" ng-class="[{'label-success': HDC.onu.blacklist == 'Não'},
                                                  {'label-danger': HDC.onu.blacklist == 'Sim'}]"
                      ng-if="!HDC.atualizandoblacklist">
                      {{HDC.onu.blacklist}}</span>

                    <span class="label label-default ng-scope" style="cursor: pointer;"
                      ng-click="HDC.removeBlacklist(HDC.contrato.serial)"
                      ng-if="HDC.onu.blacklist == 'Sim' && !HDC.atualizandoblacklist">Remover</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <ul class="nav nav-tabs">
          <li class="active">
            <a data-target="#lanstatus" data-toggle="tab">
              <i class="glyphicon glyphicon-list-alt"></i> LAN status</a>

          </li>

          <div class="row" ng-if="HDC.contrato.interface">
            <button class="btn btn-warning" title="Reescreve as VLANs na ONU" ng-click="HDC.vlanRewrite()"
              style="margin-left: 10px;"><i class="glyphicon glyphicon-edit"></i>
              Reescrever VLANs
            </button>
            <button class="btn btn-warning" title="Gerenciar as VLAN 2 de Multicast nas portas da ONU" data-toggle="modal" data-target="#frmvlanmulticast"
              style="margin-left: 10px;"><i class="glyphicon glyphicon-edit"></i>
              VLANs Multicast
            </button>
          </div>

        </ul>
        <div id="lanstatus" class="tab-pane fade in active">
          <div class="tab-content">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center">Porta</th>
                  <th class="vert-align text-center">VLANID</th>
                  <th class="vert-align text-center" ng-if="HDC.contrato.olt_modelo=='FBT'">VEIP</th>
                  <th class="vert-align text-center">Estado Operacional</th>

                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="porta in HDC.onu.lans">
                  <td class="vert-align text-center"><span class="badge">{{$index+1}}</span></td>
                  <td class="vert-align text-center"><strong>{{porta.PVID != 4088
                      ? porta.PVID : ''}}</strong></td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo=='FBT'">
                    <strong>{{porta.VEIP}}</strong>
                  </td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo=='FBT'"><span
                      class="label label-default" ng-class="[{'label-success': porta.OperStatus == 'UP'},
                          {'label-danger': porta.OperStatus !== 'UP'}]">{{porta.OperStatus}}</span>
                  </td>
                  <td class="vert-align text-center" ng-if="HDC.contrato.olt_modelo=='ZTE'"><span
                      class="label label-default"
                      ng-class="[{'label-success': porta.Operate_status !== 'disable'},
                                            {'label-danger': porta.Operate_status == 'disable'}]">{{porta.Operate_status}}</span>
                  </td>
                </tr>
              </tbody>
            </table>

          </div>
        </div>

        <ul class="nav nav-tabs" ng-if="HDC.contrato.olt_modelo=='ZTE'">
          <li class="active">
            <a data-target="#configonu" data-toggle="tab">
              <i class="glyphicon glyphicon-list-alt"></i> Configurações da ONU <img
                src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandoconfig"></a>
          </li>
          <span class="pull-right">
            <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoconfig"> <button
              class="btn btn-default btn-sm" ng-click="HDC.getOnuConfig(HDC.contrato.serial)"><i
                class="glyphicon glyphicon-refresh"></i> Atualizar</button>
          </span>

        </ul>

        <div id="configonu" class="tab-pane fade in active" ng-if="HDC.contrato.olt_modelo=='ZTE'">
          <div class="tab-content">
            <table class="table table-bordered table-hover" ng-if="!HDC.onu.config.hasOwnProperty('interface')">
              <tbody>
                <tr>
                  <td class="vert-align text-center"><b>Sem dados</b></td>
                </tr>
              </tbody>
            </table>

            <table class="table table-bordered table-hover" ng-if="HDC.onu.config.hasOwnProperty('interface')">

              <tbody>
                <tr>
                  <td class="vert-align text-center"><b>Interface</b></td>
                  <td class="vert-align">
                    <span ng-repeat="item in HDC.onu.config.interface"><span class="label label-primary">{{item}}</span>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="vert-align text-center"><b>PON</b></td>
                  <td class="vert-align" width="80%"><span ng-repeat="item in HDC.onu.config.pon_onu"><span
                        class="label label-primary">{{item}}</span> </span></td>
                </tr>
                <tr>
                  <td class="vert-align text-center"><b>VPORT1</b></td>
                  <td class="vert-align" width="80%"><span ng-repeat="item in HDC.onu.config.vport1"><span
                        class="label label-primary">{{item}}</span> </span></td>
                </tr>
                <tr>
                  <td class="vert-align text-center"><b>VPORT2</b></td>
                  <td class="vert-align" width="80%"><span ng-repeat="item in HDC.onu.config.vport2"><span
                        class="label label-primary">{{item}}</span> </span></td>
                </tr>
                <tr>
                  <td class="vert-align text-center"><b>VPORT3</b></td>
                  <td class="vert-align" width="80%"><span ng-repeat="item in HDC.onu.config.vport3"><span
                        class="label label-primary">{{item}}</span> </span></td>
                </tr>
                <tr>
                  <td class="vert-align text-center"><b>VPORT4</b></td>
                  <td class="vert-align" width="80%"><span ng-repeat="item in HDC.onu.config.vport4"><span
                        class="label label-primary">{{item}}</span> </span></td>
                </tr>
              </tbody>
            </table>
            VEIP: {{HDC.onu.lans[0].VEIP}}

          </div>
        </div>


        <div ng-if="HDC.contrato.tr069 == 1" id="dispositivos-conectados-container">
          <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#lanstatus" data-toggle="tab">
                <i class="glyphicon glyphicon-stats"></i> Dispositivos conectados
                ({{HDC.atualizandoAssocDevices || HDC.atualizandoTr069 ? '...' :
                HDC.currentTr069Config.totalAssociatedDevices}})</a>
            </li>
            <span class="pull-right">
              <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoAssocDevices"> <button
                class="btn btn-default btn-sm" ng-click="HDC.getAssocDevices();"><i
                  class="glyphicon glyphicon-refresh"></i>
                Atualizar</button>
            </span>
          </ul>
          <div id="landevices" ng-if="HDC.contrato.tr069 == 1" class="tab-pane fade in active"
            style="margin-bottom: 20px;">
            <div class="tab-content" style="overflow-y: auto; max-height: 200px;">
              <table class="table table-bordered"
                ng-if="HDC.atualizandoAssocDevices || HDC.atualizandoTr069 || HDC.currentTr069Config.totalAssociatedDevices == 0"
                style="margin-bottom: 0px;">
                <tr>
                  <td class="vert-align text-center">
                    {{HDC.atualizandoAssocDevices || HDC.atualizandoTr069 ? 'Buscando...' : 'Não há dispositivos
                    conectados.'}}
                  </td>
                </tr>
              </table>

              <table class="table table-bordered"
                ng-if="!HDC.atualizandoAssocDevices && !HDC.atualizandoTr069 && HDC.currentTr069Config.totalAssociatedDevices > 0"
                style="margin-bottom: 0px;">
                <thead>
                  <tr>
                    <th class="vert-align text-center">Interface</th>
                    <th class="vert-align text-center">Hostname</th>
                    <th class="vert-align text-center">IP</th>
                    <th class="vert-align text-center">MAC</th>
                    <th class="vert-align text-center">Conectado a</th>
                    <th class="vert-align text-center">Tempo conectado</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="(id, device) in HDC.currentTr069Config.associatedDevices" value="{{id}}">
                    <td class="vert-align text-center">
                      {{device.interface == 'ethernet' ? 'Cabo' : (device.interface == 'wlan' ? 'Wi-Fi' : '')}}
                    </td>
                    <td class="vert-align text-center">
                      {{device.hostname ? device.hostname : '[desconhecido]'}}
                    </td>
                    <td class="vert-align text-center">
                      {{device.ip}}
                    </td>
                    <td class="vert-align text-center">
                      {{device.mac}}
                    </td>
                    <td class="vert-align text-center">
                      <b>{{device.connectedAt}}</b>
                    </td>
                    <td class="vert-align text-center">
                      {{device.uptimeText}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>


        <div ng-if="HDC.contrato.tr069 == 1" id="gerencia-tr069-container" style="margin-bottom: 15px;">
          <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#gerencia-tr069" data-toggle="tab">
                <i class="glyphicon glyphicon-cog"></i> Gerência/TR069</a>
            </li>
            <span class="pull-right">
              <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizando_ip_dhcp"> <button
                class="btn btn-default btn-sm" ng-click="HDC.getDHCPIP();"><i class="glyphicon glyphicon-refresh"></i>
                Atualizar</button>
            </span>
          </ul>
          <div id="gerencia-tr069" class="tab-pane fade in active">
            <div class="tab-content">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th ng-if="HDC.contrato.preset == 1" class="vert-align text-center">Preset</th>
                    <th class="vert-align text-center">IP DHCP</th>
                    <th class="vert-align text-center">Interface web</th>
                    <th class="vert-align text-center">Senha Admin</th>
                    <th class="vert-align text-center" ng-if="HDC.userRoles.includes('root')">ACS <span class="text-danger" title="Visível apenas para usuários ROOT (COR)">(<i class="glyphicon glyphicon-user text-danger"></i>)</span></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td ng-if="HDC.contrato.preset == 1" class="vert-align text-center"
                      style="width: 30%; font-size: 9pt;">
                      <span ng-if="!HDC.atualizandoTr069 && !HDC.portalAPIError && !HDC.onuNotFound" class="label"
                        ng-class="HDC.tr069Config.presetEnabled ? 'label-success' : 'label-danger'">{{HDC.tr069Config.presetEnabled ?
                        'Habilitado' : 'Indisponível'}}</span>
                      <span ng-if="HDC.atualizandoTr069 || HDC.portalAPIError || HDC.onuNotFound"
                        class="label label-default">{{HDC.atualizandoTr069 ? 'Carregando...' : 'Sem informação'}}</span>
                    </td>
                    <td class="vert-align text-center" style="width: 30%; font-size: 9pt;">
                      <span ng-if="!HDC.contrato.ip_dhcp" class="label label-default">{{!HDC.atualizando_ip_dhcp ?
                        'Desconectado' : 'Buscando...'}}</span>
                      <span ng-if="!HDC.atualizando_ip_dhcp && HDC.contrato.ip_dhcp"
                        class="label label-success">{{HDC.contrato.ip_dhcp}}</span>
                    </td>
                    <td class="vert-align text-center" style="padding: 3px 0px;">
                      <button ng-if="HDC.contrato.ip_dhcp" class="btn btn-sm btn-primary"
                        ng-click="HDC.openONUInterface('dhcp');">Acessar (DHCP)</button>

                      <div ng-if="HDC.contrato.ip_dhcp && HDC.wan.ipv4 && HDC.wan.ipv4.trim() !== ''"
                        style="display: inline-block; width: 10px;">
                        &nbsp;
                      </div>

                      <button
                        ng-if="HDC.wan.ipv4 && HDC.wan.ipv4.trim() !== '' && HDC.tr069Config.operationMode != 'bridge'"
                        class="btn btn-sm btn-primary" ng-click="HDC.openONUInterface('pppoe');">Acessar
                        (PPPoE)</button>

                      <button disabled
                        ng-if="!HDC.contrato.ip_dhcp && (!HDC.wan.ipv4 || HDC.wan.ipv4.trim() === '' || HDC.tr069Config.operationMode == 'bridge')"
                        class="btn btn-sm btn-danger"
                        title="O cliente deve estar conectado via DHCP ou PPPoE, para obter acesso à interface da ONU">Indisponível</button>
                    </td>

                    <td class="vert-align text-center" style="padding: 3px 0px;">
                      <button ng-if="HDC.onuNotFound !== true && HDC.tr069Config.identifier" class="btn btn-sm btn-warning" ng-really-message="Deseja realmente reconfigurar a senha de admin para acesso à ONU?" ng-really-click="HDC.resetOnuAdminPassword();">
                        Reconfigurar
                      </button>
                      <button disabled
                        ng-if="HDC.onuNotFound === true || !HDC.tr069Config.identifier"
                        class="btn btn-sm btn-danger"
                        title="Esta ONU não foi encontrada no banco de dados do GenieACS">Indisponível</button>
                    </td>

                    <td ng-if="HDC.userRoles.includes('root')" class="vert-align text-center" style="padding: 3px 0px;">
                      <button ng-if="HDC.onuNotFound !== true && HDC.tr069Config.identifier" class="btn btn-sm btn-acs" ng-click="HDC.openGenieACS();">
                        Abrir no GenieACS
                      </button>
                      <button disabled
                        ng-if="HDC.onuNotFound === true || !HDC.tr069Config.identifier"
                        class="btn btn-sm btn-danger"
                        title="Esta ONU não foi encontrada no banco de dados do GenieACS">Indisponível</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div>
        <ul class="nav nav-tabs">
          <li class="active">
            <a data-target="#provisionamentos" data-toggle="tab">
              Provisionamentos</a>
          </li>
          <span class="pull-right">
            <button class="btn btn-default btn-sm"
              ng-click="HDC.getProvisions(HDC.contrato.serial)"><i class="glyphicon glyphicon-refresh"></i>
              Atualiza</button>
        </ul>
        <div id="provisionamentos" class="tab-pane fade in active">
          <div class="tab-content">
            <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="onuprovisionamentos">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th></th>
                    <th class="vert-align text-center">Status</th>
                    <th class="vert-align text-center">Data</th>
                    <th class="vert-align text-center">OLT</th>
                    <th class="vert-align text-center">OLT IP</th>
                    <th class="vert-align text-center">OLT Modelo</th>
                    <th class="vert-align text-center">Placa</th>
                    <th class="vert-align text-center">Porta</th>
                    <th class="vert-align text-center">Info</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="clickable vert-align text-center" ng-repeat-start="item in HDC.provisions">
                    <td style="cursor: pointer;" class="vert-align text-center" title="Mais informações" ng-click="HDC.provisionClick(item)"
                        data-toggle="collapse" data-target="#details-{{item.id}}">
                        <span class="glyphicon" ng-class="[{'glyphicon-chevron-down': item.expanded != 1}, {'glyphicon-chevron-up': item.expanded == 1} ]" style="cursor: pointer;"></span>
                    </td>
                    <td class="vert-align text-center"><span class="label"
                        ng-class="[{'label-success': item.status == 'finished'},
                                  {'label-warning': item.status == 'queued'},
                                  {'label-default': item.status == 'started'},
                                  {'label-danger': item.status == 'failed' || item.status == 'canceled'}]">{{item.status}}</span>
                    </td>

                    <td class="vert-align text-center">{{item.enqueued_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{item.olt}}</td>
                    <td class="vert-align text-center">{{item.olt_ip}}</td>
                    <td class="vert-align text-center">{{item.olt_model}}</td>
                    <td class="vert-align text-center">{{item.slot}}</td>
                    <td class="vert-align text-center">{{item.pon}}</td>
                    <td class="vert-align text-center">{{item.exc_info}}</td>
                </tr>
                <tr ng-repeat-end>
                    <td colspan="13" style="padding: 0 !important;">
                        <div id="details-{{item.id}}" class="collapse">

                            <div class="col-md-4 vert-align">

                                <table class="table table-bordered table-hover">
                                    <caption>Jobs <button class="btn btn-default btn-sm"
                                        ng-click="HDC.getJobs(item)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button></caption>

                                    <thead>
                                        <tr>
                                            <th>Status</th>
                                            <th>Descrição</th>
                                            <th>Data Início</th>
                                            <th>Data Fim</th>
                                            <th>Info</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="job in item.jobs">
                                            <td><span class="label"
                                                ng-class="[{'label-success': job.status == 'finished'},
                                                          {'label-warning': job.status == 'queued'},
                                                          {'label-default': job.status == 'deferred'},
                                                          {'label-default': job.status == 'started'},
                                                          {'label-danger': job.status == 'failed' || job.status == 'canceled'}]">{{job.status}}</td>
                                            <td>{{job.description}}</td>
                                            <td>{{job.started_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                            <td>{{job.ended_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                            <td>{{job.exc_info}}</td>
                                        </tr>
                                    </tbody>


                                </table>

                            </div>
                    </td>
                </tr>
                </tbody>
              </table>
            </div>

          </div>
        </div>
      </div>

      <div>
        <ul class="nav nav-tabs">
          <li class="active">
            <a data-target="#logsprovisionamento" data-toggle="tab">
              <svg class="blinking" width="8" height="5">
                <circle fill="#337ab7" r="2" cx="2" cy="2"></circle>
              </svg> Logs GenieACS</a>
          </li>

          <div style="float: left; padding-top: 10px; padding-left: 10px;" ng-if="HDC.contrato.mesh && HDC.onuNotFound !== true && HDC.tr069Config.identifier">
            <span><b>Status do mesh na ONU:</b></span>

            <span class="label label-default" ng-if="HDC.meshStatus.status == 'loading'">carregando...</span>

            <div ng-if="HDC.meshStatus.status == 'not_configured'" style="display: inline-block;">
              <span class="label label-default" title="A configuração ainda não foi iniciada.">desativado</span>
              <button class="btn btn-primary btn-vsm" style="width: auto;" ng-click="HDC.startMeshConfig();">Ativar</button>
            </div>

            <span class="label label-success" ng-if="HDC.meshStatus.status == 'configured'" title="O Mesh foi ativado na ONU.">ativado</span>

            <div ng-if="HDC.meshStatus.status == 'configuring'" style="display: inline-block;">
              <span class="label label-primary" title="O sistema está ativando o Mesh na ONU.">ativando...</span>
              <button class="btn btn-danger btn-vsm" style="width: auto;" ng-click="HDC.cancelMeshConfig();">cancelar</button>
            </div>

            <span class="label label-danger" ng-if="HDC.meshStatus.status == 'unknown'" title="Contacte o COR, para verificar a causa do erro.">erro desconhecido</span>

            |

            <span><b>Roteadores Mesh conectados:</b></span>
            <span>{{HDC.meshStatus.totalConfiguredRouters}}/{{HDC.meshStatus.totalMeshRouters}}</span>
          </div>

        </ul>
        <div id="logsprovisionamento" class="tab-pane fade in active">
          <div class="tab-content">
            <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="onulogs">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th class="vert-align text-center">Data</th>
                    <th class="vert-align text-center">Evento</th>
                    <th class="vert-align text-center">Log</th>
                    <th class="vert-align text-center">OLT</th>
                    <th class="vert-align text-center">Placa</th>
                    <th class="vert-align text-center">Porta</th>
                    <th class="vert-align text-center">Modulo</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in HDC.onu_logs">
                    <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                    <td class="vert-align text-center">{{item.task}}</td>
                    <td class="vert-align text-center">{{item.log}}</td>
                    <td class="vert-align text-center">{{item.olt}}</td>
                    <td class="vert-align text-center">{{item.placa}}</td>
                    <td class="vert-align text-center">{{item.porta}}</td>
                    <td class="vert-align text-center">{{item.script}}</td>

                  </tr>
                </tbody>
              </table>
            </div>

          </div>
        </div>
      </div>

      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#histsinal" data-toggle="tab">
            <i class="glyphicon glyphicon-object-align-bottom"></i> Histórico de sinal</a>
        </li>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandosinal"> <button
            class="btn btn-default btn-sm" ng-disabled="HDC.onu.sinal == undefined || HDC.onu == {}"
            ng-click="HDC.gravasinal()"><i class="glyphicon glyphicon-floppy-saved"></i> Gravar Sinal Atual</button>
          <button class="btn btn-default btn-sm" ng-click="HDC.getHistSinal()"><i
              class="glyphicon glyphicon-refresh"></i>
            Atualiza</button>
        </span>

      </ul>
      <div id="histsinal" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="pre-scrollable ng-scope" style="height:80px;">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center">Data</th>
                  <th class="vert-align text-center">Distância</th>
                  <th class="vert-align text-center">Send Power</th>
                  <th class="vert-align text-center">Receive Power</th>
                  <th class="vert-align text-center">OLT Receive Power</th>
                  <th class="vert-align text-center">Temperature</th>
                  <th class="vert-align text-center">Voltage</th>
                  <th class="vert-align text-center">Bias Current</th>
                  <th class="vert-align text-center">Comentário</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="item in HDC.sinal">
                  <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{item.rttvalue}}</td>
                  <td class="vert-align text-center">{{item.sendpower}}</td>
                  <td class="vert-align text-center">{{item.recvpower}}</td>
                  <td class="vert-align text-center">{{item.oltrecvpower}}</td>
                  <td class="vert-align text-center">{{item.temperature}}</td>
                  <td class="vert-align text-center">{{item.voltage}}</td>
                  <td class="vert-align text-center">{{item.biascurrent}}</td>
                  <td class="vert-align text-center">{{item.comentario}}</td>
                </tr>
              </tbody>
            </table>
          </div>

        </div>
      </div>

      <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
        <li class="active">
          <a href="#" data-target="#configs-wifi" data-toggle="tab">
            <i class="glyphicon glyphicon-signal"></i> Configurações Wi-Fi</a>
        </li>
      </ul>
      <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
        <div id="configs-wifi" class="tab-pane active">
          <div class="panel panel-default">
            <div class="panel-body align-center">
              <div ng-if="HDC.portalAPIError === true">
                Não foi possível buscar as configurações da ONU do cliente.
              </div>
              <div ng-if="HDC.onuNotFound === true || !HDC.contrato.ip_dhcp">
                Esta ONU não consta no banco de dados do GenieACS ou o cliente não está conectado na rede DHCP. Caso ela tenha sido provisionada recentemente,
                atualize
                os dados em cerca de dois minutos.
              </div>
              <div ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true && HDC.contrato.ip_dhcp"
                class="wifi-config-container align-left">
                <table>
                  <tr>
                    <td style="padding-left: 5px;">
                      <table class="table valign-middle" style="margin-bottom: 0px; width: 200px;">
                        <tr>
                          <td colspan="2" style="padding: 5px; text-align: center;"><b>Wi-Fi 2.4GHz</b></td>
                        </tr>
                        <tr>
                          <td colspan="2">
                            <table class="radio-table"
                              ng-class="{'disabled': HDC.tr069Config.operationMode == 'bridge'}"
                              style="margin: 5px 0px;">
                              <tr>
                                <td class="success" ng-class="{'active': HDC.tr069Config.wifi[1].enabled == 1}"
                                  ng-click="HDC.setWifiEnabled(1, 1);">ATIVADO</td>
                                <td class="danger" ng-class="{'active': HDC.tr069Config.wifi[1].enabled == 0}"
                                  ng-click="HDC.setWifiEnabled(1, 0);">DESATIVADO</td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td><b>Canal:</b></td>
                          <td>
                            <select ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069"
                              class="form-control" id="wifi-2g-channel"
                              ng-model="HDC.tr069Config.wifi[1].selectedChannel">
                              <option value="auto">Automático</option>
                              <option ng-repeat="channel in HDC.tr069Config.wifi[1].possibleChannels"
                                value="{{channel}}">{{channel}}</option>
                            </select>
                          </td>
                        </tr>
                        <tr>
                          <td><b>SSID:</b></td>
                          <td>
                            <input ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069"
                              class="form-control" type="text" id="wifi-2g-ssid"
                              ng-model="HDC.tr069Config.wifi[1].ssid">
                          </td>
                        </tr>
                        <tr>
                          <td><b>{{HDC.currentTr069Config.wifi[1].passphrase == '' ? 'Nova senha:' : 'Senha:'}}</b></td>
                          <td>
                            <input ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069"
                              class="form-control" type="text" id="wifi-2g-password"
                              ng-model="HDC.tr069Config.wifi[1].passphrase">
                          </td>
                        </tr>
                      </table>
                    </td>
                    <td class="valign-bottom">
                      <div style="height: 115px; width: 1px; background: #DDD; margin: 0px 20px;">&nbsp;</div>
                    </td>
                    <td>
                      <table class="table valign-middle" style="margin-bottom: 0px; width: 200px;">
                        <tr>
                          <td colspan="2" style="padding: 5px; text-align: center;"><b>Wi-Fi 5.8GHz</b></td>
                        </tr>
                        <tr>
                          <td colspan="2">
                            <table class="radio-table"
                              ng-class="{'disabled': HDC.tr069Config.operationMode == 'bridge'}"
                              style="margin: 5px 0px;">
                              <tr ng-if="HDC.contrato['5G'] == 1">
                                <td class="success" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 1}"
                                  ng-click="HDC.setWifiEnabled(5, 1);">ATIVADO</td>
                                <td class="danger" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 0}"
                                  ng-click="HDC.setWifiEnabled(5, 0);">DESATIVADO</td>
                              </tr>
                              <tr ng-if="HDC.contrato['5G'] != 1">
                                <td class="not-available" ng-class="{'active': HDC.tr069Config.wifi[5].enabled == 1}">
                                  NÃO DISPONÍVEL
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td><b>Canal:</b></td>
                          <td>
                            <select
                              ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.contrato['5G'] != 1 || HDC.atualizandoTr069"
                              class="form-control" id="wifi-5g-channel"
                              ng-model="HDC.tr069Config.wifi[5].selectedChannel">
                              <option value="auto">Automático</option>
                              <option ng-repeat="channel in HDC.tr069Config.wifi[5].possibleChannels"
                                value="{{channel}}">{{channel}}</option>
                            </select>
                          </td>
                        </tr>
                        <tr>
                          <td><b>SSID:</b></td>
                          <td>
                            <input
                              ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.contrato['5G'] != 1 || HDC.atualizandoTr069"
                              class="form-control" type="text" id="wifi-5g-ssid"
                              ng-model="HDC.tr069Config.wifi[5].ssid">
                          </td>
                        </tr>
                        <tr>
                          <td><b>{{HDC.currentTr069Config.wifi[5].passphrase == '' ? 'Nova senha:' : 'Senha:'}}</b></td>
                          <td>
                            <input
                              ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.contrato['5G'] != 1 || HDC.atualizandoTr069"
                              class="form-control" type="text" id="wifi-5g-password"
                              ng-model="HDC.tr069Config.wifi[5].passphrase">
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr
                    ng-if="HDC.tr069Config.operationMode == 'router' && (HDC.currentTr069Config.wifi[1].passphrase == '' || HDC.currentTr069Config.wifi[5].passphrase == '')">
                    <td colspan="3" style="padding-top: 10px; padding-left: 5px;">
                      * Caso o campo "nova senha" seja deixado em branco, a senha atual do cliente <b>não será
                        alterada ou removida</b>.
                    </td>
                  </tr>
                  <tr ng-if="HDC.tr069Config.operationMode == 'bridge'">
                    <td colspan="3" style="padding-top: 10px; padding-left: 5px;">
                      * A ONU está em modo bridge. Não será possível alterar as configurações do Wi-Fi.
                    </td>
                  </tr>
                  <tr>
                    <td colspan="3" style="padding: 5px 0px 15px 0px; text-align: center;">
                      <button
                        ng-disabled="(HDC.tr069Config.wifi[1].ssid == HDC.currentTr069Config.wifi[1].ssid && HDC.tr069Config.wifi[1].passphrase == HDC.currentTr069Config.wifi[1].passphrase && HDC.tr069Config.wifi[1].enabled == HDC.currentTr069Config.wifi[1].enabled && HDC.tr069Config.wifi[5].ssid == HDC.currentTr069Config.wifi[5].ssid && HDC.tr069Config.wifi[5].passphrase == HDC.currentTr069Config.wifi[5].passphrase && HDC.tr069Config.wifi[5].enabled == HDC.currentTr069Config.wifi[5].enabled && HDC.tr069Config.wifi[1].selectedChannel == HDC.currentTr069Config.wifi[1].selectedChannel && HDC.tr069Config.wifi[5].selectedChannel == HDC.currentTr069Config.wifi[5].selectedChannel) || HDC.atualizandoTr069 || HDC.tr069Config.operationMode == 'bridge'"
                        class="btn"
                        ng-class="[(HDC.tr069Config.wifi[1].ssid == HDC.currentTr069Config.wifi[1].ssid && HDC.tr069Config.wifi[1].passphrase == HDC.currentTr069Config.wifi[1].passphrase && HDC.tr069Config.wifi[1].enabled == HDC.currentTr069Config.wifi[1].enabled && HDC.tr069Config.wifi[5].ssid == HDC.currentTr069Config.wifi[5].ssid && HDC.tr069Config.wifi[5].passphrase == HDC.currentTr069Config.wifi[5].passphrase && HDC.tr069Config.wifi[5].enabled == HDC.currentTr069Config.wifi[5].enabled && HDC.tr069Config.wifi[1].selectedChannel == HDC.currentTr069Config.wifi[1].selectedChannel && HDC.tr069Config.wifi[5].selectedChannel == HDC.currentTr069Config.wifi[5].selectedChannel) || HDC.atualizandoTr069 || HDC.tr069Config.operationMode == 'bridge' ? 'btn-default' : 'btn-primary', {'margin-top-10': HDC.currentTr069Config.wifi[1].passphrase != '' && HDC.currentTr069Config.wifi[5].passphrase != ''}]"
                        ng-really-message="Deseja realmente salvar as alterações do Wi-Fi?"
                        ng-really-click="HDC.saveWifi();"><i class="glyphicon glyphicon-ok btn-sm-icon"></i>
                        Salvar</button>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
        <li class="active">
          <a href="#" data-target="#configs-operation" data-toggle="tab">
            <i class="glyphicon glyphicon-cog"></i> Modo de operação</a>
        </li>

        <!-- <span class="pull-right">
              <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoTr069"> <button class="btn btn-default btn-sm"
                ng-click="HDC.getTr069Config(HDC.contrato.login);"><i class="glyphicon glyphicon-refresh"></i>
                Atualiza</button>
            </span> -->
      </ul>
      <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
        <div id="configs-operation" class="tab-pane active">
          <div class="panel panel-default">
            <div class="panel-body">
              <div ng-if="HDC.portalAPIError === true" class="align-center" style="width: 100%;">
                Não foi possível buscar as configurações da ONU do cliente.
              </div>
              <div ng-if="HDC.onuNotFound === true || !HDC.contrato.ip_dhcp" class="align-center" style="width: 100%;">
                Esta ONU não consta no banco de dados do GenieACS ou o cliente não está conectado na rede DHCP. Caso ela tenha sido provisionada recentemente,
                atualize
                os dados em cerca de dois minutos.
              </div>
              <table ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true && HDC.contrato.ip_dhcp" style="width: 100%;">
                <tr>
                  <td align="center">

                    <div class="operation-config-container">
                      <table class="table valign-middle" style="margin-bottom: 0px;">
                        <tr>
                          <td style="padding: 5px; text-align: center;"><b>Modo router</b></td>
                        </tr>
                        <tr>
                          <td id="router-description" style="height: 147px;">
                            <p style="text-align: justify;">
                              Este é o <b>modo de operação padrão do modem</b>. Com este modo ativado, o modem fará
                              também o papel de um roteador,
                              <b>não necessitando de qualquer equipamento adicional na rede</b>.
                            </p>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 5px; text-align: center;">
                            <button
                              ng-disabled="HDC.tr069Config.operationMode == 'router' || HDC.atualizandoTr069 || HDC.atualizandoOperationMode == 'router'"
                              class="btn"
                              ng-class="[{'btn-primary': HDC.tr069Config.operationMode != 'router'}, {'btn-default': HDC.tr069Config.operationMode == 'router'}, {'no-pointer': HDC.tr069Config.operationMode == 'router'}]"
                              ng-really-message="Deseja realmente alterar o modo de operação da ONU para router?"
                              ng-really-click="HDC.setOperationMode('router');"><i
                                ng-if="HDC.tr069Config.operationMode == 'router'"
                                class="glyphicon glyphicon-ok btn-sm-icon"></i>
                              {{HDC.tr069Config.operationMode == 'router' ? 'Ativado' : (HDC.atualizandoOperationMode ==
                              'router' ? 'Ativando...' : 'Ativar')}}</button>
                          </td>
                        </tr>
                      </table>
                    </div>

                  </td>
                  <td align="center">

                    <div class="operation-config-container">
                      <table class="table valign-middle" style="margin-bottom: 0px;">
                        <tr>
                          <td style="padding: 5px; text-align: center;"><b>Modo bridge</b></td>
                        </tr>
                        <tr>
                          <td id="bridge-description" style="height: 147px;">
                            <p style="text-align: justify;">
                              Este modo permite que o cliente utilize seu roteador pessoal para conectar à nossa rede.
                              Dessa
                              forma, <b>as configurações do
                                nosso modem (como rede Wi-Fi, redirecionamento de portas etc) serão desativadas e o
                                cliente
                                passará a gerenciar essas
                                configurações diretamente em seu roteador pessoal</b>.
                            </p>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 5px; text-align: center;">
                            <button
                              ng-disabled="HDC.tr069Config.operationMode == 'bridge' || HDC.atualizandoTr069 || HDC.atualizandoOperationMode == 'bridge'"
                              class="btn"
                              ng-class="[{'btn-primary': HDC.tr069Config.operationMode != 'bridge'}, {'btn-default': HDC.tr069Config.operationMode == 'bridge'}, {'no-pointer': HDC.tr069Config.operationMode == 'bridge'}]"
                              ng-really-message="Deseja realmente alterar o modo de operação da ONU para bridge?"
                              ng-really-click="HDC.setOperationMode('bridge');"><i
                                ng-if="HDC.tr069Config.operationMode == 'bridge'"
                                class="glyphicon glyphicon-ok btn-sm-icon"></i>
                              {{HDC.tr069Config.operationMode == 'bridge' ? 'Ativado' : (HDC.atualizandoOperationMode ==
                              'bridge' ? 'Ativando...' : 'Ativar')}}</button>
                          </td>
                        </tr>
                      </table>
                    </div>

                  </td>

                  <td style="vertical-align: top;">
                    <div id="operation-mode-log-container">
                      <table id="operation-mode-log">
                        <thead>
                          <tr>
                            <th colspan="3">Log de alterações do modo de operação</th>
                          </tr>
                          <tr>
                            <th>Data</th>
                            <th>Operador</th>
                            <th>Descrição</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr ng-if="HDC.operationModeLogs.length == 0 && !HDC.atualizandoTr069">
                            <td colspan="3" class="bg-warning">
                              Não houve alterações do modo de operação.
                            </td>
                          </tr>
                          <tr ng-if="HDC.atualizandoTr069">
                            <td colspan="3" class="bg-warning">
                              Carregando...
                            </td>
                          </tr>
                          <tr ng-repeat="log in HDC.operationModeLogs"
                            ng-class="{'bg-success-dark': log.status && log.status == 'executing'}">
                            <td>{{ log.timestamp | amDateFormat:'DD/MM/YYYY HH:mm:ss' }}</td>
                            <td>{{ log.noc_operator }}</td>
                            <td>
                              {{ log.status && log.status == 'executing' ? 'Você está alterando o modo de operação para
                              '
                              : 'A ONU do cliente foi alterada para o modo ' }}<b>{{ log.mode }}</b>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </td>
                </tr>
              </table>

            </div>
          </div>
        </div>
      </div>

      <!--
      <hr class="message-inner-separator">
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#logsprovisionamento" data-toggle="tab">
            <i class="glyphicon glyphicon-console"></i> Logs de provisionamento</a>

        </li>

      </ul>
      <div id="logsprovisionamento" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="pre-scrollable ng-scope" style="height:120px;">
            <table class="table table-bordered">
              ng-if="HDC.contrato.tipohost == 'Porta GPON OLT' || HDC.contrato.tipohost == 'FTTA'">
      <thead>
        <tr>
          <th class="vert-align text-center">Data Processamento</th>
          <th class="vert-align text-center">Data Alteração</th>
          <th class="vert-align text-center">Operador</th>
          <th class="vert-align text-center">Patrimônio</th>
          <th class="vert-align text-center">Modelo</th>
          <th class="vert-align text-center">Serial</th>
          <th class="vert-align text-center">MAC</th>
          <th class="vert-align text-center">Sit. Equipamento</th>
          <th class="vert-align text-center">Escopo</th>
          <th class="vert-align text-center">Wi-fi</th>
          <th class="vert-align text-center">IPTV</th>
          <th class="vert-align text-center">STB</th>
          <th class="vert-align text-center">Log</th>
          <th class="vert-align text-center">Detalhes</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="item in HDC.ftthlogs" ng-class="[{'success': item.erro == 0}, {'danger' : item.erro == 1}]">
          <td class="vert-align text-center">{{item.data_processamento | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center">{{item.data_alteracao | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center">{{item.operador}}</td>
          <td class="vert-align text-center">{{item.patrimonio}}</td>
          <td class="vert-align text-center">{{item.modelo}}</td>
          <td class="vert-align text-center">{{item.serial}}</td>
          <td class="vert-align text-center">{{item.mac}}</td>
          <td class="vert-align text-center">
            <span class="label label-danger" ng-if="item.situacaoequipamento==0">Desativado</span>
            <span class="label label-success" ng-if="item.situacaoequipamento==1">Ativado</span>
          </td>
          <td class="vert-align text-center">{{item.escopo}}</td>
          <td class="vert-align text-center"><span class="label label-default" ng-if="item.wifi==0">Não</span>
            <span class="label label-default" ng-if="item.wifi==1">Sim</span>
          </td>
          <td class="vert-align text-center"><span class="label label-default" ng-if="item.iptv==0">Não</span>
            <span class="label label-default" ng-if="item.iptv==1">Sim</span>
          </td>
          <td class="vert-align text-center"> <span class="label label-default">{{item.stb}}</span> </td>
          <td class="vert-align text-center">{{item.log}}</td>
          <td class="vert-align text-center">{{item.detalhes}}</td>
        </tr>
      </tbody>
      </table>
    </div>

  </div>
  </div>
  -->
    </div>
  </div>
  <!-- FIM ONU -->


  <!-- CPE -->
  <div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('cliente')">
    <div class="panel-heading clearfix">

      <h3 class="panel-title" style="margin-top: 5px;"><strong>CPE</strong></h3>
    </div>
    <div class="panel-body">
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#wanstatus" data-toggle="tab">
            <i class="glyphicon glyphicon-globe"></i> WAN Status</a>
        </li>
        <button class="btn"
          ng-class="{disabled: HDC.statusCGNAT.ativo != 1 || !HDC.podeRemoverCgnat, 'btn-warning': HDC.statusCGNAT.solicitacaoExiste == 0 || (HDC.statusCGNAT.solicitacaoExiste == 1 && HDC.statusCGNAT.solicitacaoPendente), 'btn-success': HDC.statusCGNAT.status == 'EXECUTADO', 'btn-danger': (HDC.statusCGNAT.status == 'NEGADO' || HDC.statusCGNAT.status == 'RESTAURADO')}"
          title="Envia uma solicitação ao COR, para remoção do CGNAT deste cliente"
          ng-click="HDC.removerCGNAT(HDC.contrato.login)" style="margin-left: 10px;"><i
            class="glyphicon glyphicon-remove"></i>
          Solicitar remoção do CGNAT</button>
        <span ng-if="HDC.statusCGNAT.solicitacaoExiste == 1" style="margin-left: 10px;">
          (<b>Status:</b> {{HDC.statusCGNAT.statusMessage}})
        </span>
        <span ng-if="HDC.statusCGNAT.ativo == 1 && HDC.statusCGNAT.solicitacaoExiste != 1 && !HDC.podeRemoverCgnat" style="margin-left: 10px;">
          (Não é possível solicitar a remoção do CGNAT. O cliente precisa ter contratado o pacote IP FIX ou IP FIXO)
        </span>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandowan"> <button class="btn btn-default btn-sm"
            ng-click="HDC.getWanStatus(HDC.contrato.login)"><i class="glyphicon glyphicon-refresh"></i>
            Atualiza</button>
        </span>
      </ul>
      <div id="wanstatus" class="tab-pane fade in active">
        <div class="tab-content">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="vert-align text-center">Status</th>
                <th ng-if="HDC.wan.status === 'Desconectado' && HDC.onuNotFound !== true && HDC.tr069Config.identifier && HDC.tr069Config.operationMode == 'router'" class="vert-align text-center" style="max-width: 50px !important;">Reconfigurar</th>
                <th class="vert-align text-center">Autentic.</th>
                <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Uptime</th>
                <th class="vert-align text-center">IPv4</th>
                <th class="vert-align text-center">IPv6</th>
                <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">MAC CPE</th>
                <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">MTU</th>
                <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Download</th>
                <th class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">Upload</th>
                <th class="vert-align text-center">BNG</th>
                <th class="vert-align text-center">BNG IP</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan">
                  <span class="label label-default"
                    ng-class="[{'label-success': HDC.wan.status == 'Conectado'},
                                                {'label-danger': HDC.wan.status == 'Desconectado'}]">{{HDC.wan.status}}</span>
                </td>
                <td ng-if="HDC.wan.status === 'Desconectado' && HDC.onuNotFound !== true && HDC.tr069Config.identifier && HDC.tr069Config.operationMode == 'router'" class="vert-align text-center" style="max-width: 50px !important;">
                  <button class="btn btn-primary btn-sm btn-smaller" ng-really-message="Deseja realmente reconfigurar a conexão PPPoE?" ng-really-click="HDC.redoPppoe();">Reconfigurar PPPoE</button>
                </td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan"><span class="label label-primary">{{HDC.wan.auth_type}}</span></td>
                <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'">{{HDC.wan.uptime}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan">{{HDC.wan.ipv4}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan">{{HDC.wan.ipv6}}</td>
                <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                    src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.mac}}</td>
                <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                    src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.mtu}}</td>
                <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                    src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.down}}</td>
                <td class="vert-align text-center" ng-if="HDC.wan.router=='juniper'"><img
                    src="assets/images/ajax-loader-barra.gif" ng-show="HDC.atualizandowan">{{HDC.wan.up}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan">{{HDC.wan.router_desc}}</td>
                <td class="vert-align text-center"><img src="assets/images/ajax-loader-barra.gif"
                    ng-show="HDC.atualizandowan">{{HDC.wan.router_ip}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#routers-tr069" data-toggle="tab">
            <i class="glyphicon glyphicon-globe"></i> Roteadores TR069</a>
        </li>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoRoutersCliente"> <button
            class="btn btn-default btn-sm" ng-click="HDC.getRoutersAlocados(HDC.contrato.login);"><i
              class="glyphicon glyphicon-refresh"></i>
            Atualiza</button>
        </span>
      </ul>
      <div id="routers-tr069" class="tab-pane fade in active">
        <div class="tab-content">
          <table class="table table-bordered">
            <thead ng-if="HDC.routers_alocados.length > 0">
              <tr>
                <th class="centered">Patrimônio</th>
                <th class="centered">MAC</th>
                <th class="centered">Serial</th>
                <th class="centered">Marca</th>
                <th class="centered">Modelo</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-if="HDC.routers_alocados.length < 1">
                <td class="centered" colspan="6" style="padding: 4px;">
                  <b>Não há roteadores com TR069 alocados para este cliente</b>
                </td>
              </tr>
              <tr ng-repeat="router in HDC.routers_alocados">
                <td class="centered">
                  {{router.patrimonio}}
                </td>
                <td class="centered">
                  {{router.mac}}
                </td>
                <td class="centered">
                  {{router.serial}}
                </td>
                <td class="centered">
                  {{router.marca}}
                </td>
                <td class="centered">
                  {{router.modelo}}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#logs" data-toggle="tab">
            <i class="glyphicon glyphicon-globe"></i> Logs de autenticação</a>
        </li>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandolog"> <button class="btn btn-default btn-sm"
            ng-click="HDC.atualizaLogs(HDC.contrato.login)" ng-disabled="HDC.atualizandolog"><i
              class="glyphicon glyphicon-refresh"></i> Atualiza</button>
        </span>
      </ul>
      <div id="logs" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="pre-scrollable ng-scope" style="height:100px;">
            <table class="table table-bordered" ng-if="HDC.rsyslog == false">
              <thead>
                <tr>
                  <th class="vert-align text-center">Mensagem</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="log in HDC.logs">
                  <td class="vert-align text-center">{{log}}</td>
                </tr>
              </tbody>
            </table>

            <table class="table table-bordered" ng-if="HDC.rsyslog == true">
              <thead>
                <tr>
                  <th class="vert-align text-center">Data/Hora</th>
                  <th class="vert-align text-center">Router</th>
                  <th class="vert-align text-center">Mensagem</th>

                </tr>
              </thead>

              <tbody>
                <tr ng-repeat="log in HDC.logs">
                  <td class="vert-align text-center">{{log.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                  <td class="vert-align text-center">{{log.concentrador}}</td>
                  <td class="vert-align text-center">{{log.mensagem}}</td>
                </tr>
              </tbody>

            </table>
          </div>
        </div>
      </div>

      <ul class="nav nav-tabs" ng-if="HDC.contrato.tr069 == 1" style="margin-top: 15px;">
        <li class="active">
          <a href="#" data-target="#configs-portmapping" data-toggle="tab">
            <i class="glyphicon glyphicon-share-alt"></i> Redirecionamentos de porta</a>
        </li>

        <!-- <div class="row">
          <button class="btn btn-warning" ng-disabled="keys(HDC.tr069Config.portMappings).length == 0" title="Exclui e recria todos os redirecionamentos de porta na ONU do cliente" ng-really-message="Deseja realmente reconfigurar todos os redirecionamentos de portas do cliente?" ng-really-click="HDC.redoPortMappings()"
            style="margin-left: 10px;"><i class="glyphicon glyphicon-repeat"></i>
            Reconfigurar redirecionamentos
          </button>
        </div> -->

        <!-- <span class="pull-right">
              <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoTr069"> <button class="btn btn-default btn-sm"
                ng-click="HDC.getTr069Config(HDC.contrato.login);"><i class="glyphicon glyphicon-refresh"></i>
                Atualiza</button>
            </span> -->
      </ul>
      <div class="tab-content" ng-if="HDC.contrato.tr069 == 1">
        <div id="configs-portmapping" class="tab-pane active">
          <div class="panel panel-default">
            <div class="panel-body" style="text-align: center; padding: 0px; padding-bottom: 10px;">
              <div ng-if="HDC.portalAPIError === true">
                Não foi possível buscar as configurações da ONU do cliente.
              </div>
              <div ng-if="HDC.onuNotFound === true || !HDC.contrato.ip_dhcp">
                Esta ONU não consta no banco de dados do GenieACS ou o cliente não está conectado na rede DHCP. Caso ela tenha sido provisionada recentemente,
                atualize
                os dados em cerca de dois minutos.
              </div>
              <table ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true && HDC.contrato.ip_dhcp"
                class="table table-bordered table-striped align-center valign-middle" style="margin-bottom: 10px;">
                <thead>
                  <tr>
                    <th>Editar</th>
                    <th>Status</th>
                    <th>Serviço</th>
                    <th>IP externo</th>
                    <th>Porta externa</th>
                    <th>IP interno</th>
                    <th>Porta interna</th>
                    <th>MAC</th>
                    <th>Criado em</th>
                    <th>Remover</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-if="keys(HDC.tr069Config.portMappings).length == 0">
                    <td colspan="10" class="bg-warning">
                      Este cliente não possui redirecionamentos de porta.
                    </td>
                  </tr>
                  <tr ng-if="keys(HDC.tr069Config.portMappings).length > 0"
                    ng-repeat="mapping in HDC.tr069Config.portMappings">
                    <td>
                      <button class="btn btn-vsm btn-primary" title="Editar" data-toggle="modal"
                        data-target="#frmredirecionamento"
                        ng-click="HDC.selecionarRedirecionamento(mapping); HDC.initFrmRedirecionamento"><i
                          class="glyphicon glyphicon-pencil"></i></button>
                    </td>
                    <td><span class="label"
                        ng-class="[{'label-success': mapping.enabled && mapping.status == 'executed'}, {'label-danger': !mapping.enabled}, {'label-warning': mapping.status != 'executed'}]">{{mapping.enabled
                        && mapping.status == 'executed' ? 'Ativo' : (!mapping.enabled ? 'Inativo' : (mapping.status !=
                        'executed' ? 'Criando' : '[indefinido]'))}}</span>
                    </td>
                    <td>{{mapping.description}}</td>
                    <td>{{HDC.currentTr069Config.connection.externalIP}}</td>
                    <td>{{mapping.externalPort}}</td>
                    <td>{{HDC.currentTr069Config.lanIpRangeStr + mapping.internalIP}}</td>
                    <td>{{mapping.internalPort}}</td>
                    <td>{{mapping.mac}}</td>
                    <td>{{mapping.executedAt.substr(0, 10).split('-').reverse().join('/')}}</td>
                    <td>
                      <button class="btn btn-vsm btn-danger" title="Remover"
                        ng-really-message="Deseja realmente excluir o redirecionamento da porta <b>{{mapping.externalPort}}</b>?"
                        ng-really-click="HDC.excluirRedirecionamento(mapping.id);"><i
                          class="glyphicon glyphicon-trash"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>

              <button ng-if="HDC.portalAPIError !== true && HDC.onuNotFound !== true && HDC.contrato.ip_dhcp" ng-disabled="HDC.atualizandoTr069 || HDC.contrato.cgnat == 1"
                class="btn btn-default" data-toggle="modal" data-target="#frmnovoredirecionamento"
                ng-click="HDC.initFrmnovoredirecionamento();"><i class="glyphicon glyphicon-plus btn-sm-icon"></i>
                Adicionar redirecionamento</button>

              <div
                ng-if="HDC.contrato.cgnat == 1"
                style="margin-top: 10px;">
                <span class="text-danger"><b>* Este cliente possui CGNAT ativo. Para adicionar redirecionamentos de porta, solicite a remoção do CGNAT e pesquise o login novamente após a remoção.</b></span>
              </div>

              <div
                ng-if="(HDC.contrato.cgnat == 0 && !HDC.currentTr069Config.connection.ip_fix && !HDC.currentTr069Config.connection.ip_fixo) && HDC.onuNotFound !== true && HDC.contrato.ip_dhcp"
                style="margin-top: 10px;">
                <b>* Este cliente não possui os pacotes "IP Fix" ou "IP Fixo" contratados. Será necessário alertar o
                  cliente de que seu IP público pode ser alterado quando reconectar-se (seu IP público é necessário para
                  acessar os equipamentos com redirecionamentos de portas). Caso o cliente deseje um endereço fixo,
                  será necessário contratar um destes dois pacotes.</b>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- FIM CPE -->

  <!-- ANALISE DE REDE BASICA -->
  <div class="panel panel-primary" ng-hide="HDC.wan.router !=='juniper'">
    <div class="panel-heading clearfix">

      <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Básica</strong></h3>
    </div>
    <div class="panel-body">
      <!-- PING -->
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#ping" data-toggle="tab">
            <i class="glyphicon glyphicon-equalizer"></i> Ping </a>

        </li>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoPing === 1"> <button
            class="btn btn-default btn-sm" ng-click="HDC.atualizaPing(HDC.contrato.login)"
            ng-disabled="HDC.testandoPing===1"><i class="glyphicon glyphicon-play"></i> Iniciar Ping</button> <button
            class="btn btn-default btn-sm" ng-click="HDC.pausarPing()"
            ng-disabled="HDC.testandoPing===2 || HDC.testandoPing===0"><i class="glyphicon glyphicon-pause"></i> Pausar
            Ping</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarPing()"
            ng-disabled="HDC.testandoPing===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Ping</button>
        </span>

      </ul>
      <div id="ping" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="row">
            <div class="col-md-2">
              <table class="table table-bordered table-sm" style="margin-bottom: 0px;">
                <thead>
                  <tr>
                    <th class="vert-align text-center" style="width: 200px;">Respostas</th>
                  </tr>
                </thead>
              </table>
              <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="pingresults">
                <table class="table table-bordered table-sm">

                  <tbody>
                    <tr ng-repeat="ping in HDC.ping.packets">
                      <td class="vert-align text-center" style="width: 200px;"><span
                          ng-if="ping.rtt !=='lost'">{{ping.rtt}} ms</span> <span
                          ng-if="ping.rtt =='lost'">Perdido</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <table class="table table-bordered">
                <tbody>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Enviados</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsSent}}</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Recebidos</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsReceived}}</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Perdidos</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.packetsLost}}
                      ({{HDC.ping.packetsLostPerc}}%)</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Min</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.min}} ms</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Med</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.avg}} ms</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Max</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{HDC.ping.max}} ms</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-md-10">
              <div id="graficoPing"></div>
            </div>

          </div>
        </div>
      </div>
      <!-- FIM PING -->
      <!--<hr class="message-inner-separator">-->
      <!-- TRAFEGO -->
      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#trafego" data-toggle="tab">
            <i class="glyphicon glyphicon-random"></i> Tráfego instantâneo </a>

        </li>
        <span class="pull-right">
          <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoTrafego === 1"> <button
            class="btn btn-default btn-sm" ng-click="HDC.atualizaTrafego(HDC.contrato.login, HDC.contrato.circuitid)"
            ng-disabled="HDC.testandoTrafego===1"><i class="glyphicon glyphicon-play"></i> Iniciar Trafego</button>
          <button class="btn btn-default btn-sm" ng-click="HDC.pausarTrafego()"
            ng-disabled="HDC.testandoTrafego===2 || HDC.testandoTrafego===0"><i class="glyphicon glyphicon-pause"></i>
            Pausar Trafego</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarTrafego()"
            ng-disabled="HDC.testandoTrafego===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Trafego</button>
        </span>
      </ul>
      <div id="trafego" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="row">
            <div class="col-md-2">
              <table class="table table-bordered">
                <colgroup span="2"></colgroup>
                <tr>
                  <th colspan="2" scope="colgroup" class="vert-align text-center label-success">
                    <strong>Download</strong>
                  </th>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.outputBitsPerSecond | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.minOutput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.avgOutput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.maxOutput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.outputPacketsPerSecond}} pps</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.outputBytes | bytes}}</td>
                </tr>
                <colgroup span="2"></colgroup>
                <tr>
                  <th colspan="2" scope="colgroup" class="vert-align text-center label-danger"><strong>Upload</strong>
                  </th>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Atual</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.inputBitsPerSecond | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Min</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.minInput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Med</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.avgInput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Max</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.maxInput | bytes}}/s</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Pacotes/s</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.inputPacketsPerSecond}} pps</td>
                </tr>
                <tr>
                  <td scope="col" class="vert-align text-center" width="20%"><strong>Tráfego</strong></td>
                  <td class="vert-align text-center" width="80%">{{HDC.trafego.inputBytes | bytes}}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-10">
              <div id="graficoTrafego"></div>
            </div>
          </div>

        </div>
      </div>


    </div>
  </div>
  <!-- FIM ANALISE DE REDE BASICO -->

  <!-- Lista de dispositivos da rede interna IPV6 -->

  <div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('cliente')">
    <div class="panel-heading clearfix">
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandoipv6list"> <button
          class="btn btn-default btn-sm" ng-click="HDC.getIpv6List()"><i class="glyphicon glyphicon-refresh"></i>
          Atualiza</button>
      </span>
      <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Lista de dispositivos da rede interna
        IPv6 | <b> {{HDC.ipv6list.length}} dispositivo(s) </b></h3>
    </div>
    <div class="panel-body">
      <uib-accordion close-others="false">
        <div uib-accordion-group class="panel-default" is-open="status.open" ng-repeat="item in HDC.ipv6list">
          <uib-accordion-heading>
            <img src="assets/images/ajax-loader.gif" ng-show="item.testing === 1">{{item.source_address}} <span
              style="font-size: 85%;">| Última atividade:</b> {{item.last_activity | amDateFormat:'DD/MM/YYYY
              HH:mm:ss'}}
              | Média: {{item.avg_rtt}} ms | Perdidos: {{item.lost}} ({{item.lost_perc}}%)</span><i
              class="pull-right glyphicon"
              ng-class="{'glyphicon-chevron-down': status.open, 'glyphicon-chevron-right': !status.open}"></i><span
              class="pull-right"><br>

          </uib-accordion-heading>
          <p><b>Última atividade:</b> {{item.last_activity | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</p>
          <div>
            <span class="pull-left">
              <button class="btn btn-default btn-sm" ng-click="HDC.atualizaPingV6(item)"
                ng-disabled="item.testing===1"><i class="glyphicon glyphicon-play"></i> Iniciar Ping</button> <button
                class="btn btn-default btn-sm" ng-click="HDC.pausarPingV6(item)"
                ng-disabled="item.testing===2 || item.testing===0"><i class="glyphicon glyphicon-pause"></i> Pausar
                Ping</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarPingV6(item)"
                ng-disabled="item.testing===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Ping</button>
            </span>

          </div>
          &nbsp;
          <hr>
          <div class="row">
            <div class="col-md-2">
              <table class="table table-bordered table-sm" style="margin-bottom: 0px;">
                <thead>
                  <tr>
                    <th class="vert-align text-center" style="width: 200px;">Respostas</th>
                  </tr>
                </thead>
              </table>
              <div class="pre-scrollable" style="height:150px;overflow-y: scroll;" id="pingresultsv6_{{item.index}}">
                <table class="table table-bordered table-sm">

                  <tbody>
                    <tr ng-repeat="ping in item.packets">
                      <td class="vert-align text-center" style="width: 200px;"><span
                          ng-if="ping.rtt !=='lost'">{{ping.rtt}} ms</span> <span
                          ng-if="ping.rtt =='lost'">Perdido</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <table class="table table-bordered">
                <tbody>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Enviados</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.sent}}</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Recebidos</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.received}}</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Perdidos</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.lost}} ({{item.lost_perc}}%)</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Min</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.min_rtt}} ms</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Med</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.avg_rtt}} ms</td>
                  </tr>
                  <tr>
                    <td class="vert-align text-center" style="width: 25%;"><strong>Max</strong></td>
                    <td class="vert-align text-center" style="width: 75%;">{{item.max_rtt}} ms</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="col-md-10">
            <div id="graficoPingV6_{{item.index}}"></div>
          </div>
        </div>
      </uib-accordion>
    </div>
  </div>

  <!-- ANALISE DE REDE AVANÇADA -->
  <div class="panel panel-primary" ng-hide="HDC.wan.router !=='juniper'">
    <div class="panel-heading clearfix">

      <h3 class="panel-title" style="margin-top: 5px;margin-bottom: 5px;"><strong>Análise de Rede - Avançada</strong>
      </h3>
    </div>
    <div class="panel-body">
      <!-- TORCH -->

      <ul class="nav nav-tabs">
        <li class="active">
          <a data-target="#torch" data-toggle="tab">
            <i class="glyphicon glyphicon-equalizer"></i> Conexões instantâneas </a>

        </li>
        <span class="pull-right">
          <form class="form-inline" role="form">
            <div class="form-group">
              Janela de Monitoramento:
              <select class="form-control" ng-model="HDC.torch.timeout">
                <option value="10">10 segundos</option>
                <option value="60">1 minuto</option>
                <option value="300">5 minutos</option>
                <option value="600">10 minutos</option>
                <option value="900">15 minutos</option>
              </select>
            </div>
            <img src="assets/images/ajax-loader.gif" ng-show="HDC.testandoTorch === 1"> <button
              class="btn btn-default btn-sm" ng-click="HDC.atualizaTorch()" ng-disabled="HDC.testandoTorch===1"><i
                class="glyphicon glyphicon-play"></i> Iniciar Torch</button> <button class="btn btn-default btn-sm"
              ng-click="HDC.pausarTorch()" ng-disabled="HDC.testandoTorch===2 || HDC.testandoTorch===0"><i
                class="glyphicon glyphicon-pause"></i>
              Pausar Torch</button> <button class="btn btn-default btn-sm" ng-click="HDC.cancelarTorch()"
              ng-disabled="HDC.testandoTorch===0"><i class="glyphicon glyphicon-stop"></i> Cancelar Torch</button>
          </form>
        </span>
      </ul>

      <div id="torch" class="tab-pane fade in active">
        <div class="tab-content">
          <div class="pre-scrollable" style="height:280px;overflow-y: scroll;" id="pingresults">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="vert-align text-center" style="width: 10%;">
                    <a href="#" ng-click="HDC.sort('protocol')"> Protocolo
                      <span ng-show="HDC.sortBy == 'protocol' && HDC.sortOrder == '-'" class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'protocol' && HDC.sortOrder == '+'" class="fa fa-caret-up"></span>
                    </a>
                  </th>
                  <th class="vert-align text-center" style="width: 25%;">
                    <a href="#" ng-click="HDC.sort('source-address')"> IP Origem
                      <span ng-show="HDC.sortBy == 'source-address' && HDC.sortOrder == '-'"
                        class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'source-address' && HDC.sortOrder == '+'"
                        class="fa fa-caret-up"></span>
                    </a>
                  </th>
                  <th class="vert-align text-center" style="width: 10%;">
                    <a href="#" ng-click="HDC.sort('source-port')"> Porta Origem
                      <span ng-show="HDC.sortBy == 'source-port' && HDC.sortOrder == '-'"
                        class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'source-port' && HDC.sortOrder == '+'" class="fa fa-caret-up"></span>
                    </a>
                  </th>
                  <th class="vert-align text-center" style="width: 25%;">
                    <a href="#" ng-click="HDC.sort('destination-address')"> IP Destino
                      <span ng-show="HDC.sortBy == 'destination-address' && HDC.sortOrder == '-'"
                        class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'destination-address' && HDC.sortOrder == '+'"
                        class="fa fa-caret-up"></span>
                    </a>
                  </th>
                  <th class="vert-align text-center" style="width: 10%;">
                    <a href="#" ng-click="HDC.sort('destination-port')"> Porta Destino
                      <span ng-show="HDC.sortBy == 'destination-port' && HDC.sortOrder == '-'"
                        class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'destination-port' && HDC.sortOrder == '+'"
                        class="fa fa-caret-up"></span>
                    </a>
                  </th>
                  <th class="vert-align text-center" style="width: 20%;">
                    <a href="#" ng-click="HDC.sort('total-octets')"> Total Octets
                      <span ng-show="HDC.sortBy == 'total-octets' && HDC.sortOrder == '-'"
                        class="fa fa-caret-down"></span>
                      <span ng-show="HDC.sortBy == 'total-octets' && HDC.sortOrder == '+'"
                        class="fa fa-caret-up"></span>
                    </a>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="torch in HDC.torch.connections">
                  <td class="vert-align text-center" style="width: 10%;">{{torch.protocol | protocol}}</td>
                  <td class="vert-align text-center" style="width: 25%;">{{torch.sourceAddress}}</td>
                  <td class="vert-align text-center" style="width: 10%;">{{torch.sourcePort}}</td>
                  <td class="vert-align text-center" style="width: 25%;">{{torch.destinationAddress}}</td>
                  <td class="vert-align text-center" style="width: 10%;">{{torch.destinationPort}}</td>
                  <td class="vert-align text-center" style="width: 20%;">{{torch.totalOctets}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- FIM TORCH -->

    </div>
  </div>
  <!-- FIM ANALISE DE REDE AVANÇADA -->


  <!-- TESTES CAMPO -->

  <div class="panel panel-primary" ng-if="HDC.contrato.hasOwnProperty('cliente')">
    <div class="panel-heading clearfix">
      <span class="pull-right">
        <img src="assets/images/ajax-loader.gif" ng-show="HDC.atualizandotestes"> <button class="btn btn-default btn-sm"
          ng-click="HDC.getAutotesteTestes()"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button>
      </span>
      <h3 class="panel-title" style="margin-top: 5px;"><span class="badge"></span> Testes realizados em campo</h3>
    </div>
    <div class="panel-body">
      <div class="pre-scrollable ng-scope" style="height:180px;">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="vert-align text-center">Data</th>
              <th class="vert-align text-center">Técnico</th>
              <th class="vert-align text-center">Plano</th>
              <th class="vert-align text-center">Plano Down</th>
              <th class="vert-align text-center">Plano Up</th>
              <th class="vert-align text-center">Teste</th>
              <th class="vert-align text-center">Status</th>
              <th class="vert-align text-center">Resultados</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="item in HDC.testes">
              <td class="vert-align text-center">{{item.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
              <td class="vert-align text-center">{{item.tecnico}}</td>
              <td class="vert-align text-center">{{item.plano_nome}}</td>
              <td class="vert-align text-center">{{item.plano_down}}</td>
              <td class="vert-align text-center">{{item.plano_up}}</td>
              <td class="vert-align text-center">{{item.teste}}</td>
              <td class="vert-align text-center"><span class="label label-default"
                  ng-class="[{'label-danger': item.status == 'nok'}, {'label-success': item.status == 'ok'}]">{{item.status}}</span>
              </td>
              <td class="vert-align text-center">{{item.resultados}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- FIM TESTES CAMPO-->

<div ng-include="'app/helpdesk/vlan.multicast.form.html'"></div>
<div ng-include="'app/helpdesk/redirecionamento.form.html'"></div>
<div ng-include="'app/helpdesk/redirecionamento.novo.form.html'"></div>
<div ng-include="'app/helpdesk/instrucoes.aceite.html'"></div>

<div ng-include="'app/common/loading.modal.html'"></div>
