
(function () {
    'use strict';

    angular
        .module('app')
        .factory('AuthorizationService', Service);

    function Service($localStorage, $rootScope, $cookies, $http, jwtHelper, API_CONFIG, $location) {
        var service = {};

        service.Authorize = Authorize;
        service.CurrentRole = CurrentRole;

        return service;

        /**
        * @description determine if an array contains one or more items from another array.
        * @param {array} haystack the array to search.
        * @param {array} arr the array providing items to check for in the haystack.
        * @return {boolean} true|false if haystack contains at least one item from arr.
        */
        function findOne(haystack, arr) {
          return arr.some(function (v) {
            return haystack.indexOf(v) >= 0;
          }); 
        };

        function Authorize(scopes){
            if(scopes){
                if($localStorage.currentUser !== undefined){
                    if(jwtHelper.isTokenExpired($localStorage.currentUser.token)){
                        $rootScope.operador = {"username":""};
                        $rootScope.eventoSelecionado = {id: ''};
                        $cookies.put('eventoSelecionado.id', '');
                        $cookies.put('eventoSelecionado.descricao', '');
                        $cookies.put('eventoSelecionado.categoria', '');

                        redirectToLoginPage();
                        return
                    };
                    if(scopes=="*"){
                        return true;
                    } else {
                        var tokenPayload = jwtHelper.decodeToken($localStorage.currentUser.token);
                        var userscopes = tokenPayload.scope;
                        return (findOne(scopes, userscopes));    
                    }
                } else {
                    $rootScope.operador = {"username":""};
                    $rootScope.eventoSelecionado = {id: ''};
                    $cookies.put('eventoSelecionado.id', '');
                    $cookies.put('eventoSelecionado.descricao', '');
                    $cookies.put('eventoSelecionado.categoria', '');
                    
                    redirectToLoginPage();
                    return
                    //return false;
                }
            } else {
                return true;
            }
        }

        // Redireciona para /login, mantendo os parâmetros atuais da URL em "goto"
        function redirectToLoginPage() {
            
            var currentUrl = $location.url();
            if (currentUrl.indexOf('/login') === -1) {

                var searchParams
                if (currentUrl && currentUrl != '/') {
                    searchParams = 'goto=' + encodeURIComponent(currentUrl)
                }
                else
                    searchParams = {}

                $location.path('/login').search(searchParams);
            }
        }

        function CurrentRole(){
          if($localStorage.currentUser){   
            if(jwtHelper.isTokenExpired($localStorage.currentUser.token)){
               throw new tokenExpiredError();
            };  
            var tokenPayload = jwtHelper.decodeToken($localStorage.currentUser.token);
            return tokenPayload.role;
          }  
        }

        function tokenExpiredError(){
            this.errorcode = '0x001',
            this.message = "Erro";
            this.description = "Token expirado.";
        }

        function notLoggedError(){
            this.errorcode = '0x002',
            this.message = "Erro";
            this.description = "Deslogado.";   
        }
    }
})();