'use strict';

angular.module('app')

    .factory('LoginService', function ($resource, API_CONFIG) {

        return $resource(API_CONFIG.url + '/auth/login', {},
            {
                doLogin: {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    }
                },
                doLogout: {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    }
                }
            }
        );
    });
