
(function () {
    'use strict';

    angular
        .module('app')
        .controller('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', Controller);

    function Controller($location, AuthenticationService, AuthorizationService, toaster, $rootScope) {
        var vm = this;

        vm.login = login;
        vm.current_date = new Date();

        initController();

        function initController() {
            // reset login status
            AuthenticationService.Logout();
        };

        function login() {
            vm.loading = true;
            AuthenticationService.Login(vm.username, vm.senha, function (result) {

                if (result === true) {
                    var roles = AuthorizationService.CurrentRole();

                    var params = $location.search();

                    if (params && params.goto) {
                        // Extract and decode the "goto" parameter from the URL
                        var gotoParam = decodeURIComponent(params.goto);
                      
                        // Split the decoded parameter into path and query string
                        var path, queryString;
                        if (gotoParam.indexOf('?') !== -1) {
                            var splitParams = gotoParam.split('?');
                            path = splitParams[0];
                            queryString = splitParams[1];
                        } else {
                            path = gotoParam;
                            queryString = '';
                        }
                      
                        // Update the location path and search
                        if (queryString) {
                          $location.path(path).search(queryString);
                        } else {
                          $location.path(gotoParam).search({});
                        }
                    }
                    else if (roles.includes("materiais")) {
                        $location.path('/materiais/patrimonios');
                    } else if (roles.includes("helpdesk")) {
                        $location.path('/helpdesk');
                    }  else if (roles.includes("terceiro")) {
                        $location.path('/comercial/cobertura');
                    } else if (roles.includes("comercial")) {
                        $location.path('/comercial/cobertura');

                    } else {
                        $location.path('/dashboard');
                    }

                    $rootScope.$broadcast('logged');
                } else {

                    toaster.pop('error', result.data.status, result.data.mensagem);
                }
            });
        };
    }

})();    