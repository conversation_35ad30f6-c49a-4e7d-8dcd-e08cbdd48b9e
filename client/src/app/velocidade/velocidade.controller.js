(function () {
    'use strict';

    angular
        .module('app')
        .controller('VelocidadeController', VelocidadeController);

    /** @ngInject */
    function VelocidadeController($http, API_CONFIG, $filter) {

        var vm = this;

        vm.busca = busca;

        vm.tipo = 'ip';
        vm.termos = '';

        vm.open1 = open1;
        vm.opened1 = false;
        vm.open2 = open2;
        vm.opened2 = false;

        var inicio = new Date();
        var fim = new Date();
        inicio.setDate(inicio.getDate() - 7);

        vm.dtinicio = inicio;
        vm.dtfim = fim;
          
        vm.resultados = [];

        function busca(){
            vm.resultados = [];
            var filtro = vm.tipo + '=' + vm.termos;
            if((vm.dtinicio != null) && (vm.dtfim != null)){
                filtro += '&dtinicio=' + $filter('date')(vm.dtinicio, "yyyy-MM-dd") + 
                    '&dtfim=' + $filter('date')(vm.dtfim, "yyyy-MM-dd");
            };
            $http.get(API_CONFIG.url+'/velocidade?' + filtro)
                .then(function(response) {
                    angular.copy(response.data, vm.resultados);
                });  
        }

        function open1() {
          vm.opened1 = true;
        };

        function open2() {
          vm.opened2 = true;
        };
    }

})();