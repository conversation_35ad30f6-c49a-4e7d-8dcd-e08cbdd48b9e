<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-move"></i> Relatórios</li>
    <li class="active"><i class="glyphicon glyphicon-dashboard"></i> Medições de Velocidade</li>
</ol>

<div class="barra">
    <div class="form-group">
     <div class="form-group pull-right">
        <form class="form-inline" role="form">
          <div class="form-group">
            <p class="input-group">Período:</p>
            <p class="input-group">
              <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="VC.dtinicio" is-open="VC.opened1" clear-text="Limpar" close-text="Fechar" current-text="Hoje" name="dtinicio"/>
              <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="VC.open1()"><i class="glyphicon glyphicon-calendar"></i></button>
              </span>
              <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="VC.dtfim" is-open="VC.opened2" clear-text="Limpar" close-text="Fechar" current-text="Hoje"/>
              <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="VC.open2()"><i class="glyphicon glyphicon-calendar"></i></button>
              </span>
            </p>
            <p class="input-group">
              <select class="form-control" ng-model="VC.tipo" ng-init="VC.tipo = 'ip'">
                    <option value="ip">IP</option>
                 <!--   <option value="username">Username</option> -->
              </select>
            </p>
            <p class="input-group">
              <input size="30" maxlength="30" class="form-control" type="text" ng-model="VC.termos">
            </p>
            <p class="input-group">
              <button class="btn btn-default" title="Pesquisar" ng-click="VC.busca()">Pesquisar</button>
            </p>
          </div>
        </form>
      </div>
    </div>
</div>

<div>
    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">IP</th>
        <!--  <th class="vert-align text-center">Username</th> -->
          <th class="vert-align text-center">Download (Mb/s)</th>
          <th class="vert-align text-center">Upload (Mb/s)</th>
          <th class="vert-align text-center">S.O.</th>
          <th class="vert-align text-center">Browser</th>
        </tr>
      </thead>
      <tbody>
      <tr ng-repeat="teste in VC.resultados">
          <td class="vert-align text-center">{{teste.data | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
          <td class="vert-align text-center">{{teste.ip}}</td>
          <!-- <td class="vert-align text-center">{{teste.username}}</td> -->
          <td class="vert-align text-center">{{teste.download}}</td>
          <td class="vert-align text-center">{{teste.upload}}</td>
          <td class="vert-align text-center">{{teste.so}}</td>
          <td class="vert-align text-center">{{teste.browser}} {{teste.browser_versao}}</td>
      </tr>
      </tbody>
    </table>
</div>
