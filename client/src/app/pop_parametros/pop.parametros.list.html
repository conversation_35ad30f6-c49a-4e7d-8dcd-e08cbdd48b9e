<div class="barra">
        <div class="form-group">
            <button type="button" class="btn btn-success btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="PPC.novo()" authorize="['pops.write']"><i class="glyphicon glyphicon-plus"></i><br>Incluir</button>
            <button type="button" class="btn btn-warning btn-incluir text-center" data-toggle="modal" data-target="#frmparametros" data-placement="top" rel="tooltip" ng-if="eventoSelecionado.id != ''" ng-click="PPC.clone()" authorize="['pops.write']"><i class="glyphicon glyphicon-copy"></i><br>Clonar</button>
        </div>
</div>

<div ng-if="PPC.parametros.length > 0">

<h5>Parâmetro Atual</h5>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th class="vert-align text-center">Alimentação da concessionária de energia</th>
            <th class="vert-align text-center">Alimentação do POP</th>
            <th class="vert-align text-center">Responsável pelo fornecimento de energia</th>
            <th class="vert-align text-center">Tensão do banco de baterias</th>
            <th class="vert-align text-center">Capacidade do banco de baterias (Ah)</th>
            <th class="vert-align text-center">Data</th>
            <th class="vert-align text-center">Evento</th>
            <th class="vert-align text-center">Operador</th>

          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.alimentacao_concessionaria}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.alimentacao_pop}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.responsavel_energia}}</a></td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.tensao_baterias}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.capacidade_baterias}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.idevento}}</td>
            <td class="vert-align text-center">{{ PPC.ultimoparametro.username}}</td>
          </tr>
        </tbody>
      </table>
</div>

    <h5>Histórico de alterações <span class="badge">{{PPC.parametros.length}}</span></h5>

    <table class="table table-striped table-hover table-bordered">
      <thead>
        <tr>
          <th class="vert-align text-center">Alimentação da concessionária de energia</th>
          <th class="vert-align text-center">Alimentação do POP</th>
          <th class="vert-align text-center">Responsável pelo fornecimento de energia</th>
          <th class="vert-align text-center">Tensão do banco de baterias</th>
          <th class="vert-align text-center">Capacidade do banco de baterias (Ah)</th>
          <th class="vert-align text-center">Data</th>
          <th class="vert-align text-center">Evento</th>
          <th class="vert-align text-center">Operador</th>
        </tr>
      </thead>
      <tbody>
               <tr ng-repeat="parametro in PPC.parametros | filter:filtro" ng-class="{'success': parametro.idevento == eventoSelecionado.id}">
                 <td class="vert-align text-center">{{ parametro.alimentacao_concessionaria}}</td>
                 <td class="vert-align text-center">{{ parametro.alimentacao_pop}}</td>
                 <td class="vert-align text-center">{{ parametro.responsavel_energia}}</a></td>
                 <td class="vert-align text-center">{{ parametro.tensao_baterias}}</td>
                 <td class="vert-align text-center">{{ parametro.capacidade_baterias}}</td>
                 <td class="vert-align text-center">{{ parametro.datacad | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                 <td class="vert-align text-center">{{ parametro.idevento}}</td>
                 <td class="vert-align text-center">{{ parametro.username}}</td>
      </tbody>
      </table>
