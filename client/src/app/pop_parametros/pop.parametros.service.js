'use strict';

angular.module('app')

    .factory('PopParametrosService', function ($resource, API_CONFIG) {
        return $resource(API_CONFIG.url + '/pops/:pop/parametros', {},
            {
                getParametros: {method: 'GET', isArray: false},
                insertParametros: {
                    method: 'POST',
                    params: {pop: '@pop'},
                    headers: {
                        'Content-Type': 'application/json;charset=UTF8'
                    },
                    isArray: false
                }
            }
        );
    })
    .service('PopParametro', function () {
        return {};
    });
