<div class="modal" id="frmparametros" tabindex="-1" role="dialog"
aria-labelledby="frmparametroslabel" aria-hidden="true" modal="showModal" close="cancel()">

<div class="modal-dialog">
<div class="modal-content">
    <!-- Modal Header -->
    <div class="modal-header">
        <button type="button" class="close"
           data-dismiss="modal">
               <span aria-hidden="true">&times;</span>
               <span class="sr-only">Fechar</span>
        </button>
        <h4 class="modal-title" id="frmparametroslabel">
            Parâmetros gerais
        </h4>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
        <form role="form" name="frmparametros" class="form-horizontal">

          <div class="form-group">
            <label for="concessionaria">Alimentação da concessionária de energia</label>
              <select class="form-control" ng-model="PPC.parametro.alimentacao_concessionaria" name="concessionaria"
                ng-change="PPC.changeAlimentacao()"
                required>
                <option value="Assinante - CDM">Assinante - CDM</option>
                <option value="12 VDC">12 VDC</option>
                <option value="24 VDC">24 VDC</option>
                <option value="48 VDC">48 VDC</option>
                <option value="127 VAC">127 VAC</option>
                <option value="220 VAC">220 VAC</option>
              </select>
          </div>

          <div class="form-group">
            <label for="alimentacao">Alimentação do POP</label>
              <select class="form-control" ng-model="PPC.parametro.alimentacao_pop" name="alimentacao" required>
                <option value="AC - Nobreak com bateria externa">AC - Nobreak com bateria externa</option>
                <option value="AC - Nobreak com bateria interna">AC - Nobreak com bateria interna</option>
                <option value="AC - Sem Nobreak">AC - Sem Nobreak</option>
                <option value="DC - Fonte Chaveada 24 VDC">DC - Fonte Chaveada 24 VDC</option>
                <option value="DC - Fonte Chaveada 48 VDC">DC - Fonte Chaveada 48 VDC</option>
                <option value="DC - Fonte POE 24 VDC nos assinantes - CDM">DC - Fonte POE 24 VDC nos assinantes - CDM</option>
                <option value="DC - Fonte Retificadora 48 VDC">DC - Fonte Retificadora 48 VDC</option>
                <option value="DC - Painel Solar + Controlador">DC - Painel Solar + Controlador</option>
                <option value="DC - Rede elétrica do assinante"></option>
              </select>
          </div>


          <div class="form-group">
            <label for="responsavel">Responsável pelo fornecimento de energia</label>
              <select class="form-control" ng-model="PPC.parametro.responsavel_energia" name="responsavel" required>
                <option value="Assinante">Assinante</option>
                <option value="Condomínio">Condomínio</option>
                <option value="Telemídia">Telemídia</option>
                <option value="Outros">Outros</option>
              </select>
          </div>

          <div class="form-group">
            <label for="tensao">Tensão do banco de baterias</label>
              <select class="form-control" ng-model="PPC.parametro.tensao_baterias" name="tensao" required>
                <option value="12 VDC">12 VDC</option>
                <option value="24 VDC">24 VDC</option>
                <option value="48 VDC">48 VDC</option>
                <option value="Sem Banco">Sem Banco</option>
              </select>
          </div>


        <div class="form-group">
            <label for="capacidade">Capacidade do banco de baterias (Ah)</label>
                <input type="text" class="form-control"
                  id="capacidade"
                  name="capacidade"
                  ng-model="PPC.parametro.capacidade_baterias"
                  >
        </div>

        </form>


    </div>

    <!-- Modal Footer -->
    <div class="modal-footer">
        <button type="button" class="btn btn-default"
                data-dismiss="modal">
                    Fechar
        </button>

      <button type="button" class="btn btn-primary" ng-click="PPC.saveParametro(PPC.parametro);" ng-disabled="frmparametros.$invalid" data-dismiss="modal">
                Salvar</button>
    </div>
</div>
</div>
</div>
