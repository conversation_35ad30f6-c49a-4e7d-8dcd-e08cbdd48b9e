(function () {
    'use strict';

    angular
        .module('app')
        .controller('PopParametrosController', PopParametrosController);

    /** @ngInject */
    function PopParametrosController($scope, $route, $rootScope, toaster, PopParametrosService) {

        var vm = this;

        vm.parametros = [];
        vm.getParametros = getParametros;
        vm.saveParametro = saveParametro;
        vm.changeAlimentacao = changeAlimentacao;
        vm.novo = novo;
        vm.clone = clone;
        vm.parametro = {};


        activate();

        function activate() {
            getParametros();
        }

        function getParametros(){
            var Parametros = PopParametrosService.get({pop: $scope.$parent.pop.pop});
            Parametros.$promise.then(function (data) {
            vm.parametros = data.dados;
            console.log(vm.parametros);
            vm.ultimoparametro = angular.copy(vm.parametros[0]);
            });
        }

        function saveParametro() {

            vm.parametro.pop = $scope.$parent.pop.pop;
            vm.parametro.idevento = $rootScope.eventoSelecionado.id;
            vm.parametro.username = $rootScope.operador.username;


            PopParametrosService.insertParametros(vm.parametro, function (response) {
                if (response.status === 'OK') {
                    toaster.pop('success', "Parâmetro salvo", "Parâmetro adicionado com sucesso!");
                    getParametros();
                } else {
                    toaster.pop('error', response.mensagem, response.dados);
                }
                vm.parametro = {};
            });

        };

        function clone() {
          vm.parametro = angular.copy(vm.ultimoparametro);
        };

        function novo() {
           vm.parametro = {};
        };

        function changeAlimentacao(){
          if(vm.parametro.alimentacao_concessionaria == 'Assinante - CDM'){
              vm.parametro.alimentacao_pop = 'DC - Fonte POE 24 VDC nos assinantes - CDM';
              vm.parametro.responsavel_energia = 'Assinante';
              vm.parametro.tensao_baterias = 'Sem Banco';
              vm.parametro.capacidade_baterias = '0';
          }

        }

    }

})();
