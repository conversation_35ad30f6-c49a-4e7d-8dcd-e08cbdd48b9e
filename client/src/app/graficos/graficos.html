<ol class="breadcrumb">
  <li><a href="/dashboard"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li class="active"><i class="glyphicon glyphicon-signal"></i> Gráficos</li>
</ol>


<div class="admin-menu-tree">
    <script type="text/ng-template" id="children_panel.html">
        <div ui-tree-handle>
           <!--<iframe width="100%" height="405px" frameborder="0" allowfullscreen scrolling="no" seamless="seamless"> -->
            <div style="min-width: 900px; min-height: 405px;">
            <a href="/graficos/{{::item.graphid}}?title={{::item.title}}" target="_blank">
              <!-- <img ng-src="{{'http://lab.pocos-net.com.br/chart2.php?graphid=' + item.graphid + '&period=3600' | trustAsResourceUrl}}" spinkit-image-preloader='rotating-plane-spinner' style="display: block; margin:auto;"></a> -->
                <img ng-src="{{graficos[item.graphid] | trustAsResourceUrl}}" spinkit-image-preloader='circle-spinner' style="display: block; margin:auto;"></a>
            </div>
           <!-- </iframe> -->

        </div>


        </div>
    </script>

    <script type="text/ng-template" id="tree_children_renderer.html">
        <div ui-tree-handle>
           <!-- <a class="tree-icon" data-nodrag ng-click="toggleChildren(this)"><span class="fa" ng-class="{'fa-folder': collapsed, 'fa-folder-open': !collapsed}"></span></a> -->
            <a class="btn btn-primary btn-xs" data-nodrag ng-click="toggleChildren(this)"><span
                    class="glyphicon"
                    ng-class="{
          'glyphicon-plus': collapsed,
          'glyphicon-minus': !collapsed
        }"></span></a>
            <a href="" data-nodrag ng-click="toggleChildren(this)">{{ ::item.title }}</a>
            <span class="pull-right"><i class="glyphicon" ng-class="{
            'glyphicon-globe': item.current == 'cidade',
            'glyphicon-record' : item.current == 'pop',
            'glyphicon-tasks' : item.current == 'host'
            }"></i>
            <select ng-show="item.current=='graficos' && !this.collapsed" ng-model="periodo[item.graphid]" ng-init="periodo[item.graphid]=3600">
                <option value="3600" selected="true">1 hora</option>
                <option value="21600">6 horas</option>
                <option value="43200">12 horas</option>
                <option value="86400">24 horas</option>
                <option value="604800">1 semana</option>
                <option value="2592000">1 mês</option>
                <option value="15552000">6 meses</option>
                <option value="31104000">1 ano</option>
            </select>
            <a class="btn btn-primary btn-xs" data-nodrag ng-click="refreshImage(item.graphid, periodo[item.graphid])" ng-show="item.current=='graficos' && !this.collapsed">
                 <i class="glyphicon glyphicon-refresh" ></i> Atualizar</a>
            </span>
        </div>

        </div>
        <ol ui-tree-nodes="treeOptions" ng-model="item.children" ng-class="{hidden: collapsed}">
            <li ng-repeat="item in item.children" ui-tree-node ng-include="item.current=='grafico' && 'children_panel.html' || 'tree_children_renderer.html'" data-collapsed="!item.expanded">
            </li>
        </ol>
    </script>
    <div ui-tree="treeOptions" id="admin-tree-root" data-drag-enabled="false">
        <ol ui-tree-nodes ng-model="children">
            <li ng-repeat="item in children" ui-tree-node ng-include="'tree_children_renderer.html'" data-collapsed="!item.expanded">
            </li>
        </ol>
    </div>
</div>

