'use strict';

angular.module('app')

    .controller('GraficoDetailController', ['$scope', '$route', '$routeParams', function ($scope, $route, $routeParams) {

        var zabbixurl = 'http://lab.pocos-net.com.br/chart2.php?graphid=';
        $scope.graphid = $route.current.params.id;
        var graphid = $scope.graphid;
        var title = '';
        
        if($routeParams.title !== undefined){
           title = $routeParams.title; 
        };
        
        $scope.graficos = [
            {
                period: 3600,
                url: zabbixurl + graphid + '&period=3600&decache=' + Math.random(),
                title: title + ' (1 Hora)'
            },
            {
                period: 86400,
                url: zabbixurl + graphid + '&period=86400&decache=' + Math.random(),
                title: title + ' (1 Dia)'
            },
            {
                period: 604800,
                url: zabbixurl + graphid + '&period=604800&decache=' + Math.random(),
                title: title + ' (1 Semana)'
            },
            {
                period: 2592000,
                url: zabbixurl + graphid + '&period=2592000&decache=' + Math.random(),
                title: title + ' (1 Mês)'
            },
            {
                period: 31104000,
                url: zabbixurl + graphid + '&period=31104000&decache=' + Math.random(),
                title: title + ' (1 Ano)'
            }
        ];

        $scope.refreshImage = function (id) {

            $scope.graficos[id].url = zabbixurl + graphid + '&period=' + $scope.graficos[id].period + '&decache=' + Math.random();
        };

    }]);

