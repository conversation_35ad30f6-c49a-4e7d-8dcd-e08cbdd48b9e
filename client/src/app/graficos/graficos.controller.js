'use strict';

angular.module('app')

    .controller('GraficosController', ['$scope', '$http', 'API_CONFIG', function ($scope, $http, API_CONFIG) {

        $scope.treeOptions = {};

        $scope.selectedItem = {};

        $scope.graficos = [];

        var params = '';

        $http.get(API_CONFIG.url + '/graficos').then(function (response) {
            $scope.children = response.data;
        });

        $scope.toggleChildren = function (scope) {
            if (!scope.$nodeScope.$modelValue.children) {
                //scope.$nodeScope.$modelValue.getList("children").then(function(data) {

                if (scope.$nodeScope.$modelValue.current === 'graficos') {
                    params = scope.$nodeScope.$modelValue.graphid;
                    $scope.graficos[params] = 'http://lab.pocos-net.com.br/chart2.php?graphid=' +
                        params + '&period=3600' + '&decache=' + Math.random();
                } else {
                    params = scope.$nodeScope.$modelValue.title;
                }

                $http.get(API_CONFIG.url + '/graficos?current=' +
                    scope.$nodeScope.$modelValue.current + '&params=' + params).then(function (response) {
                    scope.$nodeScope.$modelValue.children = response.data;
                    if (scope.$nodeScope.$modelValue.children.length > 0) {
                        scope.toggle();
                    }
                });


            } else {
                if (scope.$nodeScope.$modelValue.children.length > 0) {
                    scope.toggle();
                }
            }
        };

        $scope.refreshImage = function (graphid, periodo) {
            $scope.graficos[graphid] = 'http://lab.pocos-net.com.br/chart2.php?graphid=' +
                graphid + '&period=' + periodo + '&decache=' + Math.random();
        };


    }]);

