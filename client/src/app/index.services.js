'use strict';

angular.module('ngClickCopy', [])
.service('ngCopy', ['$window', 'toaster', function ($window, toaster) {
	var body = angular.element($window.document.body);
	var textarea = angular.element('<textarea/>');
	textarea.css({
		position: 'fixed',
		opacity: '0'
	});

	return function (toCopy) {
		textarea.val(toCopy);
		body.append(textarea);
		textarea[0].select();

		try {
			var successful = document.execCommand('copy');
			if (!successful) throw successful;
			//toaster.pop('success', "Copiado", "Endereço do grupo copiado para a Área de Transferência!");
		} catch (err) {
			window.prompt("Copy to clipboard: Ctrl+C, Enter", toCopy);
		}

		textarea.remove();
	}
}]);